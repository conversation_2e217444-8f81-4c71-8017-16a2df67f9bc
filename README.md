# Оглавление

## Порядок сборки

1. Сборка через Maven(`mvn clean`, затем `mvn compile`).
2. Можно запускать.


## Запуск

1. [Запуск на тестовом сервере](#запуск-на-тестовом-сервере)
2. [Запуск на локальной машине](#запуск-на-локальной-машине)
3. [Запуск через Docker](#запуск-через-docker)

## Дополнительные возможности

1. [Ручки для принудительного запуска тасок (@Scheduled)](#ручки-для-принудительного-запуска-тасок)
2. [Дебаг проекта с помощью приложения](#дебаг-проекта-с-помощью-приложения)

---------------------------------------

## Запуск

**Рекомендуемая версия JDK - 1.8.0_152**. **P.S. На 1.8.0_322 тоже запускается**

### Запуск на тестовом сервере:

```sh
java -jar app.jar --spring.profiles.active=dev --spring.config.location=/path/to/additional/settings
```

Приложение запускается с профилем настроек *dev*. Это значит, что приложение возьмет настройки из файла настроек `application-dev`, доступном в classpath. Кроме того, в файле, который указан в необязательной опции `--spring.config.location`, ты можешь указать дополнительные настройки, которые объединятся с настройками профиля и при необходимости переопределят совпадающие. Например, в этом файле можно задать настройки, которые не хочется хранить в самом приложении (и системе контроля версий), вроде доступов к базам данных и токенов api внешних сервисов.

### Запуск на локальной машине:

```sh
java -jar app.jar --spring.profiles.active=debug --spring.config.location=/path/to/additional/settings
```

# Запуск через Docker

## Для Windows:

1. Находясь в директории проекта:

```shell
docker compose -f .\docker\docker-compose.yml up -d
```

2. Для первого запуска потребуется исходное состояние БД, для этого нужен дамп. как вариант его можно положить в директорию выше, т.е. `..\dump.sql` копируем и накатываем дамп:

```shell
docker cp ..\dump.sql oskelly-dev-pg:/dump.sql
docker exec oskelly-dev-pg psql -d oskelly -U oskelly -f /dump.sql -v ON_ERROR_STOP=1
```

Для Windows

```shell
psql -U oskelly -d oskelly -f d380dump.sql
```

3. Добавляем проперти файл `application-local.yaml` он в `.gitignore` и у каждого свой. Создаем две директории и добавляем в `resources.images` и `resources.userfiles` - как показано в шаблоне ниже

```yaml
bank-account:
  payment-version: tcb-1.0

resources:
  images:
    pathToDir: { ТУТ СОЗДАЙ ПАПКУ У СЕБЯ ЛОКАЛЬНО - СЮДА ПОЛОЖИ ПУТЬ }
  userfiles:
    pathToDir: { ТУТ СОЗДАЙ ПАПКУ У СЕБЯ ЛОКАЛЬНО - СЮДА ПОЛОЖИ ПУТЬ }
```

4. Запускаем OskellyApplication с Program Args:
    1. `--spring.profiles.active=base,master,development`
    2. `--spring.config.location=.\oskelly-ext-dev\src\main\resources\`
5. прокручиваем к подразделу разделу "Для любой системы"

## Для MacOS:

[Сборка проекта oskelly-ext-dev](https://wiki.yandex.ru/onboardingvit/build-oskelly-ext-dev/)

## Для любой системы

1. Открываем http://localhost:8080, регистрируем нового пользователя
2. В БД меняем роль на админа

```sql
select *
from "user"
order by id desc;

update user_authority_binding
set user_id = ?
where user_id = 11;
```

где user_id = ? - id зарегистрированного пользователя

3. done

## Дополнительные возможности

### Ручки для принудительного запуска тасок

Для упрощения дебага и тестирования тасок были добавлены ручки для принудительного запуска.

```java
import su.reddot.presentation.api.v2.ScheduledTaskRunnerControllerApiV2;
```

В этом контроллере есть две ручки:

- ```/api/v2/task/runner/available``` эта ручка возвращает список названий всех доступных тасок для запуска
- ```/api/v2/task/runner/run``` эта ручка для запуска тасок, она имеет один обязательный параметр ```taskName```
  значение для этого параметра надо брать из результата предыдущей ручки

**Важный момент!**

Эти ручки буду работать только с профилем ```debug```

### Дебаг проекта с помощью приложения

Для тестирования приложения локально с использованием мобильного приложения на iOS, выполните следующие шаги:

**Пререквизиты:**

- iPhone.
- Доступ к монолиту `oskelly-ext-dev`, запущенному локально на доступном порту.
- Доступ к [AppCenter](https://install.appcenter.ms/users/Oskelly/apps/iOS-Ru) для установки приложения на iPhone. Для получения доступа обратитесь к @<EMAIL>
- Рабочий Google аккаунт (@oskelly.ru) для входа в AppCenter и скачивания профиля разработчика.

**Шаг 1: Запуск монолита**

Запустите монолит `oskelly-ext-dev` локально на любом доступном порту.

**Шаг 2: Настройка iPhone**

1. Откройте браузер на вашем iPhone и перейдите по
   ссылке [AppCenter](https://install.appcenter.ms/users/Oskelly/apps/iOS-Ru).
2. Запросите доступ к приложению у @<EMAIL>
3. После получения доступа, войдите в свой рабочий Google аккаунт (@oskelly.ru) на AppCenter.
4. Скачайте профиль от AppCenter.
5. Установите профиль от AppCenter на вашем iPhone:
6. Включите режим разработчика на вашем iPhone.
7. Скачайте последнюю доступную сборку приложения из AppCenter на ваш iPhone.

**Шаг 3: Настройка приложения на iPhone.**

1. Узнайте адрес машины, по которому запущен сервер монолита в локальной сети. Этот адрес можно получить через маршрутизатор или другими доступными способами.
2. Откройте скачанное приложение на вашем iPhone.
3. В настройках приложения (Настройки -> Поиск -> OSKELLY -> Enter the server address) укажите адрес машины в локальной сети и убедитесь, что порт, на котором работает сервер, открыт.

После выполнения всех указанных шагов, ваш iPhone будет отправлять запросы на сервер, запущенный на локальной машине.
Теперь вы можете провести тестирование приложения локально с использованием мобильного приложения на iOS.

### Swagger
Web UI http://localhost:8080/swagger-ui.html
API specification http://localhost:8080/documentation/v2/api-docs