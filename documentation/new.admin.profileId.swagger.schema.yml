openapi: 3.0.3
info:
  title: Oskelly passport API
  description: Generated DTO, Controller, Swagger Documentation, Client
  version: "1.0"
paths:
  /api/v3/admin/user/profile/id:
    get:
      summary: Get profiles by admin
      tags:
        - Admin profiles
      operationId: getProfilesByAdmin
      parameters:
        - $ref: "#/components/parameters/UserIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetProfilesResponse"
    post:
      summary: Create profile
      tags:
        - Admin profiles
      operationId: createProfileByAdmin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateProfileRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateProfileResponse"
    put:
      summary: Update profile
      tags:
        - Admin profiles
      operationId: updateProfileByAdmin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateProfileRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateProfileResponse"
    delete:
      summary: Delete profile
      tags:
        - Admin profiles
      operationId: deleteProfileByAdmin
      parameters:
        - $ref: "#/components/parameters/ProfileIdParam"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Api2ResponseOfString"
  /api/v3/admin/user/profile/id/order/filter:
    post:
      summary: Admin get orders
      tags:
        - Admin orders
      operationId: getOrdersByAdmin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetOrderRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetOrderResponse"
  /api/v3/admin/user/profile/id/view:
    post:
      summary: Get user data access
      tags:
        - Admin user data
      operationId: getUserDataAccess
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserDataAccessRequestDTO"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDataAccessResponseDTO"
  /api/v3/admin/user/profile/id/order:
    post:
      summary: Admin create order
      tags:
        - Admin orders
      operationId: createOrderByAdmin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrderResponse"
    put:
      summary: Update order
      tags:
        - Admin orders
      operationId: updateOrderByAdmin
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
      responses:
        "200":
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateOrderResponse"
components:
  parameters:
    UserIdParam:
      in: query
      name: userId
      required: true
      schema:
        type: integer
        format: int64
    ProfileIdParam:
      in: query
      name: profileId
      required: true
      schema:
        type: string
    PassportIdParam:
      name: passportId
      in: query
      required: true
      schema:
        type: string
    OrderIdParam:
      name: orderId
      in: query
      required: true
      schema:
        type: string
  schemas:
    UserDataAccessDTO:
      properties:
        value:
          type: string
        reason:
          type: string
    Inn:
      properties:
        number:
          type: string
    PassportRu:
      properties:
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
        seriesNumber:
          type: string
        issuedBy:
          type: string
        issuedAt:
          type: string
          format: date-time
    Phone:
      properties:
        phoneNumber:
          type: string
    Profile:
      properties:
        id:
          type: string
          format: uuid
        userId:
          type: integer
          format: int64
        inn:
          $ref: "#/components/schemas/Inn"
        passportRu:
          $ref: "#/components/schemas/PassportRu"
        phone:
          $ref: "#/components/schemas/Phone"
    Order:
      properties:
        orderId:
          type: integer
          format: int64
        profileId:
          type: string
          format: uuid
        userId:
          type: integer
          format: int64
        inn:
          $ref: "#/components/schemas/Inn"
        passportRu:
          $ref: "#/components/schemas/PassportRu"
        phone:
          $ref: "#/components/schemas/Phone"
    CreateProfileRequest:
      properties:
        profile:
          $ref: "#/components/schemas/Profile"
    CreateProfileResponse:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Profile'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
    UpdateProfileRequest:
      properties:
        profile:
          $ref: "#/components/schemas/Profile"
    UpdateProfileResponse:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Profile'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
    UserDataAccessRequestDTO:
      properties:
        targetUserId:
          type: "integer"
          format: "int64"
        email:
          type: "boolean"
        phone:
          type: "boolean"
        sex:
          type: "boolean"
        birthdate:
          type: "boolean"
    UserDataAccessResponseDTO:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          properties:
            email:
              $ref: "#/components/schemas/UserDataAccessDTO"
            phone:
              $ref: "#/components/schemas/UserDataAccessDTO"
            sex:
              $ref: "#/components/schemas/UserDataAccessDTO"
            birthdate:
              $ref: "#/components/schemas/UserDataAccessDTO"

    GetOrderRequest:
      properties:
        orderIds:
          type: array
          items:
            type: integer
            format: int64
    GetOrderResponse:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          properties:
            orders:
             type: array
             items:
               $ref: "#/components/schemas/Order"
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    CreateOrderRequest:
      properties:
        order:
          $ref: "#/components/schemas/Order"
    CreateOrderResponse:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Order'
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
    GetProfilesResponse:
      required:
        - data
      type: object
      properties:
        data:
          type: object
          properties:
            profiles:
             type: array
             items:
               $ref: "#/components/schemas/Profile"
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
        timestamp:
          type: integer
          format: int64
        validationMessages:
          type: object
          additionalProperties:
            type: string
    ApiFailResponse:
      properties:
        failCode:
          type: string
          enum:
            - OK
            - UNKNOWN_ERROR
            - FIELD_REQUIRED
            - UNABLE_TO_FIND_PASSPORT
        failText:
          type: string
    Api2ResponseOfString:
      type: "object"
      properties:
        data:
          type: "string"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfString"