openapi: 3.0.3

info:
  title: Oskelly Admin API v2, promocodes
  description: Oskelly Admin API v2, promocodes
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

paths:
  /api/v2/admin/promocodes:
    get:
      tags:
        - Promocode
      summary: Get list of promocodes
      operationId: getPromocodes
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/PageSizeParam'
        - name: 'isActive'
          schema:
            type: boolean
          in: query
          required: false
        - name: 'excludeOneTimePromoCode'
          schema:
            type: boolean
          in: query
          required: false
        - name: 'createdAtFrom'
          schema:
            type: string
            format: date
          in: query
          required: false
        - name: 'createdAtTo'
          schema:
            type: string
            format: date
          in: query
          required: false
        - name: 'codeContains'
          schema:
            type: string
          in: query
          required: false
        - name: 'sortBy'
          schema:
            type: string
            enum:
              - beginPrice
              - code
              - expiresAt
              - createdAt
          in: query
          required: false
        - name: 'descending'
          schema:
            type: boolean
          in: query
          required: false
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePromocodes'
  /api/v2/admin/promocodes/create:
    post:
      tags:
        - Promocode
      summary: Create promocode
      operationId: createPromocode
      requestBody:
        description: Optional description in *Markdown*
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminPanelPromocodeBase'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePromocode'
  /api/v2/admin/promocodes/update:
    post:
      tags:
        - Promocode
      summary: Update promocode
      operationId: updatePromocode
      parameters:
        - $ref: '#/components/parameters/IdParam'
      requestBody:
        description: Optional description in *Markdown*
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminPanelPromocodeBase'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePromocode'
  /api/v2/admin/promocodes/detail:
    get:
      tags:
        - Promocode
      summary: Get promocode detailed info
      operationId: detailPromocode
      parameters:
        - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePromocode'
  /api/v2/admin/promocodes/delete:
    post:
      tags:
        - Promocode
      summary: Delete promocode
      operationId: deletePromocode
      parameters:
        - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePromocode'
  /api/v2/admin/promocodes/clone:
    post:
      tags:
        - Promocode
      summary: Clone promocode
      operationId: clonePromocode
      parameters:
        - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponsePromocode'
  /api/v2/admin/promocodes/parse:
    post:
      tags:
        - Promocode
      summary: Parse promocode file
      operationId: parsePromocodeFile
      parameters:
        - $ref: '#/components/parameters/parseItemType'
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - file
              type: object
              properties:
                file:
                  type: string
                  format: binary
            encoding:
              data:
                contentType: text/plain
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PromocodeRuleLinkInfoArray'

components:
  parameters:
    IdParam:
      name: id
      in: query
      description: Promocode ID
      required: true
      schema:
        type: integer
        format: int64
    parseItemType:
      name: type
      in: query
      description: Promocode parse item type
      required: true
      schema:
        $ref: '#/components/schemas/ParseItemType'
    PageParam:
      name: page
      in: query
      description: page number
      required: false
      schema:
        type: integer
        format: int32
        default: 1
    PageSizeParam:
      name: rowsPerPage
      in: query
      description: page number
      required: false
      schema:
        type: integer
        format: int32
        default: 15

  schemas:
    PromocodeRuleLinkInfoArray:
      required:
        - data
      type: object
      properties:
        data:
          type: array
          items:
            $ref: './partial.yaml#/components/schemas/PromocodeTypeDTO'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    PromocodeRuleLinkInfo:
      required:
        - id
      type: object
      properties:
        id:
          type: integer
          description: Link item ID
          format: int32
        name:
          type: string
          description: Link item name
    PromocodeFilterInfo:
      required:
        - value
      type: object
      properties:
        value:
          type: integer
          description: Expected value
          format: int32
        relation:
          $ref: '#/components/schemas/FilterRelation'
    Api2ResponsePromocode:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/AdminPanelPromocode'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    Api2ResponsePromocodes:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/Page'
        errorData:
          type: object
        executionTimeMillis:
          type: integer
          format: int64
        message:
          type: string
    Page:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/AdminPanelPromocode'
        itemsCount:
          type: integer
          format: int32
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
    AdminPanelPromocodeBase:
      required:
        - code
        - numberOfApplies
        - appliedNumberType
      type: object
      properties:
        code:
          type: string
          description: Promocode name
        description:
          type: string
          description: Promocode description
        percent:
          type: number
          description: Active from price
          format: double
        amount:
          type: number
          description: Active from price
          format: double
        beginPrice:
          type: number
          description: Active from price
          format: double
        startsAt:
          type: string
          format: date-time
        expiresAt:
          type: string
          format: date-time
        type:
          type: object
          items:
            $ref: './partial.yaml#/components/schemas/PromocodeTypeDTO'
        numberOfApplies:
          type: number
        appliedNumberType:
          $ref: '#/components/schemas/PromoCodeAppliedNumberType'
        resetRange:
          type: string
          items:
            $ref: './partial.yaml#/components/schemas/PromoCodeResetRange'
        barter:
          type: boolean
        iosApplication:
          type: boolean
        androidApplication:
          type: boolean
        productList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        exceptProductList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        exceptBrandsList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        brandsList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        categoriesList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        exceptCategoriesList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        sellersList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        exceptSellersList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        buyersList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        productConditionList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        exceptProductConditionList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        userLoyaltyTagList:
          type: array
          items:
            $ref: '#/components/schemas/PromocodeRuleLinkInfo'
        ordersCountFilters:
          type: array
          items:
            $ref: "#/components/schemas/PromocodeFilterInfo"
        ordersRelativeNumbers:
          type: array
          items:
            $ref: "#/components/schemas/PromocodeFilterInfo"
        orderAmountFilters:
          type: array
          items:
            $ref: "#/components/schemas/PromocodeFilterInfo"
        ordersAmountSummaryFilters:
          type: array
          items:
            $ref: "#/components/schemas/PromocodeFilterInfo"
    AdminPanelPromocode:
      allOf:
        - $ref: '#/components/schemas/AdminPanelPromocodeBase'
        - type: object
          required:
            - id
          properties:
            id:
              type: integer
              description: Promocode ID
              format: int32
            dealsCount:
              type: integer
              format: int32
            createdBy:
              $ref: './partial.yaml#/components/schemas/UserDTO'
    ParseItemType:
      type: string
      enum:
        - BUYER
        - BRAND
        - CATEGORY
        - SELLER
        - PRODUCT
    PromoCodeAppliedNumberType:
      type: string
      enum:
        - PER_BUYER
        - GLOBALLY
    FilterRelation:
      type: string
      description: Relation to expected value
      enum:
        - EQUALS
        - EQUALS_OR_LESS
        - LESS
        - EQUALS_OR_GREATER
        - GREATER