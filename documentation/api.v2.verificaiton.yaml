openapi: 3.0.3

info:
  title: Oskelly API v2, Phone verification
  version: 1.0.0

tags:
  - name: verification-controller-api-v-2
    description: Phone verification Controller Api V 2

paths:
  /api/v2/verification/verifyPhoneNumber:
    post:
      tags:
        - verification-controller-api-v-2
      summary: verifyPhoneNumber
      operationId: verifyPhoneNumberUsingPOST
      requestBody:
        description: "payload"
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerificationPayloadDto'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
              example: {
                "data": true,
                "executionTimeMillis": 0,
                "timestamp": 0
              }
        '403':
          description: Forbidden
        '500':
          description: Not Found

  /api/v2/verification/verify:
    post:
      tags:
        - verification-controller-api-v-2
      summary: verify
      operationId: verifyUsingPOST
      requestBody:
        description: "payload"
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerificationPayloadDto'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfBoolean'
              example: {
                "data": true,
                "executionTimeMillis": 0,
                "timestamp": 0
              }
        '403':
          description: Forbidden
        '500':
          description: Not Found

  /api/v2/verification/generate:
    post:
      tags:
        - verification-controller-api-v-2
      summary: generateVerificationCode
      operationId: generateVerificationCodeUsingPOST
      requestBody:
        description: "payload"
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerationPayloadDto'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
              example: {
                "data": "UUID",
                "executionTimeMillis": 0,
                "timestamp": 0
              }
        '400':
          description: Bad Request
        '409':
          description: Conflict
        '429':
          description: Too Many Requests
        '500':
          description: Internal Server Error

  /api/v2/verification/verifyPhoneNumberAndGetJWT:
    post:
      tags:
        - verification-controller-api-v-2
      summary: verifyPhoneNumberAndGetJWT
      operationId: verifyPhoneNumberAndGetJWTUsingPOST
      requestBody:
        description: "payload"
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerificationPayloadDto'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Api2ResponseOfString'
              example: {
                "data": true,
                "executionTimeMillis": 0,
                "timestamp": 0
              }
        '403':
          description: Forbidden
        '500':
          description: Not Found

components:
  schemas:
    VerificationPayloadDto:
      type: "object"
      required:
        - "code"
        - "operation"
        - "phoneNumber"
        - "token"
      properties:
        code:
          type: "string"
          pattern: "^\\d{4}$"
        context:
          type: "object"
          additionalProperties:
            type: "string"
        operation:
          type: "string"
          enum:
            - "VERIFY_PHONE_NUMBER"
            - "AUTH_OR_REGISTER"
            - "VERIFY_PHONE_NUMBER_3RD_PARTY"
        phoneNumber:
          type: "string"
          pattern: "^\\+\\d{6,15}$"
        token:
          type: "string"
      title: "VerificationPayloadDto"

    GenerationPayloadDto:
      type: "object"
      required:
        - "operation"
        - "phoneNumber"
        - "path"
      properties:
        context:
          type: "object"
          additionalProperties:
            type: "string"
        operation:
          type: "string"
          enum:
            - "VERIFY_PHONE_NUMBER"
            - "AUTH_OR_REGISTER"
            - "VERIFY_PHONE_NUMBER_3RD_PARTY"
        path:
          type: "string"
          enum:
            - "SMS"
            - "WA"
        phoneNumber:
          type: "string"
          pattern: "^\\+\\d{6,15}$"
      title: "GenerationPayloadDto"

    Api2ResponseOfBoolean:
      type: "object"
      properties:
        data:
          type: "boolean"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfBoolean"

    Api2ResponseOfString:
      type: "object"
      properties:
        data:
          type: "string"
        errorData:
          type: "object"
        executionTimeMillis:
          type: "integer"
          format: "int64"
        humanMessage:
          type: "string"
        message:
          type: "string"
        timestamp:
          type: "integer"
          format: "int64"
        validationMessages:
          type: "object"
          additionalProperties:
            type: "string"
      title: "Api2ResponseOfString"
