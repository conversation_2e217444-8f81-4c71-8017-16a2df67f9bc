openapi: 3.0.3

info:
  title: Oskelly Admin API v2, user balances
  description: Oskelly Admin API v2, user balances
  version: "1.0"

servers:
  - url: http://localhost:8080/
    description: Localhost test

paths:
  /api/v2/admin/userbalance/payout-with-cash-withdraw:
    post:
      tags:
        - user-balance-controller-api-v-2
      summary: manual cash payout
      operationId: cashPayoutUsingPOST
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCashPayoutDTO'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/Api2ResponseOfCashPayout'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found

components:
  schemas:
    Api2ResponseOfCashPayout:
      title: Api2ResponseOfCashPayout
      type: object
      required:
        - data
      properties:
        data:
          type: string
        message:
          type: string
        error:
          type: string
        timestamp:
          type: integer
          format: int64

    UserCashPayoutDTO:
      type: object
      required:
        - orderId
      properties:
        orderId:
          type: integer
          format: int64
          example: 1234567
