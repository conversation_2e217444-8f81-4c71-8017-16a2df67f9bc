openapi: 3.0.1
info:
  title: OpenAPI definition
  version: v1
servers:
  - description: Generated server url
    url: http://localhost:8086
paths:
  /api/v1/products/count:
    post:
      operationId: getProductsCount
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemsCountRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyLong'
          description: OK
      tags:
        - products-controller
  /api/v1/products/filters:
    post:
      operationId: getFilters
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FiltersRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyFiltersData'
          description: OK
      tags:
        - products-controller
  /api/v1/products/list:
    post:
      operationId: getProducts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemsRequest'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyPageProduct'
          description: OK
      tags:
        - products-controller
  /api/v1/products/list/personalized:
    post:
      operationId: getProductsPersonalized
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemsRequestPersonalized'
        required: true
      responses:
        "200":
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BaseApiResponseBodyPageProduct'
          description: OK
      tags:
        - products-controller
  /api/v1/synonyms/update:
    post:
      operationId: updateSynonyms
      parameters:
        - in: query
          name: synonymNames
          required: false
          schema:
            type: array
            items:
              type: string
              enum:
                - CATEGORY
                - BRAND
                - PRODUCT_MODEL
                - COLOR
      responses:
        "200":
          content:
            '*/*':
              schema:
                type: object
                properties:
                  data:
                    type: boolean
          description: OK
      tags:
        - synonym-controller
components:
  schemas:
    ItemsRequest:
      type: object
      properties:
        queryExperiment:
          type: string
        baseCategory:
          type: string
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Filter'
        offset:
          type: integer
          format: int64
        limit:
          type: integer
          format: int32
        sorting:
          type: array
          items:
            $ref: '#/components/schemas/SortingData'
        search:
          $ref: '#/components/schemas/SearchData'
      required:
        - filters
        - page
        - pageLength
        - sorting
    ItemsRequestPersonalized:
      type: object
      properties:
        queryExperiment:
          type: string
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Filter'
        offset:
          type: integer
          format: int64
        limit:
          type: integer
          format: int32
        sorting:
          type: array
          items:
            $ref: '#/components/schemas/SortingData'
        boosting:
          type: array
          items:
            $ref: '#/components/schemas/BoostingData'
      required:
        - filters
        - page
        - pageLength
        - sorting
    ItemsCountRequest:
      type: object
      properties:
        queryExperiment:
          type: string
        baseCategory:
          type: string
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Filter'
        search:
          $ref: '#/components/schemas/SearchData'
      required:
        - filters
    FiltersRequest:
      type: object
      properties:
        queryExperiment:
          type: string
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Filter'
        include:
          type: array
          items:
            type: string
        search:
          $ref: '#/components/schemas/SearchData'
      required:
        - filters
        - include
    BaseApiResponseBodyFiltersData:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/FiltersData'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    BaseApiResponseBodyLong:
      type: object
      properties:
        data:
          type: integer
          format: int64
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    BaseApiResponseBodyPageProduct:
      type: object
      properties:
        data:
          $ref: '#/components/schemas/PageProduct'
        errorMessage:
          type: string
          description: Error message
        humanErrorMessage:
          type: string
          description: Human readable error message
    FilterValue:
      type: object
      discriminator:
        propertyName: type
        mapping:
          LONG_LIST: '#/components/schemas/LongListFilterValue'
          STRING_LIST: '#/components/schemas/StringListFilterValue'
          BOOLEAN: '#/components/schemas/BooleanFilterValue'
          BIG_DECIMAL_RANGE: '#/components/schemas/BigDecimalRangeFilterValue'
      properties:
        type:
          type: string
      required:
        - type
    LongListFilterValue:
      type: object
      allOf:
        - $ref: '#/components/schemas/FilterValue'
        - properties:
            values:
              type: array
              items:
                type: integer
                format: int64
      required:
        - type
        - values
    StringListFilterValue:
      type: object
      allOf:
        - $ref: '#/components/schemas/FilterValue'
        - properties:
            values:
              type: array
              items:
                type: string
      required:
        - type
        - values
    BigDecimalRangeFilterValue:
      type: object
      allOf:
        - $ref: '#/components/schemas/FilterValue'
        - properties:
            from:
              type: number
            to:
              type: number
      required:
        - type
    BooleanFilterValue:
      type: object
      allOf:
        - $ref: '#/components/schemas/FilterValue'
        - properties:
            value:
              type: boolean
      required:
        - type
        - value
    Filter:
      type: object
      discriminator:
        propertyName: type
        mapping:
          COMPOSITE_FILTER: '#/components/schemas/CompositeFilter'
          LONG_VALUES: '#/components/schemas/LongValuesFilter'
          STRING_VALUES: '#/components/schemas/StringValuesFilter'
          BOOLEAN_VALUE: '#/components/schemas/BooleanValueFilter'
          BIG_DECIMAL_RANGE: '#/components/schemas/BigDecimalRangeFilter'
      properties:
        type:
          type: string
      required:
        - type
    CompositeFilter:
      type: object
      allOf:
        - $ref: '#/components/schemas/Filter'
        - properties:
            childFilters:
              type: array
              items:
                $ref: '#/components/schemas/Filter'
      required:
        - type
        - childFilters
    LongValuesFilter:
      type: object
      allOf:
        - $ref: '#/components/schemas/Filter'
        - properties:
            values:
              type: array
              items:
                type: integer
                format: int64
            filterType:
              $ref: '#/components/schemas/MultiValueFilterType'
      required:
        - type
        - values
        - filterType
    StringValuesFilter:
      type: object
      allOf:
        - $ref: '#/components/schemas/Filter'
        - properties:
            values:
              type: array
              items:
                type: string
            filterType:
              $ref: '#/components/schemas/MultiValueFilterType'
      required:
        - type
        - values
    BigDecimalRangeFilter:
      type: object
      allOf:
        - $ref: '#/components/schemas/Filter'
        - properties:
            from:
              type: number
            to:
              type: number
      required:
        - type
    BooleanValueFilter:
      type: object
      allOf:
        - $ref: '#/components/schemas/Filter'
        - properties:
            value:
              type: boolean
            filterType:
              $ref: '#/components/schemas/SingleValueFilterType'
      required:
        - type
        - value
    FiltersData:
      type: object
      properties:
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/AvailableFilterValues'
        itemsCount:
          type: integer
          format: int64
        sorting:
          type: array
          items:
            type: string
      required:
        - filters
        - itemsCount
        - sorting
    AvailableFilterValues:
      type: object
      discriminator:
        propertyName: type
        mapping:
          LONG_LIST: '#/components/schemas/LongValues'
          STRING_LIST: '#/components/schemas/StringValues'
          BIG_DECIMAL_RANGE: '#/components/schemas/BigDecimalRangeValues'
          BOOLEAN_LIST: '#/components/schemas/BooleanValues'
      properties:
        type:
          type: string
      required:
        - type
    BigDecimalRangeValues:
      type: object
      allOf:
        - $ref: '#/components/schemas/AvailableFilterValues'
        - type: object
          properties:
            from:
              type: number
            to:
              type: number
    BooleanValues:
      type: object
      allOf:
        - $ref: '#/components/schemas/AvailableFilterValues'
        - type: object
          properties:
            values:
              type: array
              items:
                type: boolean
      required:
        - values
    StringValues:
      type: object
      allOf:
        - $ref: '#/components/schemas/AvailableFilterValues'
        - type: object
          properties:
            values:
              type: array
              items:
                type: string
      required:
        - values
    LongValues:
      type: object
      allOf:
        - $ref: '#/components/schemas/AvailableFilterValues'
        - type: object
          properties:
            values:
              type: array
              items:
                type: integer
                format: int64
      required:
        - values
    PageProduct:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Product'
        totalAmount:
          type: integer
          format: int64
        totalPages:
          type: integer
          format: int32
      required:
        - items
        - totalAmount
        - totalPages
    Product:
      type: object
      properties:
        id:
          type: integer
          format: int64
      required:
        - id
    SearchData:
      type: object
      properties:
        query:
          type: string
      required:
        - query
    SortingData:
      type: object
      properties:
        code:
          type: string
        nullValue:
          type: string
          enum:
            - FIRST
            - LAST
          default: LAST
        order:
          type: string
          enum:
            - ASC
            - DESC
          default: ASC
      required:
        - code
        - nullValue
        - order
    SingleValueFilterType:
      type: string
      enum:
        - EQ
        - NE
    MultiValueFilterType:
      type: string
      enum:
        - IN
        - NI
    BoostingData:
      type: object
      properties:
        productId:
          type: integer
          format: int64
        score:
          type: number
          format: double
    FilterSubscriptionCatalogRequest:
      type: object
      properties:
        boosting:
          type: array
          items:
            $ref: '#/components/schemas/BoostingData'
        filters:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Filter'
        search:
          $ref: '#/components/schemas/SearchData'
        sorting:
          type: array
          items:
            $ref: '#/components/schemas/SortingData'
      required:
        - boosting
        - filters
        - sorting