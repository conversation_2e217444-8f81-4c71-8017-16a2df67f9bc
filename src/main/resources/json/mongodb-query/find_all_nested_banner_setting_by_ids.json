{
  aggregate: "{{COLLECTION_NAME}}",
  cursor: {},
  pipeline: [
    {
      $match: {
        _id: {
          $in: [
            {{BANNER_SETTING_IDS}}
          ]
        }
      }
    },
    {
      $graphLookup: {
        from: "banner-setting",
        connectFromField: "contentBlocks.segments.contentIds",
        connectToField: "_id",
        as: "descendants",
        maxDepth: {{MAX_DEPTH}},
        startWith: {
          $reduce: {
            input: {
              $reduce: {
                input: "$contentBlocks",
                initialValue: [],
                in: {
                  $concatArrays: [
                    "$$value",
                    "$$this.segments"
                  ]
                }
              }
            },
            initialValue: [],
            in: {
              $cond: {
                if: {
                  $isArray: "$$this.contentIds"
                },
                then: {
                  $setUnion: [
                    "$$value",
                    "$$this.contentIds"
                  ]
                },
                else: "$$value"
              }
            }
          }
        },
        restrictSearchWithMatch: {
          $expr: {
            $and: [
              {
                $in: [
                  "$filter._class",
                  [
                    {{TARGET_FILTER_CLASSES}}
                  ]
                ]
              },
              {
                $or: [
                  {
                    $eq: [
                      {
                        $size: "$contentBlocks"
                      },
                      0
                    ]
                  },
                  {
                    $anyElementTrue: {
                      $map: {
                        input: "$contentBlocks",
                        as: "block",
                        in: {
                          $and: [
                            {
                              $eq: [
                                "$$block.isEnable",
                                true
                              ]
                            },
                            {
                              $in: [
                                "$$block.type",
                                [
                                  {{TARGET_CONTENT_BLOCK_TYPES}}
                                ]
                              ]
                            }
                          ]
                        }
                      }
                    }
                  }
                ]
              }
            ]
          }
        }
      }
    },
    {
      $addFields: {
        allDocuments: {
          $concatArrays: [
            [
              "$$ROOT"
            ],
            "$descendants"
          ]
        }
      }
    },
    {
      $unwind: "$allDocuments"
    },
    {
      $replaceRoot: {
        newRoot: "$allDocuments"
      }
    },
    {
      $project: {
        _id: 1,
        "contentBlocks.isEnable": 1,
        "contentBlocks.type": 1,
        "contentBlocks._class": 1,
        "contentBlocks.blockId": 1,
        "contentBlocks.segments.contentIds": 1,
        "contentBlocks.segments.isEnable": 1,
        "contentBlocks.segments.segmentId": 1,
        "generalInfoBlock.title": 1,
        "generalInfoBlock.description": 1,
        "filter._class": 1
      }
    },
    {
      $group: {
        _id: "$_id",
        contentBlocks: {
          $first: "$contentBlocks"
        },
        generalInfoBlock: {
          $first: "$generalInfoBlock"
        },
        filter: {
          $first: "$filter"
        }
      }
    },
    {
      $match: {
        "filter._class": {
          $in: [
            {{TARGET_FILTER_CLASSES}}
          ]
        }
      }
    }
  ]
}