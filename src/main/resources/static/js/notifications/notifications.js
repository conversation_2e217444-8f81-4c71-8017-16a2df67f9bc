if (isNotificationPage()){
    $('section.bottom_menu ul li').removeClass('active');
    $('section.bottom_menu ul li.bottom-notification').addClass('active');

}

let api = new NotificationsApiClient();
let render = new NotificationsRender();

function isNotificationPage(){
    let pathname = window.location.pathname;
    return pathname == '/notifications';
}


$(document).ready(function () {
    $(".open_publication_popup").off("click");


});



$('body').on('click', '.cart_icon .notifications-li', function () {
    $('.cart_icon .notifications-li .notifications_comments').slideDown(0);
    clickNotificationIcon();
});

async function clickNotificationIcon(){
    if(!isAnnonymous()){
        if(!($('.cart_icon .notifications-li').attr('data-is-render-nocomments') == 'true')){
            if(isNotificationPage){
                let dataNocomments = await api.getNocomments();
                console.log('dataNocomments', dataNocomments);
                render.renderHeaderNocommentNotification(dataNocomments);
                render.renderPageNocommentNotification(dataNocomments, 1);
                $('.cart_icon .notifications-li').attr('data-is-render-nocomments', 'true');
            }
            else{
                let dataNocomments = await api.getNocomments();
                console.log('dataNocomments', dataNocomments);
                render.renderHeaderNocommentNotification(dataNocomments);
                $('.cart_icon .notifications-li').attr('data-is-render-nocomments', 'true');
            }
        }
    }
}

$('body').on('click', '.comments-page-block , .comments-header-block', function(){
    clickCommentBlock();
});

async function clickCommentBlock(){
    if(!isAnnonymous()){
        if(!($('.cart_icon .notifications-li').attr('data-is-render-comments') == 'true')){
            if(isNotificationPage()){
                let dataComments = await api.getComments();
                console.log('dataComments', dataComments);
                render.renderHeaderCommentNotification(dataComments);
                render.renderPageCommentNotification(dataComments,1);
                $('.cart_icon .notifications-li').attr('data-is-render-comments', 'true');
            }
            else{
                let dataComments = await api.getComments();
                console.log('dataComments', dataComments);
                render.renderHeaderCommentNotification(dataComments);
                $('.cart_icon .notifications-li').attr('data-is-render-comments', 'true');
            }
        }
    }
}


async function initNotifications(){
    if(!isAnnonymous()) {
        let dataBubbles = await api.getBubbles();
        console.log('dataBubbles', dataBubbles);
        render.renderHeaderBubblesNotification(dataBubbles);

        if (isNotificationPage()) {
            console.log('/notifications');
            let dataNocomments = await api.getNocomments();
            console.log('dataNocomments', dataNocomments);
            render.renderHeaderNocommentNotification(dataNocomments);
            render.renderPageNocommentNotification(dataNocomments, 1);
            $('.cart_icon .notifications-li').attr('data-is-render-nocomments', 'true');
        }
    }
}

initNotifications();


$('body').on('mousedown', '.notification-block', function() {
    let $this = $(this);
    let isRead = $this.attr('data-is-read');
    if(isRead == 'false') {
        let id = $this.attr('data-notification-id');
        api.readById(id);
        $this.attr('data-is-read', true);
        $this.find('.circle').remove();
    }
});


async function showMoreNocomments(page){
    let data = await api.getNocomments({page: page, pageSize: 20});
    render.renderPageNocommentNotification(data, page);
}

async function showMoreComments(page){
    let data = await api.getComments({page: page, pageSize: 20});
    render.renderPageCommentNotification(data, page);
}

$('body').on('click', '.pagination_show_more .show_more', function(){
    let $this = $(this);
    let page = $this.attr('data-page');
    let type = $this.attr('data-type');
    if(type == 'nocomments'){
        showMoreNocomments(parseInt(page) + 1);
    }
    else if(type == 'comments'){
        showMoreComments(parseInt(page) + 1);
    }
});


$('body').on('click', '.subscribe_button_notification', function(){
    let $this = $(this);
    let userId = $this.attr('data-user-id');
    let fdata = new FormData();
    fdata.append("userId", userId);
    console.log(userId);
    $.ajax({
        type: "POST",
        url: '/api/v2/publicprofile/following/toggle',
        data: fdata,
        cache: false,
        processData: false,
        contentType: false
    }).done(function(result){
        if(result.data){
            $this.addClass('following');
            $this.find('a').text('Подписан (-а)')
        }
        else{
            $this.removeClass('following');
            $this.find('a').text('Подписаться')
        }
    }).fail(function (result) {
        console.log('result sub fail', result);
    });
});


function isAnnonymous() {
    return $('.cart_icon .notifications-li').attr('is-anonymous') == 'true' ? true : false;
}

$(function () {
    $.get('/layout/header_notifications', null).done(data => {
        $('.notifications-li').append( data );
    });
});
