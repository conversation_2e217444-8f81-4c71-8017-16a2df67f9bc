$('body').on('click', '.item .add-basket', function(){
    let $this = $(this);
    let prodId = $this.attr('data-product-id');
    let sizeId = $this.attr('data-size-id');

    if(!sizeId){
        console.log('size undef');
        let sizeText = $this.parents('.item').find('.jq-selectbox__select-text').text();

        let dropdownItemLists = $this.parents('.item').find('.jq-selectbox select option');
        dropdownItemLists.each(function(){
            let $listItem = $(this);
            if($listItem.text() == sizeText){
                console.log('find size');
                sizeId = $listItem.attr('data-size-id');
            }
        });
    }

    console.log('ids',prodId,sizeId);

    if(prodId && sizeId){
        $.ajax({
            method: "PUT",
            url: "/api/v2/cart/items?productId=" + prodId + "&sizeId=" + sizeId
        }).done(function () {
            window.location.pathname = '/cart';
        });
    }
});

$('body').on('click', '.wishlist-root-div .heart, .price-sub-root-div .heart', function(e){
    e.preventDefault();
    let $this = $(this);
    let $countHeader = $('.wishlist-header-count');
    let count = $this.find('.hearth-count-div').text();
    let prodId = $this.attr('data-product-id');
    if($this.hasClass('active')){
        $.ajax({
            method:'PUT',
            url: '/api/v2/catalog/products/' + prodId + '/dislike'
        }).done(function(){
            let $items = $(`.wishlist-root-div .heart[data-product-id=${prodId}], .price-sub-root-div .heart[data-product-id=${prodId}]`);
            $items.removeClass('active');
            count--;
            $items.find('.hearth-count-div').text(count);
            let headerCount = $('.wishlist-header-count').text();
            $('.whishlist-count').text(--headerCount);
        }).fail(function (data) {
            console.log(data.responseJSON)
        });

    }
    else{
        $.ajax({
            method:'PUT',
            url: '/api/v2/catalog/products/' + prodId + '/like'
        }).done(function(){
            let $items = $(`.wishlist-root-div .heart[data-product-id=${prodId}], .price-sub-root-div .heart[data-product-id=${prodId}]`);
            $items.addClass('active');
            count++;
            $items.find('.hearth-count-div').text(count);
            let headerCount = $('.wishlist-header-count').text();
            $('.whishlist-count').text(++headerCount);
        }).fail(function (data) {
            console.log(data.responseJSON)
        });
    }

    console.log('heart')
});

$('body').on('click' ,'.wishlist-root-div .item .remove-item', function(e){
    e.preventDefault();
    let $this = $(this);
    let prodId = $this.attr('data-product-id');
    let count = $this.parents('.item').find('.hearth-count-div').text();

    if(!$this.parents('.item').find('.heart').hasClass('active')){
        return;
    }

    $.ajax({
        method:'PUT',
        url: '/api/v2/catalog/products/' + prodId + '/dislike'
    }).done(function(){
        let $items = $(`.wishlist-root-div .heart[data-product-id=${prodId}], .price-sub-root-div .heart[data-product-id=${prodId}]`);
        $items.removeClass('active');
        count--;
        $items.find('.hearth-count-div').text(count);
        let headerCount = $('.wishlist-header-count').text();
        $('.whishlist-count').text(--headerCount)


        $this.parents('.item').remove();
        renderWishList($('.pagination-likes .pagination ul li.active a').text());
        document.querySelector(".scroll-like").scrollIntoView({ behavior: 'smooth'});
    });
});


$('body').on('click', '.price-sub-root-div .item .remove-item', function(e){
    e.preventDefault();
    let $this = $(this);
    let prodId = $this.attr('data-product-id');

    if($this.attr('is-follow') == 'false'){
        return;
    }

    $.ajax({
        method:'PUT',
        url: '/api/v2/catalog/products/' + prodId + '/unfollowPrice'
    }).done(function(){

        $this.attr('is-follow', 'false');
        let count = $('.price-sub-header-count').text();
        count--;
        $('.price-sub-count').text(count);


        $this.parents('.item').remove();
    });

});

async function getCartProducts() {
    return await Promise.resolve($.get('/api/v2/cart'));
}