const CatalogApiClient = function(id, type) {
    this.endpoint = "/catalog/"+ (type == 'set'? 'set' : 'banner') +"/" + id + "/template";
    this.availableFiltersEndpoint = "/api/v2/catalog/availableFilters";
};

CatalogApiClient.prototype.getCatalog = function (requestParams) {

    return $.get(this.endpoint, requestParams);

};


CatalogApiClient.prototype.getAvailableFilters = function (requestParams) {

    return $.get(this.availableFiltersEndpoint, requestParams);

};



