$(document).ready(function () {

    $('.translate').each(function () {
        $(this).removeClass('translate')
        $(this).on('input', function () {
            var val = translate($(this).val())
            $(this).val(val)
        })
    })
    var chars = { "Ё": "YO", "Й": "I", "Ц": "TS", "У": "U", "К": "K", "Е": "E", "Н": "N", "Г": "G", "Ш": "SH", "Щ": "SCH", "З": "Z", "Х": "H", "Ъ": "'", "ё": "yo", "й": "i", "ц": "ts", "у": "u", "к": "k", "е": "e", "н": "n", "г": "g", "ш": "sh", "щ": "sch", "з": "z", "х": "h", "ъ": "'", "Ф": "F", "Ы": "I", "В": "V", "А": "a", "П": "P", "Р": "R", "О": "O", "Л": "L", "Д": "D", "Ж": "ZH", "Э": "E", "ф": "f", "ы": "i", "в": "v", "а": "a", "п": "p", "р": "r", "о": "o", "л": "l", "д": "d", "ж": "zh", "э": "e", "Я": "Ya", "Ч": "CH", "С": "S", "М": "M", "И": "I", "Т": "T", "Ь": "'", "Б": "B", "Ю": "YU", "я": "ya", "ч": "ch", "с": "s", "м": "m", "и": "i", "т": "t", "ь": "'", "б": "b", "ю": "yu" };
    function translate(word) {
        return word.split('').map(function (char) {
            return chars[char] || char;
        }).join("");
    }

    $('.validate').each(function () {
        var rules = $(this).attr('v-rules').split('|')

        $(this).removeClass('validate')
        $(this).removeAttr('v-rules')
        $(this).on('input', function () {
            if (rules.indexOf('trim') !== -1) {
                $(this).val($(this).val().trim())
            }

            if (!itemValidate($(this).val(), rules)) {
                $(this).addClass('invalid')
            } else {
                $(this).removeClass("invalid")
            }
        })
    })

    function itemValidate(value, rules) {
        var valid = true
        for (var i = 0; i < rules.length; i++) {
            var rule = rules[i]
            var arr = false;
            if (rule.indexOf(':') !== -1) {
                rule = rule.split(':')
                arr = true
            }

            var data = {
                value: value,
            }
            if (arr) {
                data['param'] = rule[1]
                rule = rule[0]
            }
            if (!validator[rule].call(data)) {
                valid = false
                break
            }
        }
        return valid
    }

    var validator = {
        required: function () {
            return this.value.trim().length ? true : false
        },
        email: function () {
            var re = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;
            return re.test(String(this.value).toLowerCase());
        },
        min: function () {
            return this.value.length >= this.param
        },
        max: function () {
            return this.value.length <= this.param
        },
        minNum: function () {
            return this.value >= this.param
        },
        maxNum: function () {
            return this.value <= this.param
        },
        number: function () {
            return !isNaN(Number(this.value))
        },
        charset: function () {
            return charsetRegex[this.param].test(this.value)
        },
        trim: function () {
            return true
        },
        start: function () {
            return this.value.indexOf(this.param) === 0
        }
    }

    var charsetRegex = {
        ru: /^[а-яА-ЯЁё ]*$/,
        ruNum: /^[а-яА-ЯЁё1-9 ]*$/,
        ruSpace: /^[а-яА-ЯЁё\s]*$/,
        ruAddress: /([А-Яа-яЁё0-9\-\.]+\s*)+/,
        ruCity: /([А-Яа-яЁё\-\.]+\s*)+/,
        phone: /\+7 \(\d{3}\) \d{3}-\d{2}-\d{2}/,
        date: /([0-2][0-9]|(3)[0-1])(\.)(((0)[0-9])|((1)[0-2]))(\.)\d{4}/,
        name: /\s*[А-Яа-яёЁё\-]{3,}\s*/,
        payAccJur: /40702 810 \d \d{4} \d{7}/,
        payAccIp: /40802 810 \d \d{4} \d{7}/,
        payAccPhys : /40817 810 \d \d{4} \d{7}/
    }
});