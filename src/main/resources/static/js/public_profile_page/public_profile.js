let PublicProfile = function () {
    let profileClient = new PublicProfileClient();
    let profileId = $('.all_user_page .user_info').attr('data-profile-id');
    let currentUserId;


    let allFollowings = [];
    let currentFollowings = [];
    let currentFollowingsPage = 1;


    let allFollowers = [];
    let currentFollowers = [];
    let currentFollowersPage = 1;


    let queryFollowings = '';
    let queryFollowers = '';

    const pageSize = 20;



    $("body").on("keyup", " #popup_subscribers .text_search", function () {
        let value = $(this).val();
        queryFollowers = value;
        currentFollowersPage = 1;
        //$("#popup_subscribers .scroll_content").mCustomScrollbar("destroy");
        profileClient.getFollowersPage(profileId, 1, pageSize, queryFollowers)
            .done(function (followers) {
                if(followers && followers.data){
                    let searchFollowers = followers.data.items;
                    $("#popup_subscribers .popup_block.empty_block_pop").removeClass('empty_block_pop');
                    $("#popup_subscribers .mCSB_container").html(generateFollowings(searchFollowers));
                    images_first_letter();

                    //$('#popup_subscribers .scroll_content').mCustomScrollbar("update");
                }
                $('#popup_subscribers .scroll_content').mCustomScrollbar({
                    scrollInertia: 0,
                    autoDraggerLength: false,
                    axis: "y",
                    callbacks:{
                        onTotalScroll:function(){
                            profileClient.getFollowersPage(profileId, ++currentFollowersPage, pageSize, queryFollowers)
                                .done(function (newFollowers){
                                    console.log('popup_subscribers scroll');
                                    let blocks = $(newFollowers.data.items.map(f => generateFollowing(f)).join(' '));
                                    $("#popup_subscribers .mCSB_container").append(blocks);
                                    images_first_letter();
                                });
                        },
                        onTotalScrollOffset: 50
                    }
                });
            });
    });

    $("body").on("keyup", " #popup_subscribe .text_search", function () {
        let value = $(this).val();
        queryFollowings = value;
        currentFollowingsPage = 1;
        profileClient.getFollowingPage(profileId, 1, pageSize, queryFollowings)
            .done(function (followings) {
                if(followings && followings.data){
                    let searchFollowings = followings.data.items;
                    $("#popup_subscribe .popup_block.empty_block_pop").removeClass('empty_block_pop');
                    $("#popup_subscribe .mCSB_container").html(generateFollowings(searchFollowings));
                    images_first_letter();
                }
                $('#popup_subscribe .scroll_content').mCustomScrollbar({
                    scrollInertia: 0,
                    autoDraggerLength:false,
                    axis: "y",
                    callbacks:{
                        onTotalScroll:function(){
                            profileClient.getFollowingPage(profileId, ++currentFollowingsPage, pageSize, queryFollowings)
                                .done(function (newFollowings){
                                    console.log('popup_subscribers scroll');
                                    let blocks = $(newFollowings.data.items.map(f => generateFollowing(f)).join(' '));
                                    $("#popup_subscribe .mCSB_container").append(blocks);
                                    images_first_letter();
                                });
                        },
                        onTotalScrollOffset: 50
                    }
                });
            });
    });


    (function init() {
        currentUserId = profileId ? profileId : null;
        initWishList();
        slideDownActiveRootCategory();
    })();
    function  initWishList() {
        let urlParams = new URLSearchParams(window.location.search);

        let page = (urlParams.get('wlpage') && urlParams.get('wlpage') > 0 ? urlParams.get('wlpage') : 1);
            $.get("/profile/" + profileId + "/wishlist-fragment", {page: page})
                .done(function (response) {
                    $('.catalog_page .all_catalog_wishlist_items .catalog_wishlist').html($(response).find(".catalog_wishlist").html());
                    $('.catalog_page .all_catalog_wishlist_items .catalog_wishlist').siblings('.pagination_show_more').html($(response).find('.pagination_show_more').html());

                    // let html = $(response.data.html);
                    // if(html.length > 0){
                    //
                    //
                    //     $('.catalog_page .all_catalog_wishlist_items .catalog_wishlist').html($(html).html());
                    //     $('.catalog_page .all_catalog_wishlist_items .catalog_wishlist').siblings('.pagination_show_more').replaceWith($(html).siblings('.pagination_show_more'));
                    //
                    // }
                });

    }

    function slideDownActiveRootCategory() {
        $(".root_item > a.active").parent().find(">ul").css("display", "block");
    }

    (function onFollowButtonClick() {

        $("section.all_user_page .user_page .user_info .name_follow .follow a").off().on("click", function (e) {
            var $this = $(this);
            if($this.parent().hasClass('profile_logout')){
                return;
            }
            else{
                e.preventDefault();
            }
            profileClient.toggleFollowing(profileId)
                .done(function (followingState) {
                    if(followingState.data === true){
                        $this.parents('.follow').addClass("following");
                        $this.html("Подписан (-а)");

                        let analyticsService = new AnalyticsService();
                        analyticsService.profileSubscribe();
                    }
                    else{
                        $this.parents('.follow').removeClass("following");
                        $this.html("Подписаться");
                    }
                });


        } );
    })();

    (function onLogoutButtonClick() {

        $(".profile_logout a").on("click", function (e) {
        } );
    })();

    (function onFollowersPopupOpen() {
        $ ( ".open_subscribers_popup" ).off().on ( "click" , function ( e ) {
            e.preventDefault ();
            if(!$(this).hasClass("button--show-register-popup")) {
                $("body").addClass("active");

                $("#popup_subscribers .scroll_content").mCustomScrollbar("destroy");
                profileClient.getFollowersPage(profileId, 1, pageSize, queryFollowers)
                    .done(function (followers) {
                        if(followers && followers.data){
                            currentFollowersPage = 1;
                            let firstFollowers = followers.data.items;
                            $("#popup_subscribers .popup_block.empty_block_pop").removeClass('empty_block_pop');
                            $("#popup_subscribers .overflow_block").html(generateFollowings(firstFollowers));
                            images_first_letter();
                        }
                        $('#popup_subscribers .scroll_content').mCustomScrollbar({
                            axis: "y",
                            advanced:{ updateOnContentResize: true },
                            callbacks:{
                                onTotalScroll:function(){
                                    profileClient.getFollowersPage(profileId, ++currentFollowersPage, pageSize, queryFollowers)
                                        .done(function (newFollowers){
                                            console.log('popup_subscribers scroll');
                                            let blocks = $(newFollowers.data.items.map(f => generateFollowing(f)).join(' '));
                                            $("#popup_subscribers .mCSB_container").append(blocks);
                                            images_first_letter();
                                        });
                                }
                            }
                        });
                    }).always(function () {
                    $("#popup_subscribers").fadeIn(function () {

                    });


                });
            }
        } );

    })();

    (function onFollowingsPopupOpenClick() {
        $ ( ".open_subscribe_popup" ).off().on ( "click" , function ( e ) {
            e.preventDefault ();
            if(!$(this).hasClass("button--show-register-popup")) {

                $("body").addClass("active");

                $("#popup_subscribe .scroll_content").mCustomScrollbar("destroy");

                profileClient.getFollowingPage(profileId, 1, pageSize, queryFollowings)
                    .done(function (followings) {
                        if(followings && followings.data){
                            currentFollowingsPage = 1;
                            let firstFollowing = followings.data.items;
                            $("#popup_subscribe .popup_block.empty_block_pop").removeClass('empty_block_pop');
                            $("#popup_subscribe .overflow_block").html(generateFollowings(firstFollowing));
                            images_first_letter();
                        }
                        $('#popup_subscribe .scroll_content').mCustomScrollbar({
                            axis: "y",
                            callbacks:{
                                onTotalScroll:function(){
                                    profileClient.getFollowingPage(profileId, ++currentFollowingsPage, pageSize, queryFollowings)
                                        .done(function (newFollowing){
                                            console.log('popup_subscribe scroll');
                                            let blocks = $(newFollowing.data.items.map(f => generateFollowing(f)).join(' '));
                                            $("#popup_subscribe .mCSB_container").append(blocks);
                                            images_first_letter();
                                        });
                                }
                            }
                        });
                    })
                    .always(function () {
                        $("#popup_subscribe").fadeIn(function () {
                        });
                    });
            }

        } );
    })();
    
    function generateFollowings(followings) {
        let followingsHtml = $(followings.map(f => generateFollowing(f)).join(' '));
        return followingsHtml;
    }

    function generateFollowing(following) {
        if(!following) {return '';}
        let status = (following.isPro) ? 'Бутик' : 'Частный продавец';
        return `
            <div class='block' data-profile-id="${following.id}">
                <div class='left_info_user'>
                    <div class='user_img'>
                        <a href="/profile/${following.id}">
                            ${following.avatarPath ? `<img src='${following.avatarPath}' onerror="this.remove();"/>` : ''}
                        </a>
                        <span></span>
                    </div>
                    <div class='user_name_block'>
                        <div class='user_name'><a class="user_name" href="/profile/${following.id}">${following.nickname}</a></div>
                        <p>${status}</p>
                        <p>${following.productsCount ? following.productsCount : 'Нет' } товаров</p>
                    </div>
                </div>
                ${currentUserId && currentUserId === following.id ? '' : `<div class='subscribe'>
                    <a href='#' ${following.isFollowed ? 'class="active"' : ''}>${following.isFollowed ? 'Подписки' : 'Подписаться'}</a>
                </div>`}
            </div>
        `
    }

    (function onPrevWishPageClick() {
        $('.all_catalog_wishlist_items').on('click', '.catalog_wishlist + .pagination_show_more .pagination .prev_pag a', function (e) {
            e.preventDefault();
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('wlpage', $(this).attr('data-prev-page'));
            window.history.replaceState({}, '', `${location.pathname}?${urlParams}`);
            initWishList();
            $(window).scrollTop(0);

        });
    })();

    (function onWishPageClick() {
        $('.all_catalog_wishlist_items').on('click', '.catalog_wishlist + .pagination_show_more .pagination ul li a', function (e) {
            e.preventDefault();
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('wlpage', $(this).text());
            if($(this).text() === '1'){
                urlParams.delete('wlpage');
            }
            window.history.replaceState({}, '', `${location.pathname}?${urlParams}`);
            initWishList();
            $(window).scrollTop(0);

        });
    })();

    (function onNextWishPageClick() {
        $('.all_catalog_wishlist_items').on('click', '.catalog_wishlist + .pagination_show_more .pagination .next_pag a', function (e) {
            e.preventDefault();
            let urlParams = new URLSearchParams(window.location.search);
            urlParams.set('wlpage', $(this).attr('data-next-page'));
            window.history.replaceState({}, '', `${location.pathname}?${urlParams}`);
            initWishList();
            $(window).scrollTop(0);

        });
    })();

    (function onWishShowMoreClick() {
        $ ( ".all_catalog_wishlist_items" ).on ( "click", ".catalog_wishlist + .pagination_show_more .show_more a", function (e) {
            e.preventDefault ();
            let urlParams = new URLSearchParams(window.location.search);
            let nextPage = $('.catalog_wishlist + .pagination_show_more .pagination .next_pag a').attr('data-next-page');
            urlParams.set('wlpage', nextPage);
            window.history.replaceState({}, '', `${location.pathname}?${urlParams}`);


            $.get("/profile/" + profileId + "/wishlist-fragment", {page: nextPage})
                .done(function (response) {

                    $('.catalog_page .all_catalog_wishlist_items .catalog_wishlist').append($(response).find(".catalog_wishlist").html());
                    $('.catalog_page .all_catalog_wishlist_items .catalog_wishlist').siblings('.pagination_show_more').html($(response).find('.pagination_show_more').html());
                });

        } );
    })();

    (function onTabClick(){
        $('.click_user_title_block a').on("click", function () {
            if($(this).hasClass("products_tab_link")){
                let urlParams = new URLSearchParams(window.location.search);
                window.history.replaceState({}, '', `${location.pathname.replace('/wishlist', '')}?${urlParams}`);
            }
            else if($(this).hasClass("wishlist_tab_link")){
                let urlParams = new URLSearchParams(window.location.search);
                window.history.replaceState({}, '', `${location.origin + /profile/ + profileId}/wishlist?${urlParams}`);

            }
        })
    })();
    
    $(".popup_subscribers_subscribe .popup_block .overflow_block .block .subscribe a").off();
    $(".popup_subscribers_subscribe .popup_block .overflow_block").on("click", ".block .subscribe a", function (e) {
        e.preventDefault();
        var $this = $(this);

        if($this.hasClass("active")){
            $this.removeClass("active");
            $this.html("Подписаться");
        } else {
            $this.addClass("active");
            $this.html("Подписки");
        }
        let profileId = $this.closest('.block').attr('data-profile-id');
        if(profileId) {
            profileClient.toggleFollowing(profileId)
                .done(function (followingState) {
                    if (followingState.data === true) {
                        $this.addClass("active");
                        $this.html("Подписки");
                    } else {
                        $this.removeClass("active");
                        $this.html("Подписаться");
                    }
                });
        }
        else{
            console.log("PROFILE ID NOT FOUND");
        }

    });

    function images_first_letter (  ) {
        console.log('images_first_letter')
        if($(".all_user_page .user_page .user_img").hasClass("user_img")){
            var $this = $(this);
            var first_letter_name = $(".all_user_page .user_page .user_info .name_follow .name").text().substring(0, 1);
            $(".all_user_page .user_page .user_img .img span").text(first_letter_name);
            $(".all_user_page .user_page .user_img .img span").css("line-height",$(".all_user_page .user_page .user_img").height() +"px");
        }

        if($(".popup_subscribers_subscribe .popup_block .overflow_block .block .left_info_user .user_img").hasClass("user_img")){

            for ( var i = 0; i < $(".popup_subscribers_subscribe .popup_block .overflow_block .block .left_info_user .user_img").length; i ++){

                var first_letter_name = $(".popup_subscribers_subscribe .popup_block .overflow_block .block").eq(i).find(".user_name").text().substring(0, 1).toUpperCase();
                $(".popup_subscribers_subscribe .popup_block .overflow_block .block .left_info_user .user_img").eq(i).find("span").remove();
                $(".popup_subscribers_subscribe .popup_block .overflow_block .block .left_info_user .user_img").eq(i).append("<span>"+first_letter_name+"</span>");
                $(".popup_subscribers_subscribe .popup_block .overflow_block .block .left_info_user .user_img").eq(i).find("span").css("line-height",$(".popup_subscribers_subscribe .popup_block .overflow_block .block .left_info_user .user_img").eq(i).height() +"px");
            }
        }

        if($(".seller").hasClass("seller")){

            for ( var i = 0; i < $(".seller").length; i ++){

                var first_letter_name_seller = $(".seller").eq(i).find("a .name_info .name_seller").text().substring(0, 1);
                $(".seller").eq(i).find("a .img span").remove();
                $(".seller").eq(i).find("a .img").append("<span>"+first_letter_name_seller+"</span>");
                $(".seller").eq(i).find("a .img span").css("line-height",$(".seller").eq(i).find("a .img").height() +"px");
            }
        }
    }

    function runNotificationsForm($notificationsWrap) {
        const requestName = '/api/v2/account/emailGroups';
        const $checkboxes = $notificationsWrap.find('input[type="checkbox"]');
        let enabledIds = [];

        function saveCheckbox () {
            const $currCheckbox = $(this);
            const id = parseInt($currCheckbox.attr('data-id'), 10);
            const isSelected = $currCheckbox.is(':checked');
            const isGeneral = $currCheckbox.attr('data-is-general') === 'true';

            if (isGeneral && !isSelected) {
                enabledIds = [];
            } else if (isSelected) {
                enabledIds.push(id);
            } else if (!isSelected) {
                enabledIds = enabledIds.filter((subId) => subId !== id);
            }
            const params = enabledIds.map((id) => `notificationGroupIds=${id}`);

            $.ajax({
                url: params.length ? `${requestName}?${params.join('&')}` : requestName,
                type: 'PUT',
            });
        };

        function toggleAllCheckboxes() {
            const isChecked = $(this).is(':checked');

            $checkboxes.each(function () {
                const $checkbox = $(this);
                const isGeneral = $checkbox.attr('data-is-general') === 'true';

                if (isGeneral) return;

                if (!isChecked) {
                    $checkbox.prop('checked', isChecked);
                }
                $checkbox.prop('disabled', !isChecked);
                $checkbox.parent().parent().toggleClass('disabled', !isChecked);
            });
       }

        $.get(requestName).done((response) => {
            if (!response.data) return;

            response.data.forEach((checkData, i) => {
                const $currCheckbox = $checkboxes.eq(i);
                $currCheckbox.prop('checked', checkData.isSelected);
                $currCheckbox.attr('data-is-general', checkData.isGeneral);
                $currCheckbox.attr('data-id', checkData.id);

                if (checkData.isGeneral) {
                    $currCheckbox.change(toggleAllCheckboxes);
                    if (!checkData.isSelected) $currCheckbox.change();
                }
                if (checkData.isSelected) {
                    enabledIds.push(checkData.id);
                }
                $currCheckbox.change(saveCheckbox);
            });
        });
    }

    (function checkIsNotificationsAndRun() {
        const $notificationsWrap = $('.notify-form');
        if (!$notificationsWrap.length) return;

        runNotificationsForm($notificationsWrap);
    })();
};




$(function () {
    let publicProfile = new PublicProfile();
});
