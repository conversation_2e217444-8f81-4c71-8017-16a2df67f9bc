const PublicProfileClient = function() {
    this.endpoint = "/api/v2/publicprofile";
};

PublicProfileClient.prototype.getFollowers = function (userId) {

    return $.get(this.endpoint + "/followers", {userId : userId});

};

PublicProfileClient.prototype.getFollowing = function (userId) {

    return $.get(this.endpoint + "/followings",  {userId : userId});

};

PublicProfileClient.prototype.getFollowersPage = function (userId, page, pageSize, query) {

    return $.get(this.endpoint + "/followers-page",
        {
            userId : userId,
            page: page,
            pageSize: pageSize,
            query: query
        });

};

PublicProfileClient.prototype.getFollowingPage = function (userId, page, pageSize, query) {

    return $.get(this.endpoint + "/followings-page",
        {
            userId : userId,
            page: page,
            pageSize: pageSize,
            query: query
        });

};

PublicProfileClient.prototype.toggleFollowing = function (userId) {
    return $.post(this.endpoint + "/following/toggle", {userId : userId});
};

PublicProfileClient.prototype.getWishList = function (userId, page) {
    return $.get(this.endpoint + "/wishlist", {userId : userId, page : page});
};