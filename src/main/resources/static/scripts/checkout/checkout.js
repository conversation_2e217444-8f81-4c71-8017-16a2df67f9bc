$(function() {
    const checkoutClient = new CheckoutClient();
    const payment = new Best2Pay();

    var $component = $(".js-checkout");
    var $cartSizeBadge = $(".headerPanel-cart .headerPanel-badge");

    $component.on("click", ".js-remove", remove);
    $component.on("click", ".js-discount-change", changeDiscount);
    $component.on("focus blur", ".js-discount-input", toggleFocus);
    $component.on("keypress", ".js-discount-input", submitDiscount);
    $component.on("click", ".js-complete", completeCheckout);
    $component.on("click", ".input-group-label", submitDiscount);
    $(document).on("click", "#SEND_BUTTON", function() {
        alert();
    });


    initAddressSuggestions();
	separateFlatValueFromAddress();

    function toggleFocus(event) {
        $(".checkout_discount_input:visible")
            .toggleClass("focus")
            .find(".input-group-label")
                .toggleClass("focus");

        if (event.type === "focusout"
            && $(".js-discount:visible").attr("data-discount-is-set") === "true") {
            $(".js-discount-input").prop("disabled",  true);
        }

        if ($(".js-discount:visible").attr("data-discount-is-set") === "true"
            || ($(".js-input-state", ".js-discount:visible").length > 0
                && $(".js-input-state", ".js-discount:visible").css("display") !== 'none')) {
            $(".js-input-prompt").toggleClass("hide");
            $(".js-input-state").toggleClass("hide");
            $(".input-group-label__text").toggleClass("input-group-label__text--hide");
        }
    }

    function submitDiscount(event) {
        if (event.keyCode && event.keyCode !== 13) {
            return;
        }

        // var $input = $(event.target);
        var $input = $('#discount-input');

        var discountCode = $input.val().trim();

        $component.fadeTo(0, 0.5).css("pointer-events", "none");
        if (discountCode !== "") {
            checkoutClient.updateDiscount(discountCode, true)
                .done(function(data) {
                    updateState(data);
                })
                .fail(function(response) {
                    $(".js-discount:visible").attr("data-discount-is-set", "false");
                    $(".checkout_discount_input:visible + .form-error")
                        .addClass("is-visible")
                        .html(response.responseJSON.message);
                })
                .always(function() {
                    $component.fadeTo(0, 1).css("pointer-events", "");
                });
        }
        // else if ($(".js-discount:visible").attr("data-discount-is-set") === "true") {
        else {
            checkoutClient.updateDiscount(null, true)
                .done(function(data) {
                    updateState(data);
                })
                .fail(function(response) {
                    $(".checkout_discount_input:visible + .form-error")
                        .addClass("is-visible")
                        .html(response.responseJSON.message);
                })
                .always(function() {
                    $component.fadeTo(0, 1).css("pointer-events", "");
                });
        }


        event.preventDefault();
    }

    function changeDiscount(event) {
        $(".js-discount:visible").attr("data-discount-is-set", "false");
        $('.js-discount-input', '.js-discount:visible').val('');
        checkoutClient.updateDiscount(null)
            .done(function(data) {
                updateState(data);
            })
            .fail(function(response) {
                $(".checkout_discount_input:visible + .form-error")
                    .addClass("is-visible")
                    .html(response.responseJSON.message);
            });
        $(event.target)
            .closest(".js-discount")
            .find(".js-discount-input")
            .prop("disabled", false)
            .trigger("focus");
    }

    function completeCheckout(event) {
        if (!userAgreementIsValid()) return;
		var address_with_flat = addFlatToAddress($("#checkout-address").val());
        checkoutClient.complete($("#checkout-name").val(), $("#checkout-phone").val(),
            address_with_flat, $("#checkout-city").val(), $("#checkout-nickname").val(),
            $("#checkout-email").val(), $("#checkout-zipcode").val(), $("#checkout-comment").val(),
            $("#checkout-extensive-address").val()
            )
            .done(function(data) {
                switch(data.paymentSystem) {
                    case 'best2pay':
                        pay(data);
                        break;
                    default:
                        console.log(data);
                        window.location = data.bank_url;
                        break;
                }
            })
            .fail(function(response) {
                alert("Ошибка: " + response.responseJSON.message);
            });
    }

    function pay(params) {
        payment.pay({
            sector: params.sector,
            code: params.code,
            amount: params.amount,
            reference: params.reference,
            description: params.reference,
            preauth: params.preauth
        });
    }

    function updateState(newState) {
        $component.html(newState.renderedContent);

        $cartSizeBadge.html(newState.size);

        if (!newState.size) {
            $component.addClass("align-center");
            $cartSizeBadge.addClass("hide");
        }

        $component.find("[data-mfp-src='#login-popup']").magnificPopup();

        initAddressSuggestions();
    }

    function remove(event) {

        var $itemToRemove = $(event.target).closest(".js-item");
        var $itemId = $itemToRemove.attr("data-id");
        if (!$itemId) { return; }

        $component.fadeTo(0, 0.5).css("pointer-events", "none");
        checkoutClient.removeItem($itemId, "removeFromCheckout")
            .done(function(data) {
                updateState(data);
            })
            .always(function() {
                $component.fadeTo(0, 1).css("pointer-events", "");
            });
    }

    function userAgreementIsValid() {

        var $userAgreement = $(".checkout_user_agreement");

        if ($userAgreement.find("input").prop("checked")) { return true; }

        $userAgreement.find(".form-error").addClass("is-visible");

        $("html, body").animate({
            scrollTop: $(window).width() < 1280 ?
                /* на мобиле шапка сайта позиционируется абсолютно и перекрывает флажок */
                $userAgreement.offset().top - $(".headerPanel").outerHeight()
                : $userAgreement.offset().top
        }, 200);

        return false;
    }

    function initAddressSuggestions() {
		
		//Решили пока отключить сервис дадаты
		return;

        var $city = $("#checkout-city");
        var $address = $("#checkout-address");
        var $zipcode = $("#checkout-zipcode");
		var $flat = $("#checkout-flat");

        $city.suggestions({
            token: "abb1e065856a2c6747958a4a21c2ce8fd27ceacc",
            type: "ADDRESS",
            bounds: "city-settlement",
            onSelect: function(suggest) {
                handleSuggestedAddress(suggest, $city, $address, $zipcode, $flat);
            },
            onSelectNothing: function () {
                $(this).val("");
                $zipcode.val("");
            },

            formatSelected: formatCity,
            hint: false
        });

        $address.suggestions({
            token: "abb1e065856a2c6747958a4a21c2ce8fd27ceacc",
            type: "ADDRESS",
            hint: false,

            constraints: $city,

            onSelect: function(suggest) {
                handleSuggestedAddress(suggest, $city, $address, $zipcode, $flat);
            },
            onSelectNothing: function () {
                $(this).val("")
            }
        });
		
    }
	
	function getFlatSeparator(){
		return ", кв ";
	}
	
	function setAddressAndFlat(suggest, $address, $flat){
		if(!suggest || !suggest.flat) return;
		$flat.val(suggest.flat);
		var flat_address_suffix = getFlatSeparator() + suggest.flat;
		$address.val($address.val().replace(flat_address_suffix, ""));
	}
	
	function addFlatToAddress(address){
		var flat = $("#checkout-flat").val();
		if(!flat) return address;
		return address + getFlatSeparator() + flat;
	}
	
	function separateFlatValueFromAddress(){
        var $address = $("#checkout-address");
		var $flat = $("#checkout-flat");
		var address = $address.val();
		if(!address) return;
		var flat_sep = getFlatSeparator();
		var flat_sep_index = address.indexOf(flat_sep);
		if(flat_sep_index == -1) return;
		$address.val(address.substring(0, flat_sep_index));
		$flat.val(address.substring(flat_sep_index + flat_sep.length));
	}

    function handleSuggestedAddress(suggest, $city, $address, $zipcode, $flat) {
        $zipcode.val(suggest.data.postal_code);
		setAddressAndFlat(suggest.data, $address, $flat);
        $("#checkout-extensive-address").val(JSON.stringify(suggest.data));

        $component.fadeTo(0, 0.5).css("pointer-events", "none");
		var address_with_flat = addFlatToAddress($address.val());
        checkoutClient.updateDelivery($city.val(), address_with_flat, $zipcode.val(), suggest.data)
            .always(function() {
                $component.fadeTo(0, 1).css("pointer-events", "");
            });
    }

    function formatCity(suggestion) {
        const address = suggestion.data;
        if (address.city_with_type === address.region_with_type) {
            return address.settlement_with_type || address.city_with_type;
        } else {
            return join([
                address.city,
                join([address.settlement_type_full, address.settlement], " ")]);
        }
    }

    function join(strings, optionalSeparator) {
        const sep = optionalSeparator || ", ";
        return strings.filter(function (s) {
            return s
        }).join(sep);
    }

});