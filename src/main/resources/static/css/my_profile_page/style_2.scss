.container {
	margin-left: auto;
	margin-right: auto;
	padding-left: 10px;
	padding-right: 10px; }
.container .row {
	margin-left: -10px;
	margin-right: -10px; }
.container .row:after {
	content: "";
	display: table-cell;
	clear: both; }
.container .row > [class*="col-"] {
	position: relative;
	box-sizing: border-box;
	min-height: 1px;
	float: left;
	padding-left: 10px;
	padding-right: 10px; }

.col-1 {
	width: 8.3333333333%; }

.col-2 {
	width: 16.6666666667%; }

.col-3 {
	width: 25%; }

.col-4 {
	width: 33.3333333333%; }

.col-5 {
	width: 41.6666666667%; }

.col-6 {
	width: 50%; }

.col-7 {
	width: 58.3333333333%; }

.col-8 {
	width: 66.6666666667%; }

.col-9 {
	width: 75%; }

.col-10 {
	width: 83.3333333333%; }

.col-11 {
	width: 91.6666666667%; }

.col-12 {
	width: 100%; }

@font-face {
	font-family: 'Playfair Display Bold';
	src: url("../fonts/PlayfairDisplay/PlayfairDisplay-Bold.eot");
	src: url("../fonts/PlayfairDisplay/PlayfairDisplay-Bold.eot?#iefix") format("embedded-opentype"), url("../fonts/PlayfairDisplay/PlayfairDisplay-Bold.woff2") format("woff2"), url("../fonts/PlayfairDisplay/PlayfairDisplay-Bold.woff") format("woff"), url("../fonts/PlayfairDisplay/PlayfairDisplay-Bold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal; }
@font-face {
	font-family: 'Roboto Medium';
	src: url("../fonts/Roboto/Roboto-Medium.eot");
	src: url("../fonts/Roboto/Roboto-Medium.eot?#iefix") format("embedded-opentype"), url("../fonts/Roboto/Roboto-Medium.woff2") format("woff2"), url("../fonts/Roboto/Roboto-Medium.woff") format("woff"), url("../fonts/Roboto/Roboto-Medium.ttf") format("truetype");
	font-weight: 500;
	font-style: normal; }
@font-face {
	font-family: 'Helvetica Regular';
	src: url("../fonts/Helvetica/Helvetica-Regular.eot");
	src: url("../fonts/Helvetica/Helvetica-Regular.eot?#iefix") format("embedded-opentype"), url("../fonts/Helvetica/Helvetica-Regular.woff2") format("woff2"), url("../fonts/Helvetica/Helvetica-Regular.woff") format("woff"), url("../fonts/Helvetica/Helvetica-Regular.ttf") format("truetype");
	font-weight: normal;
	font-style: normal; }
@font-face {
	font-family: 'Roboto Bold';
	src: url("../fonts/Roboto/Roboto-Bold.eot");
	src: url("../fonts/Roboto/Roboto-Bold.eot?#iefix") format("embedded-opentype"), url("../fonts/Roboto/Roboto-Bold.woff2") format("woff2"), url("../fonts/Roboto/Roboto-Bold.woff") format("woff"), url("../fonts/Roboto/Roboto-Bold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal; }
@font-face {
	font-family: 'Montserrat Medium';
	src: url("../fonts/Montserrat/Montserrat-Medium.eot");
	src: url("../fonts/Montserrat/Montserrat-Medium.eot?#iefix") format("embedded-opentype"), url("../fonts/Montserrat/Montserrat-Medium.woff2") format("woff2"), url("../fonts/Montserrat/Montserrat-Medium.woff") format("woff"), url("../fonts/Montserrat/Montserrat-Medium.ttf") format("truetype");
	font-weight: 500;
	font-style: normal; }
@font-face {
	font-family: 'Roboto Regular';
	src: url("../fonts/Roboto/Roboto-Regular.eot");
	src: url("../fonts/Roboto/Roboto-Regular.eot?#iefix") format("embedded-opentype"), url("../fonts/Roboto/Roboto-Regular.woff2") format("woff2"), url("../fonts/Roboto/Roboto-Regular.woff") format("woff"), url("../fonts/Roboto/Roboto-Regular.ttf") format("truetype");
	font-weight: normal;
	font-style: normal; }
@font-face {
	font-family: 'Open Sans';
	src: url("../fonts/Open_Sans/OpenSans.eot");
	src: url("../fonts/Open_Sans/OpenSans.eot?#iefix") format("embedded-opentype"), url("../fonts/Open_Sans/OpenSans.woff2") format("woff2"), url("../fonts/Open_Sans/OpenSans.woff") format("woff"), url("../fonts/Open_Sans/OpenSans.ttf") format("truetype");
	font-weight: normal;
	font-style: normal; }
@font-face {
	font-family: 'Open Sans Bold';
	src: url("../fonts/Open_Sans/OpenSans-Bold.eot");
	src: url("../fonts/Open_Sans/OpenSans-Bold.eot?#iefix") format("embedded-opentype"), url("../fonts/Open_Sans/OpenSans-Bold.woff2") format("woff2"), url("../fonts/Open_Sans/OpenSans-Bold.woff") format("woff"), url("../fonts/Open_Sans/OpenSans-Bold.ttf") format("truetype");
	font-weight: bold;
	font-style: normal; }
@font-face {
	font-family: 'Open Sans Semibold';
	src: url("../fonts/Open_Sans/OpenSans-Semibold.eot");
	src: url("../fonts/Open_Sans/OpenSans-Semibold.eot?#iefix") format("embedded-opentype"), url("../fonts/Open_Sans/OpenSans-Semibold.woff2") format("woff2"), url("../fonts/Open_Sans/OpenSans-Semibold.woff") format("woff"), url("../fonts/Open_Sans/OpenSans-Semibold.ttf") format("truetype");
	font-weight: 600;
	font-style: normal; }
@font-face {
	font-family: 'Playfair Display Regular';
	src: url("../fonts/PlayfairDisplay/PlayfairDisplay-Regular.eot");
	src: url("../fonts/PlayfairDisplay/PlayfairDisplay-Regular.eot?#iefix") format("embedded-opentype"), url("../fonts/PlayfairDisplay/PlayfairDisplay-Regular.woff2") format("woff2"), url("../fonts/PlayfairDisplay/PlayfairDisplay-Regular.woff") format("woff"), url("../fonts/PlayfairDisplay/PlayfairDisplay-Regular.ttf") format("truetype");
	font-weight: normal;
	font-style: normal; }
.clearfix:after {
	content: '';
	clear: both;
	display: table; }

body,
input,
textarea,
a,
label {
	font-size: 14px;
	color: #333333;
	font-family: "Open Sans"; }

section.seller_buyer_page .seller_top_title_block {
	background: url("/images/icons/seller_page_bg.jpg") no-repeat center center;
	background-size: cover; }
section.seller_buyer_page .buyer_top_title_block {
	background: url("/images/icons/buyer_page_bg.jpg") no-repeat center center;
	background-size: cover; }
section.seller_buyer_page .seller_buyer_top_title_block {
	padding: 86px 0 82px;
	text-align: center;
	position: relative; }
section.seller_buyer_page .seller_buyer_top_title_block:before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	z-index: 0; }
section.seller_buyer_page .seller_buyer_top_title_block .center_block {
	max-width: 500px;
	margin: 0 auto;
	position: relative;
	z-index: 1; }
section.seller_buyer_page .seller_buyer_top_title_block .center_block .title_page {
	color: #ffffff;
	font-size: 34px; }
section.seller_buyer_page .seller_buyer_top_title_block .center_block .under_title {
	color: #E0E0E0;
	font-size: 14px;
	line-height: 21px;
	margin-top: 15px; }
section.seller_buyer_page .menu_seller_buyer {
	padding: 30px 0;
	border-bottom: 1px solid rgba(51, 51, 51, 0.1); }
section.seller_buyer_page .menu_seller_buyer .wrapper {
	max-width: 1120px; }
section.seller_buyer_page .menu_seller_buyer ul {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
section.seller_buyer_page .menu_seller_buyer ul li a {
	font-size: 16px;
	font-family: "Open Sans Semibold";
	text-transform: uppercase;
	letter-spacing: .8px; }
section.seller_buyer_page .menu_seller_buyer ul li a em {
	display: none; }
section.seller_buyer_page .menu_seller_buyer ul li a span {
	display: inline-block; }

section.what_oskelly {
	padding: 78px 0 115px; }
section.what_oskelly .wrapper {
	max-width: 1120px; }
section.what_oskelly .flex_block {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center;
	border: 1px solid #F2F2F2;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding: 44px 53px; }
section.what_oskelly .flex_block .left_text {
	width: 500px;
	max-width: calc(100% - 420px); }
section.what_oskelly .flex_block .left_text p {
	font-size: 16px;
	line-height: 24px;
	margin-top: 9px; }
section.what_oskelly .flex_block .right_img {
	width: 400px;
	height: 265px; }
section.what_oskelly .flex_block .right_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: cover;
	-ms-object-fit: cover;
	-moz-object-fit: cover;
	-o-object-fit: cover;
	object-fit: cover; }

section.how_to_sell {
	padding: 80px 0;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.how_to_sell .wrapper {
	max-width: 1120px; }
section.how_to_sell .flex_block_sell {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 42px;
	position: relative;
	/*align-items: self-start;*/ }
section.how_to_sell .flex_block_sell .left_block_sell {
	width: 540px;
	max-width: calc(100% - 430px); }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li {
	position: relative;
	padding: 5px 5px 38px 38px; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li:last-child {
	padding-bottom: 0; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li:last-child:before {
	display: none; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li:before {
	content: "";
	position: absolute;
	top: 0;
	left: 11px;
	background: #27AE60;
	width: 1px;
	height: 100%; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li .number {
	position: absolute;
	top: 0;
	left: 0;
	text-align: center;
	width: 24px;
	height: 24px;
	line-height: 24px;
	color: #ffffff;
	font-size: 14px;
	font-family: "Open Sans Bold";
	background: #27AE60;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li .name_block {
	font-size: 16px;
	font-family: "Open Sans Semibold"; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li p {
	color: #828282;
	line-height: 21px;
	margin-top: 8px;
	margin-bottom: 21px; }
section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li p:last-child {
	margin-bottom: 0; }
section.how_to_sell .flex_block_sell .right_block_sell {
	width: 418px;
	padding: 14px 7px;
	position: relative;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
section.how_to_sell .flex_block_sell .right_block_sell.active_fixed {
	visibility: visible; }
section.how_to_sell .flex_block_sell .right_block_sell .text_block {
	text-align: center;
	padding: 34px 47px 31px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid rgba(189, 189, 189, 0.6); }
section.how_to_sell .flex_block_sell .right_block_sell .text_block p {
	font-size: 16px;
	letter-spacing: .11px;
	line-height: 22px;
	margin-top: 24px; }
section.how_to_sell .flex_block_sell .right_block_sell .text_block p strong {
	font-family: "Open Sans Semibold"; }
section.how_to_sell .flex_block_sell .right_block_sell .text_block .button_back {
	margin-top: 34px; }
section.how_to_sell .flex_block_sell .right_block_sell .text_block .button_back a {
	display: inline-block;
	font-size: 16px;
	font-family: "Open Sans Semibold";
	color: #fff;
	padding: 15px 18px;
	background: #000000;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: 294px;
	text-align: center;
	max-width: 100%; }
section.how_to_sell .flex_block_sell .right_block_sell .text_block .button_back a:hover {
	background: #505050; }

section.expertise_oskelly {
	padding: 80px 0 75px;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.expertise_oskelly .wrapper {
	max-width: 1120px; }
section.expertise_oskelly .expertise_oskelly_left_right_block {
	margin-top: 41px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block {
	float: left;
	width: calc(100% - 650px);
	max-width: 395px;
	margin-top: 3px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block .block {
	margin-bottom: 25px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block .block .name_block {
	line-height: 18px;
	font-size: 16px;
	font-family: "Open Sans Semibold"; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block .block p {
	color: #828282;
	line-height: 21px;
	font-size: 14px;
	margin-top: 7px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block {
	float: right;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block {
	width: 290px;
	height: 197px;
	-webkit-border-radius: 0 0 2px 2px;
	-ms-border-radius: 0 0 2px 2px;
	-moz-border-radius: 0 0 2px 2px;
	-o-border-radius: 0 0 2px 2px;
	border-radius: 0 0 2px 2px;
	border: 1px solid rgba(189, 189, 189, 0.6);
	position: relative;
	margin: 0 7px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding-bottom: 20px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block:last-child {
	margin-left: 23px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: scale-down;
	-ms-object-fit: scale-down;
	-moz-object-fit: scale-down;
	-o-object-fit: scale-down;
	object-fit: scale-down; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_false:after {
	content: "";
	position: absolute;
	top: -1px;
	left: -1px;
	width: calc(100% + 2px);
	height: 3px;
	background: #FF0000; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_false:before {
	content: "";
	position: absolute;
	bottom: 20px;
	left: calc(50% - 9px);
	background: url("/images/icons/block_false_icon.svg") no-repeat center center;
	width: 18px;
	height: 18px; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_true:after {
	content: "";
	position: absolute;
	top: -1px;
	left: -1px;
	width: calc(100% + 2px);
	height: 3px;
	background: #27AE60; }
section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_true:before {
	content: "";
	position: absolute;
	bottom: 20px;
	left: calc(50% - 9px);
	background: url("/images/icons/block_true_icon.svg") no-repeat center center;
	width: 18px;
	height: 18px; }
section.expertise_oskelly .expertise_oskelly_left_right_block:after {
	content: "";
	display: table;
	clear: both; }

section.safe_deal {
	padding: 80px 0 78px;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.safe_deal.safe_deal_buyer {
	padding: 80px 0 56px; }
section.safe_deal .wrapper {
	max-width: 1120px; }
section.safe_deal .under_title_text {
	margin: 18px 0 0;
	text-align: left;
	max-width: 100%; }
section.safe_deal .list_safe_deal_info {
	margin-top: 30px; }
section.safe_deal .list_safe_deal_info ul {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: nowrap;
	justify-content: space-between; }
section.safe_deal .list_safe_deal_info ul li {
	position: relative;
	padding: 42px 20px 0 0;
	box-sizing: border-box; }
section.safe_deal .list_safe_deal_info ul li.number_one {
	width: 23%; }
section.safe_deal .list_safe_deal_info ul li.number_two {
	width: 28%; }
section.safe_deal .list_safe_deal_info ul li.number_tree {
	width: 32%; }
section.safe_deal .list_safe_deal_info ul li.number_four {
	width: 17%; }
section.safe_deal .list_safe_deal_info ul li:last-child {
	padding-right: 0; }
section.safe_deal .list_safe_deal_info ul li:last-child:before {
	display: none; }
section.safe_deal .list_safe_deal_info ul li:before {
	content: "";
	position: absolute;
	top: 12px;
	left: 0px;
	background: #27AE60;
	width: 100%;
	height: 1px; }
section.safe_deal .list_safe_deal_info ul li .number {
	position: absolute;
	top: 0;
	left: 0;
	text-align: center;
	width: 24px;
	height: 24px;
	line-height: 24px;
	color: #ffffff;
	font-size: 14px;
	font-family: "Open Sans Bold";
	background: #27AE60;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%; }
section.safe_deal .list_safe_deal_info ul li .name_list {
	font-size: 16px;
	font-family: "Open Sans Semibold";
	line-height: 22px; }

section.shipping_returns,
section.commission_oskelly {
	padding: 80px 0 85px;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.shipping_returns .wrapper,
section.commission_oskelly .wrapper {
	max-width: 1120px; }
section.shipping_returns .under_title_text,
section.commission_oskelly .under_title_text {
	margin: 18px 0 0;
	text-align: left;
	max-width: 100%; }
section.shipping_returns .flex_block_shipping_returns,
section.shipping_returns .flex_block_commission,
section.commission_oskelly .flex_block_shipping_returns,
section.commission_oskelly .flex_block_commission {
	margin-top: 44px;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
section.shipping_returns .flex_block_shipping_returns .block,
section.shipping_returns .flex_block_commission .block,
section.commission_oskelly .flex_block_shipping_returns .block,
section.commission_oskelly .flex_block_commission .block {
	width: 485px;
	max-width: 50%; }
section.shipping_returns .flex_block_shipping_returns .block .name_block,
section.shipping_returns .flex_block_commission .block .name_block,
section.commission_oskelly .flex_block_shipping_returns .block .name_block,
section.commission_oskelly .flex_block_commission .block .name_block {
	font-size: 16px;
	font-family: "Open Sans Semibold";
	letter-spacing: .11px;
	line-height: 18px; }
section.shipping_returns .flex_block_shipping_returns .block p,
section.shipping_returns .flex_block_commission .block p,
section.commission_oskelly .flex_block_shipping_returns .block p,
section.commission_oskelly .flex_block_commission .block p {
	color: #828282;
	margin-top: 8px;
	font-size: 14px;
	line-height: 21px; }
section.shipping_returns .flex_block_shipping_returns .block ul,
section.shipping_returns .flex_block_commission .block ul,
section.commission_oskelly .flex_block_shipping_returns .block ul,
section.commission_oskelly .flex_block_commission .block ul {
	margin-top: 18px; }
section.shipping_returns .flex_block_shipping_returns .block ul li,
section.shipping_returns .flex_block_commission .block ul li,
section.commission_oskelly .flex_block_shipping_returns .block ul li,
section.commission_oskelly .flex_block_commission .block ul li {
	color: #828282;
	margin-bottom: 14px;
	font-size: 14px;
	line-height: 21px; }
section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav,
section.shipping_returns .flex_block_commission .block .block_bottom_nav,
section.commission_oskelly .flex_block_shipping_returns .block .block_bottom_nav,
section.commission_oskelly .flex_block_commission .block .block_bottom_nav {
	margin-top: 32px; }
section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul,
section.shipping_returns .flex_block_commission .block .block_bottom_nav ul,
section.commission_oskelly .flex_block_shipping_returns .block .block_bottom_nav ul,
section.commission_oskelly .flex_block_commission .block .block_bottom_nav ul {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li,
section.shipping_returns .flex_block_commission .block .block_bottom_nav ul li,
section.commission_oskelly .flex_block_shipping_returns .block .block_bottom_nav ul li,
section.commission_oskelly .flex_block_commission .block .block_bottom_nav ul li {
	max-width: 25%;
	position: relative;
	padding-right: 45px;
	font-size: 14px;
	font-family: "Open Sans Semibold";
	letter-spacing: .11px;
	color: #333333;
	line-height: 18px;
	margin: 0; }
section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li:last-child,
section.shipping_returns .flex_block_commission .block .block_bottom_nav ul li:last-child,
section.commission_oskelly .flex_block_shipping_returns .block .block_bottom_nav ul li:last-child,
section.commission_oskelly .flex_block_commission .block .block_bottom_nav ul li:last-child {
	padding-right: 0; }
section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li:last-child:before,
section.shipping_returns .flex_block_commission .block .block_bottom_nav ul li:last-child:before,
section.commission_oskelly .flex_block_shipping_returns .block .block_bottom_nav ul li:last-child:before,
section.commission_oskelly .flex_block_commission .block .block_bottom_nav ul li:last-child:before {
	display: none; }
section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li:before,
section.shipping_returns .flex_block_commission .block .block_bottom_nav ul li:before,
section.commission_oskelly .flex_block_shipping_returns .block .block_bottom_nav ul li:before,
section.commission_oskelly .flex_block_commission .block .block_bottom_nav ul li:before {
	content: "";
	position: absolute;
	top: calc(50% - 11px);
	right: 10px;
	background: url("/images/icons/green_arrow_nav.svg") no-repeat center center;
	width: 11px;
	height: 22px; }
section.shipping_returns .flex_block_shipping_returns .block,
section.commission_oskelly .flex_block_shipping_returns .block {
	width: 434px; }
section.shipping_returns .flex_block_shipping_returns .block:first-child,
section.commission_oskelly .flex_block_shipping_returns .block:first-child {
	width: 534px; }

section.become_boutique {
	background: #FAFAFA;
	padding: 85px 0 80px;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.become_boutique .wrapper {
	max-width: 1120px; }
section.become_boutique .flex_block {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center; }
section.become_boutique .flex_block .left_block_text {
	width: calc(100% - 430px);
	max-width: 490px; }
section.become_boutique .flex_block .left_block_text p {
	margin-top: 26px;
	line-height: 26px;
	font-size: 16px; }
section.become_boutique .flex_block .right_form_block {
	width: 414px;
	margin-right: 60px; }
section.become_boutique .flex_block .right_form_block .form_block {
	padding: 33px 30px;
	background: #ffffff; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block {
	margin-bottom: 23px; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block span {
	font-size: 14px;
	display: block;
	margin-bottom: 10px; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block textarea,
section.become_boutique .flex_block .right_form_block .form_block .inp_block input {
	width: 100%;
	height: 42px;
	border: 1px solid #E5E5E5;
	outline: none;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block textarea {
	resize: none;
	height: 140px; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block_submit {
	margin-top: 30px; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block_submit .submit_form {
	font-size: 16px;
	font-family: "Open Sans Semibold";
	color: #fff;
	padding: 11px 0;
	width: 100%;
	background: #000000;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	text-align: center;
	outline: none; }
section.become_boutique .flex_block .right_form_block .form_block .inp_block_submit .submit_form:hover {
	background: #505050; }
section.become_boutique .flex_block .right_form_block .thanks_block {
	padding: 87px 52px 52px;
	text-align: center;
	background: #ffffff;
	display: none; }
section.become_boutique .flex_block .right_form_block .thanks_block p {
	font-size: 14px;
	line-height: 21px;
	margin-top: 30px; }

section.useful_tip {
	padding: 100px 0 63px;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.useful_tip.useful_tip_buyer {
	padding: 81px 0 71px; }
section.useful_tip.useful_tip_buyer .flex_block_tip .block {
	margin-bottom: 48px; }
section.useful_tip .wrapper {
	max-width: 1120px; }
section.useful_tip .flex_block_tip {
	margin-top: 47px;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
section.useful_tip .flex_block_tip .block {
	width: calc(50% - 10px);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding-right: 25px;
	margin-bottom: 60px; }
section.useful_tip .flex_block_tip .block .icon {
	height: 25px;
	position: relative; }
section.useful_tip .flex_block_tip .block .icon svg {
	position: absolute;
	top: 50%;
	left: 0;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%); }
section.useful_tip .flex_block_tip .block .name_block {
	font-size: 16px;
	line-height: 18px;
	font-family: "Open Sans Semibold";
	margin-top: 15px; }
section.useful_tip .flex_block_tip .block p {
	margin-top: 10px;
	line-height: 21px;
	color: #828282; }

section.offer_your_price {
	padding: 82px 0 79px;
	border-top: 1px solid rgba(51, 51, 51, 0.1); }
section.offer_your_price .wrapper {
	max-width: 1120px; }
section.offer_your_price .flex_block_price {
	margin-top: 40px;
	max-width: 950px;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
section.offer_your_price .flex_block_price .block {
	width: calc(50% - 50px);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
section.offer_your_price .flex_block_price .block .icon {
	height: 25px;
	position: relative; }
section.offer_your_price .flex_block_price .block .icon svg {
	position: absolute;
	top: 50%;
	left: 0;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%); }
section.offer_your_price .flex_block_price .block .name_block {
	font-size: 16px;
	line-height: 18px;
	font-family: "Open Sans Semibold";
	margin-top: 12px; }
section.offer_your_price .flex_block_price .block p {
	margin-top: 10px;
	line-height: 21px;
	color: #828282; }
section.offer_your_price .go_to_help {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center;
	border: 1px solid rgba(189, 189, 189, 0.6);
	padding: 27px 44px 20px 34px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	max-width: 927px;
	margin-top: 45px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
section.offer_your_price .go_to_help .icon_left {
	font-size: 22px;
	text-align: center;
	font-family: "Open Sans Bold";
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #E0E0E0;
	width: 45px;
	height: 45px;
	line-height: 45px;
	color: rgba(51, 51, 51, 0.5); }
section.offer_your_price .go_to_help .text_center {
	max-width: 406px;
	font-size: 16px;
	line-height: 24px;
	letter-spacing: .11px; }
section.offer_your_price .go_to_help .text_center strong {
	font-family: "Open Sans Semibold"; }
section.offer_your_price .go_to_help .button_back a {
	display: inline-block;
	font-size: 16px;
	font-family: "Open Sans Semibold";
	color: #fff;
	padding: 15px 18px;
	background: #000000;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: 294px;
	text-align: center;
	max-width: 100%; }
section.offer_your_price .go_to_help .button_back a:hover {
	background: #505050; }

section.about {
	padding-top: 75px; }
section.about .about_page_block {
	background: rgba(245, 245, 245, 0.5);
	padding: 80px 0;
	margin-top: 70px; }
section.about .about_page_block .about_flex_block {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center; }
section.about .about_page_block .left_text_info {
	width: 50%;
	max-width: 455px; }
section.about .about_page_block .left_text_info .page_title_block {
	text-align: left;
	margin-bottom: 30px; }
section.about .about_page_block .left_text_info p {
	margin-bottom: 22px;
	font-size: 16px;
	line-height: 24px; }
section.about .about_page_block .left_text_info .button_style {
	margin-top: 30px;
	width: 294px; }
section.about .about_page_block .left_text_info .button_style a {
	display: block;
	box-sizing: border-box;
	line-height: 44px;
	border: 1px solid #141414;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	text-align: center;
	font-size: 14px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	color: #141414; }
section.about .about_page_block .left_text_info .button_style a:hover {
	background: rgba(235, 235, 237, 0.3); }
section.about .about_page_block .right_img {
	width: 50%; }
section.about .about_page_block .right_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: cover;
	-ms-object-fit: cover;
	-moz-object-fit: cover;
	-o-object-fit: cover;
	object-fit: cover; }

section.examination_authenticity {
	padding: 118px 0 100px; }
section.examination_authenticity .wrapper {
	max-width: 1114px; }
section.examination_authenticity .flex_block_examination_authenticity {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 42px 66px 34px;
	align-items: center; }
section.examination_authenticity .flex_block_examination_authenticity .left_img {
	width: 320px; }
section.examination_authenticity .flex_block_examination_authenticity .left_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain; }
section.examination_authenticity .flex_block_examination_authenticity .right_info {
	width: calc(100% - 350px);
	padding-left: 98px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
section.examination_authenticity .flex_block_examination_authenticity .right_info .page_title_block {
	text-align: left;
	margin-bottom: 14px; }
section.examination_authenticity .flex_block_examination_authenticity .right_info p {
	margin-bottom: 23px;
	line-height: 24px;
	font-size: 16px; }
section.examination_authenticity .flex_block_examination_authenticity .right_info p:last-child {
	margin-bottom: 0; }

section.start_selling.start_selling_about {
	background: #FAFAFA; }

.order_confirmation_get_paid {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 99;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	/*overflow: auto;*/
	display: none; }
.order_confirmation_get_paid .popup_block {
	width: 750px;
	max-width: 100%;
	margin: 0 auto;
	top: 50%;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%);
	max-height: 100vh;
	overflow: auto;
	position: relative;
	box-sizing: border-box;
	padding: 30px 40px 30px 50px;
	background: #FFFFFF; }
.order_confirmation_get_paid .popup_block .close {
	position: absolute;
	right: 40px;
	top: 43px;
	width: 16px;
	height: 16px;
	cursor: pointer; }
.order_confirmation_get_paid .popup_block .title_popup {
	font-size: 26px;
	line-height: 32px;
	padding-right: 20px; }
.order_confirmation_get_paid .popup_block .text_popup_error {
	color: #FF0000;
	font-size: 12px;
	line-height: 16px;
	padding: 5px 12px;
	background: rgba(255, 0, 0, 0.05);
	border: 1px solid rgba(255, 0, 0, 0.13);
	margin-bottom: 20px; }
.order_confirmation_get_paid .popup_block .toggle_block_info {
	background: #edfff4;
	padding: 15px;
	margin-top: 30px; }
.order_confirmation_get_paid .popup_block .toggle_block_info .name_click {
	padding: 0 30px;
	position: relative;
	cursor: pointer; }
.order_confirmation_get_paid .popup_block .toggle_block_info .name_click.active:before {
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	-moz-transform: rotate(0deg);
	-o-transform: rotate(0deg);
	transform: rotate(0deg); }
.order_confirmation_get_paid .popup_block .toggle_block_info .name_click .icon {
	position: absolute;
	top: 1px;
	left: 0; }
.order_confirmation_get_paid .popup_block .toggle_block_info .name_click .name_info_click {
	font-size: 14px;
	font-family: "Open Sans Bold"; }
.order_confirmation_get_paid .popup_block .toggle_block_info .name_click:before {
	content: "";
	position: absolute;
	top: 4px;
	right: 0;
	background: url("/images/icons/ic_arrow_p.svg") no-repeat center center;
	width: 13px;
	height: 8px;
	background-size: 100%;
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg);
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.order_confirmation_get_paid .popup_block .toggle_block_info .text_area_info {
	padding-top: 23px;
	display: none; }
.order_confirmation_get_paid .popup_block .toggle_block_info .text_area_info p {
	font-size: 14px;
	color: #4F4F4F;
	line-height: 20px; }
.order_confirmation_get_paid .popup_block .toggle_block_info .text_area_info ol {
	counter-reset: heading;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .toggle_block_info .text_area_info ol li {
	position: relative;
	padding-left: 15px;
	font-size: 14px;
	color: #4F4F4F;
	line-height: 20px; }
.order_confirmation_get_paid .popup_block .toggle_block_info .text_area_info ol li:before {
	counter-increment: heading;
	content: counter(heading) ". ";
	position: absolute;
	top: 0;
	left: 0;
	font-size: 14px;
	color: #4F4F4F;
	line-height: 20px; }
.order_confirmation_get_paid .popup_block form {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 24px; }
.order_confirmation_get_paid .popup_block form .form_block_inp {
	margin-bottom: 20px;
	position: relative;
	width: calc(50% - 20px);
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block form .form_block_inp span {
	display: block;
	font-size: 14px;
	margin-bottom: 10px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .inp_text {
	width: 100%;
	height: 42px;
	font-size: 16px;
	padding: 0 30px 0 14px;
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	outline: none; }
.order_confirmation_get_paid .popup_block form .form_block_inp.none_mob .mob_show {
	display: none; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_tip {
	position: relative;
	padding-right: 12px;
	border-bottom: 1px solid #F0F0F0;
	cursor: pointer; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_tip:before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	background: url("/images/icons/tip_before_icon.svg") no-repeat center center;
	width: 6px;
	height: 12px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_tip span {
	float: left; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_tip .selected_tip {
	float: right;
	font-size: 13px;
	color: #BDBDBD; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_tip:after {
	content: "";
	display: table;
	clear: both; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block {
	width: 100%; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox {
	z-index: 10;
	width: 100%;
	height: 42px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	line-height: 42px;
	border: 1px solid #E5E5E5;
	cursor: pointer;
	padding: 0 14px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	color: #202020; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox.opened {
	border: 1px solid #202020; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox__dropdown {
	background: #fff;
	width: calc(100% + 2px);
	left: -1px;
	top: 41px !important;
	/*border: 1px solid #E5E5E5;*/
	-webkit-border-radius: 0 0 2px 2px;
	-ms-border-radius: 0 0 2px 2px;
	-moz-border-radius: 0 0 2px 2px;
	-o-border-radius: 0 0 2px 2px;
	border-radius: 0 0 2px 2px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox__dropdown ul {
	border: 1px solid #E5E5E5;
	background: #fff; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox__trigger-arrow {
	background: url("/images/icons/arrow_down_styler.svg") no-repeat center center;
	width: 12px;
	height: 6px;
	position: absolute;
	top: 50%;
	right: 10px;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%);
	background-size: 100%; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox li {
	padding: 0 14px;
	color: #202020;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox li.sel {
	font-family: "Open Sans Semibold"; }
.order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-selectbox li:hover, .order_confirmation_get_paid .popup_block form .form_block_inp .select_block .jq-select-multiple li:hover {
	background: #F2F2F2; }
.order_confirmation_get_paid .popup_block form .form_block_inp .error_text {
	color: #FF0000;
	font-size: 14px;
	line-height: 18px;
	margin-top: 10px;
	display: none; }
.order_confirmation_get_paid .popup_block form .form_block_inp .error_text_empty {
	color: #FF0000;
	font-size: 14px;
	line-height: 18px;
	margin-top: 10px;
	display: none; }
.order_confirmation_get_paid .popup_block form .form_block_inp .pos_rel_inp {
	position: relative; }
.order_confirmation_get_paid .popup_block form .form_block_inp .pos_rel_inp.data_inp_area {
	width: 140px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .focus_text {
	display: none;
	background: #E6FBED;
	color: #219653;
	font-size: 12px;
	line-height: 18px;
	padding: 10px 17px 15px 34px;
	position: absolute;
	left: 0;
	bottom: 80px;
	width: 100%;
	box-sizing: border-box;
	z-index: 21; }
.order_confirmation_get_paid .popup_block form .form_block_inp .focus_text .icon_focus {
	width: 12px;
	height: 12px;
	background: #219653;
	color: #ffffff;
	position: absolute;
	top: 15px;
	left: 12px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .focus_text .icon_focus svg {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }
.order_confirmation_get_paid .popup_block form .form_block_inp .focus_text:before {
	content: "";
	position: absolute;
	bottom: -20px;
	right: 17px;
	background: url("/images/icons/Polygon_inp.svg") no-repeat center center;
	width: 47px;
	height: 34px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .icon_true {
	display: none;
	position: absolute;
	right: 11px;
	top: 14px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .icon_false {
	display: none;
	position: absolute;
	right: 12px;
	top: 15px; }
.order_confirmation_get_paid .popup_block form .form_block_inp .false_form .inp_text {
	border: 1px solid #ff0000 !important; }
.order_confirmation_get_paid .popup_block form .form_block_inp .false_form .inp_text.not_obligatory {
	border: 1px solid #E5E5E5 !important; }
.order_confirmation_get_paid .popup_block form .form_block_inp .false_form .icon_false {
	display: block; }
.order_confirmation_get_paid .popup_block form .form_block_inp .false_form .error_text {
	display: block; }
.order_confirmation_get_paid .popup_block form .form_block_inp .true_form .icon_true {
	display: block; }
.order_confirmation_get_paid .popup_block form .form_block_inp .two_inp_address > div {
	width: 100px;
	float: left;
	margin-right: 28px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block {
	margin-top: 34px;
	display: none; }
.order_confirmation_get_paid .popup_block .edited_add_all_block.edited_saves_all_show {
	display: block; }
.order_confirmation_get_paid .popup_block .edited_add_all_block.edited_saves_all_block_return {
	margin-top: 20px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block.edited_saves_all_block_return .name_block_pop {
	margin: 20px 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info {
	margin-bottom: 34px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info p {
	font-size: 14px;
	color: #BDBDBD;
	margin-top: 12px;
	line-height: 18px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block {
	position: relative;
	margin-bottom: 18px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label {
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding: 17px 50px;
	color: #333333;
	font-size: 14px;
	cursor: pointer;
	display: block;
	position: relative;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .name_block {
	font-size: 14px;
	font-family: "Open Sans"; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .name_block span {
	color: #333333;
	padding-left: 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon {
	position: absolute;
	right: 11px;
	top: 12px;
	width: 25px;
	height: 25px;
	-webkit-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-ms-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-o-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	opacity: 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a span {
	display: none;
	font-size: 13px;
	font-family: "Open Sans Semibold";
	padding-right: 5px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save {
	margin-bottom: 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul {
	display: inline-block;
	vertical-align: top;
	margin-right: 30px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li {
	margin-bottom: 11px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li:last-child {
	margin-bottom: 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li strong {
	color: #BDBDBD;
	display: inline-block;
	width: 36px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li span {
	color: #202020;
	margin-left: 5px;
	display: inline-block; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:before {
	content: "";
	position: absolute;
	top: calc(50% - 8px);
	left: 16px;
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	border: 1px solid #BDBDBD;
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:after {
	content: "";
	position: absolute;
	display: none;
	width: 8px;
	height: 8px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #000000;
	left: 20px;
	top: calc(50% - 4px); }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label > div {
	margin-bottom: 11px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label > div:last-child {
	margin-bottom: 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio'] {
	display: none; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label {
	border: 1px solid #4F4F4F; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label .edited_icon {
	opacity: 1; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label .name_block {
	font-size: 14px;
	font-family: "Open Sans Bold"; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label .name_block span {
	font-family: "Open Sans Bold";
	color: #333333;
	padding-left: 0; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label:after {
	display: block; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block:hover .radio_info label {
	border: 1px solid #4F4F4F; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block:hover .radio_info label .edited_icon {
	opacity: 1; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes {
	position: relative;
	padding-right: 35px; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes .edited_icon {
	position: absolute;
	right: 10px;
	top: 10px;
	width: 25px;
	height: 25px;
	-webkit-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-ms-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-o-box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes .edited_icon a {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }
.order_confirmation_get_paid .popup_block .edited_add_all_block .add_address_new a {
	position: relative;
	line-height: 25px;
	color: #202020;
	font-size: 12px;
	padding-left: 38px;
	display: inline-block; }
.order_confirmation_get_paid .popup_block .edited_add_all_block .add_address_new a:before {
	width: 25px;
	height: 25px;
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	background: #F2F2F2 url("/images/icons/add_block_icon_plus.svg") no-repeat center center; }
.order_confirmation_get_paid .popup_block .popup_number_order {
	color: #BDBDBD;
	font-size: 14px;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .name_block_pop {
	font-size: 14px;
	font-family: "Open Sans Bold";
	line-height: 18px;
	margin-top: 37px; }
.order_confirmation_get_paid .popup_block .text_popup {
	color: #4F4F4F;
	background: rgba(237, 255, 244, 0.5);
	padding: 10px 16px;
	font-size: 14px;
	line-height: 18px; }
.order_confirmation_get_paid .popup_block .text_popup .name_popup_text {
	font-size: 14px;
	font-family: "Open Sans Bold"; }
.order_confirmation_get_paid .popup_block .text_popup .name_popup_text svg {
	display: inline-block;
	vertical-align: middle;
	margin-right: 7px; }
.order_confirmation_get_paid .popup_block .text_popup p {
	margin-top: 15px;
	line-height: 20px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs {
	margin-top: 33px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab.active {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order {
	margin-top: 37px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	border: 1px solid #F2F2F2;
	padding: 27px 16px 25px 30px;
	position: relative;
	margin-bottom: 30px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.true_block {
	border: 1px solid #42B874; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.true_block .product_right_confirm_reject .confirm_reject {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.true_block .product_right_confirm_reject .confirm_reject_clicked {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.true_block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_false {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.false_block {
	border: 1px solid rgba(255, 0, 0, 0.5); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.false_block .product_right_confirm_reject .confirm_reject {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.false_block .product_right_confirm_reject .confirm_reject_clicked {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block.false_block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .sale {
	position: absolute;
	left: 14px;
	top: 7px;
	background: url("/images/icons/sale_icon.svg") no-repeat center center;
	width: 25px;
	height: 25px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_img {
	width: 100px;
	height: 115px;
	overflow: hidden;
	position: relative; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain;
	object-position: center; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info {
	padding-left: 45px;
	padding-right: 10px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: calc(100% - 152px - 100px); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .product_name a {
	font-size: 14px;
	font-family: "Open Sans Semibold";
	color: #000000;
	text-transform: uppercase;
	letter-spacing: .4px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .product_size,
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .product_type {
	color: #BDBDBD;
	font-size: 12px;
	margin-top: 8px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .prices_block {
	margin-top: 25px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .prices_block .prices {
	font-size: 16px;
	font-family: "Open Sans Semibold";
	display: inline-block;
	vertical-align: middle;
	letter-spacing: .22px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .your_profit {
	font-size: 12px;
	color: #42B874;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject {
	width: 152px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject div {
	margin-bottom: 10px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject div a {
	display: block;
	width: 100%;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	text-align: center;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	font-size: 12px;
	font-family: "Open Sans Semibold";
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 31px;
	line-height: 29px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject div.confirm a {
	color: #FFFFFF;
	background: #000000;
	border: 1px solid #000000; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject div.confirm a:hover {
	background: #505050;
	border: 1px solid #505050; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject div.reject a {
	color: #333333;
	background: #FFFFFF;
	border: 1px solid rgba(32, 32, 32, 0.1); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject div.reject a:hover {
	background: rgba(32, 32, 32, 0.1); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false div {
	font-size: 12px;
	font-family: "Open Sans Semibold";
	position: relative;
	margin-bottom: 10px;
	padding: 10px 10px 10px 40px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false div.active {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false .confirm_reject_true {
	color: #27AE60; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false .confirm_reject_true:before {
	content: "";
	position: absolute;
	top: calc(50% - 8px);
	left: 20px;
	background: url("/images/icons/rectangle_true.svg") no-repeat center center;
	width: 14px;
	height: 14px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false .confirm_reject_false {
	color: #FF0000; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false .confirm_reject_false:before {
	content: "";
	position: absolute;
	top: calc(50% - 8px);
	left: 20px;
	background: url("/images/icons/rectangle_false.svg") no-repeat center center;
	width: 14px;
	height: 14px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .change_his_mind a {
	display: block;
	width: 100%;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	text-align: center;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	font-size: 12px;
	font-family: "Open Sans Semibold";
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 31px;
	line-height: 29px;
	color: #333333;
	background: #FFFFFF;
	border: 1px solid rgba(32, 32, 32, 0.1); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .change_his_mind a:hover {
	background: rgba(32, 32, 32, 0.1); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label {
	margin-top: 30px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product > .name_block_pop {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label {
	margin-top: 0; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label input[type='radio'] {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label input[type='radio']:checked + .block_label_product {
	border: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label input[type='radio']:checked + .block_label_product .block_product:after {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label input[type='radio']:checked + .block_label_product .hide_show_blocks {
	height: auto;
	padding-bottom: 30px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label .block_label_product {
	border: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label .block_label_product .block_product {
	padding: 0 0 17px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label .block_label_product .block_product:before {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label .block_label_product .block_product:after {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label.blocks_order_one_product .block_label .hide_show_blocks {
	height: auto;
	padding: 0; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label {
	margin-top: 20px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label input[type='radio'] {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label input[type='radio']:checked + .block_label_product {
	border: 1px solid #4F4F4F; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label input[type='radio']:checked + .block_label_product .block_product:after {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label input[type='radio']:checked + .block_label_product .hide_show_blocks {
	height: auto;
	padding-bottom: 30px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product {
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	color: #333333;
	font-size: 14px;
	cursor: pointer;
	display: block;
	position: relative;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product {
	position: relative;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	padding: 17px 50px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_img {
	width: 69px;
	height: 95px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #F0F0F0; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: calc(100% - 71px);
	padding-left: 17px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .product_name {
	letter-spacing: .8px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .product_name a {
	font-size: 14px;
	font-family: "Open Sans Semibold"; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .prices {
	font-size: 14px;
	margin-top: 7px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .prices span {
	font-size: 14px;
	color: #27AE60; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .product_type,
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .product_size {
	font-size: 12px;
	color: #BDBDBD;
	margin-top: 6px;
	line-height: 14px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .your_profit {
	color: #27AE60;
	font-size: 12px;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product .product_info .product_not_confirmed {
	color: #FF0000;
	font-size: 12px;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product:before {
	content: "";
	position: absolute;
	top: calc(50% - 8px);
	left: 16px;
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	border: 1px solid #BDBDBD;
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .block_label_product .block_product:after {
	content: "";
	position: absolute;
	display: none;
	width: 8px;
	height: 8px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #000000;
	left: 20px;
	top: calc(50% - 4px); }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks {
	height: 0;
	overflow: hidden;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	padding: 0 40px 0 50px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list {
	margin-top: 20px;
	margin-bottom: 50px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list:first-child {
	padding-right: 40px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul {
	margin-top: 20px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li {
	font-size: 13px;
	color: #333333;
	margin-bottom: 14px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li .ckeck_list label {
	position: relative;
	cursor: pointer;
	padding-left: 20px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li .ckeck_list label:before {
	content: "";
	position: absolute;
	width: 14px;
	height: 14px;
	border: 1px solid #F0F0F0;
	top: 2px;
	left: 0px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li .ckeck_list label:after {
	display: none;
	content: "";
	position: absolute;
	width: 14px;
	height: 12px;
	top: 2px;
	left: 0;
	background: url("/images/icons/check_pop_list.svg") no-repeat center center;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li .ckeck_list input[type='checkbox'] {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li .ckeck_list input[type='checkbox']:checked + label:after {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .blocks_two .blocks_two_list ul li .ckeck_list input[type='checkbox']:checked + label:before {
	background: #333333;
	border: 1px solid #333333; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .textarea_block {
	margin-top: 20px; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .textarea_block textarea {
	width: 100%;
	resize: none;
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	font-size: 13px;
	height: 118px;
	color: #333333;
	outline: none;
	/* Firefox 19+ */
	/* Firefox 18- */ }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .textarea_block textarea::-webkit-input-placeholder {
	color: #BDBDBD; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .textarea_block textarea::-moz-placeholder {
	color: #BDBDBD; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .textarea_block textarea:-moz-placeholder {
	color: #BDBDBD; }
.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order.blocks_order_label .block_label .hide_show_blocks .textarea_block textarea:-ms-input-placeholder {
	color: #BDBDBD; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button {
	margin-top: 40px;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_true .right_button .button_resume_submit,
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_true .right_button .button_resume {
	pointer-events: auto;
	background: #000000; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_true .right_button .button_resume_submit:hover,
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_true .right_button .button_resume:hover {
	background: #505050; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_true .left_info .true_block_info {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_false .right_button .button_resume {
	pointer-events: auto;
	background: #000000; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_false .right_button .button_resume:hover {
	background: #505050; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_false .left_info .true_block_info {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button.active_false .left_info .false_block_info {
	display: block; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info {
	max-width: 310px; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info .true_block_info {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info .true_block_info div {
	font-size: 14px;
	color: #BDBDBD;
	line-height: 18px; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info .false_block_info {
	display: none; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info .false_block_info .error_info {
	padding-left: 30px;
	font-size: 15px;
	color: #FA0000;
	letter-spacing: .11px;
	font-family: "Open Sans Semibold";
	position: relative; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info .false_block_info .error_info:before {
	content: "!";
	position: absolute;
	top: 0;
	left: 0;
	width: 13px;
	height: 13px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #FA0000;
	color: #ffffff;
	line-height: 13px;
	text-align: center;
	font-size: 10px; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .left_info .false_block_info p {
	margin-top: 10px;
	color: #BDBDBD;
	font-size: 12px;
	line-height: 18px; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .right_button .button_back {
	margin-right: 27px;
	font-size: 14px;
	color: #333333;
	display: inline-block;
	vertical-align: middle; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .right_button .button_resume_submit,
.order_confirmation_get_paid .popup_block .popup_bottom_info_button .right_button .button_resume {
	pointer-events: none;
	background: rgba(0, 0, 0, 0.3);
	width: 238px;
	height: 46px;
	line-height: 46px;
	text-align: center;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	display: inline-block;
	vertical-align: middle;
	font-size: 14px;
	font-family: "Open Sans Semibold";
	color: #FFFFFF; }
.order_confirmation_get_paid .popup_block .profit_block {
	margin-top: 40px; }
.order_confirmation_get_paid .popup_block .profit_block ul {
	margin-top: 11px;
	width: 205px; }
.order_confirmation_get_paid .popup_block .profit_block ul li {
	font-size: 13px;
	color: #828282;
	margin-bottom: 12px;
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: nowrap;
	justify-content: space-between;
	align-items: baseline; }
.order_confirmation_get_paid .popup_block .profit_block ul li span {
	margin-left: 10px;
	color: #333333; }
.order_confirmation_get_paid .popup_block .profit_block ul li span.red_text {
	color: #ff0000; }
.order_confirmation_get_paid .popup_block .profit_block ul li:last-child span {
	font-family: "Open Sans Bold";
	font-size: 15px; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button_two {
	text-align: center;
	margin-top: 50px; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button_two .button a {
	pointer-events: none;
	background: rgba(0, 0, 0, 0.3);
	width: 100%;
	height: 46px;
	line-height: 46px;
	text-align: center;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	display: inline-block;
	vertical-align: middle;
	font-size: 14px;
	font-family: "Open Sans Semibold";
	color: #FFFFFF; }
.order_confirmation_get_paid .popup_block .popup_bottom_info_button_two p {
	font-size: 12px;
	color: #BDBDBD;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .blocks_get_paid {
	position: relative;
	margin-top: 30px;
	padding-bottom: 20px; }
.order_confirmation_get_paid .popup_block .blocks_get_paid:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: -50px;
	background: #F2F2F2;
	width: calc(100% + 90px);
	height: 4px; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	position: relative;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	margin-bottom: 18px;
	align-items: center; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block:last-child {
	margin-bottom: 0; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_img {
	width: 100px;
	height: 115px;
	overflow: hidden;
	position: relative;
	border: 1px solid #F0F0F0;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain;
	object-position: center; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info {
	padding-left: 45px;
	padding-right: 10px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: calc(100% - 100px); }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_name a {
	font-size: 14px;
	font-family: "Open Sans Semibold";
	color: #000000;
	text-transform: uppercase;
	letter-spacing: .4px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	display: block; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_size,
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_type {
	color: #BDBDBD;
	font-size: 12px;
	margin-top: 8px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .prices_block .prices {
	font-size: 16px;
	font-family: "Open Sans Semibold";
	display: inline-block;
	vertical-align: middle;
	letter-spacing: .22px; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .your_profit {
	font-size: 12px;
	color: #42B874;
	margin-top: 10px; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_info_flex {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_info_flex > div {
	width: 50%; }
.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_info_flex .product_info_block_right {
	text-align: right; }
.order_confirmation_get_paid .popup_block .all_pay_block {
	display: none; }
.order_confirmation_get_paid .popup_block .all_pay_block.active {
	display: block; }
.order_confirmation_get_paid .popup_block .left_right_blocks {
	display: -webkit-box;
	/* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;
	/* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;
	/* TWEENER - IE 10 */
	display: -webkit-flex;
	/* NEW - Chrome */
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport {
	width: 92px;
	height: 109px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px dashed #BDBDBD;
	position: relative;
	margin-right: 16px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	margin-top: 20px;
	background: #ffffff; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list .img,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport .img,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list .img,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport .img {
	position: relative;
	background: #000;
	height: 100%;
	display: none; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list .img img,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport .img img,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list .img img,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport .img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list .img.active,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport .img.active,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list .img.active,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport .img.active {
	z-index: 1; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list.added_photo .img,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport.added_photo .img,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list.added_photo .img,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport.added_photo .img {
	display: block; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list.added_photo .attach_label_pop,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport.added_photo .attach_label_pop,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list.added_photo .attach_label_pop,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport.added_photo .attach_label_pop {
	position: absolute;
	right: 9px;
	top: 10px;
	left: auto;
	width: 17px;
	height: 17px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	background: url("/images/icons/exchange.svg") no-repeat center center; }
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_list.added_photo .attach_label_pop .name_icon,
.order_confirmation_get_paid .popup_block .blocks_two_list .add_photo_passport.added_photo .attach_label_pop .name_icon,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_list.added_photo .attach_label_pop .name_icon,
.order_confirmation_get_paid .popup_block .photo_passport .add_photo_passport.added_photo .attach_label_pop .name_icon {
	display: none; }
.order_confirmation_get_paid .popup_block .blocks_two_list .attach_label_pop,
.order_confirmation_get_paid .popup_block .photo_passport .attach_label_pop {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	cursor: pointer; }
.order_confirmation_get_paid .popup_block .blocks_two_list .attach_label_pop .attach_input_pop,
.order_confirmation_get_paid .popup_block .photo_passport .attach_label_pop .attach_input_pop {
	display: none; }
.order_confirmation_get_paid .popup_block .blocks_two_list .attach_label_pop .name_icon,
.order_confirmation_get_paid .popup_block .photo_passport .attach_label_pop .name_icon {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	color: #D6D6D8;
	font-size: 13px;
	line-height: 20px;
	text-align: center; }
.order_confirmation_get_paid .popup_block .blocks_two_list .attach_label_pop .name_icon svg,
.order_confirmation_get_paid .popup_block .photo_passport .attach_label_pop .name_icon svg {
	margin-bottom: 5px; }

#popup_thank_confirmed_error,
#popup_data_confirmed,
#popup_unfortunately_canceled,
#popup_thank_confirmed {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 99;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.6);
	/*overflow: auto;*/
	display: none; }
#popup_thank_confirmed_error .popup_block,
#popup_data_confirmed .popup_block,
#popup_unfortunately_canceled .popup_block,
#popup_thank_confirmed .popup_block {
	width: 604px;
	max-width: 100%;
	margin: 0 auto;
	top: 50%;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%);
	max-height: 100vh;
	overflow: auto;
	position: relative;
	box-sizing: border-box;
	padding: 40px 80px;
	background: #FFFFFF;
	text-align: center; }
#popup_thank_confirmed_error .popup_block .close,
#popup_data_confirmed .popup_block .close,
#popup_unfortunately_canceled .popup_block .close,
#popup_thank_confirmed .popup_block .close {
	position: absolute;
	right: 50px;
	top: 30px;
	width: 16px;
	height: 16px;
	cursor: pointer; }
#popup_thank_confirmed_error .popup_block .icon_popup,
#popup_data_confirmed .popup_block .icon_popup,
#popup_unfortunately_canceled .popup_block .icon_popup,
#popup_thank_confirmed .popup_block .icon_popup {
	margin: 0 auto;
	width: 160px;
	height: 145px;
	background: url("/images/icons/thank_confirmed_pop_icon.svg");
	position: relative; }
#popup_thank_confirmed_error .popup_block .icon_popup svg,
#popup_data_confirmed .popup_block .icon_popup svg,
#popup_unfortunately_canceled .popup_block .icon_popup svg,
#popup_thank_confirmed .popup_block .icon_popup svg {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }
#popup_thank_confirmed_error .popup_block .title_popup,
#popup_data_confirmed .popup_block .title_popup,
#popup_unfortunately_canceled .popup_block .title_popup,
#popup_thank_confirmed .popup_block .title_popup {
	font-size: 26px;
	padding: 0 20px; }
#popup_thank_confirmed_error .popup_block p,
#popup_data_confirmed .popup_block p,
#popup_unfortunately_canceled .popup_block p,
#popup_thank_confirmed .popup_block p {
	margin-top: 15px;
	font-size: 14px;
	line-height: 20px; }
#popup_thank_confirmed_error .popup_block .error_text_popup,
#popup_data_confirmed .popup_block .error_text_popup,
#popup_unfortunately_canceled .popup_block .error_text_popup,
#popup_thank_confirmed .popup_block .error_text_popup {
	border: 1px solid rgba(255, 0, 0, 0.13);
	background: rgba(255, 0, 0, 0.05);
	padding: 10px;
	color: #FF0000;
	font-size: 12px;
	line-height: 16px;
	margin-top: 15px; }
#popup_thank_confirmed_error .popup_block .popup_button,
#popup_data_confirmed .popup_block .popup_button,
#popup_unfortunately_canceled .popup_block .popup_button,
#popup_thank_confirmed .popup_block .popup_button {
	margin-top: 20px; }
#popup_thank_confirmed_error .popup_block .popup_button a,
#popup_data_confirmed .popup_block .popup_button a,
#popup_unfortunately_canceled .popup_block .popup_button a,
#popup_thank_confirmed .popup_block .popup_button a {
	display: inline-block;
	width: 350px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	text-align: center;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	font-size: 12px;
	font-family: "Open Sans Semibold";
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 46px;
	line-height: 44px;
	color: #FFFFFF;
	background: #000000;
	border: 1px solid #000000; }
#popup_thank_confirmed_error .popup_block .popup_button a:hover,
#popup_data_confirmed .popup_block .popup_button a:hover,
#popup_unfortunately_canceled .popup_block .popup_button a:hover,
#popup_thank_confirmed .popup_block .popup_button a:hover {
	background: #505050;
	border: 1px solid #505050; }

@media only screen and (max-width: 1200px) {
	section.become_boutique .flex_block .right_form_block {
		margin-right: 0; } }
@media only screen and (max-width: 1024px) {
	.wrapper {
		padding: 0 13px; }

	section.seller_buyer_page .seller_top_title_block {
		background: url("/images/icons/seller_page_bg_1.jpg") no-repeat center center;
		background-size: cover; }
	section.seller_buyer_page .buyer_top_title_block {
		background: url("/images/icons/buyer_page_bg_2.jpg") no-repeat center center;
		background-size: cover; }
	section.seller_buyer_page .menu_seller_buyer {
		padding: 15px 0 4px; }
	section.seller_buyer_page .menu_seller_buyer ul li {
		text-align: center;
		width: calc(100%/3);
		margin: 12px 0 23px; }
	section.seller_buyer_page .menu_seller_buyer ul li a {
		font-size: 14px; }
	section.seller_buyer_page .menu_seller_buyer ul li a em {
		display: inline-block; }
	section.seller_buyer_page .menu_seller_buyer ul li a span {
		display: none; }

	section.what_oskelly {
		padding: 59px 0 60px; }
	section.what_oskelly .flex_block {
		padding: 57px 27px; }
	section.what_oskelly .flex_block .left_text {
		width: 50%;
		max-width: 100%; }
	section.what_oskelly .flex_block .right_img {
		width: 43%;
		height: auto; }

	section.how_to_sell {
		padding: 60px 0 79px; }
	section.how_to_sell .flex_block_sell {
		display: block;
		margin-top: 39px; }
	section.how_to_sell .flex_block_sell .left_block_sell {
		width: 100%;
		max-width: 100%; }
	section.how_to_sell .flex_block_sell .right_block_sell {
		top: 0;
		width: 100%;
		margin-top: 69px;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		padding: 0 63px; }
	section.how_to_sell .flex_block_sell .right_block_sell .text_block p {
		max-width: 320px;
		margin: 24px auto 0; }

	section.expertise_oskelly {
		padding: 61px 0 61px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block {
		margin-top: 40px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block {
		float: left;
		max-width: 46%;
		width: 290px;
		margin: 0;
		display: block; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block {
		margin: 0; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_true {
		margin: 34px 0 0; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block {
		float: right;
		width: 54%;
		max-width: 100%;
		margin-top: 3px; }

	section.safe_deal {
		padding: 60px 0 61px; }
	section.safe_deal .list_safe_deal_info {
		margin-top: 37px; }
	section.safe_deal .list_safe_deal_info ul li {
		padding: 45px 32px 0 0; }
	section.safe_deal .list_safe_deal_info ul li.number_two {
		width: 24%; }
	section.safe_deal .list_safe_deal_info ul li.number_tree {
		width: 30%; }
	section.safe_deal .list_safe_deal_info ul li.number_four {
		width: 23%; }
	section.safe_deal .list_safe_deal_info ul li .name_list {
		font-size: 12px;
		line-height: 16px; }

	section.safe_deal.safe_deal_buyer {
		padding: 60px 0 47px; }

	section.commission_oskelly {
		padding: 60px 0 48px; }
	section.commission_oskelly .flex_block_commission {
		margin-top: 34px;
		display: block; }
	section.commission_oskelly .flex_block_commission .block {
		width: 100%;
		max-width: 100%;
		margin-bottom: 43px; }
	section.commission_oskelly .flex_block_commission .block:last-child {
		margin-bottom: 0; }

	section.shipping_returns {
		padding: 59px 0 59px; }
	section.shipping_returns .flex_block_shipping_returns {
		margin-top: 34px;
		display: block; }
	section.shipping_returns .flex_block_shipping_returns .block {
		width: 100%;
		max-width: 85%; }
	section.shipping_returns .flex_block_shipping_returns .block:first-child {
		max-width: 85%;
		width: 100%;
		margin-bottom: 64px; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav {
		margin-top: 25px; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul {
		justify-content: flex-start; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li {
		padding-right: 54px; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li:before {
		right: 20px; }

	section.become_boutique {
		padding: 60px 0; }
	section.become_boutique .flex_block {
		display: block; }
	section.become_boutique .flex_block .left_block_text {
		width: 100%;
		max-width: 100%; }
	section.become_boutique .flex_block .left_block_text p {
		margin-top: 28px; }
	section.become_boutique .flex_block .right_form_block {
		width: 100%;
		margin-top: 47px; }
	section.become_boutique .flex_block .right_form_block .thanks_block {
		padding: 87px 52px 71px; }
	section.become_boutique .flex_block .right_form_block .thanks_block p {
		line-height: 18px; }

	section.useful_tip {
		padding: 58px 0 30px; }
	section.useful_tip .flex_block_tip .block {
		width: calc(50% - 15px);
		padding-right: 0;
		margin-bottom: 40px; }
	section.useful_tip .flex_block_tip .block p br {
		display: none; }

	section.useful_tip.useful_tip_buyer {
		padding: 58px 0 44px; }
	section.useful_tip.useful_tip_buyer .flex_block_tip .block {
		margin-bottom: 28px; }

	section.offer_your_price {
		padding: 60px 0; }
	section.offer_your_price .flex_block_price {
		display: block;
		margin-top: 28px; }
	section.offer_your_price .flex_block_price .block {
		width: 100%;
		margin-bottom: 18px; }
	section.offer_your_price .go_to_help {
		margin-top: 19px;
		padding: 19px 20px; }
	section.offer_your_price .go_to_help .icon_left {
		display: none; }

	section.about {
		padding-top: 55px; }
	section.about .about_page_block {
		margin-top: 98px;
		padding: 60px 0px 51px; }
	section.about .about_page_block .about_flex_block {
		padding: 0 22px; }
	section.about .about_page_block .about_flex_block .left_text_info {
		width: 100%;
		max-width: 100%; }
	section.about .about_page_block .about_flex_block .left_text_info .page_title_block {
		font-size: 24px;
		margin-bottom: 13px; }
	section.about .about_page_block .about_flex_block .left_text_info p {
		margin-bottom: 20px;
		font-size: 14px;
		line-height: 21px; }
	section.about .about_page_block .about_flex_block .left_text_info .button_style {
		margin-top: 0; }
	section.about .about_page_block .about_flex_block .right_img {
		display: none; }

	section.examination_authenticity {
		padding: 93px 0 100px; }
	section.examination_authenticity .flex_block_examination_authenticity {
		padding: 0px 22px; }
	section.examination_authenticity .flex_block_examination_authenticity .left_img {
		width: 167px; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info {
		width: calc(100% - 167px);
		padding-left: 105px; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info .page_title_block {
		font-size: 20px;
		margin-bottom: 13px;
		letter-spacing: 1px; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info p {
		margin-bottom: 20px;
		font-size: 14px;
		line-height: 21px; }

	#popup_data_confirmed,
	#popup_unfortunately_canceled,
	#popup_thank_confirmed_error,
	#popup_thank_confirmed {
		width: 100%;
		height: 100vh;
		background: #ffffff; }
	#popup_data_confirmed .popup_block,
	#popup_unfortunately_canceled .popup_block,
	#popup_thank_confirmed_error .popup_block,
	#popup_thank_confirmed .popup_block {
		width: 100%;
		height: 100%; }
	#popup_data_confirmed .popup_block .popup_center,
	#popup_unfortunately_canceled .popup_block .popup_center,
	#popup_thank_confirmed_error .popup_block .popup_center,
	#popup_thank_confirmed .popup_block .popup_center {
		width: 604px;
		max-width: 100%;
		max-height: 100vh;
		padding: 40px 0;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		position: relative;
		top: 50%;
		left: 0;
		-webkit-transform: translate(0, -50%);
		-ms-transform: translate(0, -50%);
		-moz-transform: translate(0, -50%);
		-o-transform: translate(0, -50%);
		transform: translate(0, -50%);
		margin: 0 auto; }
	#popup_data_confirmed .popup_block .close,
	#popup_unfortunately_canceled .popup_block .close,
	#popup_thank_confirmed_error .popup_block .close,
	#popup_thank_confirmed .popup_block .close {
		right: 30px; }
	#popup_data_confirmed .popup_block .title_popup,
	#popup_unfortunately_canceled .popup_block .title_popup,
	#popup_thank_confirmed_error .popup_block .title_popup,
	#popup_thank_confirmed .popup_block .title_popup {
		padding: 0; }
	#popup_data_confirmed .popup_block .icon_popup,
	#popup_unfortunately_canceled .popup_block .icon_popup,
	#popup_thank_confirmed_error .popup_block .icon_popup,
	#popup_thank_confirmed .popup_block .icon_popup {
		margin: 0 auto 12px; }
	#popup_data_confirmed .popup_block .popup_button,
	#popup_unfortunately_canceled .popup_block .popup_button,
	#popup_thank_confirmed_error .popup_block .popup_button,
	#popup_thank_confirmed .popup_block .popup_button {
		margin-top: 30px; }
	#popup_data_confirmed .popup_block .popup_button a,
	#popup_unfortunately_canceled .popup_block .popup_button a,
	#popup_thank_confirmed_error .popup_block .popup_button a,
	#popup_thank_confirmed .popup_block .popup_button a {
		font-size: 14px; }

	.order_confirmation_get_paid .popup_block {
		overflow: inherit;
		width: 100%;
		max-width: 100%;
		height: 100vh;
		padding: 20px 16px; }
	.order_confirmation_get_paid .popup_block .close {
		position: absolute;
		top: 28px;
		left: 15px;
		background: url("/images/icons/arrow_back_prof.svg") no-repeat center center;
		width: 13px;
		height: 20px;
		background-size: 100%;
		cursor: pointer;
		z-index: 1; }
	.order_confirmation_get_paid .popup_block .close svg {
		display: none; }
	.order_confirmation_get_paid .popup_block .title_popup {
		text-align: center;
		font-size: 22px;
		line-height: 22px;
		padding: 0 15px; }
	.order_confirmation_get_paid .popup_block form {
		display: block; }
	.order_confirmation_get_paid .popup_block form .form_block_inp {
		width: 100%; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block {
		margin-top: 0; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info {
		padding: 23px 30px 23px 30px;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		float: none;
		width: 100%;
		text-align: center;
		margin-bottom: 0; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info .name_block {
		font-size: 22px;
		font-family: "Open Sans"; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info:before {
		content: "";
		position: absolute;
		top: 26px;
		left: 15px;
		background: url("/images/icons/arrow_back_prof.svg") no-repeat center center;
		width: 13px;
		height: 20px;
		background-size: 100%;
		cursor: pointer;
		z-index: 1; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block {
		border-bottom: 1px solid #F0F0F0;
		margin-bottom: 0; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label {
		border: none !important;
		color: #BDBDBD;
		padding: 25px 30px 21px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:before {
		top: 25px;
		left: 1px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:after {
		left: 6px;
		top: 30px;
		width: 6px;
		height: 6px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label > div {
		margin-bottom: 11px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .address_info span:first-child {
		display: block;
		margin-bottom: 5px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul {
		display: block; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon {
		position: static;
		width: auto;
		height: auto;
		-webkit-box-shadow: none;
		-ms-box-shadow: none;
		-moz-box-shadow: none;
		-o-box-shadow: none;
		box-shadow: none;
		margin-top: 27px;
		opacity: 1; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a {
		position: static;
		-webkit-transform: translate(0, 0);
		-ms-transform: translate(0, 0);
		-moz-transform: translate(0, 0);
		-o-transform: translate(0, 0);
		transform: translate(0, 0); }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a span {
		display: inline-block;
		vertical-align: middle; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a svg {
		display: inline-block;
		vertical-align: middle; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .add_address_new {
		margin-top: 10px; }
	.order_confirmation_get_paid .popup_block .popup_number_order {
		text-align: center; }
	.order_confirmation_get_paid .popup_block .overflow_block {
		overflow: auto;
		height: calc(100vh - 250px);
		box-sizing: border-box; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background: #FFFFFF;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		padding: 10px 16px 30px;
		display: block;
		text-align: center;
		margin-top: 0; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .left_info {
		max-width: 100%; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .left_info .false_block_info {
		max-width: 308px;
		margin: 0 auto; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .left_info .false_block_info .error_info {
		display: inline-block; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .left_info .true_block_info > div {
		display: inline-block; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .right_button {
		margin-top: 18px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .right_button .button_resume {
		width: 100%; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .right_button .button_back {
		display: none; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .popup_bottom_info_button .right_button .button_resume_submit {
		width: 100%; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid:before {
		width: 100%;
		left: 0; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_img {
		width: 69px;
		height: 81px; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info {
		padding-left: 30px;
		padding-right: 0;
		width: calc(100% - 70px); }
	.order_confirmation_get_paid .popup_block .popup_bottom_info_button_two {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background: #FFFFFF;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		padding: 10px 16px 30px;
		display: block;
		text-align: center;
		margin-top: 0; }

	#popup_get_paid .popup_block {
		padding: 20px 12px; }
	#popup_get_paid .popup_block .overflow_block {
		height: calc(100vh - 180px); } }
@media only screen and (max-width: 767px) {
	.title_block_sm {
		font-size: 22px;
		line-height: 26px;
		letter-spacing: .9px; }

	section.seller_buyer_page .menu_seller_buyer {
		display: none; }
	section.seller_buyer_page .seller_buyer_top_title_block {
		padding: 68px 0px 49px; }
	section.seller_buyer_page .seller_buyer_top_title_block .wrapper {
		padding: 0; }
	section.seller_buyer_page .seller_buyer_top_title_block .center_block .title_page {
		font-size: 22px;
		line-height: 28px;
		letter-spacing: 1px; }
	section.seller_buyer_page .seller_buyer_top_title_block .center_block .under_title {
		font-size: 12px;
		line-height: 19px;
		margin-top: 10px;
		padding: 0 14px; }

	section.what_oskelly {
		padding: 40px 0 40px; }
	section.what_oskelly .flex_block {
		display: block;
		padding: 19px 10px; }
	section.what_oskelly .flex_block .left_text {
		width: 100%; }
	section.what_oskelly .flex_block .left_text .title_block_sm {
		font-size: 18px;
		line-height: 24px; }
	section.what_oskelly .flex_block .left_text p {
		font-size: 14px;
		line-height: 21px;
		margin-top: 6px; }
	section.what_oskelly .flex_block .right_img {
		display: none; }

	section.how_to_sell {
		padding: 41px 0 38px; }
	section.how_to_sell .flex_block_sell {
		margin-top: 34px; }
	section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li {
		padding: 1px 5px 27px 35px; }
	section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li .name_block {
		font-size: 14px;
		line-height: 18px; }
	section.how_to_sell .flex_block_sell .left_block_sell .info_all_block ul li p {
		font-size: 12px;
		line-height: 18px;
		margin-bottom: 17px; }
	section.how_to_sell .flex_block_sell .right_block_sell {
		padding: 0px;
		margin-top: 39px; }
	section.how_to_sell .flex_block_sell .right_block_sell .text_block {
		padding: 34px 20px 31px; }
	section.how_to_sell .flex_block_sell .right_block_sell .text_block p {
		max-width: 100%;
		margin-top: 29px;
		font-size: 14px;
		line-height: 20px; }
	section.how_to_sell .flex_block_sell .right_block_sell .text_block .button_back {
		margin-top: 19px; }

	section.expertise_oskelly {
		padding: 41px 0 18px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block {
		margin-top: 31px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block {
		float: none;
		width: 100%;
		margin-top: 32px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block .block {
		margin-bottom: 22px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block .block .name_block {
		font-size: 14px;
		line-height: 18px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_right_block .block p {
		line-height: 18px;
		font-size: 12px;
		margin-top: 5px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block {
		float: none;
		width: 100%;
		max-width: 100%;
		display: -webkit-box;
		/* OLD - iOS 6-, Safari 3.1-6 */
		display: -moz-box;
		/* OLD - Firefox 19- (buggy but mostly works) */
		display: -ms-flexbox;
		/* TWEENER - IE 10 */
		display: -webkit-flex;
		/* NEW - Chrome */
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block {
		width: calc(50% - 4px);
		min-height: 140px;
		height: auto;
		padding: 0 16px 20px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_true {
		margin-top: 0; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_true:before {
		bottom: 15px; }
	section.expertise_oskelly .expertise_oskelly_left_right_block .expertise_left_block .block.block_false:before {
		bottom: 15px; }

	section.safe_deal {
		padding: 41px 0 40px; }
	section.safe_deal .under_title_text {
		margin-top: 12px; }
	section.safe_deal .list_safe_deal_info {
		margin-top: 23px; }
	section.safe_deal .list_safe_deal_info ul {
		display: block; }
	section.safe_deal .list_safe_deal_info ul li {
		width: 100% !important;
		padding: 4px 0 37px 35px; }
	section.safe_deal .list_safe_deal_info ul li:last-child {
		padding-bottom: 0; }
	section.safe_deal .list_safe_deal_info ul li:before {
		left: 12px;
		top: 0;
		width: 1px;
		height: 100%; }

	section.safe_deal.safe_deal_buyer {
		padding: 41px 0 39px; }
	section.safe_deal.safe_deal_buyer .under_title_text br {
		display: none; }

	section.commission_oskelly {
		padding: 42px 0 26px; }
	section.commission_oskelly .under_title_text {
		margin: 10px 0 0;
		font-size: 14px;
		line-height: 22px; }
	section.commission_oskelly .flex_block_commission {
		margin-top: 27px; }
	section.commission_oskelly .flex_block_commission .block {
		margin-bottom: 33px; }
	section.commission_oskelly .flex_block_commission .block .name_block {
		font-size: 14px; }
	section.commission_oskelly .flex_block_commission .block ul {
		margin-top: 15px; }
	section.commission_oskelly .flex_block_commission .block ul li {
		margin-bottom: 14px;
		font-size: 12px;
		line-height: 18px; }

	section.become_boutique {
		padding: 41px 0; }
	section.become_boutique .flex_block .left_block_text p {
		margin-top: 12px;
		font-size: 14px;
		line-height: 22px;
		margin-bottom: 22px; }
	section.become_boutique .flex_block .left_block_text p:last-child {
		margin-bottom: 0; }
	section.become_boutique .flex_block .right_form_block {
		margin-top: 30px; }
	section.become_boutique .flex_block .right_form_block .form_block {
		padding: 33px 16px; }
	section.become_boutique .flex_block .right_form_block .thanks_block {
		padding: 56px 30px 71px; }
	section.become_boutique .flex_block .right_form_block .thanks_block p {
		line-height: 22px; }
	section.become_boutique .flex_block .right_form_block .thanks_block p br {
		display: none; }

	section.useful_tip {
		padding: 48px 0 24px; }
	section.useful_tip .flex_block_tip {
		display: block;
		margin-top: 30px; }
	section.useful_tip .flex_block_tip .block {
		width: 100%;
		margin-bottom: 27px; }
	section.useful_tip .flex_block_tip .block .name_block {
		font-size: 14px; }
	section.useful_tip .flex_block_tip .block p {
		font-size: 12px;
		line-height: 18px; }

	section.shipping_returns {
		padding: 40px 0; }
	section.shipping_returns .flex_block_shipping_returns {
		margin-top: 23px; }
	section.shipping_returns .flex_block_shipping_returns .block {
		max-width: 100%; }
	section.shipping_returns .flex_block_shipping_returns .block:first-child {
		max-width: 100%;
		margin-bottom: 41px; }
	section.shipping_returns .flex_block_shipping_returns .block .name_block {
		font-size: 14px; }
	section.shipping_returns .flex_block_shipping_returns .block p {
		font-size: 12px;
		line-height: 18px; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav {
		border: 1px solid rgba(51, 51, 51, 0.1);
		-webkit-border-radius: 2px;
		-ms-border-radius: 2px;
		-moz-border-radius: 2px;
		-o-border-radius: 2px;
		border-radius: 2px;
		margin-top: 22px;
		padding: 20px; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul {
		display: block;
		text-align: center;
		margin: 0; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li {
		padding: 0 0 49px;
		max-width: 100%; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li:last-child {
		padding: 0; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li br {
		display: none; }
	section.shipping_returns .flex_block_shipping_returns .block .block_bottom_nav ul li:before {
		content: "";
		position: absolute;
		bottom: 13px;
		left: 50%;
		top: auto;
		-webkit-transform: translate(-50%, 0) rotate(90deg);
		-ms-transform: translate(-50%, 0) rotate(90deg);
		-moz-transform: translate(-50%, 0) rotate(90deg);
		-o-transform: translate(-50%, 0) rotate(90deg);
		transform: translate(-50%, 0) rotate(90deg); }

	section.offer_your_price {
		padding: 40px 0; }
	section.offer_your_price .flex_block_price {
		margin-top: 19px; }
	section.offer_your_price .flex_block_price .block .name_block {
		font-size: 14px; }
	section.offer_your_price .flex_block_price .block p {
		font-size: 12px;
		line-height: 18px;
		margin-top: 5px; }
	section.offer_your_price .go_to_help {
		display: block;
		padding: 19px 12px 27px; }
	section.offer_your_price .go_to_help .icon_left {
		display: block; }
	section.offer_your_price .go_to_help .text_center {
		font-size: 14px;
		line-height: 18px;
		margin: 20px 0; }
	section.offer_your_price .go_to_help .button_back a {
		max-width: 100%;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		font-size: 14px;
		padding: 17px 0; }

	section.useful_tip.useful_tip_buyer {
		padding: 43px 0 44px; }
	section.useful_tip.useful_tip_buyer .flex_block_tip .block p {
		margin-top: 4px; }

	section.about .about_page_block {
		margin-top: 49px;
		padding: 33px 0px 40px; }
	section.about .about_page_block .about_flex_block {
		padding: 0; }
	section.about .about_page_block .about_flex_block .left_text_info .page_title_block {
		letter-spacing: 0px; }
	section.about .about_page_block .about_flex_block .left_text_info p {
		margin-bottom: 20px;
		font-size: 12px;
		line-height: 17px; }
	section.about .about_page_block .about_flex_block .left_text_info .button_style {
		max-width: 100%;
		margin-top: 30px; }

	section.examination_authenticity {
		padding: 51px 0 23px; }
	section.examination_authenticity .flex_block_examination_authenticity {
		display: block;
		padding: 0; }
	section.examination_authenticity .flex_block_examination_authenticity .left_img {
		width: 167px;
		margin: 0 auto; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info {
		width: 100%;
		padding-left: 0px;
		margin-top: 50px; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info .page_title_block {
		line-height: 26px; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info p {
		margin-bottom: 20px;
		font-size: 12px;
		line-height: 17px; }
	section.examination_authenticity .flex_block_examination_authenticity .right_info p br {
		display: none; }

	section.about_media .read_more {
		margin: 20px auto 10px; }

	#popup_data_confirmed .popup_block,
	#popup_unfortunately_canceled .popup_block,
	#popup_thank_confirmed_error .popup_block,
	#popup_thank_confirmed .popup_block {
		padding: 40px 10px 0;
		overflow: inherit; }
	#popup_data_confirmed .popup_block .popup_center,
	#popup_unfortunately_canceled .popup_block .popup_center,
	#popup_thank_confirmed_error .popup_block .popup_center,
	#popup_thank_confirmed .popup_block .popup_center {
		padding: 25vh 0 110px 0;
		height: 100vh;
		box-sizing: border-box;
		overflow: auto; }
	#popup_data_confirmed .popup_block .close,
	#popup_unfortunately_canceled .popup_block .close,
	#popup_thank_confirmed_error .popup_block .close,
	#popup_thank_confirmed .popup_block .close {
		right: 30px; }
	#popup_data_confirmed .popup_block .icon_popup,
	#popup_unfortunately_canceled .popup_block .icon_popup,
	#popup_thank_confirmed_error .popup_block .icon_popup,
	#popup_thank_confirmed .popup_block .icon_popup {
		margin: 0 auto; }
	#popup_data_confirmed .popup_block .title_popup,
	#popup_unfortunately_canceled .popup_block .title_popup,
	#popup_thank_confirmed_error .popup_block .title_popup,
	#popup_thank_confirmed .popup_block .title_popup {
		font-size: 16px;
		line-height: 20px; }
	#popup_data_confirmed .popup_block p,
	#popup_unfortunately_canceled .popup_block p,
	#popup_thank_confirmed_error .popup_block p,
	#popup_thank_confirmed .popup_block p {
		font-size: 12px;
		line-height: 16px; }
	#popup_data_confirmed .popup_block .popup_button,
	#popup_unfortunately_canceled .popup_block .popup_button,
	#popup_thank_confirmed_error .popup_block .popup_button,
	#popup_thank_confirmed .popup_block .popup_button {
		margin-top: 0;
		position: fixed;
		left: 10px;
		bottom: 0;
		width: calc(100% - 20px);
		background: #fff;
		padding: 0 0 30px 0; }
	#popup_data_confirmed .popup_block .popup_button a,
	#popup_unfortunately_canceled .popup_block .popup_button a,
	#popup_thank_confirmed_error .popup_block .popup_button a,
	#popup_thank_confirmed .popup_block .popup_button a {
		width: 100%; }

	.order_confirmation_get_paid .popup_block {
		padding: 12px 10px; }
	.order_confirmation_get_paid .popup_block .title_popup {
		font-size: 16px; }
	.order_confirmation_get_paid .popup_block .title_popup em {
		display: none; }
	.order_confirmation_get_paid .popup_block .overflow_block {
		height: calc(100vh - 220px); }
	.order_confirmation_get_paid .popup_block .popup_number_order {
		font-size: 12px;
		margin-top: 6px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs {
		margin-top: 24px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order {
		margin-top: 32px; }
	.order_confirmation_get_paid .popup_block .name_block_pop span {
		display: none; }
	.order_confirmation_get_paid .popup_block .text_popup {
		padding: 10px 12px;
		font-size: 12px;
		line-height: 16px; }
	.order_confirmation_get_paid .popup_block .text_popup .name_popup_text {
		font-size: 12px; }
	.order_confirmation_get_paid .popup_block .text_popup p {
		margin-top: 10px;
		line-height: 18px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info {
		padding: 16px 0px 16px 0px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info:before {
		top: 15px;
		left: 9px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info .name_block {
		font-size: 16px;
		padding: 0 30px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .name_block_info p {
		font-size: 12px;
		line-height: 16px;
		text-align: left; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label {
		padding: 19px 20px 17px 0; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:before {
		top: 17px;
		right: 0px;
		left: auto; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:after {
		left: auto;
		right: 5px;
		top: 22px; }
	.order_confirmation_get_paid .popup_block .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon {
		margin-top: 16px; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block {
		align-items: inherit; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_img {
		width: 58px;
		height: 60px; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info {
		padding-left: 10px; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .prices_block {
		margin-top: 5px; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .prices_block .prices {
		font-size: 14px; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_info_flex {
		display: block; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_info_flex .product_info_block_left {
		display: none; }
	.order_confirmation_get_paid .popup_block .blocks_get_paid .block .product_info .product_info_flex .product_info_block_right {
		text-align: left;
		width: 100%; } }
@media only screen and (max-width: 600px) {
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order {
		display: -webkit-box;
		/* OLD - iOS 6-, Safari 3.1-6 */
		display: -moz-box;
		/* OLD - Firefox 19- (buggy but mostly works) */
		display: -ms-flexbox;
		/* TWEENER - IE 10 */
		display: -webkit-flex;
		/* NEW - Chrome */
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block {
		display: block;
		width: calc(50% - 5px);
		padding: 23px 6px 5px;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .sale {
		left: 7px;
		top: 9px;
		width: 16px;
		height: 15px;
		background-size: 100%; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_img {
		width: 100%; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info {
		width: 100%;
		padding: 0;
		margin-top: 5px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .product_name a {
		font-size: 12px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .product_size,
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .product_type {
		margin-top: 6px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .prices_block {
		margin-top: 23px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .prices_block .prices {
		font-size: 12px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_info .your_profit {
		margin-top: 7px;
		font-size: 11px; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject {
		width: 100%;
		margin-top: 10px;
		text-align: center; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false > div {
		padding: 10px 0 10px 20px;
		display: inline-block; }
	.order_confirmation_get_paid .popup_block .popup_step_tabs .popup_tab .blocks_order .block .product_right_confirm_reject .confirm_reject_clicked .confirm_reject_true_false > div:before {
		left: 0 !important; } }
@media only screen and (max-width: 400px) {
	section.seller_buyer_page .seller_top_title_block {
		background: url("/images/icons/seller_page_bg_2.jpg") no-repeat center center;
		background-size: cover; } }
.lightboxOverlay {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 9999;
	background-color: black;
	opacity: 0.8; }
