
let activeBansPageNumber = 0;
let noActiveBansPageNumber = 0;

let pageSize = 40;

function renderAllBans(){
    activeBansPageNumber = 0;
    noActiveBansPageNumber = 0;
    renderActiveBans();
    renderNoActiveBans();
}


function renderActiveBans(){
    let href = window.location.href;
    let userId = href.substring(href.lastIndexOf('/') + 1).split("?")[0];
    $.ajax({
        type: "POST",
        url: "/adminpanel/bans/all?pageNumber=" + activeBansPageNumber + "&pageSize=" + pageSize,
        dataType: 'json',
        contentType: 'application/json',
        processData: false,
        data: JSON.stringify({
            banIds: [],
            userIds: [userId],
            banTypes:  [],
            isBaned: true,
            isDeleted: false
        })
    }).done(function(data){
        let $banList = $("#ban-list");
        $banList.children(".ban").remove();
        //$(".ban", $banList).remove();
        data.map(item => $banList.find(".no-bans").after(getRenderBan(item)));

        if(data.length > 0){
            $banList.find(".no-bans").hide();
            $(".is-baned-show").show();
        }
        else{
            $banList.find(".no-bans").show();
        }
        console.log(data);
    }).fail(function(data){
        console.log("renderActiveBans error", data);
    });
}

function renderNoActiveBans(){
    let href = window.location.href;
    let userId = href.substring(href.lastIndexOf('/') + 1).split("?")[0];
    $.ajax({
        type: "POST",
        url: "/adminpanel/bans/all?pageNumber=" + noActiveBansPageNumber + "&pageSize=" + pageSize,
        dataType: 'json',
        contentType: 'application/json',
        processData: false,
        data: JSON.stringify({
            banIds: [],
            userIds: [userId],
            banTypes:  [],
            isBaned: false,
            isDeleted: null
        })
    }).done(function(data){
        let $prevBanList = $("#ban-list .infringer__block");
        data.map(item => $prevBanList.append(getRenderBan(item)));

        if(data.length > 0){
            if(data.filter(x => !x.isDeleted).length > 0){
                $(".is-baned-show").show();
            }
            $prevBanList.find(".no-bans").hide();
        }
        else{
            $prevBanList.find(".no-bans").show();
        }
        console.log(data);
    }).fail(function(data){
        console.log("renderNoActiveBans error", data);
    });
}

function renderActiveBansForPopup(){
    let href = window.location.href;
    let userId = href.substring(href.lastIndexOf('/') + 1);
    $.ajax({
        type: "POST",
        url: "/adminpanel/bans/all?pageNumber=0&pageSize=20",
        dataType: 'json',
        contentType: 'application/json',
        processData: false,
        data: JSON.stringify({
            banIds: [],
            userId: [userId],
            banTypes:  [],
            isBaned: true,
            isDeleted: null
        })
    }).done(function(data){
        let $banList = $("#ban-list");
        $banList.find(".ban").remove();
        data.map(item => $banList.find(".no-bans").after(getRenderBan(item)));

        if(data.length > 0){
            $banList.find(".no-bans").hide();
        }
        else{
            $banList.find(".no-bans").show();
        }
        console.log(data);
    }).fail(function(data){
        console.log("renderActiveBans error", data);
    });
}

function renderNoActiveBansForPopup(){
    let href = window.location.href;
    let userId = href.substring(href.lastIndexOf('/') + 1);
    $.ajax({
        type: "POST",
        url: "/adminpanel/bans/all?pageNumber=0&pageSize=20",
        dataType: 'json',
        contentType: 'application/json',
        processData: false,
        data: JSON.stringify({
            banIds: [],
            userId: [userId],
            banTypes:  [],
            isBaned: false,
            isDeleted: null
        })
    }).done(function(data){
        let $prevBanList = $("#ban-list .infringer__block");
        data.map(item => $prevBanList.append(getRenderBan(item)));

        if(data.length > 0){
            $prevBanList.find(".no-bans").hide();
        }
        else{
            $prevBanList.find(".no-bans").show();
        }
        console.log(data);
    }).fail(function(data){
        console.log("renderNoActiveBans error", data);
    });
}


// $("html").on("click", "#ban-list .ban .button--unblock", function(){
//     let id = $(this).parents(".ban").attr("data-ban-id");
//     $.ajax({
//         type: "PATCH",
//         url: "/adminpanel/bans/" + id,
//     }).done(function(data){
//         console.log("delete ban: " + id);
//     });
// });
//
// $("html").on("click", "#ban-list .ban .button--delete", function(){
//     let id = $(this).parents(".ban").attr("data-ban-id");
//     $.ajax({
//         type: "POST",
//         url: "/adminpanel/bans/" + id,
//     }).done(function(data){
//         console.log("delete ban: " + id);
//     });
// });

function getRenderBan(data){
    return `
                    <div class="form__wrap form__wrap--blocks form__wrap--flex ban" data-ban-id="${data.id}" style="display:block;">
                        <div class="form__wrap-block">
                            <div class="form__text form__text--dark form__text--line-height-20">
                                ${data.title} ${data.isDeleted ? `<div>(Удален)</div>` : ``}
                            </div>
                            <div class="form__text form__text--line-height-20">
                                ${renderBanDate(data.startDate, data.endDate)}
                            </div>
                            <br>
                            <div class="form__text form__text--line-height-20">
                                ${data.description}
                            </div>
                        </div>
                        <div class="form__wrap-block form__wrap-block--align-right" style="${data.isBaned ? '' : 'display:none;'}">
                            <div class="button button--stripped button--unblock"><span class="button__text button__text--blue button__text--font-size-12 button__text--font-weight-600" style="cursor: pointer;">Отменить</span></div>
                            <div class="button button--stripped button--delete"><span class="button__text button__text--blue button__text--font-size-12 button__text--font-weight-600" style="cursor: pointer;">Удалить</span></div>
                        </div>
                    </div>
    `;
}

function renderBanDate(startDateSeconds, endDateSeconds){
    let startDate = new Date(startDateSeconds);
    let endDate = new Date(endDateSeconds);
    if(!endDateSeconds){
        return startDate.toLocaleDateString("ru", {year: 'numeric', month: 'long', day: '2-digit'});
    }
    return startDate.toLocaleDateString("ru", {year: 'numeric', month: 'long', day: '2-digit'}) + " - "
        + endDate.toLocaleDateString("ru", {year: 'numeric', month: 'long', day: '2-digit'});
}


// data: JSON.stringify({
//         "banIds": [],
//         "userIds": [userId],
//         "banTypes": [
//             "USER_BAN",
//             "PUBLISH_BAN",
//             "WARNING",
//             "COMMENT_BAN",
//             "STORIES_BAN",
//         ],
//         "isBaned": true,
//         "isDeleted": false
//     }
// ),


renderAllBans();


$("body").on("click", "#send-ban button[type='submit']", function (e) {
    e.preventDefault();
    let $this = $(this);
    let banType = $this.parents(".form__wrap--action").attr("data-ban-type");
    if(banType === "RESET_PASSWORD") return;
    let href = window.location.href;
    let indexOfQuestionMark = href.lastIndexOf('?');
    let endOfUserId = indexOfQuestionMark == -1 ? href.length : href.lastIndexOf('?');
    let userId = href.substring(href.lastIndexOf('/') + 1, endOfUserId);
    let startDate = banType === "WARNING" ? Math.ceil(Date.now() / 1000) : $this.parents(".form__wrap--action").attr("startDate");
    let endDate = banType === "WARNING" ? null : $this.parents(".form__wrap--action").attr("endDate");
    let description = $this.parents(".form__wrap--action").find("textarea").val();
    if (!description) description = "";

    let d = new Date();
    endDate = parseInt(endDate) + ((d.getHours() * 3600 ) + (d.getMinutes() * 60) + d.getSeconds());

    userBan({
            "userId":userId,
            "banType":banType,
            "startDate": startDate,
            "endDate": endDate,
            "description":description
        }
    );
    $('.reportrange span').text("");
    $('#send-ban .form--modal').hide();
});


function userBan(userBan) {
    $.ajax({
        type: "POST",
        url: "/adminpanel/bans",
        data: JSON.stringify(userBan),
        dataType: "json",
        contentType: "application/json; charset=utf-8",
    }).done(function(){
        renderAllBans();
    }).fail(function(){
        renderAllBans();
    });
}


$("html").on("click", ".button--unblock", function(){
   let $this = $(this);
   let banId = $this.parents(".ban").attr("data-ban-id");
   console.log("banId",banId);
    $.ajax({
        type: "PATCH",
        url: "/adminpanel/bans/" + banId,
        dataType: "json",
        contentType:'application/json'
    }).done(function(){
        renderAllBans();
    }).fail(function(){
        renderAllBans();
    });
});

$("html").on("click", ".button--delete", function(){
    let $this = $(this);
    let banId = $this.parents(".ban").attr("data-ban-id");
    console.log("banId",banId);
    $.ajax({
        type: "POST",
        url: "/adminpanel/bans/" + banId,
        dataType: "json",
        contentType:'application/json'
    }).done(function(){
        renderAllBans();
    }).fail(function(){
        renderAllBans();
    });
});


$("html").on("click", ".form__wrap--action[data-ban-type='RESET_PASSWORD'] button[type='submit']", function(){
    let $this = $(this);
    let email = $this.parents(".form__wrap").attr("data-email");
    console.log(email);
    $.ajax({
        type: "POST",
        url: "/api/v2/account/reset?email=" + email,
        dataType: "json",
        contentType:'application/json'
    });
});






$(function(){
 $.ajax({
     type: 'GET',
     url: '/adminpanel/bans/data',
     dataType: "json",
     contentType:'application/json'
 }).done(function (data){
     console.log(data);
     let index = 0
     for (const [key, value] of Object.entries(data)) {
         let $typeRoot;
         if(index % 2 === 0){
             $typeRoot = $('#send-ban .form__wrap-block--margin-left-17').first();
             $typeRoot.append(renderBanType(key, value));
         }
         else{
             $typeRoot = $('#send-ban .form__wrap-block--margin-left-17').last();
             $typeRoot.prepend(renderBanType(key, value));
         }
         index++;
     }

     $(function() {
         var start = moment();
         var end = moment();

         function cb(start, end) {
             $('.reportrange span').html(end.format('D MMMM YYYY'));
         }

         $('.reportrange').daterangepicker({
             singleDatePicker: true,
             minDate: new Date(),
             startDate: start
         }, cb);


         $('.reportrange').on('apply.daterangepicker', function(ev, picker) {
             let $this = $(this);
             // $this.parents('.form__wrap--action').attr('startDate', Math.ceil(picker.startDate._d.getTime()) / 1000);
             $this.parents('.form__wrap--action').attr('endDate', Math.ceil(picker.startDate._d.getTime()) / 1000);
         });
     });
 });
});

function renderBanType(type, text){
    return `
<div class="form__wrap form__wrap--action" data-ban-type="${type}">
   <button class="button button--no-back button--action" id="block-publication" type="button"><span class="button__text">${text}</span></button>
   <div action="#" class="form--modal" method="POST">
      <div class="form__control">
         <label class="form__label form__label--white-space-wrap">
            <span class="form__label-text">Срок</span>
            <div class="form__select-wrap">
               <div class="reportrange" style="background: #fff; cursor: pointer; padding: 5px 10px; border: 1px solid #ccc; width: 100%">
                  <i class="fa fa-calendar"></i>&nbsp;
                  <span></span> <i class="fa fa-caret-down"></i>
               </div>
            </div>
         </label>
      </div>
      <div class="form__control">
         <label class="form__label form__label--white-space-wrap"><span class="form__label-text">Комментарий</span><textarea class="form__textarea form__textarea--modal" name="comment"></textarea></label>
      </div>
      <div class="form__control">
         <button class="button button--regular button--modal" type="submit"><span class="button__text">Сохранить</span></button>
      </div>
   </div>
</div>
    `;
}

function renderWarningType(type, text){
    return `
<div class="form__wrap form__wrap--action" data-ban-type="${type}">
   <button class="button button--no-back button--action" type="button"><span class="button__text">${text}</span></button>
   <div action="#" class="form--modal" method="POST">
      <div class="form__control">
         <label class="form__label form__label--white-space-wrap"><span class="form__label-text">Комментарий</span><textarea class="form__textarea form__textarea--modal" name="comment"></textarea></label>
      </div>
      <div class="form__control">
         <button class="button button--regular button--modal" type="submit"><span class="button__text">Сохранить</span></button>
      </div>
   </div>
</div>
    `;
}

$('body').on('click', '#send-ban button.button--action', function (e) {
    e.preventDefault();
    let $this = $(this);
    let $modal = $this.parent().find('.form--modal')
    let isHidden = $modal.is(":hidden");
    $('#send-ban .form--modal').hide();
    if(isHidden){
        $this.parent().find('.form--modal').toggle();
    }
})
