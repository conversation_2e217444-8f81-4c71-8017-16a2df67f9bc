$(document).ready(function (){
	$ ( ".open__filter" ).on ( "click" , function () {
		var $this = $(this);
		$this.parent().find(".filter__table").slideToggle(0);
	} );
	
	$ ( ".tabs__content--sort .selected__block-sort" ).on ( "click" , function () {
		var $this = $(this);
		$this.parent().find(".select__block--sort").stop().slideToggle(300);
	} );
	
	$ ( ".tabs__content--sort .select__block--sort ul li label" ).on ( "click" , function () {
		var $this = $(this);
		$this.parents(".tabs__content--sort").find(".selected__block-sort").text($this.text());
		$this.parents(".tabs__content--sort").find(".select__block--sort").slideUp(300);
	} );
	
	$('.alert_img .alert_text_red .alert__text_ic').mouseenter(function() {
		var $this = $(this);
		$this.parent(".alert__text_show").find(".hover__img_show").show();
		$this.parent(".alert__text_show").find(".hover__img_show img").attr('src',$this.parents(".required__photos_wrapper").find(">img").attr('src'));
	});
	$('.alert_img .alert_text_red .alert__text_ic').mouseleave(function() {
		var $this = $(this);
		$this.parent(".alert__text_show").find(".hover__img_show").hide();
	});
	
	$(".tab_checkbox--hide-show input[type=checkbox]").change(function(){
		var $this = $(this);
		
		if ($this.is(":checked") ){
			$this.parents(".tab_checkbox--hide-show").find(".hide__block").show();
		}else{
			$this.parents(".tab_checkbox--hide-show").find(".hide__block").hide();
		}
	});
	
	$ ( ".popup__content--review .edit__img" ).on ( "click" , function (e) {
		e.preventDefault();
		var $this = $(this);
		$this.parents(".images__review").toggleClass("active");
	} );
	
	$ ( ".popup__content--review .close_btn" ).on ( "click" , function (e) {
		e.preventDefault();
		var $this = $(this);
		$this.parents(".featherlight").find(".featherlight-close").click();
	} );
	
	$ ( ".edit__new_review" ).on ( "click" , function (e) {
		e.preventDefault();
		var $this = $(this);
		$this.parent(".review__edit").toggleClass("active");
		$this.siblings(".textarea__edit--review").focus();
	} );
	
	$('.add__new--review .textarea__new--review').keyup(function(){
		var keyed = $(this).val();
		var $this = $(this);
		if (keyed.length > 1 ){
			$this.parents().siblings(".review__buttons").find(".button_review").removeClass("button__disabled");
		} else{
			$this.parents().siblings(".review__buttons").find(".button_review").addClass("button__disabled");
		}
	});
	
});
