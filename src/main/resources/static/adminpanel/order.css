.form.form--edit-deal {
    padding: 0px 15px
}

.form__title-text {
    margin-bottom: 30px;
}

.address_info {
    background: #f8fcfe;
    border: 1px solid #e1eef4;
    padding: 20px;
    transition: .4s;
    cursor: pointer;
    position: relative;
    margin-left: 20px
}

.address_info .button {
    padding: 0px;
}

.address_info:hover {
    background: #e7f6fd
}

.form__label:hover .button.button--comments,
.address_info.content__row-block:hover .button.button--comments {
    display: inline-block
}

.payment_info {
    border: 1px solid #e1eef4;
    padding: 20px;
    transition: .4s;
    position: relative;
    margin-top: 14px;
}

.payment_info .button.button--edit-deal-info {
    position: absolute;
    right: 10px;
    top: 10px
}

.payment_info .content__row-block {
    margin-right: 30px;
}

.user_title,
.info_title {
    font-family: 'Roboto', sans-serif;
    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 14px;
    display: flex;
    align-items: center;

    color: #999999;

    mix-blend-mode: normal;
}

.info_title,
.info_value {
    width: max-content;
}

.info_value,
.user_name {
    font-family: 'Roboto', sans-serif;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    /* identical to box height, or 167% */

    display: flex;
    align-items: center;

    color: #333333;
    margin-top: 10px;
}

.user_address {
    font-family: 'Roboto', sans-serif;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    /* identical to box height, or 167% */

    display: flex;
    align-items: center;

    color: #999999;
}

.user_alert {
    font-family: 'Roboto', sans-serif;
    font-style: normal;
    font-weight: normal;
    font-size: 12px;
    line-height: 20px;
    color: #FF0000;
    /*height: 20px;*/
}

.user_alert .alert {
    top: -2px;
    background: #FF0000;
    border: 1px solid #FF0000;
    box-sizing: border-box;
}

.user_alert .alert__text {
    /*font-family: Roboto;*/
    /*font-style: normal;*/
    font-weight: bold;
    font-size: 11px;
    line-height: 10px;
    display: flex;
    align-items: center;
    color: #FFFFFF;
}

.user_alert.alert--margin-left-5 {
    margin-left: 5px;
}

.disabled {
    pointer-events: none;
    cursor: not-allowed;
    border: 1px solid #999;
    color: #333333;
    opacity: 0.5;
}

.form__input.fias_error {
    border: 1px solid #FF0000;
}

.warning {
    color: #0000cc;
}

.form__title #add_address.form__title-text,
.form__title #edit_address.form__title-text,
.form__title #edit_address.form__title-text .user_alert,
.form__title #add_pyment,
.form__title #edit_pyment {
    display: none;
}

.form__title #add_address.form__title-text.active,
.form__title #edit_address.form__title-text.active,
.form__title #edit_address.form__title-text .user_alert.active,
.form__title #add_pyment.active,
.form__title #edit_pyment.active  {
    display: block;
}

#edit_counterparty_form .validate_results.country_results,
#edit_counterparty_form .validate_results.billing_address_country_results,
#edit_counterparty_form .validate_results.billing_address_city_results,
#edit_address_form2 .validate_results.city_results,
#edit_address_form2 .validate_results.country_results,
#edit_address_form2 .validate_results.address_results {
    display: none;
    position: absolute;
    width: 100%;
    /*height:200px;*/
    background: #ffffff;
    /*padding: 17px 0 7px;*/
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.18);
    border-radius: 2px;
    z-index: 1;

    transition: all 0.3s ease;
    /*padding: 8px 12px 9px;*/
    font-size: 13px;

    overflow-y: auto;
    max-height: 200px;
}

#edit_counterparty_form .validate_results.country_results,
#edit_counterparty_form .validate_results.billing_address_country_results,
#edit_counterparty_form .validate_results.billing_address_city_results,
#edit_address_form2 .validate_results.city_results.active,
#edit_address_form2 .validate_results.country_results.active,
#edit_address_form2 .validate_results.address_results.active {
    display: block;
}

#edit_counterparty_form .validate_results.country_results,
#edit_counterparty_form .validate_results.billing_address_country_results,
#edit_counterparty_form .validate_results.billing_address_city_results,
#edit_address_form2 .validate_results.city_results ul,
#edit_address_form2 .validate_results.country_results ul,
#edit_address_form2 .validate_results.address_results ul {
    margin-top: 7px;
    padding-inline-start: 0px;
}

#edit_counterparty_form .validate_results.country_results,
#edit_counterparty_form .validate_results.billing_address_country_results,
#edit_counterparty_form .validate_results.billing_address_city_results,
#edit_address_form2 .validate_results.city_results ul li,
#edit_address_form2 .validate_results.country_results ul li,
#edit_address_form2 .validate_results.address_results ul li {
    -webkit-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    padding: 4px 12px 4px;
    display: block;
    font-size: 13px;
}

#edit_counterparty_form .validate_results.country_results,
#edit_counterparty_form .validate_results.billing_address_country_results,
#edit_counterparty_form .validate_results.billing_address_city_results,
#edit_address_form2 .validate_results.city_results ul li span,
#edit_address_form2 .validate_results.country_results ul li span,
#edit_address_form2 .validate_results.address_results ul li span {
    color: #BDBDBD;
    margin-left: 3px;
}

#edit_counterparty_form .validate_results.country_results,
#edit_counterparty_form .validate_results.billing_address_country_results,
#edit_counterparty_form .validate_results.billing_address_city_results,
#edit_address_form2 .validate_results.city_results ul li:hover,
#edit_address_form2 .validate_results.country_results ul li:hover,
#edit_address_form2 .validate_results.address_results ul li:hover {
    background: #F2F2F2;
}

.parent-text-with-dots {
    overflow: hidden;
}

.text-with-dots {
    width: 90%;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.product_row, .order_row {
    cursor: pointer;
}

.table__cell .img_block {
    width: 44px;
    height: 44px;
    position: relative;
    overflow: hidden;
    -webkit-border-radius: 2px;
    -ms-border-radius: 2px;
    -moz-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
}

.table__cell .img_block .img_hover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    opacity: 0;
    -webkit-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.table__cell .img_block .img_hover span {
    text-align: center;
    /*position: absolute;*/
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    color: #ffffff;
    font-size: 14px;
    font-family: "Open Sans Bold";
}

.table__cell .img_block .img_hover img {
    width: 100%;
    height: 100%;
    -webkit-object-fit: cover;
    -ms-object-fit: cover;
    -moz-object-fit: cover;
    -o-object-fit: cover;
    object-fit: cover;
}

.table__cell .img_block:hover .img_hover {
    opacity: 1;
}

.payment_info_card_number{
    width: 110px;
}
