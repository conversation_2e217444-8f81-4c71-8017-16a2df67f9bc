.table__cell.table__cell-flex-0 {
    flex: 0;
    padding: 0 13px;
    position: relative; }

.check_table {
    position: absolute;
    left: 0;
    top: 0; }
.check_table label {
    position: relative;
    cursor: pointer; }
.check_table label:before {
    content: "";
    position: absolute;
    width: 14px;
    height: 14px;
    border: 1px solid #E1EEF4;
    top: -7px;
    left: 0px;
    -webkit-border-radius: 1px;
    -moz-border-radius: 1px;
    border-radius: 1px; }
.check_table label:after {
    display: none;
    content: "";
    position: absolute;
    width: 14px;
    height: 14px;
    top: -7px;
    left: 0px;
    background: #165ADF url("/images/icons/check_icon.png") no-repeat center center;
    -webkit-border-radius: 1px;
    -moz-border-radius: 1px;
    border-radius: 1px; }
.check_table input[type='checkbox'] {
    display: none; }
.check_table input[type='checkbox']:checked + label:after {
    display: block; }

.tabs__content-row-block > form {
    display: inline-block;
    vertical-align: middle; }

.show_hide-info {
    display: none;
    vertical-align: middle; }
.show_hide-info.active {
    display: inline-block; }
.show_hide-info .checked_el_number {
    display: inline-block;
    vertical-align: middle; }
.show_hide-info .checked_el_number span {
    font-size: 13px; }
.show_hide-info .open_select {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    margin: 0 20px; }
.show_hide-info .open_select .open_select-neme {
    min-width: 198px;
    height: 30px;
    padding: 7px 30px 7px 17px;
    color: #ffffff;
    background: #165ADF;
    font-size: 13px;
    line-height: 18px;
    cursor: pointer; }
.show_hide-info .open_select .open_select-neme.disabled {
    pointer-events: none;
    opacity: 0.1; }
.show_hide-info .open_select .open_select-neme:before {
    content: "";
    position: absolute;
    top: 0;
    right: 11px;
    background: url("/images/icons/arrow_icon.png") no-repeat center center;
    width: 13px;
    height: 100%;
    z-index: 1; }
.show_hide-info .open_select .open_select-list {
    display: none;
    position: absolute;
    top: 36px;
    left: 0;
    width: 100%;
    background: #FFFFFF;
    box-shadow: 1px 1px 6px rgba(105, 119, 133, 0.29);
    z-index: 1;
    padding: 18px 0; }
.show_hide-info .open_select .open_select-list ul {
    padding: 0;
    margin: 0; }
.show_hide-info .open_select .open_select-list ul li {
    list-style: none;
    padding: 6px 15px;
    cursor: pointer; }
.show_hide-info .open_select .open_select-list ul li.height_none {
    padding: 0;
    height: 0; }
.show_hide-info .open_select .open_select-list ul li.disabled {
    pointer-events: none;
    opacity: .3; }
.show_hide-info .open_select .open_select-list ul li:hover {
    background: #f4f8fb; }

.popup_required {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none; }
.popup_required .popup_block {
    position: absolute;
    top: 50%;
    left: 50%;
    overflow: auto;
    transform: translate(-50%, -50%);
    background: #ffffff;
    width: 387px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 43px 44px 28px;
    max-height: 100vh; }
.popup_required .popup_block .title_popup {
    font-size: 20px;
    line-height: 20px;
    color: #000; }
.popup_required .popup_block .form_popup {
    margin-top: 30px; }
.popup_required .popup_block .form_popup span {
    font-size: 12px;
    line-height: 14px;
    color: #999999; }
.popup_required .popup_block .form_popup .inp_block {
    margin-top: 3px; }
.popup_required .popup_block .form_popup .inp_block input {
    border: 1px solid #E1EEF4;
    box-sizing: border-box;
    border-radius: 2px;
    width: 100%;
    padding: 0 10px;
    height: 40px;
    font-size: 13px;
    outline: none; }
.popup_required .popup_block .form_popup .btn_popup {
    text-align: right;
    margin-top: 30px; }
.popup_required .popup_block .form_popup .btn_popup input {
    background: #165ADF;
    border-radius: 4px;
    display: inline-block;
    color: #ffffff;
    font-size: 13px;
    line-height: 15px;
    outline: none;
    width: 116px;
    height: 32px;
    text-align: center;
    border: none; }

.active_inline {
    display: inline-block;
    vertical-align: middle; }

.workload_open {
    display: none;
    vertical-align: middle;
    color: #165ADF;
    text-decoration: underline !important;
    margin-left: 25px;
    font-size: 14px;
    line-height: 16px; }
.workload_open.active {
    display: inline-block; }

.popup_workload_retouchers {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 99;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none; }
.popup_workload_retouchers .popup_block {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #ffffff;
    min-width: 740px;
    max-height: 100vh;
    max-width: 740px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    overflow: auto; }
.popup_workload_retouchers .popup_block .title_popup {
    padding: 27px 28px;
    font-size: 18px;
    line-height: 21px;
    color: #333333; }
.popup_workload_retouchers .popup_block .filter_popup_sm {
    padding: 0 28px 15px;
    border-bottom: 1px solid #f2f2f2;
    display: flex;
    align-items: center;
    justify-content: flex-start; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data {
    position: relative;
    margin-right: 37px; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .selected_data {
    position: relative;
    min-width: 80px;
    font-size: 13px;
    line-height: 15px;
    padding: 0 20px;
    cursor: pointer; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .selected_data:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    background: url("/images/icons/icon_calendar.png") no-repeat center center;
    width: 12px;
    height: 12px; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .selected_data:after {
    content: "";
    position: absolute;
    top: 4px;
    right: 0;
    background: url("/images/icons/arrow_b.png") no-repeat center center;
    width: 9px;
    height: 7px; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .open_list_data {
    position: absolute;
    left: 0;
    background: #FFFFFF;
    box-shadow: 1px 1px 6px rgba(105, 119, 133, 0.29);
    width: 184px;
    padding: 16px 17px;
    z-index: 1;
    top: 23px;
    display: none; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .open_list_data ul {
    margin: 0;
    padding: 0; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .open_list_data ul li {
    list-style: none; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .open_list_data ul li a {
    font-size: 13px;
    line-height: 20px;
    color: #333333;
    position: relative;
    padding: 8px 20px;
    cursor: pointer;
    display: block; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .open_list_data ul li a.active:before {
    display: block; }
.popup_workload_retouchers .popup_block .filter_popup_sm .select_data .open_list_data ul li a:before {
    content: "";
    position: absolute;
    top: 12px;
    left: 0;
    background: url("/images/icons/check_blue.png") no-repeat center center;
    width: 8px;
    height: 7px;
    display: none; }
.popup_workload_retouchers .popup_block .filter_popup_sm span {
    font-size: 14px;
    line-height: 16px;
    display: inline-block;
    vertical-align: middle; }
.popup_workload_retouchers .popup_block .filter_popup_sm .datapicher_inputs {
    margin-left: 32px; }
.popup_workload_retouchers .popup_block .filter_popup_sm .datapicher_inputs input {
    width: 117px;
    height: 30px;
    border: 1px solid #E1EEF4;
    box-sizing: border-box;
    border-radius: 2px;
    font-size: 14px;
    line-height: 16px;
    padding: 0 10px;
    display: inline-block;
    vertical-align: middle;
    margin-right: 24px; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result {
    padding: 22px 20px 0; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result.active .retouchers-result-show-all {
    display: block; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result.active .retouchers-result_flex {
    display: block; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result.active .retouchers-result_flex .popup_workload_retouchers-result-block {
    display: none; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result.active .retouchers-result_flex .popup_workload_retouchers-result-block.active {
    display: block;
    width: 100%;
    padding: 0 0;
    background: none; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result.active .retouchers-result_flex .popup_workload_retouchers-result-block.active .result-block-hide-block {
    display: block; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result-show-all {
    display: none;
    margin-bottom: 24px; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result-show-all a {
    color: #165ADF;
    font-size: 13px;
    line-height: 20px;
    position: relative;
    padding-left: 20px; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result-show-all a:before {
    content: "";
    position: absolute;
    top: 2px;
    left: 0;
    background: url("/images/icons/arrow_left.png") no-repeat center;
    width: 7px;
    height: 11px; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    max-height: 253px;
    overflow: auto; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block {
    padding: 10px 14px;
    background: #F9F9F9;
    width: calc(33.3333333% - 30px);
    margin-bottom: 39px;
    cursor: pointer; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-name {
    font-size: 15px;
    line-height: 20px;
    color: #000000;
    font-weight: 500; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-info {
    font-size: 13px;
    line-height: 20px;
    color: #000000;
    opacity: 0.6;
    margin-top: 5px; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-hide-block {
    display: none;
    margin-top: 31px;
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
    color: #165ADF;
    padding-right: 50px; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-hide-block.color_black {
    color: #000; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-hide-block.color_black span,
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-hide-block.color_black a {
    color: #000; }
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-hide-block span,
.popup_workload_retouchers .popup_block .popup_workload_retouchers-result .retouchers-result_flex .popup_workload_retouchers-result-block .result-block-hide-block a {
    color: #165ADF;
    display: inline-block; }

.tab__wrapper_title--defect {
    margin-top: 20px; }

.popup_item_open .required__photo_menu--items {
    cursor: pointer; }
.popup_item_open .required__photo_menu--items .photo-src {
    width: 100%;
    height: 100%;
    object-fit: cover; }
.popup_item_open .required__photo_menu--items .inline_popup {
    display: none; }
.popup_item_open .required__photo_menu--items span {
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.4px;
    color: #ffffff;
    position: absolute;
    left: 0;
    bottom: 0; }

/*.featherlight .featherlight-content{
	background: transparent;
	img{
		background: #ffffff;
	}
}*/
.popup_item {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background: rgba(0, 0, 0, 0.6);
    z-index: 5; }
.popup_item.active {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    align-items: center; }
.popup_item .inline_popup img {
    height: calc(80vh - 50px);
    width: auto;
    display: block;
    margin: 0 auto 10px; }
.popup_item .inline_popup .text-btn_bottom {
    width: 100%;
    text-align: center; }
.popup_item .inline_popup .text-btn_bottom .text_bot {
    color: #ffffff;
    margin: 0; }
.popup_item .inline_popup .text-btn_bottom .text_bot .text_inp_b {
    display: inline-block;
    vertical-align: middle;
    border: none;
    outline: none;
    background: transparent;
    width: calc(100% - 30px);
    text-align: center;
    font-size: 12px;
    line-height: 14px;
    color: #ffffff; }
.popup_item .inline_popup .text-btn_bottom .text_bot span {
    display: inline-block;
    vertical-align: middle;
    font-size: 12px;
    line-height: 14px; }
.popup_item .inline_popup .text-btn_bottom .text_bot .required__photo_menu--item {
    display: inline-block;
    vertical-align: middle; }
.popup_item .inline_popup .text-btn_bottom .text_bot .required__photo_menu--item.required__photo_menu--item-edit {
    position: relative; }
.popup_item .inline_popup .text-btn_bottom .text_bot .required__photo_menu--item.required__photo_menu--item-edit:before {
    content: "";
    line-height: 26px;
    background: url("/images/icons/edit_icon.png") no-repeat center center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0; }
.popup_item .inline_popup .text-btn_bottom .text_bot .required__photo_menu--item.required__photo_menu--item-ok:before {
    content: "OK";
    font-size: 12px;
    line-height: 26px; }
.popup_item .inline_popup .text-btn_bottom ul {
    margin-top: 28px; }
.popup_item .inline_popup .text-btn_bottom ul li {
    list-style: none;
    display: inline-block;
    margin: 0 25px;
    color: #ffffff;
    font-size: 13px;
    line-height: 15px;
    position: relative; }
.popup_item .inline_popup .text-btn_bottom ul li .input_file {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    font-size: 0;
    border: none;
    background: transparent;
    opacity: 0;
    cursor: pointer; }
.popup_item .inline_popup .text-btn_bottom ul li label {
    display: block; }
.popup_item .inline_popup .text-btn_bottom ul li .required__photo_menu--item {
    margin: 0 auto 6px; }

.photo_defect .inline {
    display: none; }
.photo_defect.second__photos .required__photos_wrapper {
    border: none !important; }
.photo_defect .required__photo_menu--item-show {
    color: #ffffff;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.4px;
    width: 100%;
    height: 100%; }
.photo_defect .required__photo_menu--item-show span {
    position: absolute;
    bottom: 0;
    left: 0; }
.photo_defect .required__photo_menu--item-show:before {
    display: none; }
.input_disabled {
    pointer-events:none; }
.input_locked {
    pointer-events:none; }

/*# sourceMappingURL=scss.css.map */
