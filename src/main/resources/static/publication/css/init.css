@-webkit-keyframes swing {
	20% {
		-webkit-transform: rotate3d(0, 0, 1, 15deg);
		transform: rotate3d(0, 0, 1, 15deg); }
	40% {
		-webkit-transform: rotate3d(0, 0, 1, -10deg);
		transform: rotate3d(0, 0, 1, -10deg); }
	60% {
		-webkit-transform: rotate3d(0, 0, 1, 5deg);
		transform: rotate3d(0, 0, 1, 5deg); }
	80% {
		-webkit-transform: rotate3d(0, 0, 1, -5deg);
		transform: rotate3d(0, 0, 1, -5deg); }
	100% {
		-webkit-transform: rotate3d(0, 0, 1, 0deg);
		transform: rotate3d(0, 0, 1, 0deg); } }
@-moz-keyframes swing {
	20% {
		-moz-transform: rotate3d(0, 0, 1, 15deg);
		transform: rotate3d(0, 0, 1, 15deg); }
	40% {
		-moz-transform: rotate3d(0, 0, 1, -10deg);
		transform: rotate3d(0, 0, 1, -10deg); }
	60% {
		-moz-transform: rotate3d(0, 0, 1, 5deg);
		transform: rotate3d(0, 0, 1, 5deg); }
	80% {
		-moz-transform: rotate3d(0, 0, 1, -5deg);
		transform: rotate3d(0, 0, 1, -5deg); }
	100% {
		-moz-transform: rotate3d(0, 0, 1, 0deg);
		transform: rotate3d(0, 0, 1, 0deg); } }
@-o-keyframes swing {
	20% {
		transform: rotate3d(0, 0, 1, 15deg); }
	40% {
		transform: rotate3d(0, 0, 1, -10deg); }
	60% {
		transform: rotate3d(0, 0, 1, 5deg); }
	80% {
		transform: rotate3d(0, 0, 1, -5deg); }
	100% {
		transform: rotate3d(0, 0, 1, 0deg); } }
@keyframes swing {
	20% {
		-webkit-transform: rotate3d(0, 0, 1, 15deg);
		-moz-transform: rotate3d(0, 0, 1, 15deg);
		transform: rotate3d(0, 0, 1, 15deg); }
	40% {
		-webkit-transform: rotate3d(0, 0, 1, -10deg);
		-moz-transform: rotate3d(0, 0, 1, -10deg);
		transform: rotate3d(0, 0, 1, -10deg); }
	60% {
		-webkit-transform: rotate3d(0, 0, 1, 5deg);
		-moz-transform: rotate3d(0, 0, 1, 5deg);
		transform: rotate3d(0, 0, 1, 5deg); }
	80% {
		-webkit-transform: rotate3d(0, 0, 1, -5deg);
		-moz-transform: rotate3d(0, 0, 1, -5deg);
		transform: rotate3d(0, 0, 1, -5deg); }
	100% {
		-webkit-transform: rotate3d(0, 0, 1, 0deg);
		-moz-transform: rotate3d(0, 0, 1, 0deg);
		transform: rotate3d(0, 0, 1, 0deg); } }
@-webkit-keyframes shake {
	0%,100% {
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0); }
	10%,30%,50%,70%,90% {
		-webkit-transform: translate3d(-10px, 0, 0);
		transform: translate3d(-10px, 0, 0); }
	20%,40%,60%,80% {
		-webkit-transform: translate3d(10px, 0, 0);
		transform: translate3d(10px, 0, 0); } }
@-moz-keyframes shake {
	0%,100% {
		-moz-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0); }
	10%,30%,50%,70%,90% {
		-moz-transform: translate3d(-10px, 0, 0);
		transform: translate3d(-10px, 0, 0); }
	20%,40%,60%,80% {
		-moz-transform: translate3d(10px, 0, 0);
		transform: translate3d(10px, 0, 0); } }
@-o-keyframes shake {
	0%,100% {
		transform: translate3d(0, 0, 0); }
	10%,30%,50%,70%,90% {
		transform: translate3d(-10px, 0, 0); }
	20%,40%,60%,80% {
		transform: translate3d(10px, 0, 0); } }
@keyframes shake {
	0%,100% {
		-webkit-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0); }
	10%,30%,50%,70%,90% {
		-webkit-transform: translate3d(-10px, 0, 0);
		-moz-transform: translate3d(-10px, 0, 0);
		transform: translate3d(-10px, 0, 0); }
	20%,40%,60%,80% {
		-webkit-transform: translate3d(10px, 0, 0);
		-moz-transform: translate3d(10px, 0, 0);
		transform: translate3d(10px, 0, 0); } }
@-webkit-keyframes pulse {
	0% {
		-webkit-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1); }
	50% {
		-webkit-transform: scale3d(1.03, 1.03, 1.03);
		transform: scale3d(1.03, 1.03, 1.03); }
	100% {
		-webkit-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1); } }
@-moz-keyframes pulse {
	0% {
		-moz-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1); }
	50% {
		-moz-transform: scale3d(1.03, 1.03, 1.03);
		transform: scale3d(1.03, 1.03, 1.03); }
	100% {
		-moz-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1); } }
@-o-keyframes pulse {
	0% {
		transform: scale3d(1, 1, 1); }
	50% {
		transform: scale3d(1.03, 1.03, 1.03); }
	100% {
		transform: scale3d(1, 1, 1); } }
@keyframes pulse {
	0% {
		-webkit-transform: scale3d(1, 1, 1);
		-moz-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1); }
	50% {
		-webkit-transform: scale3d(1.03, 1.03, 1.03);
		-moz-transform: scale3d(1.03, 1.03, 1.03);
		transform: scale3d(1.03, 1.03, 1.03); }
	100% {
		-webkit-transform: scale3d(1, 1, 1);
		-moz-transform: scale3d(1, 1, 1);
		transform: scale3d(1, 1, 1); } }
@-webkit-keyframes bounce {
	0%,100%,20%,53%,80% {
		-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0); }
	40%,43% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -7px, 0);
		transform: translate3d(0, -7px, 0); }
	70% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -4px, 0);
		transform: translate3d(0, -4px, 0); }
	90% {
		-webkit-transform: translate3d(0, -1px, 0);
		transform: translate3d(0, -1px, 0); } }
@-moz-keyframes bounce {
	0%,100%,20%,53%,80% {
		-moz-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-moz-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0); }
	40%,43% {
		-moz-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-moz-transform: translate3d(0, -7px, 0);
		transform: translate3d(0, -7px, 0); }
	70% {
		-moz-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-moz-transform: translate3d(0, -4px, 0);
		transform: translate3d(0, -4px, 0); }
	90% {
		-moz-transform: translate3d(0, -1px, 0);
		transform: translate3d(0, -1px, 0); } }
@-o-keyframes bounce {
	0%,100%,20%,53%,80% {
		-o-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transform: translate3d(0, 0, 0); }
	40%,43% {
		-o-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transform: translate3d(0, -7px, 0); }
	70% {
		-o-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transform: translate3d(0, -4px, 0); }
	90% {
		transform: translate3d(0, -1px, 0); } }
@keyframes bounce {
	0%,100%,20%,53%,80% {
		-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-moz-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-o-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-webkit-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0); }
	40%,43% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-moz-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-o-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -7px, 0);
		-moz-transform: translate3d(0, -7px, 0);
		transform: translate3d(0, -7px, 0); }
	70% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-moz-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-o-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -4px, 0);
		-moz-transform: translate3d(0, -4px, 0);
		transform: translate3d(0, -4px, 0); }
	90% {
		-webkit-transform: translate3d(0, -1px, 0);
		-moz-transform: translate3d(0, -1px, 0);
		transform: translate3d(0, -1px, 0); } }
@-webkit-keyframes switch-on-position {
	0% {
		left: 0;
		right: 50%; }
	25% {
		left: 0;
		right: 37.5%; }
	100% {
		left: 50%;
		right: 0; } }
@-moz-keyframes switch-on-position {
	0% {
		left: 0;
		right: 50%; }
	25% {
		left: 0;
		right: 37.5%; }
	100% {
		left: 50%;
		right: 0; } }
@-o-keyframes switch-on-position {
	0% {
		left: 0;
		right: 50%; }
	25% {
		left: 0;
		right: 37.5%; }
	100% {
		left: 50%;
		right: 0; } }
@keyframes switch-on-position {
	0% {
		left: 0;
		right: 50%; }
	25% {
		left: 0;
		right: 37.5%; }
	100% {
		left: 50%;
		right: 0; } }
@-webkit-keyframes switch-off-position {
	0% {
		left: 50%;
		right: 0; }
	25% {
		left: 37.5%;
		right: 0; }
	100% {
		left: 5%;
		right: 50%; } }
@-moz-keyframes switch-off-position {
	0% {
		left: 50%;
		right: 0; }
	25% {
		left: 37.5%;
		right: 0; }
	100% {
		left: 5%;
		right: 50%; } }
@-o-keyframes switch-off-position {
	0% {
		left: 50%;
		right: 0; }
	25% {
		left: 37.5%;
		right: 0; }
	100% {
		left: 5%;
		right: 50%; } }
@keyframes switch-off-position {
	0% {
		left: 50%;
		right: 0; }
	25% {
		left: 37.5%;
		right: 0; }
	100% {
		left: 5%;
		right: 50%; } }
.ny-container {
	margin: 0 auto;
	max-width: 1340px;
	padding: 0 16px; }
.ny-container::after {
	clear: both;
	content: "";
	display: block; }

.ny-layer--mobile {
	overflow-x: hidden;
	overflow-y: hidden; }

.ny-layer--orders, .ny-layer--product, .ny-layer--products, .ny-layer--sales, .ny-layer--wishlist {
	overflow-x: hidden; }

.ny-validate {
	font-size: 10px;
	margin-top: 5px;
	color: #FF0000;
	display: none; }

.ny-row::after {
	clear: both;
	content: "";
	display: block; }

/*@media only screen and (max-width: 1380px) {
	.ny-container {
		max-width: 1024px; } }
@media only screen and (max-width: 1060px) {
	.ny-container {
		max-width: 768px; } }
@media only screen and (max-width: 800px) {
	.ny-container {
		max-width: -webkit-calc(100% - 30px);
		max-width: -moz-calc(100% - 30px);
		max-width: calc(100% - 30px); } }*/
.ny-button {
	width: auto;
	display: inline-block;
	border: none;
	text-align: center;
	cursor: pointer;
	padding: 0;
	font-family: "Open Sans";
	font-weight: 500; }
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

input[type=number] {
	-moz-appearance:textfield;
}
.ny-button--nyp-af, .ny-button--nyp-photo, .ny-button--product-inline {
	border: 1px solid #202020;
	font-size: 12px;
	text-align: center;
	background-color: transparent;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.ny-button--nyp-draft, .ny-button--nyp-remove, .ny-button--nyp-size, .ny-button--product {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	background-color: #202020;
	color: #fff;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--product_one{
	background: rgba(0,0,0,.4);
	border:1px solid transparent;
}

.ny-button--nyp-category, .ny-button--nyp-remove--clear, .ny-button--nyp-size--clear, .ny-button--swipe-remove {
	border: 0;
	font-size: inherit;
	background-color: transparent;
	color: #333; }

.ny-button--nyp-category:active, .ny-button--nyp-category:hover {
	background-color: transparent !important;
	border-color: transparent !important;
	text-decoration: underline; }

.ny-button--nyp-remove--clear:active, .ny-button--nyp-remove--clear:hover {
	background-color: transparent !important;
	border-color: transparent !important;
	text-decoration: underline; }

.ny-button--nyp-size--clear:active, .ny-button--nyp-size--clear:hover {
	background-color: transparent !important;
	border-color: transparent !important;
	text-decoration: underline; }

.ny-button--swipe-remove:active, .ny-button--swipe-remove:hover {
	background-color: transparent !important;
	border-color: transparent !important;
	text-decoration: underline; }

.ny-button--ask-cancel:active, .ny-button--ask-cancel:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--card:active, .ny-button--card:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--edit-save:active, .ny-button--edit-save:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--filters-button:active, .ny-button--filters-button:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--followers:active, .ny-button--followers:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--loader-preview:active, .ny-button--loader-preview:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--nyp-af:active, .ny-button--nyp-af:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--nyp-photo:active, .ny-button--nyp-photo:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--product-inline:active, .ny-button--product-inline:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--return-upload:active, .ny-button--return-upload:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--size:active, .ny-button--size:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--trackline-return:active, .ny-button--trackline-return:hover {
	border-color: #828282;
	color: #828282; }

.ny-button--ask-success:active, .ny-button--ask-success:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--card-more:active, .ny-button--card-more:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--edit-down:active, .ny-button--edit-down:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--invite:active, .ny-button--invite:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--loader-save:active, .ny-button--loader-save:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--nyp-draft:active, .ny-button--nyp-draft:hover {
	opacity: 1;
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--nyp-remove:active, .ny-button--nyp-remove:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--nyp-size:active, .ny-button--nyp-size:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }
.ny-button--nyp-size{
	background: #000000;
	color: #ffffff!important;
}
.ny-button--nyp-size--clear{
	background: none;
	color: #333333!important;
}
.ny-button--product:active, .ny-button--product:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--return-save:active, .ny-button--return-save:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f; }

.ny-button--nyp-category, .ny-button--swipe-remove {
	text-decoration: underline;
	display: inline-block;
	font-size: 14px;
	position: relative;
	left: 50%;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	display: none;
	color: #202020; }

.ny-button--nyp-category:hover, .ny-button--swipe-remove:hover {
	text-decoration: none; }

.ny-button--product {
	display: inline-block;
	padding: 13px 101px;
	 }

.ny-button--product-inline {
	display: inline-block;
	float: right;
	padding: 13px 74px 13px 64px;
	font-size: 14px; }

.ny-button--nyp-draft {
	display: block;
	width: 100%;
	padding: 14px 0; }

.ny-button--nyp-draft--disable {
	opacity: .25;
	pointer-events: none; }

.ny-button--nyp-draft--close {
	position: absolute;
	bottom: 14px;
	right: 8px;
	width: 20px;
	height: 20px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	background: #fff;
	opacity: 0;
	-webkit-transition: all .2s ease-in;
	-o-transition: all .2s ease-in;
	-moz-transition: all .2s ease-in;
	transition: all .2s ease-in; }

.ny-button--nyp-remove {
	display: inline-block;
	margin-left: 20px;
	font-size: 16px;
	padding: 13px 55px; }

.ny-button--nyp-remove--clear {
	padding-left: 20px;
	padding-right: 20px;
	margin-left: 0; }

.ny-button--nyp-size {
	display: inline-block;
	margin-left: 20px;
	font-size: 12px;
	padding: 10px 35px; }

.ny-button--nyp-size--clear {
	padding-left: 20px;
	padding-right: 20px;
	margin-left: 0; }

.ny-button--nyp-af {
	padding: 6px 20px;
	display: inline-block;
	margin-left: 10px; }
.ny-button--nyp-af:hover {
	border: 1px solid #000;
	background: #000;
	color: #fff; }

.ny-button--nyp-photo {
	display: block;
	padding: 5px 0;
	width: 100%; }

.ny-button--nyp-photo-add {
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: transparent;
	outline: 0; }
.ny-button--nyp-photo-add:before {
	content: "";
	display: block;
	width: 25px;
	height: 25px;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	background: #fff; }

.ny-button--nyp-cf-add {
	font-size: 12px;
	color: #202020;
	background: 0 0;
	line-height: 25px;
	float: right;
	position: relative;
	top: 2px;
	-webkit-transition: opacity .2s ease-in;
	-o-transition: opacity .2s ease-in;
	-moz-transition: opacity .2s ease-in;
	transition: opacity .2s ease-in; }
.ny-button--nyp-cf-add:hover {
	opacity: .7; }

.ny-button--nyp-cf-add--app {
	font-size: 13px;
	font-weight: 700;
	color: #333;
	float: none;
	top: 0;
	display: none; }

.ny-button--nyp-cf-edit {
	width: 25px;
	height: 25px;
	position: absolute;
	top: 13px;
	right: 17px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	background: #fff;
	opacity: 0;
	-webkit-transition: opacity .2s ease-in;
	-o-transition: opacity .2s ease-in;
	-moz-transition: opacity .2s ease-in;
	transition: opacity .2s ease-in; }
.ny-button--nyp-cf-edit em{
	display: none;
}
.ny-button--nyp-cf-edited {
	padding-left: 35px;
	background-color: transparent;
	font-size: 13px;
	font-weight: 700;
	color: #333;
	margin-top: 25px;
	z-index: 2;
	position: relative;
	outline: 0; }

.ny-button--nyp-sf-add {
	font-size: 12px;
	color: #202020;
	background: 0 0;
	line-height: 25px;
	float: right;
	position: relative;
	top: 2px; }

.ny-button--nyp-sf-close {
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	background: transparent url(../pictures/icons/close-size.svg) center center/100% 100% no-repeat;
	width: 9px;
	height: 9px;
	margin-left: 14px; }

.ny-button--nyp-category__close {
	position: absolute;
	top: 18px;
	right: 30px;
	background: transparent url(../pictures/icons/close-size.svg) center center/100% 100% no-repeat;
	width: 14px;
	height: 14px; }

.ny-button--nyp-of {
	width: 30px;
	height: 30px;
	display: inline-block;
	border: 1px solid #e6e6e6;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	font-size: 14px;
	color: #bdbdbd;
	background-color: transparent;
	margin-right: 16px;
	margin-bottom: 17px; }
.ny-button--nyp-of:nth-child(5n) {
	margin-right: 0; }

.ny-button--nyp-of--active {
	font-weight: 700;
	color: #000;
	border-color: #333; }

.ny-button--banner {
	color: #fff;
	background: 0 0;
	font-size: 14px;
	font-family: Roboto;
	font-weight: 500; }

.ny-button--filters-button {
	display: none; }

.ny-button--invite {
	font-size: 14px;
	color: #fff;
	text-align: center;
	display: inline-block;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #202020;
	background-color: #202020;
	padding: 6px 37px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	margin-right: 20px; }

.ny-button--invite--fb, .ny-button--invite--tm, .ny-button--invite--vb, .ny-button--invite--vk {
	padding-left: 15px;
	padding-right: 15px; }

.ny-button--invite--stroke {
	color: #202020;
	background-color: transparent;
	padding-left: 25px;
	padding-right: 25px; }
.ny-button--invite--stroke:hover {
	background-color: transparent;
	border-color: #828282;
	color: #828282; }

.ny-button--followers {
	font-size: 12px;
	color: #202020;
	text-align: center;
	display: inline-block;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #202020;
	background-color: transparent;
	padding: 7px 0;
	width: 130px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.ny-button--followers--fill {
	color: #fff;
	background-color: #202020; }
.ny-button--followers--fill:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f;
	color: #fff; }

.ny-button--card {
	width: 100%;
	border: 1px solid #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 11px 0;
	text-align: center;
	font-size: 12px;
	background: 0 0; }

.ny-button--card--fill {
	background-color: #202020;
	color: #fff; }
.ny-button--card--fill:active, .ny-button--card--fill:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f;
	color: #fff; }

.ny-button--card--last {
	margin-top: 10px; }

.ny-button--card--green {
	border-color: #27ae60;
	color: #27ae60;
	pointer-events: none; }
.ny-button--card--green:active, .ny-button--card--green:hover {
	border-color: #27ae60;
	color: #27ae60; }

.ny-button--card--red {
	border-color: #eb5757;
	color: #eb5757;
	pointer-events: none; }
.ny-button--card--red:active, .ny-button--card--red:hover {
	border-color: #eb5757;
	color: #eb5757; }

.ny-button--feeds {
	border: 1px solid #c4c4c4;
	color: #202020;
	font-size: 12px;
	margin-right: 3px;
	background: 0 0;
	padding: 4px 11px;
	margin-bottom: 7px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }
.ny-button--feeds:hover {
	border-color: #8c8c8c; }

.ny-button--ask-cancel {
	border: 1px solid #202020;
	font-size: 12px;
	text-align: center;
	display: inline-block;
	margin-right: 20px;
	background-color: transparent;
	padding: 11px 25px;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--ask-success {
	border: 1px solid #202020;
	font-size: 12px;
	text-align: center;
	display: inline-block;
	background-color: #202020;
	padding: 11px 25px;
	color: #fff;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--ask-success--paid {
	margin-top: 10px; }

.ny-button--ask-success:first-child {
	padding-left: 50px;
	padding-right: 50px; }

.ny-button--card-more {
	border: 1px solid #202020;
	font-size: 18px;
	text-align: center;
	display: inline-block;
	background-color: #202020;
	padding: 11px 25px;
	color: #fff;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--size {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	display: block;
	background-color: transparent;
	padding: 14px 25px;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	width: 100%; }

.ny-button--size-count {
	display: inline-block;
	width: 13px;
	height: 15px;
	background-color: transparent;
	position: relative;
	top: 5px; }

.ny-button--loader-preview {
	border: 1px solid #202020;
	font-size: 12px;
	text-align: center;
	display: inline-block;
	margin-right: 30px;
	background-color: transparent;
	padding: 11px 15px;
	min-width: 200px;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--loader-save {
	background: #202020;
	color: #fff;
	font-size: 12px;
	text-align: center;
	display: inline-block;
	border: 1px solid #202020;
	padding: 11px 15px;
	min-width: 200px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--edit-picture, .ny-button--edit-remove {
	position: absolute;
	top: 10px;
	right: 10px;
	width: 25px;
	height: 25px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
	background: #fff; }

.ny-button--edit-picture {
	top: 45px; }

.ny-button--edit-save {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	display: inline-block;
	background-color: transparent;
	padding: 14px 15px;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	width: 100%; }

.ny-button--edit-save--report {
	font-size: 12px;
	margin-bottom: 10px;
	padding-top: 10px;
	padding-bottom: 10px; }

.ny-button--edit-down {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	display: inline-block;
	background-color: #202020;
	padding: 14px 15px;
	color: #fff;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	width: 100%; }

.ny-button--edit-down--report {
	font-size: 12px;
	padding-top: 10px;
	padding-bottom: 10px; }

.ny-button--edit-down--disable {
	opacity: .3;
	pointer-events: none;
	margin-top: 10px; }

.ny-button--edit-comsize {
	position: absolute;
	bottom: -5px;
	right: 0;
	width: 25px;
	height: 25px;
	background-color: transparent;
	-webkit-box-shadow: 0 0 0;
	-moz-box-shadow: 0 0 0;
	box-shadow: 0 0 0; }

.ny-button--accordion-button {
	background-color: transparent;
	color: #4f4f4f;
	font-size: 12px;
	float: right;
	line-height: 18px; }

.ny-button--return-save {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	display: inline-block;
	background-color: #202020;
	padding: 14px 100px;
	color: #fff;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--return-done {
	position: absolute;
	top: 8px;
	right: 0;
	width: 16px;
	height: 16px;
	background-color: transparent; }

.ny-button--return-upload {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	display: inline-block;
	background-color: transparent;
	padding: 14px 39px;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-button--trackline-button {
	float: right;
	font-size: 12px;
	color: #4f4f4f;
	background-color: transparent; }

.ny-button--trackline-return {
	font-size: 12px;
	color: #202020;
	text-align: center;
	display: inline-block;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #202020;
	background-color: transparent;
	padding: 4px 40px;
	position: relative;
	top: -7px; }

.ny-button--trackline-return--disable {
	opacity: .3;
	pointer-events: none; }

.ny-button--trackline-return--fill {
	color: #fff;
	background-color: #202020;
	padding-left: 20px;
	padding-right: 20px; }
.ny-button--trackline-return--fill:hover {
	background-color: #4f4f4f;
	border-color: #4f4f4f;
	color: #fff; }

.ny-button--trackline-return--fill--disable {
	color: #fff;
	background-color: #202020;
	padding-left: 20px;
	padding-right: 20px;
	opacity: .3;
	pointer-events: none;
	top: -6px; }

/*@media only screen and (max-width: 1380px) {
	.ny-button--feeds {
		padding-left: 9px;
		padding-right: 9px; } }
@media only screen and (max-width: 1060px) {
	.ny-button--swipe-remove {
		display: block;
		padding-top: 40px; }

	.ny-button--nyp-category__close {
		display: none; }

	.ny-button--nyp-size {
		margin-left: 0;
		font-size: 14px;
		padding: 14px 73px; }

	.ny-button--nyp-size--clear, .ny-button--nyp-sf-close {
		display: none; }

	.ny-button--nyp-sf-add {
		float: none;
		top: 0; }

	.ny-button--nyp-of:nth-child(5n) {
		margin-right: 16px; }

	.ny-button--nyp-cf-add--app {
		display: block; }

	.ny-button--product, .ny-button--product-inline {
		float: none;
		position: relative;
		left: 50%;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%); }

	.ny-button--product--disable, .ny-button--product-inline--disable {
		opacity: .25;
		pointer-events: none; }

	.ny-button--filters-button {
		display: block;
		position: fixed;
		top: 88%;
		left: 50%;
		-webkit-transform: translate(-50%, -50%);
		-moz-transform: translate(-50%, -50%);
		-ms-transform: translate(-50%, -50%);
		-o-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%);
		z-index: 2;
		font-size: 16px;
		color: #000;
		-webkit-border-radius: 2px;
		-moz-border-radius: 2px;
		border-radius: 2px;
		-webkit-box-shadow: 0 5px 13px rgba(0, 0, 0, 0.171);
		-moz-box-shadow: 0 5px 13px rgba(0, 0, 0, 0.171);
		box-shadow: 0 5px 13px rgba(0, 0, 0, 0.171);
		background: #fff;
		padding: 13.5px 15px;
		font-weight: 700;
		line-height: 21px; }

	.ny-button--size {
		width: 50%;
		margin: 0 auto; }

	.ny-button--size-count {
		-webkit-box-sizing: content-box;
		-moz-box-sizing: content-box;
		box-sizing: content-box;
		padding: 9px 10px;
		top: 0;
		border: 1px solid #e5e5e5; }

	.ny-button--ask-success {
		width: 47.5%;
		display: block;
		float: right;
		padding-left: 5px;
		padding-right: 5px; }
	.ny-button--ask-success:first-child {
		float: none;
		display: inline-block;
		width: auto; }

	.ny-button--ask-success--send {
		width: 100%;
		padding-right: 0;
		padding-left: 0; }
	.ny-button--ask-success--send:first-child {
		width: 100%;
		padding-right: 0;
		padding-left: 0; }

	.ny-button--ask-success--paid {
		margin-top: 20px; }

	.ny-button--ask-cancel {
		width: 47.5%;
		display: block;
		float: left;
		margin-right: 0;
		padding-left: 5px;
		padding-right: 5px; }

	.ny-button--trackline-return {
		top: -3px; }

	.ny-button--return-done {
		display: none; }

	.ny-button--return-save {
		padding-left: 70px;
		padding-right: 70px; }

	.ny-button--edit-save--report {
		width: 48%;
		margin-bottom: 0; }

	.ny-button--edit-down--report {
		width: 48%; } }
@media only screen and (max-width: 800px) {
	.ny-button--return-save {
		padding-left: 45px;
		padding-right: 45px; } }
@media only screen and (max-width: 660px) {
	.ny-button--card {
		padding: 9px 0; }

	.ny-button--invite:nth-child(2n) {
		margin-right: 0; }

	.ny-button--invite--fb, .ny-button--invite--tm, .ny-button--invite--vb, .ny-button--invite--vk {
		width: 45%;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		margin-bottom: 20px;
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center; }

	.ny-button--invite--fb span, .ny-button--invite--tm span, .ny-button--invite--vb span, .ny-button--invite--vk span {
		-webkit-box-ordinal-group: 2;
		-webkit-order: 1;
		-moz-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1; }

	.ny-button--trackline-button {
		display: block;
		width: 48%;
		-webkit-border-radius: 2px;
		-moz-border-radius: 2px;
		border-radius: 2px;
		border: 1px solid #202020;
		background-color: transparent;
		padding: 10px;
		color: #202020;
		top: 0;
		position: relative; }
	.ny-button--trackline-button:active, .ny-button--trackline-button:hover {
		border-color: #828282;
		color: #828282; }
	.ny-button--trackline-button .ny-notice {
		margin-left: 8px;
		top: 7px; }

	.ny-button--trackline-return {
		display: block;
		width: 100%;
		float: none;
		margin-right: 0;
		padding: 10px;
		top: 0; }

	.ny-button--return-save {
		width: 100%; } }
@media only screen and (max-width: 520px) {
	.ny-button--nyp-cf-edited {
		margin-top: 15px;
		padding-left: 0; }

	.ny-button--filters-button {
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		-moz-box-pack: center;
		-ms-flex-pack: center;
		justify-content: center;
		-webkit-box-align: center;
		-webkit-align-items: center;
		-moz-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		padding-left: 10px;
		padding-right: 10px;
		width: 70%;
		padding-top: 12px;
		padding-bottom: 12px; }

	.ny-button--size {
		width: 100%; }

	.ny-button--trackline-return {
		width: 100%;
		margin-right: 0;
		margin-bottom: 15px; }

	.ny-button--trackline-button {
		width: 100%;
		top: 0; }

	.ny-button--return-upload {
		width: 100%; }

	.ny-button--followers {
		width: 110px;
		padding: 6px 0;
		font-size: 11px; } }
@media only screen and (max-width: 380px) {
	.ny-button--filters-button {
		font-size: 15px; } }
@media only screen and (max-width: 374px) {
	.ny-button--nyp-size {
		margin-left: 0;
		font-size: 14px;
		padding: 14px 60px; }

	.ny-button--accordion-button {
		margin-top: 5px;
		float: none;
		display: block; }

	.ny-button--followers {
		width: 95px;
		padding: 5px 0;
		font-size: 10px; }

	.ny-button--product-inline {
		padding-right: 65px;
		padding-left: 55px; }

	.ny-button--nyp-remove {
		width: 100%;
		margin-left: 0; }

	.ny-button--nyp-remove--clear {
		width: 100%;
		margin-bottom: 20px; } }*/
.ny-circle {
	width: auto;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	display: block; }

.ny-circle--banner {
	position: absolute;
	bottom: 0;
	left: 30px;
	-webkit-transform: translateY(50%);
	-moz-transform: translateY(50%);
	-ms-transform: translateY(50%);
	-o-transform: translateY(50%);
	transform: translateY(50%);
	z-index: -1;
	width: 150px;
	height: 150px;
	-o-object-fit: cover;
	object-fit: cover; }

.ny-circle--banner--up {
	bottom: 40px; }

.ny-circle--feeds--followers, .ny-circle--profile {
	max-width: 100%; }

/*@media only screen and (max-width: 1380px) {
	.ny-circle--banner {
		width: 100px;
		height: 100px; } }
@media only screen and (max-width: 1060px) {
	.ny-circle--mobile {
		width: 70px;
		height: 70px; } }
@media only screen and (max-width: 520px) {
	.ny-circle--mobile {
		width: 50px;
		height: 50px; }

	.ny-circle--card-feature {
		max-width: 22px;
		max-height: 22px; } }*/
.ny-counter {
	position: relative;
	border: 1px solid #e6e6e6;
	padding: 11px 8px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	}

/*@media only screen and (max-width: 1060px) {
	.ny-counter {
		border: 0;
		padding-top: 13px;
		padding-bottom: 13px; } }*/
.ny-field {
	width: auto;
	display: block;
	outline: 0; }
.ny-field::-webkit-input-placeholder, .ny-field::-moz-placeholder, .ny-field::-ms-input-placeholder, .ny-field::-moz-placeholder {
	color: #bdbdbd; }

.ny-field--nyp-cf, .ny-field--nyp-of, .ny-field--nyp-pf, .ny-field--nyp-photo, .ny-field--nyp-size {
	border: 0;
	border-bottom: 1px solid #e6e6e6;
	font-size: 16px;
	color: #4f4f4f;
	padding-bottom: 5px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: border .17s ease-in;
	-o-transition: border .17s ease-in;
	-moz-transition: border .17s ease-in;
	transition: border .17s ease-in;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	padding-left: 0; }

.ny-field--nyp-cf:focus ~ .ny-label, .ny-field--nyp-of:focus ~ .ny-label, .ny-field--nyp-pf:focus ~ .ny-label, .ny-field--nyp-photo:focus ~ .ny-label, .ny-field--nyp-size:focus ~ .ny-label {
	bottom: -webkit-calc(100%);
	bottom: -moz-calc(100%);
	bottom: calc(100%);
	font-size: 12px;
	padding-bottom: 5px; }

.ny-field--nyp-cf:focus ~ .nnyp-category__linky-label--nyp-of, .ny-field--nyp-of:focus ~ .ny-label--nyp-of, .ny-field--nyp-pf:focus ~ .ny-label--nyp-of, .ny-field--nyp-photo:focus ~ .ny-label--nyp-of, .ny-field--nyp-size:focus ~ .ny-label--nyp-of {
	font-size: 11px; }

.error.ny-field--nyp-cf, .error.ny-field--nyp-of, .error.ny-field--nyp-pf, .error.ny-field--nyp-photo, .error.ny-field--nyp-size {
	border-bottom-color: red; }

.ny-field--edit-field:focus, .ny-field--edit-field:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-field--return-field:focus, .ny-field--return-field:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-field--search:focus, .ny-field--search:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-field--nyp-calc {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	display: block;
	width: 100%;
	border: 1px solid #e6e6e6;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	font-size: 14px;
	color: #333;
	padding: 10px;
	font-family: Roboto;
	font-weight: 500; }
.ny-field--nyp-calc:focus {
	border-color: #333; }

.ny-field--nyp-size {
	display: block;
	width: 100%;
	font-size: 16px; }

.ny-field--nyp-photo {
	display: block;
	width: 100%; }

.ny-field--nyp-pf {
	display: block;
	width: 100%;
	padding-left: 0; }

.ny-field--nyp-pf--hidden {
	border-bottom-color: transparent;
	pointer-events: none; }

.ny-field--nyp-cf {
	display: block;
	width: 100%; }

.ny-field--nyp-cf-radio {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	-webkit-border-radius: 0;
	-moz-border-radius: 0;
	border-radius: 0;
	z-index: 1;
	cursor: pointer; }
.ny-field--nyp-cf-radio:checked ~ .nyp-cf__field-checkmark {
	border-color: #4f4f4f; }
.ny-field--nyp-cf-radio:checked ~ .nyp-cf__field-checkmark::after {
	opacity: 1; }
.ny-field--nyp-cf-radio:checked ~ .nyp-cf__field-checkmark .nyp-cf__field-info__name {
	font-weight: 700; }
.ny-field--nyp-cf-radio:checked ~ .nyp-cf__field-checkmark .ny-button--nyp-cf-edit {
	opacity: 1;
	z-index: 2; }

.ny-field--nyp-of {
	display: block;
	width: 100%; }

.ny-field--ny-counter {
	border: 0;
	width: 30px;
	text-align: center;
	margin: 0 13px;
	font-size: 14px;
	padding: 0;
	color: #202020; }
.ny-field--ny-counter::-webkit-inner-spin-button, .ny-field--ny-counter::-webkit-outer-spin-button {
	-webkit-appearance: none;
	margin: 0; }

.ny-field--search {
	color: #bdbdbd;
	width: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-size: 12px;
	border: 1px solid #eaeaea;
	padding: 9px 13px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	font-family: "Open Sans";
	font-weight: 500; }

.ny-field--search--followers {
	padding-top: 10px;
	padding-bottom: 10px;
	font-size: 16px;
	color: #e6e6e6;
	border-color: #e6e6e6; }

.ny-field--search--swipe {
	font-size: 13px;
	border-color: transparent;
	background: #f5f5f5;
	padding-left: 33px;
	height: 40px; }

.ny-field--upload {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	cursor: pointer; }

.ny-field--size {
	display: inline-block;
	width: 25px;
	font-size: 14px;
	color: #202020;
	border: 0;
	border-bottom: 1px solid #e5e5e5;
	text-align: center;
	padding-bottom: 2px;
	position: relative;
	top: 2px;
	margin: 0 3px; }

.ny-field--gallery-upload {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	cursor: pointer; }

.ny-field--edit-field {
	border: 0;
	border-bottom: 1px solid #e5e5e5;
	font-size: 16px;
	color: #202020;
	padding: 4px 0 9px;
	width: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.ny-field--edit-field--report {
	font-size: 12px; }
.ny-field--edit-field--report:disabled {
	border-bottom-color: transparent;
	background-color: transparent; }

.ny-field--edit-field--error {
	border-bottom-color: #eb5757; }

.ny-field--edit-field--invite {
	margin-bottom: 15px; }

.ny-field--ask-field {
	opacity: 0;
	width: 0;
	height: 0;
	position: absolute;
	top: 0;
	left: 0; }
.ny-field--ask-field:checked + label:before {
	background-color: #202020;
	background-image: url(../pictures/icons/checked.svg); }

.ny-field--edit-picture {
	display: none; }

.ny-field--edit-trigger {
	position: relative;
	display: inline-block;
	-webkit-appearance: none;
	-webkit-tap-highlight-color: transparent;
	height: 22px;
	width: 40px;
	-webkit-border-radius: 1.5em;
	-moz-border-radius: 1.5em;
	border-radius: 1.5em;
	background-color: #e5e5e5;
	border-color: transparent;
	background-clip: padding-box;
	color: #202020;
	vertical-align: middle;
	-webkit-transition: all .25s linear .25s;
	-o-transition: all .25s linear .25s;
	-moz-transition: all .25s linear .25s;
	transition: all .25s linear .25s;
	cursor: pointer; }
.ny-field--edit-trigger:before {
	content: "";
	position: absolute;
	top: 50%;
	left: 0;
	bottom: 0;
	right: 50%;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	background-color: #fff;
	-webkit-border-radius: 100%;
	-moz-border-radius: 100%;
	border-radius: 100%;
	width: 18px;
	height: 18px;
	border: 0;
	background-clip: padding-box;
	z-index: 2;
	-webkit-transform-origin: right center;
	-moz-transform-origin: right center;
	-ms-transform-origin: right center;
	-o-transform-origin: right center;
	transform-origin: right center;
	-webkit-animation: switch-off-position .25s ease-out forwards;
	-moz-animation: switch-off-position .25s ease-out forwards;
	-o-animation: switch-off-position .25s ease-out forwards;
	animation: switch-off-position .25s ease-out forwards;
	-webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.19);
	-moz-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.19);
	box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.19); }
.ny-field--edit-trigger:focus {
	color: #fff;
	border-color: transparent;
	background-color: #e5e5e5;
	outline: 0; }
.ny-field--edit-trigger:checked {
	color: #fff;
	background-color: #000;
	border-color: transparent; }
.ny-field--edit-trigger:checked:before {
	-webkit-transform-origin: left center;
	-moz-transform-origin: left center;
	-ms-transform-origin: left center;
	-o-transform-origin: left center;
	transform-origin: left center;
	-webkit-animation: switch-on-position .25s ease-out forwards;
	-moz-animation: switch-on-position .25s ease-out forwards;
	-o-animation: switch-on-position .25s ease-out forwards;
	animation: switch-on-position .25s ease-out forwards; }

.ny-field--return-field {
	font-size: 14px;
	color: #202020;
	border: 0;
	border-bottom: 1px solid #e6e6e6;
	padding-bottom: 8px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: 100%; }

.ny-field--return-check {
	opacity: 0;
	width: 0;
	height: 0;
	position: absolute;
	top: 0;
	left: 0; }
.ny-field--return-check:checked + label:before {
	background-color: transparent;
	background-image: url(../pictures/icons/checked-black.svg); }

#ny-field--edit-current-price {
	color: #bdbdbd;
	background-color: transparent; }

/*@media only screen and (max-width: 1380px) {
	.ny-field--edit-field {
		font-size: 12px;
		padding-bottom: 5px; } }
@media only screen and (max-width: 1060px) {
	.ny-field--ny-counter {
		margin: 0 20px; }

	.ny-field--edit-field {
		font-size: 14px;
		padding-bottom: 10px; }

	.ny-field--edit-field-company {
		text-align: right;
		font-size: 13px;
		color: #bdbdbd;
		padding-bottom: 15px;
		padding-right: 25px; }

	.ny-field--size {
		border-bottom: 0;
		top: -2px; } }
@media only screen and (max-width: 520px) {
	.ny-field--edit-field-company {
		padding-right: 15px;
		padding-top: 6px; }

	.ny-field--search--followers {
		padding-top: 8.5px;
		padding-bottom: 8.5px;
		font-size: 12px; } }
@media only screen and (max-width: 380px) {
	.ny-field--edit-field {
		font-size: 14px;
		color: #202020;
		text-align: left;
		padding-bottom: 5px;
		padding-top: 3px; } }*/
.ny-icon {
	width: auto;
	display: inline-block;
	background-position: left top;
	background-repeat: no-repeat;
	-webkit-background-size: 100% 100%;
	-moz-background-size: 100% 100%;
	-o-background-size: 100% 100%;
	background-size: 100% 100%;
	position: relative; }

.ny-icon--nyp-photo-load, .ny-icon--nyp-photo-logo {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }

.ny-icon--swipe-check {
	background-image: url(../pictures/icons/checked-product.svg);
	width: 17px;
	height: 13px;
	position: absolute;
	top: 50%;
	right: 0;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	display: none; }

.ny-icon--swipe-color {
	display: inline-block;
	width: 16px;
	height: 16px;
	background: #000;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	float: left;
	margin-right: 8px; }

.ny-icon--swipe-color--red {
	background-color: #c30202; }

.ny-icon--swipe-color--white {
	background-color: #fff;
	border: 1px solid #c4c4c4; }

.ny-icon--swipe-color--green {
	background-color: #0f9b0c; }

.ny-icon--swipe-color--yellow {
	background-color: #ffd600; }

.ny-icon--ny-product-backspace {
	display: none; }

.ny-icon--nyp-category {
	background-image: url(../pictures/icons/nav-arrow-right.svg);
	width: 8px;
	height: 13px;
	margin-left: 15px;
	position: relative;
	top: 1px; }

.ny-icon--nyp-draft {
	background-image: url(../pictures/icons/add-public.svg);
	width: 30px;
	height: 22px; }

.ny-icon--nyp-draft--success {
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background-image: url(../pictures/icons/checked.svg);
	background-position: center center;
	background-repeat: no-repeat;
	-webkit-background-size: 55% 55%;
	-moz-background-size: 55%;
	-o-background-size: 55%;
	background-size: 55%;
	background-color: #27ae60;
	position: absolute;
	top: 0;
	right: 0; }

.ny-icon--nyp-draft--close {
	background-image: url(../pictures/icons/close-media.svg);
	width: 11px;
	height: 11px; }

.ny-icon--nyp-tooltip {
	background-image: url(../pictures/icons/tooltip.svg);
	width: 12px;
	height: 12px;
	position: absolute;
	top: 16px;
	left: 12px; }

.ny-icon--nyp-cf-add {
	background: #f2f2f2 url(../pictures/icons/plus.svg) center center/60% 60% no-repeat;
	width: 25px;
	height: 25px;
	margin-right: 13px;
	float: left;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-icon--nyp-cf-add--app {
	background-color: transparent;
	margin-right: 7px; }

.ny-icon--nyp-cf-edit {
	background-image: url(../pictures/icons/pen.svg);
	width: 13px;
	height: 13px; }

.ny-icon--nyp-cf-edited {
	background-image: url(../pictures/icons/pen-fill.svg);
	width: 15px;
	height: 15px;
	margin-left: 8px;
	position: relative;
	top: 3px; }

.ny-icon--nyp-sf-add {
	background: #f2f2f2 url(../pictures/icons/plus.svg) center center/60% 60% no-repeat;
	width: 25px;
	height: 25px;
	margin-right: 13px;
	float: left;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-icon--ny-counter {
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	width: 12px;
	height: 12px; }

.ny-icon--ny-counter--minus {
	left: 6px;
	background-image: url(../pictures/icons/counter-minus.svg);
	cursor: pointer; }

.ny-icon--ny-counter--plus {
	right: 6px;
	background-image: url(../pictures/icons/counter-plus.svg);
	cursor: pointer; }

.ny-icon--product-inline {
	background-image: url(../pictures/icons/plus.svg);
	width: 13px;
	height: 13px;
	margin-right: 9px;
	position: relative;
	top: 2px; }

.ny-icon--nyp-photo-add {
	background-image: url(../pictures/icons/plus.svg);
	width: 15px;
	height: 15px; }

.ny-icon--nyp-photo-load {
	background-image: url(../pictures/icons/add-photo.svg);
	width: 52px;
	height: 41px;
	pointer-events: none; }

.ny-icon--nyp-photo-logo {
	background-image: url(../pictures/icons/logo.svg);
	width: 89px;
	height: 14px; }

.ny-icon--banner {
	background-image: url(../pictures/icons/loader.svg);
	width: 19px;
	height: 16px;
	top: 2px; }

.ny-icon--invite {
	float: left;
	margin-right: 10px;
	top: 2px; }

.ny-icon--invite--stroke {
	background-image: url(../pictures/icons/copy.svg);
	width: 10px;
	height: 12px; }

.ny-icon--invite--fb {
	background-image: url(../pictures/icons/fb.svg);
	width: 6px;
	height: 12px; }

.ny-icon--invite--vk {
	background-image: url(../pictures/icons/vk.svg);
	width: 20px;
	height: 11px; }

.ny-icon--invite--tm {
	background-image: url(../pictures/icons/tm.svg);
	width: 15px;
	height: 13px; }

.ny-icon--invite--vb {
	background-image: url(../pictures/icons/vb.svg);
	width: 16px;
	height: 16px;
	top: 1px; }

.ny-icon--tabs--fb {
	background-image: url(../pictures/icons/fb-black.svg);
	width: 8px;
	height: 17px; }

.ny-icon--tabs--vk {
	background-image: url(../pictures/icons/vk-black.svg);
	width: 20px;
	height: 11px; }

.ny-icon--verify {
	background-image: url(../pictures/icons/verify.svg);
	width: 26px;
	height: 26px;
	background-color: #fff;
	position: absolute;
	top: 3px;
	right: 0;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	background-position: center center;
	-webkit-background-size: -webkit-calc(100% - 6px) -webkit-calc(100% - 6px);
	-moz-background-size: -moz-calc(100% - 6px) -moz-calc(100% - 6px);
	-o-background-size: calc(100% - 6px) calc(100% - 6px);
	background-size: calc(100% - 6px) calc(100% - 6px);
	-webkit-transform: translateX(50%);
	-moz-transform: translateX(50%);
	-ms-transform: translateX(50%);
	-o-transform: translateX(50%);
	transform: translateX(50%); }

.ny-icon--filters-button {
	background-image: url(../pictures/icons/filter.svg);
	width: 20px;
	height: 21px;
	float: left;
	margin-right: 10px; }

.ny-icon--search {
	position: absolute;
	top: 50%;
	right: 10px;
	background-image: url(../pictures/icons/search.svg);
	width: 9px;
	height: 10px;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	pointer-events: none; }

.ny-icon--search--swipe {
	right: auto;
	left: 12px;
	width: 13px;
	height: 14px;
	background-image: url(../pictures/icons/search-swipe.svg); }

.ny-icon--arrow {
	background-image: url(../pictures/icons/arrow-down.svg);
	width: 9px;
	height: 9px;
	top: -1px; }

.ny-icon--card-feature {
	background-image: url(../pictures/icons/card-favorite.svg);
	width: 21px;
	height: 21px; }

.ny-icon--card-close {
	background-image: url(../pictures/icons/card-remove.svg);
	width: 16px;
	height: 16px; }

.ny-icon--card-feed {
	background-image: url(../pictures/icons/card-feed.svg);
	width: 19px;
	height: 19px;
	top: -2px; }

.ny-icon--card-review {
	background-image: url(../pictures/icons/card-comments.svg);
	width: 14px;
	height: 14px; }

.ny-icon--card-like {
	background-image: url(../pictures/icons/card-like.svg);
	width: 15px;
	height: 14px; }

.ny-icon--card-tracking {
	background-image: url(../pictures/icons/feature-like.svg);
	width: 20px;
	height: 16px;
	top: 3px;
	margin-left: 3px; }

.ny-icon--card-price--down {
	background-image: url(../pictures/icons/price-down.svg);
	width: 7px;
	height: 11px;
	margin-right: 2px;
	top: 1px; }

.ny-icon--card-draft {
	background-image: url(../pictures/icons/thumb-none.svg);
	width: 63px;
	height: 49px;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }

.ny-icon--card-moderate {
	background-image: url(../pictures/icons/timer.svg);
	width: 14px;
	height: 16px;
	display: inline-block;
	margin-right: 5px;
	top: 3px; }

.ny-icon--warning-photo {
	width: 29px;
	height: 29px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background-color: #ff2020;
	background-image: url(../pictures/icons/card-notice-photo.svg);
	-webkit-background-size: 16px 12px;
	-moz-background-size: 16px 12px;
	-o-background-size: 16px 12px;
	background-size: 16px 12px;
	background-position: center 45%;
	margin-right: 15px; }

.ny-icon--warning-text {
	width: 29px;
	height: 29px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background-color: #ff2020;
	background-image: url(../pictures/icons/card-notice-desc.svg);
	-webkit-background-size: 15px 10px;
	-moz-background-size: 15px 10px;
	-o-background-size: 15px 10px;
	background-size: 15px 10px;
	background-position: center center;
	margin-right: 15px; }

.ny-icon--warning-price {
	width: 29px;
	height: 29px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background-color: #ff2020;
	background-image: url(../pictures/icons/card-notice-price.svg);
	-webkit-background-size: 9px 12px;
	-moz-background-size: 9px 12px;
	-o-background-size: 9px 12px;
	background-size: 9px 12px;
	background-position: center center;
	margin-right: 15px; }

.ny-icon--size {
	background-image: url(../pictures/icons/card-remove.svg);
	width: 100%;
	height: 100%; }

.ny-icon--size-count {
	width: 100%;
	height: 100%;
	background-image: url(../pictures/icons/size-plus.svg); }

.ny-icon--size-count--minus {
	background-image: url(../pictures/icons/size-minus.svg); }

.ny-icon--upload-notice {
	background-image: url(../pictures/icons/warning.svg);
	width: 30px;
	height: 30px;
	display: block;
	margin-right: 15px; }

.ny-icon--upload-field {
	background-image: url(../pictures/icons/banner-form.svg);
	width: 47px;
	height: 46px;
	display: block;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 15px; }

.ny-icon--edit-close {
	background-image: url(../pictures/icons/card-remove.svg);
	width: 100%;
	height: 100%; }

.ny-icon--edit-comsize, .ny-icon--edit-picture {
	background-image: url(../pictures/icons/pen.svg);
	width: 13px;
	height: 13px; }

.ny-icon--edit-remove {
	background-image: url(../pictures/icons/close-media.svg);
	width: 11px;
	height: 11px; }

.ny-icon--edit-color {
	position: absolute;
	bottom: 7px;
	left: 12px;
	width: 14px;
	height: 14px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background: #ff49d7; }

.ny-icon--edit-info {
	background-image: url(../pictures/icons/notice.svg);
	width: 15px;
	height: 15px;
	top: 6px;
	float: right; }

.ny-icon--edit-info--trackline {
	top: -2px;
	margin-left: 5px;
	z-index: 1; }
.ny-icon--edit-info--trackline + div {
	width: 65%;
	line-height: 1.4;
	padding-right: 20px;
	right: -20px;
	padding-top: 13px;
	padding-bottom: 14px;
	top: -18px; }
.ny-icon--edit-info--trackline + div:before {
	border-left-width: 5px;
	border-right-width: 5px;
	border-top-width: 8px;
	right: 22px; }
.ny-icon--edit-info--trackline + div:after {
	border-left-width: 6px;
	border-right-width: 6px;
	border-top-width: 9px;
	right: 21px; }

.ny-icon--edit-info--send {
	float: none;
	top: 2px;
	margin-right: 5px; }

.ny-icon--edit-info--report {
	top: 0;
	z-index: 1; }
.ny-icon--edit-info--report + div {
	width: 312px;
	line-height: 1.2;
	padding-right: 20px;
	right: -31px;
	top: -18px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.ny-icon--edit-info--report + div:before {
	border-left-width: 5px;
	border-right-width: 5px;
	border-top-width: 8px;
	right: 33px; }
.ny-icon--edit-info--report + div:after {
	border-left-width: 6px;
	border-right-width: 6px;
	border-top-width: 9px;
	right: 32px; }

.ny-icon--edit-info--profile {
	float: none;
	top: 2.5px;
	margin-left: 5px;
	width: 12px;
	height: 12px; }

.ny-icon--edit-info:hover + div {
	display: block; }

.ny-icon--edit-nav {
	display: none; }

.ny-icon--gallery-addon {
	background: url(../pictures/icons/edit-thumb-none.svg) left top/100% 100% no-repeat;
	width: 44px;
	height: 36px;
	margin-bottom: 5px; }

.ny-icon--gallery-addon--nyp-photo {
	width: 21px;
	height: 17px; }

.ny-icon--more-pay {
	background: url(../pictures/icons/trading-visa.svg) left top/100% 100% no-repeat;
	width: 30px;
	height: 23px;
	display: block;
	margin-top: 10px; }

.ny-icon--return-close {
	background: url(../pictures/icons/card-remove.svg) left top/100% 100% no-repeat;
	width: 16px;
	height: 16px;
	position: absolute;
	bottom: 0;
	right: 25px; }

.ny-icon--return-done {
	background: url(../pictures/icons/card-remove.svg) left top/100% 100% no-repeat;
	width: 100%;
	height: 100%; }

.ny-icon--return-info {
	background: url(../pictures/icons/orders-warning.svg) left top/100% 100% no-repeat;
	width: 23px;
	height: 21px;
	margin-right: 15px;
	margin-bottom: -5px; }

.ny-icon--trackline-action {
	width: 59px;
	height: 20px;
	background: url(../pictures/icons/trading-co.svg) left top/100% 100% no-repeat;
	top: -2px; }

.ny-icon--trackline-button {
	width: 9px;
	height: 9px;
	margin-left: 5px;
	background: url(../pictures/icons/arrow-down.svg) left top/100% 100% no-repeat; }

.ny-icon--trackline-button-up {
	-webkit-transform: rotate(-180deg);
	-moz-transform: rotate(-180deg);
	-ms-transform: rotate(-180deg);
	-o-transform: rotate(-180deg);
	transform: rotate(-180deg);
	top: 2px; }

.ny-icon--trackline-success {
	display: none; }

.ny-icon--accordion-button {
	width: 9px;
	height: 9px;
	margin-left: 5px;
	background: url(../pictures/icons/arrow-down.svg) left top/100% 100% no-repeat; }

.ny-icon--accordion-button--up {
	-webkit-transform: rotate(-180deg);
	-moz-transform: rotate(-180deg);
	-ms-transform: rotate(-180deg);
	-o-transform: rotate(-180deg);
	transform: rotate(-180deg);
	top: 2px; }

.ny-icon--notice-wash {
	background-image: url(../pictures/icons/trading-wash.svg);
	width: 26px;
	height: 30px;
	margin-right: 10px; }

.ny-icon--notice-cart {
	background-image: url(../pictures/icons/trading-cart.svg);
	width: 40px;
	height: 30px;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
	background-position: center center;
	margin-right: 10px; }

.ny-icon--notice-iron {
	background-image: url(../pictures/icons/trading-iron.svg);
	width: 33px;
	height: 30px; }

.ny-icon--sales-currency {
	background-image: url(../pictures/icons/sales-currency.svg);
	width: 10px;
	height: 10px;
	margin-right: -8px;
	top: 2px; }

/*@media only screen and (max-width: 1380px) {
	.ny-icon--edit-color {
		bottom: 7px;
		left: 7px;
		width: 10px;
		height: 10px; }

	.ny-icon--nyp-photo-logo {
		width: 75px; } }
@media only screen and (max-width: 1130px) {
	.ny-icon--edit-info--report + div {
		width: 291px; } }
@media only screen and (max-width: 1060px) {
	.ny-icon--nyp-category {
		background-image: url(../pictures/icons/nav-arrow-right--bold.svg);
		width: 9px;
		float: right; }

	.ny-icon--ny-counter {
		width: 16px;
		height: 16px; }

	.ny-icon--edit-info {
		display: none; }

	.ny-icon--edit-info--send {
		display: inline-block; }

	.ny-icon--edit-info--report {
		display: inline-block; }
	.ny-icon--edit-info--report + div {
		right: -26px; }
	.ny-icon--edit-info--report + div:before {
		right: 28px; }
	.ny-icon--edit-info--report + div:after {
		right: 27px; }

	.ny-icon--edit-info--profile {
		display: inline-block; }

	.ny-icon--edit-nav {
		background-image: url(../pictures/icons/arrow-down--bold.svg);
		width: 14px;
		height: 9px;
		top: 3px;
		right: 5px;
		position: absolute;
		display: block; }

	.ny-icon--edit-color {
		width: 14px;
		height: 14px;
		left: auto;
		bottom: auto;
		right: 90px;
		top: 5px; }

	.ny-icon--trackline-action {
		float: left; }

	.ny-icon--mobile {
		margin-right: 10px;
		-webkit-background-size: auto auto;
		-moz-background-size: auto auto;
		-o-background-size: auto auto;
		background-size: auto auto;
		width: 17px;
		height: 21px;
		background-position: left center; }

	.ny-icon--mobile-man {
		background-image: url(../pictures/icons/nav-user.svg); }

	.ny-icon--mobile-invite {
		background-image: url(../pictures/icons/nav-invite.svg); }

	.ny-icon--mobile-items {
		background-image: url(../pictures/icons/nav-storage.svg); }

	.ny-icon--mobile-orders {
		background-image: url(../pictures/icons/nav-cart.svg); }

	.ny-icon--mobile-sales {
		background-image: url(../pictures/icons/nav-sale.svg); }

	.ny-icon--mobile-feature {
		background-image: url(../pictures/icons/nav-feature.svg); }

	.ny-icon--mobile-trade {
		background-image: url(../pictures/icons/nav-trade.svg); }

	.ny-icon--mobile-blog {
		background-image: url(../pictures/icons/nav-blog.svg); }

	.ny-icon--mobile-guid {
		background-image: url(../pictures/icons/nav-guid.svg); }

	.ny-icon--mobile-how {
		background-image: url(../pictures/icons/nav-notice.svg); }

	.ny-icon--mobile-rule {
		background-image: url(../pictures/icons/nav-rule.svg); }

	.ny-icon--mobile-offer {
		background-image: url(../pictures/icons/nav-offer.svg); }

	.ny-icon--mobile-policy {
		background-image: url(../pictures/icons/nav-policy.svg); }

	.ny-icon--mobile-percent {
		background-image: url(../pictures/icons/nav-percent.svg); }

	.ny-icon--mobile-arrow {
		background-image: url(../pictures/icons/nav-arrow-right--bold.svg);
		width: 9px;
		height: 14px;
		position: absolute;
		right: 0; }

	.ny-icon--mobile-arrow--down {
		background-image: url(../pictures/icons/arrow-down--bold.svg);
		width: 14px;
		height: 9px; }

	.ny-icon--mobile-arrow--up {
		background-image: url(../pictures/icons/arrow-top--bold.svg);
		width: 14px;
		height: 9px; }

	.ny-icon--mobile-arrow--light {
		background-image: url(../pictures/icons/nav-arrow-right--light.svg);
		width: 8px;
		height: 13px; }

	.ny-icon--gallery-addon--nyp-photo {
		width: 41px;
		height: 33px; } }
@media only screen and (max-width: 660px) {
	.ny-icon--warning-photo, .ny-icon--warning-price, .ny-icon--warning-text {
		margin-right: 10px; }

	.ny-icon--trackline-button {
		display: none; }

	.ny-icon--trackline-success {
		width: 19px;
		height: 19px;
		-webkit-border-radius: 50%;
		-moz-border-radius: 50%;
		border-radius: 50%;
		background-image: url(../pictures/icons/checked.svg);
		background-position: center center;
		background-repeat: no-repeat;
		-webkit-background-size: 70% 70%;
		-moz-background-size: 70%;
		-o-background-size: 70%;
		background-size: 70%;
		background-color: #27ae60;
		position: relative;
		top: 5px;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%); }

	.ny-icon--sales-currency {
		float: left; } }
@media only screen and (max-width: 520px) {
	.ny-icon--filters-button {
		float: none;
		-webkit-box-ordinal-group: 0;
		-webkit-order: -1;
		-moz-box-ordinal-group: 0;
		-ms-flex-order: -1;
		order: -1; }

	.ny-icon--return-info {
		display: block;
		margin: 0 auto 10px; }

	.ny-icon--gallery-addon--nyp-photo {
		width: 26px;
		height: 21px; } }
@media only screen and (max-width: 380px) {
	.ny-icon--gallery-addon {
		width: 25px;
		height: 25px; } }*/
.ny-label {
	width: auto;
	display: block; }

.ny-label--nyp-cf, .ny-label--nyp-of, .ny-label--nyp-pf, .ny-label--nyp-photo, .ny-label--nyp-size {
	font-size: 14px;
	color: #333333;
	/*position: absolute;
	bottom: 5px;
	left: 0;*/
	pointer-events: none;
	-webkit-transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);
	-o-transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);
	-moz-transition: all 0.2s cubic-bezier(0, 0, 0.2, 1);
	transition: all 0.2s cubic-bezier(0, 0, 0.2, 1); }

.active.ny-label--nyp-cf, .active.ny-label--nyp-of, .active.ny-label--nyp-pf, .active.ny-label--nyp-photo, .active.ny-label--nyp-size {
	bottom: -webkit-calc(100%);
	bottom: -moz-calc(100%);
	bottom: calc(100%);
	font-size: 12px;
	padding-bottom: 5px; }

.ny-label--nyp-af {
	font-size: 14px;
	color: #333;
	font-weight: 700; }

.ny-label--nyp-calc {
	font-size: 12px;
	color: #bdbdbd;
	margin-bottom: 8px;
	line-height: 1; }

.ny-label--nyp-size {
	bottom: 10px;
	font-size: 16px; }

.ny-label--nyp-pf, .ny-label--nyp-cf {
	bottom: 10px; }

.ny-label--nyp-sf {
	font-size: 14px;
	color: #333;
	font-weight: 700;
	margin-right: 20px; }

.ny-label--nyp-sf--type {
	margin-right: 54px; }

.ny-label--nyp-of {
	bottom: 10px;
	font-size: 12px; }
.ny-label--nyp-of.active {
	font-size: 11px; }

.ny-label--size {
	color: #bdbdbd;
	font-size: 12px;
	margin-bottom: 5px; }

.ny-label--upload {
	color: #202020;
	font-size: 16px;
	line-height: 1.5; }

.ny-label--ask-field {
	display: inline-block;
	color: #bdbdbd;
	font-size: 12px;
	margin-right: 10px; }

.ny-label--ask-field--checkbox {
	font-size: 14px;
	color: #202020;
	position: relative;
	padding-left: 25px;
	cursor: pointer; }
.ny-label--ask-field--checkbox:hover:before {
	background-color: #202020; }
.ny-label--ask-field--checkbox:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	background-color: transparent;
	-webkit-transition: all .2s ease-in;
	-o-transition: all .2s ease-in;
	-moz-transition: all .2s ease-in;
	transition: all .2s ease-in;
	width: 14px;
	height: 14px;
	background-repeat: no-repeat;
	background-position: 50% 50%; }

.ny-label--edit-field {
	color: #bdbdbd;
	font-size: 12px;
	margin-bottom: 5px; }

.ny-label--edit-field--send {
	font-size: 14px;
	margin-bottom: 8px; }

.ny-label--edit-field--error {
	color: #eb5757; }

.ny-label--return-field {
	font-size: 12px;
	color: #bdbdbd;
	margin-bottom: 10px; }

.ny-label--return-check {
	display: inline-block;
	color: #202020;
	font-size: 14px;
	margin-right: 7px; }

.ny-label--return-check--checkbox {
	position: relative;
	padding-left: 25px;
	cursor: pointer; }
.ny-label--return-check--checkbox:hover:before {
	background-color: transparent; }
.ny-label--return-check--checkbox:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	background-color: transparent;
	-webkit-transition: all .2s ease-in;
	-o-transition: all .2s ease-in;
	-moz-transition: all .2s ease-in;
	transition: all .2s ease-in;
	width: 17px;
	height: 17px;
	background-repeat: no-repeat;
	background-position: 50% 50%; }

/*@media only screen and (max-width: 1060px) {
	.ny-label--nyp-af {
		position: absolute;
		font-size: 14px;
		color: #333;
		font-weight: 400;
		opacity: .4;
		top: 50%;
		-webkit-transform: translateY(-50%);
		-moz-transform: translateY(-50%);
		-ms-transform: translateY(-50%);
		-o-transform: translateY(-50%);
		transform: translateY(-50%); }

	.ny-label--nyp-sf {
		position: absolute;
		font-size: 14px;
		color: #333;
		font-weight: 400;
		opacity: .4;
		left: 0; }

	.ny-label--card-tab {
		cursor: pointer;
		font-size: 13px;
		color: #000;
		display: inline-block; }

	.ny-label--card-tab--active {
		font-weight: 700; }

	.ny-label--edit-field {
		font-size: 13px; }

	.ny-label--edit-field--comsize {
		margin-bottom: 15px; }

	.ny-label--edit-field--send {
		position: absolute;
		top: 5px;
		font-size: 14px;
		color: #202020; }

	.ny-label--size {
		margin-bottom: 10px; }

	.ny-label--size--fixed {
		font-size: 14px;
		position: absolute;
		top: 5px;
		color: #202020;
		margin-bottom: 0; }

	.ny-label--visible {
		opacity: 1; } }
@media only screen and (max-width: 520px) {
	.ny-label--edit-field--send {
		position: relative;
		top: 0;
		font-size: 12px;
		color: #bdbdbd; } }
@media only screen and (max-width: 380px) {
	.ny-label--card-tab {
		font-size: 12px; }

	.ny-label--ask-field {
		margin-right: 5px;
		font-size: 10px; }

	.ny-label--ask-field--checkbox {
		margin-right: 0;
		font-size: 12px; }
	.ny-label--ask-field--checkbox:before {
		top: -2px; }

	.ny-edit__fields:nth-child(n+3) > label.ny-label--edit-field-company {
		font-size: 12px;
		color: #bdbdbd;
		position: relative;
		top: 0;
		margin-top: -5px; } }*/
.ny-link {
	width: auto;
	display: block; }

.ny-link--card-empty:active, .ny-link--card-empty:hover {
	border-color: #4f4f4f;
	color: #4f4f4f; }

.ny-link--nyp-calc {
	font-size: 12px;
	text-decoration: underline;
	color: #000;
	display: inline-block;
	float: right; }
.ny-link--nyp-calc:hover {
	text-decoration: none; }

.ny-link--nav {
	font-size: 16px;
	color: #202020;
	font-family: "Open Sans";
	font-weight: 500;
	text-decoration: none;
	border-bottom: 2px solid transparent;
	padding-bottom: 15px; }

.ny-link--nav-active {
	font-weight: 700;
	border-bottom-color: #202020; }

.ny-link--nav:hover {
	border-bottom-color: #202020; }

.ny-link--share {
	display: block;
	text-decoration: none;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	width: 100%;
	height: 100%; }

.ny-link--share-fb {
	background-image: url(../pictures/icons/fb.svg); }

.ny-link--share-vk {
	background-image: url(../pictures/icons/vk.svg); }

.ny-link--share-tm {
	background-image: url(../pictures/icons/tm.svg); }

.ny-link--share-vb {
	background-image: url(../pictures/icons/vb.svg); }

.ny-link--feeds {
	display: inline-block;
	float: right;
	text-decoration: none;
	color: #bdbdbd; }
.ny-link--feeds:hover {
	text-decoration: underline; }

.ny-link--tabs {
	text-decoration: none;
	color: #202020; }
.ny-link--tabs:hover {
	color: #4f4f4f; }

.ny-link--filters {
	text-decoration: none;
	color: #202020; }
.ny-link--filters:hover {
	color: #9d9d9d; }

.ny-link--size {
	display: block;
	text-decoration: none;
	color: #202020;
	font-size: 14px;
	position: relative;
	text-align: center; }
.ny-link--size:hover {
	color: #4f4f4f; }

.ny-link--edit-remove {
	position: absolute;
	top: 30px;
	right: 0;
	display: inline-block;
	color: #202020;
	font-size: 12px; }
.ny-link--edit-remove:hover {
	color: #4f4f4f;
	text-decoration: none; }

.ny-link--edit-remove--mobile {
	display: none; }

.ny-link--card-empty {
	border: 1px solid #202020;
	font-size: 14px;
	text-align: center;
	display: inline-block;
	background-color: transparent;
	padding: 14px 15px;
	color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	text-decoration: none;
	margin-right: 25px; }
.ny-link--card-empty:last-child {
	margin-right: 0; }

.ny-link--edit-comsize {
	text-decoration: none;
	font-size: 14px;
	color: #bdbdbd;
	display: inline-block;
	margin-right: 8px; }
.ny-link--edit-comsize:hover {
	color: #202020; }

.ny-link--trackline {
	font-size: 14px;
	color: #4f4f4f;
	text-decoration: underline;
	margin-left: 14px; }
.ny-link--trackline:hover {
	text-decoration: none; }

/*@media only screen and (max-width: 1380px) {
	.ny-link--nav {
		font-size: 14px;
		padding-bottom: 10px; }

	.ny-link--edit-comsize {
		font-size: 12px;
		margin-right: 5px; }

	.ny-link--trackline {
		font-size: 12px;
		line-height: 16px; } }
@media only screen and (max-width: 1060px) {
	.ny-link--edit-remove {
		display: none; }

	.ny-link--size {
		font-weight: 700; }

	.ny-link--edit-comsize {
		font-size: 14px;
		margin-right: 15px; }

	.ny-link--edit-remove--mobile {
		display: block;
		color: #202020;
		font-size: 14px;
		text-align: center;
		margin-top: 25px; }
	.ny-link--edit-remove--mobile:hover {
		text-decoration: none; }

	.ny-link--trackline {
		width: -webkit-calc(100% - 50px);
		width: -moz-calc(100% - 50px);
		width: calc(100% - 50px);
		float: left; } }
@media only screen and (max-width: 660px) {
	.ny-link--card-empty {
		font-size: 12px;
		padding: 9px 12px;
		margin-right: 15px; }

	.ny-link--trackline {
		margin-bottom: 25px; } }
@media only screen and (max-width: 520px) {
	.ny-link--card-empty {
		padding: 8px 10px;
		margin-right: 10px; } }
@media only screen and (max-width: 380px) {
	.ny-link--card-empty {
		margin-bottom: 10px; } }*/
.ny-list {
	position: relative;
	border: 1px solid #e6e6e6;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	list-style: none;
	padding: 0;
	margin: 0; }
.ny-list::before {
	content: "";
	display: block;
	position: absolute;
	top: 50%;
	right: 12px;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	background: url(../pictures/icons/arrow-down.svg) center center/100% 100% no-repeat;
	width: 9px;
	height: 8px; }

.ny-list__item {
	width: 100%;
	padding: 12px 30px 12px 10px;
	display: block;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	cursor: pointer;
	position: relative; }
.ny-list__item--nyp-calc{
	padding: 8px 30px 7px 10px;
}
.ny-list__name {
	font-size: 14px;
	color: #333;
	font-family: Roboto;
	font-weight: 500;
	line-height: 1; }

.ny-list__sub {
	display: none;
	list-style: none;
	padding: 0;
	margin: 0;
	position: absolute;
	bottom: -1px;
	left: -1px;
	width: -webkit-calc(100% + 2px);
	width: -moz-calc(100% + 2px);
	width: calc(100% + 2px);
	-webkit-transform: translateY(100%);
	-moz-transform: translateY(100%);
	-ms-transform: translateY(100%);
	-o-transform: translateY(100%);
	transform: translateY(100%);
	border: 1px solid #e6e6e6;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	background: #fff;
	padding: 0;
	overflow: auto;
	max-height: 405px;
	z-index: 1; }

.ny-list__item-sub {
	background-color: #fff;
	padding: 7px 12px 8px; }
.ny-list__item-sub:hover {
	background: #e6e6e6; }
.ny-list__item-sub:last-child {
	margin-bottom: 0; }

.ny-list__name-sub {
	font-size: 14px;
	color: #202020;
	background-color: transparent;
	display: block;
	font-family: "Open Sans";
	font-weight: 500; }

.ny-list__name-desc {
	font-size: 10px;
	color: #828282; }

.ny-list--show {
	border-color: #333; }

.ny-list--nyp-sf {
	-webkit-box-flex: 1;
	-webkit-flex-grow: 1;
	-moz-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1; }

.ny-list--nyp-af {
	width: 365px; }

.ny-list__sub .ny-swipe__search {
	margin-bottom: 15px;
	padding: 5px 13px;}
.ny-list__sub .ny-swipe__search i {
	left: 23px; }

.ny-list__item-sub--top {
	padding-top: 10px;
	padding-bottom: 10px; }

/*@media only screen and (max-width: 1060px) {
	.ny-list {
		width: 100%;
		border: 0;
		border-bottom: 1px solid #f0f0f0; }
	.ny-list:before {
		background-image: url(../pictures/icons/nav-arrow-right--light.svg);
		width: 8px;
		height: 13px; }

	.ny-list__item {
		padding-top: 12px;
		padding-bottom: 12px; }

	.ny-list__name {
		display: block;
		text-align: right;
		width: 100%;
		position: absolute;
		top: 50%;
		left: 0;
		-webkit-transform: translateY(-50%);
		-moz-transform: translateY(-50%);
		-ms-transform: translateY(-50%);
		-o-transform: translateY(-50%);
		transform: translateY(-50%);
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		padding-right: 30px;
		color: #bdbdbd;
		font-size: 13px; } }
@media only screen and (max-width: 520px) {
	.ny-list__name {
		max-width: 230px;
		white-space: nowrap;
		overflow: hidden;
		-o-text-overflow: ellipsis;
		text-overflow: ellipsis;
		right: 0;
		left: auto;
		direction: rtl; } }
@media only screen and (max-width: 374px) {
	.ny-list__name {
		max-width: 200px;
		padding-top: 2px; } }*/
.ny-notice {
	background: red;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	color: #fff;
	font-size: 12px;
	width: 17px;
	height: 17px;
	display: inline-block;
	text-align: center;
	position: relative;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-weight: 400; }

.ny-notice--nav {
	margin-left: 5px;
	padding-top: 2px; }

.ny-notice--tabs {
	margin-left: 5px;
	top: -1px;
	padding-top: 2px;
	padding-left: 1.4px;
	padding-right: 1px; }

.ny-notice--tabs-link {
	margin-left: 5px;
	top: 0;
	padding-top: 2px;
	padding-left: 1.4px;
	padding-right: 1px; }

.ny-notice--loader {
	font-size: 16px;
	color: #bdbdbd;
	margin-left: 5px; }

.ny-notice--disable {
	background: 0 0;
	color: #bdbdbd;
	font-size: 16px;
	top: 0; }

.ny-notice--edit {
	display: none; }

.ny-notice--more {
	position: absolute;
	top: -2.5px;
	left: 1px;
	background: #f2f2f2;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	width: 22px;
	height: 22px;
	padding: 2.3px 5.6px 2.8px;
	line-height: 1.1;
	border: 2px solid #fff; }

.ny-notice--more--success {
	background-color: #27ae60;
	background-image: url(../pictures/icons/checked.svg);
	background-repeat: no-repeat;
	background-position: 50% 50%;
	-webkit-background-size: 10px 8px;
	-moz-background-size: 10px 8px;
	-o-background-size: 10px 8px;
	background-size: 10px 8px; }

.ny-notice--more--error {
	width: auto;
	-webkit-border-radius: 3em;
	-moz-border-radius: 3em;
	border-radius: 3em;
	height: auto;
	font-size: 12px;
	background-color: red; }

.ny-notice--trackline-step {
	padding-top: 1px;
	position: absolute;
	margin-left: 5px;
	-webkit-border-radius: 3em;
	-moz-border-radius: 3em;
	border-radius: 3em;
	width: auto;
	padding: 2px 5.9px 2.5px;
	height: auto;
	line-height: 1.1;
	font-size: 12px;
	top: -1.5px; }

/*@media only screen and (max-width: 1380px) {
	.ny-notice--tabs {
		top: 0;
		font-size: 12px;
		margin-left: 3px; }

	.ny-notice--trackline-step {
		padding: 1.3px 3.9px 1.7px;
		line-height: 1;
		font-size: 11px;
		margin-left: 1px; } }
@media only screen and (max-width: 1060px) {
	.ny-notice--card-tab {
		cursor: pointer;
		margin-left: 10px; }

	.ny-notice--edit {
		top: -1px;
		padding-top: 1px;
		right: 30px;
		position: absolute;
		display: block;
		-webkit-border-radius: 3em;
		-moz-border-radius: 3em;
		border-radius: 3em;
		width: auto;
		height: auto;
		padding: 2.5px 6px 3px;
		line-height: 1.1; }

	.ny-notice--mobile {
		padding-top: 1px;
		position: absolute;
		right: 30px;
		-webkit-border-radius: 3em;
		-moz-border-radius: 3em;
		border-radius: 3em;
		width: auto;
		padding: 2.5px 5.5px 3px;
		height: auto;
		line-height: 1.1; } }
@media only screen and (max-width: 660px) {
	.ny-notice--trackline-step {
		padding: 3.5px 6.07px;
		margin-left: -15px;
		top: 5px;
		font-size: 12px; }

	.ny-notice--tabs-link {
		font-size: 12px; } }
@media only screen and (max-width: 380px) {
	.ny-notice {
		width: 15px;
		height: 15px;
		line-height: 15px; }

	.ny-notice--card-tab {
		margin-left: 5px; }

	.ny-notice--edit {
		padding: 1.8px 5px;
		width: auto;
		height: auto;
		line-height: 1.1; }

	.ny-notice--mobile {
		width: auto;
		height: auto;
		padding: 1.5px 5.5px;
		line-height: 1.1; }

	.ny-notice--more {
		width: 22px;
		height: 22px;
		line-height: 1.1; }

	.ny-notice--trackline-step {
		line-height: 1;
		width: auto;
		height: auto; }

	.ny-notice--tabs-link {
		font-size: 11px;
		padding-top: 0; } }*/
.ny-picture {
	width: 100%;
	height: 100%;
	object-fit: cover;}

.ny-picture--banner {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	-o-object-fit: cover;
	object-fit: cover;
	z-index: -2; }

.ny-picture--card {
	margin: 0 auto;
	max-height: 100%; }

.ny-picture--select {
	width: 100%;
	-o-object-fit: cover;
	object-fit: cover; }

.ny-picture--edit {
	margin: 0 auto;
	max-height: 100%; }

.ny-picture--trackline {
	-webkit-filter: blur(4px);
	filter: blur(4px);
	position: relative;
	z-index: -1; }

.ny-select {
	width: auto;
	display: block;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	outline: 0;
	cursor: pointer;
	background: transparent url(../pictures/icons/arrow-down.svg) no-repeat -webkit-calc(100% - 10px) -webkit-calc(50% - 2px);
	background: transparent url(../pictures/icons/arrow-down.svg) no-repeat -moz-calc(100% - 10px) -moz-calc(50% - 2px);
	background: transparent url(../pictures/icons/arrow-down.svg) no-repeat calc(100% - 10px) calc(50% - 2px);
	font-family: "Open Sans";
	font-weight: 500; }

.ny-select--ask-field:focus, .ny-select--ask-field:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-select--ask:focus, .ny-select--ask:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-select--edit-select:focus, .ny-select--edit-select:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-select--size:focus, .ny-select--size:hover {
	border-color: #bdbdbd;
	color: #4f4f4f; }

.ny-select--ask {
	width: 100%;
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 5px 10px;
	font-size: 14px;
	color: #4f4f4f;
	margin-bottom: 30px; }

.ny-select--ask-field {
	width: 35%;
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 5px 10px;
	font-size: 14px;
	color: #4f4f4f;
	display: inline-block;
	float: right; }

.ny-select--size, .ny-select--edit-select {
	width: 100%;
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 5px 10px;
	color: #202020;
	font-size: 14px; }

#ny-field--edit-color {
	padding-left: 35px; }

/*@media only screen and (max-width: 1380px) {
	.ny-select--edit-select {
		font-size: 12px; }

	#ny-field--edit-color {
		padding-left: 22px; } }
@media only screen and (max-width: 1060px) {
	.ny-select--edit-select {
		font-size: 13px;
		border-top: 0;
		border-left: 0;
		border-right: 0;
		padding-bottom: 15px;
		padding-left: 0;
		-moz-text-align-last: right;
		text-align-last: right;
		-ms-text-align-last: right;
		direction: rtl;
		text-align: right;
		padding-right: 25px;
		color: #bdbdbd;
		background-image: url(../pictures/icons/nav-arrow-right--light.svg);
		background-position-y: 5px;
		background-position-x: right;
		background-repeat: no-repeat; }

	.ny-select--edit-select--send {
		border-bottom: 1px solid #e5e5e5; }

	.ny-select--size {
		padding-top: 8px;
		padding-bottom: 8px; }

	.ny-select--size--fixed {
		font-size: 13px;
		border-top: 0;
		border-left: 0;
		border-right: 0;
		padding-bottom: 15px;
		padding-left: 0;
		-moz-text-align-last: right;
		text-align-last: right;
		padding-right: 25px;
		padding-top: 5px;
		color: #bdbdbd;
		background-image: url(../pictures/icons/nav-arrow-right--light.svg);
		background-position-y: 5px;
		background-position-x: right; } }
@media only screen and (max-width: 520px) {
	.ny-select--edit-select {
		padding-right: 15px; }

	.ny-select--edit-select--send {
		text-align: left;
		-moz-text-align-last: left;
		text-align-last: left;
		-ms-text-align-last: left;
		direction: ltr;
		color: #202020; } }
@media only screen and (max-width: 380px) {
	.ny-select--ask-field {
		font-size: 12px; } }*/
.ny-switch, .ny-tooltip {
	width: auto;
	display: block; }

.ny-ask {
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	min-width: 435px;
	padding: 30px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-ask--paid {
	min-width: auto;
	width: 320px; }

.ny-ask--success {
	min-width: auto;
	width: 400px;
	padding: 40px 35px; }

.ny-ask--address {
	min-width: auto;
	width: 415px;
	padding: 40px 35px 60px; }

.ny-ask__desc {
	font-size: 14px;
	color: #000;
	margin-bottom: 25px;
	text-align: center;
	line-height: 1.4; }

.ny-ask__row:first-child:not(.ny-ask__row--dismiss):not(.ny-ask__row--paid):not(.ny-ask__row--success):not(.ny-ask__row--address) {
	margin-bottom: 50px; }

.ny-ask__fields {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 20px; }

.ny-ask__fields-group {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	position: relative; }
.ny-ask__fields-group:nth-child(2) {
	-webkit-box-flex: .4;
	-webkit-flex-grow: .4;
	-moz-box-flex: .4;
	-ms-flex-positive: .4;
	flex-grow: .4; }

.ny-ask__fields-group--disable {
	opacity: .5;
	pointer-events: none; }

.ny-ask__title {
	color: #000;
	font-size: 16px;
	margin-bottom: 35px;
	text-align: center; }

.ny-ask__title--dismiss {
	margin-bottom: 50px; }

.ny-ask__title-paid, .ny-ask__title-success {
	font-size: 16px;
	font-weight: 700;
	margin-bottom: 15px; }

.ny-ask__title-address {
	font-size: 16px;
	font-weight: 400;
	margin-bottom: 30px; }

.ny-ask__action {
	text-align: center; }

.ny-ask__field-caption {
	font-size: 14px;
	color: #202020; }

/*@media only screen and (max-width: 520px) {
	.ny-ask {
		width: 100%;
		margin-top: 0;
		margin-bottom: 0;
		padding: 0;
		max-width: 100%;
		min-width: 100%;
		-webkit-align-self: flex-start;
		-ms-flex-item-align: start;
		align-self: flex-start; }

	.ny-ask__action::after {
		clear: both;
		content: "";
		display: block; }

	.ny-ask__row--dismiss {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 100%;
		-webkit-transform: translate(-50%, -50%);
		-moz-transform: translate(-50%, -50%);
		-ms-transform: translate(-50%, -50%);
		-o-transform: translate(-50%, -50%);
		transform: translate(-50%, -50%); }

	.ny-ask__row:last-child .ny-ask__title {
		text-align: left;
		margin-bottom: 20px; }
	.ny-ask__row:last-child .ny-ask__title-address, .ny-ask__row:last-child .ny-ask__title-paid, .ny-ask__row:last-child .ny-ask__title-success {
		text-align: center; }
	.ny-ask__row:last-child .ny-ask__title-dismiss {
		text-align: center;
		margin-bottom: 50px; } }
@media only screen and (max-width: 380px) {
	.ny-ask__field-caption {
		font-size: 12px; } }*/
.ny-banner {
	position: relative;
	height: 260px; }

.ny-banner__info {
	padding-top: 170px;
	padding-left: 250px;
	padding-right: 30px; }

.ny-banner__info-name {
	font-size: 30px;
	font-family: "Open Sans";
	font-weight: 700;
	color: #fff;
	margin-bottom: 10px; }

.ny-banner__info-item {
	font-size: 12px;
	color: #fff;
	font-family: "Open Sans";
	font-weight: 400;
	margin-right: 40px; }

.ny-banner__info-button {
	display: inline-block;
	float: right;
	cursor: pointer; }

/*@media only screen and (max-width: 1380px) {
	.ny-banner {
		height: 200px; }

	.ny-banner__info {
		padding-top: 125px;
		padding-left: 230px; }

	.ny-banner__info-name {
		font-size: 20px; } }
@media only screen and (max-width: 1060px) {
	.ny-banner {
		display: none; } }*/
.ny-card {
	font-family: "Open Sans";
	font-weight: 500; }

.ny-card__list {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row wrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	margin-bottom: 30px; }

.ny-card__empty-title {
	margin-top: 50px;
	color: #202020;
	text-align: center;
	font-weight: 700;
	font-size: 20px;
	margin-bottom: 35px;
	width: 100%; }

.ny-card__empty-action {
	text-align: center;
	width: 100%; }

.ny-card__item {
	width: -webkit-calc(23.95833%);
	width: -moz-calc(23.95833%);
	width: calc(23.95833%);
	float: left;
	margin-left: 0;
	margin-bottom: 25px;
	margin-left: -webkit-calc(1.38889% + 0px);
	margin-left: -moz-calc(1.38889% + 0px);
	margin-left: calc(1.38889% + 0px); }
.ny-card__item:first-child, .ny-card__item:nth-child(4n+1) {
	margin-left: -webkit-calc(0% + 0px);
	margin-left: -moz-calc(0% + 0px);
	margin-left: calc(0% + 0px); }

.ny-card__item--popup {
	width: 100%;
	margin-bottom: 0;
	cursor: pointer; }

.ny-card__item--more {
	width: -webkit-calc(38.88889%);
	width: -moz-calc(38.88889%);
	width: calc(38.88889%);
	float: left;
	margin-left: 0;
	margin-bottom: 0; }
.ny-card__item--more .ny-card__box {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.ny-card__item--send {
	width: 32%;
	margin-left: 2%;
	margin-bottom: 15px; }
.ny-card__item--send:nth-child(4n+1) {
	margin-left: 2%; }
.ny-card__item--send:first-child, .ny-card__item--send:nth-child(3n+1) {
	margin-left: 0; }

.ny-card__label {
	color: #bdbdbd;
	font-size: 10px;
	margin-bottom: 1.5px; }

.ny-card__feature {
	position: relative;
	margin-bottom: 7px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center; }
.ny-card__feature::after {
	clear: both;
	content: "";
	display: block; }

.ny-card__feature-thumb {
	margin-right: 7px; }

.ny-card__feature-info__name {
	font-size: 14px;
	font-weight: 700;
	color: #333; }

.ny-card__feature-info__status {
	font-size: 11px;
	color: #8e8e93; }

.ny-card__title {
	font-weight: 700;
	font-size: 20px;
	color: #202020;
	width: 100%;
	margin-bottom: 25px; }

.ny-card__tabs {
	display: none; }

.ny-card__box {
	border: 1px solid #f2f2f2;
	-webkit-border-radius: 2px 2px 0 0;
	-moz-border-radius: 2px 2px 0 0;
	border-radius: 2px 2px 0 0;
	height: 100%; }

.ny-card__box--warning {
	border-color: #eb5757; }

.ny-card__box--bold {
	border-width: 2px;
	border-color: #202020;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-card__loadmore {
	text-align: center;
	margin-bottom: 50px; }

.ny-card__image {
	position: relative;
	padding-top: 25px;
	margin-bottom: 20px;
	min-height: 205px; }

.ny-card__image--empty {
	background-color: #fff; }

.ny-card__image--more, .ny-card__image--popup {
	margin-bottom: 10px;
	height: 150px;
	min-height: auto;
	padding-top: 15px; }

.ny-card__link {
	position: absolute;
	top: 10px; }

.ny-card__link--feature {
	left: 20px; }

.ny-card__link--close {
	right: 20px; }

.ny-card__link--close-feature {
	right: 0;
	top: 0; }

.ny-card__link--tracking {
	right: 20px;
	font-size: 12px;
	color: #eb5757;
	text-decoration: none; }

.ny-card__info {
	padding: 0 20px 20px; }

.ny-card__brand {
	color: #202020;
	font-size: 14px;
	margin-bottom: 7px; }

.ny-card__brand--feature, .ny-card__brand--bold {
	font-weight: 700; }

.ny-card__name {
	color: #bdbdbd;
	font-size: 12px;
	margin-bottom: 7px; }

.ny-card__size {
	color: #bdbdbd;
	font-size: 12px;
	margin-bottom: 15px; }

.ny-card__price {
	margin-bottom: 15px; }

.ny-card__price--more, .ny-card__price--popup, .ny-card__price--trading {
	margin-bottom: 0; }

.ny-card__trading-buttons {
	margin-top: 20px; }

.ny-card__price-tracking__group {
	margin-bottom: 5px; }
.ny-card__price-tracking__group::after {
	clear: both;
	content: "";
	display: block; }
.ny-card__price-tracking__group:last-child {
	margin-bottom: 0; }

.ny-card__price-tracking__group--up {
	margin-bottom: 15px; }

.ny-card__price-tracking__label {
	font-size: 12px;
	color: #bdbdbd;
	float: left; }

.ny-card__price-tracking__label--bold {
	color: #202020;
	font-weight: 700; }

.ny-card__price-tracking__price {
	font-size: 14px;
	color: #202020;
	float: right; }

.ny-card__price-tracking__price--current {
	color: #27ae60; }

.ny-card__price-tracking__price--disable {
	color: #bdbdbd; }

.ny-card__price-tracking__price--bold {
	font-weight: 700; }

.ny-card__price-tracking__status {
	font-size: 12px;
	color: #bdbdbd;
	float: right; }

.ny-card__price-tracking__status--done {
	color: #27ae60; }

.ny-card__price-tracking__status--dismiss {
	color: #eb5757; }

.ny-card__price--current {
	font-size: 14px;
	color: #202020;
	margin-right: 10px; }

.ny-card__price--current-bold {
	font-weight: 700; }

.ny-card__price--current-feature {
	font-weight: 700;
	font-size: 16px; }

.ny-card__price--old {
	font-size: 14px;
	color: #bdbdbd;
	text-decoration: line-through; }

.ny-card__price--percent {
	font-size: 12px;
	color: #333;
	display: inline-block;
	float: right;
	line-height: 1.5; }

.ny-card__price--light {
	font-size: 12px;
	color: #6fcf97;
	padding-top: 5px; }

.ny-card__warning {
	margin-bottom: 15px; }

.ny-card__draft {
	font-size: 12px;
	color: #202020;
	margin-bottom: 10px;
	margin-top: -5px; }

.ny-card__dismiss {
	font-size: 12px;
	color: #eb5757;
	line-height: 1.3; }

.ny-card__dismiss--success {
	color: #27ae60; }

.ny-card__moderate-title {
	color: #bdbdbd;
	font-size: 12px;
	margin-bottom: 3px; }

.ny-card__moderate-date {
	color: #bdbdbd;
	font-size: 12px; }

.ny-card__under {
	border: 1px solid #f2f2f2;
	-webkit-border-radius: 0 0 2px 2px;
	-moz-border-radius: 0 0 2px 2px;
	border-radius: 0 0 2px 2px;
	border-top: 0; }

.ny-card__stat {
	padding: 0;
	margin: 0;
	list-style: none;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-align: stretch;
	-webkit-align-items: stretch;
	-moz-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch; }
.ny-card__stat::after {
	clear: both;
	content: "";
	display: block; }

.ny-card__stat-item {
	width: -webkit-calc(33.33333%);
	width: -moz-calc(33.33333%);
	width: calc(33.33333%);
	float: left;
	margin-left: 0;
	margin-left: 0;
	border-right: 1px solid #f2f2f2;
	text-align: center;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: stretch;
	-webkit-align-items: stretch;
	-moz-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch;
	padding: 6px 0 1px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.ny-card__stat-item:last-child {
	border-right: 0; }

.ny-card__stat-text {
	color: #bdbdbd;
	font-size: 12px;
	margin-right: 5px; }

/*@media only screen and (max-width: 1380px) {
	.ny-card__item {
		width: -webkit-calc(32.22222%);
		width: -moz-calc(32.22222%);
		width: calc(32.22222%);
		float: left;
		margin-left: 0;
		margin-left: -webkit-calc(1.38889% + 0px);
		margin-left: -moz-calc(1.38889% + 0px);
		margin-left: calc(1.38889% + 0px); }
	.ny-card__item:nth-child(4n+1) {
		margin-left: -webkit-calc(1.38889% + 0px);
		margin-left: -moz-calc(1.38889% + 0px);
		margin-left: calc(1.38889% + 0px); }
	.ny-card__item:first-child, .ny-card__item:nth-child(3n+1) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }

	.ny-card__item--more {
		width: -webkit-calc(58.33333%);
		width: -moz-calc(58.33333%);
		width: calc(58.33333%);
		float: left;
		margin-left: 0; }

	.ny-card__item--popup {
		width: 100%; }

	.ny-card__title {
		font-size: 16px;
		margin-bottom: 15px;
		padding-top: 15px; } }
@media only screen and (max-width: 1060px) {
	.ny-card__list {
		margin-bottom: 60px; }

	.ny-card__list--moderate {
		display: none; }

	.ny-card__list--moderate-active {
		display: block; }

	.ny-card__title, .ny-card__loadmore {
		display: none; }

	.ny-card__item--more .ny-card__info {
		padding: 0 10px 10px; }

	.ny-card__tabs {
		display: block;
		list-style: none;
		padding: 0;
		margin: 0;
		margin-bottom: 30px; }
	.ny-card__tabs::after {
		clear: both;
		content: "";
		display: block; }

	.ny-card__tabs-item {
		cursor: pointer;
		border-bottom: 1px solid #cdced3;
		float: left;
		width: 50%;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		text-align: center;
		display: block;
		line-height: 17px;
		padding-bottom: 10px;
		position: relative; }
	.ny-card__tabs-item:after {
		content: "";
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 2px;
		background: #000;
		display: none; }

	.ny-card__tabs-item--active:after {
		display: block; } }
@media only screen and (max-width: 800px) {
	.ny-card__info {
		padding: 0 15px 15px; }

	.ny-card__image {
		padding-top: 15px; } }
@media only screen and (max-width: 767px) {
	.ny-card__item--more {
		width: -webkit-calc(33.33333%);
		width: -moz-calc(33.33333%);
		width: calc(33.33333%);
		float: left;
		margin-left: 0; } }
@media only screen and (max-width: 660px) {
	.ny-card__image {
		min-height: 100%;
		height: 170px;
		margin-bottom: 15px; }

	.ny-card__link--feature {
		left: 10px; }

	.ny-card__link--close {
		right: 10px; }

	.ny-card__link--close-feature {
		right: 0; }

	.ny-card__link--tracking {
		right: 10px; }

	.ny-card__info {
		padding: 0 10px 10px; }

	.ny-card__brand {
		font-size: 13px;
		margin-bottom: 4px; }

	.ny-card__name, .ny-card__size {
		margin-bottom: 5px; }

	.ny-card__price {
		margin-bottom: 10px; }

	.ny-card__price--current {
		font-size: 12px; }

	.ny-card__price--current-feature {
		font-size: 14px; }

	.ny-card__price--old {
		font-size: 12px; }

	.ny-card__price-tracking__price {
		float: left; }

	.ny-card__price-tracking__label {
		margin-right: 5px; }

	.ny-card__item {
		margin-bottom: 5px; }

	.ny-card__item--more {
		width: -webkit-calc(44.44444%);
		width: -moz-calc(44.44444%);
		width: calc(44.44444%);
		float: left;
		margin-left: 0;
		margin-bottom: 0; }
	.ny-card__item--more .ny-card__image {
		min-height: auto; }

	.ny-card__item--send {
		width: 49%;
		margin-left: 2%;
		margin-bottom: 10px; }
	.ny-card__item--send:nth-child(2n+1) {
		margin-left: 0; }
	.ny-card__item--send:nth-child(3n+1) {
		margin-left: 2%; }
	.ny-card__item--send:first-child {
		margin-left: 0; }
	.ny-card__item--send .ny-card__image {
		min-height: auto; }

	.ny-card__empty-title {
		margin-top: 30px;
		margin-bottom: 25px;
		font-size: 18px; }

	.ny-card__warning {
		margin-bottom: 10px; } }
@media only screen and (max-width: 520px) {
	.ny-card__item {
		width: -webkit-calc(49.30556%);
		width: -moz-calc(49.30556%);
		width: calc(49.30556%);
		float: left;
		margin-left: 0;
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }
	.ny-card__item:nth-child(4n+1), .ny-card__item:first-child, .ny-card__item:nth-child(3n+1) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }
	.ny-card__item:nth-child(2n) {
		margin-left: -webkit-calc(1.38889% + 0px);
		margin-left: -moz-calc(1.38889% + 0px);
		margin-left: calc(1.38889% + 0px); }

	.ny-card__item--more {
		width: -webkit-calc(61.11111%);
		width: -moz-calc(61.11111%);
		width: calc(61.11111%);
		float: left;
		margin-left: 0; }

	.ny-card__item--popup {
		width: 100%; }

	.ny-card__list {
		margin-bottom: 30px; }

	.ny-card__image {
		height: 140px; }

	.ny-card__loadmore {
		margin-top: 0; }

	.ny-card__empty-title {
		font-size: 16px; }

	.ny-card__tabs {
		margin-bottom: 10px; }

	.ny-card__feature-info__name {
		font-size: 11px; }

	.ny-card__feature-info__status {
		font-size: 10px; }

	.ny-card__price-tracking__price {
		font-size: 12px; } }
@media only screen and (max-width: 374px) {
	.ny-card__item {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }
	.ny-card__item:nth-child(2n) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }

	.ny-card__item--more {
		width: -webkit-calc(66.66667%);
		width: -moz-calc(66.66667%);
		width: calc(66.66667%);
		float: left;
		margin-left: 0; }
	.ny-card__item--more .ny-card__brand {
		font-size: 12px; }
	.ny-card__item--more .ny-card__price {
		margin-bottom: 0; }

	.ny-card__item--send {
		width: 80%;
		margin-left: auto;
		margin-right: auto;
		margin-bottom: 10px; }
	.ny-card__item--send:first-child, .ny-card__item--send:nth-child(2n), .ny-card__item--send:nth-child(2n+1), .ny-card__item--send:nth-child(3n+1) {
		margin-left: auto;
		margin-right: auto; }

	.ny-card__empty-action {
		margin-bottom: -10px; }

	.ny-card__price-tracking__price {
		float: right;
		font-size: 14px; } }*/
.ny-edit {
	padding: 40px 30px;
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	width: 1320px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-edit__box {
	position: relative; }
.ny-edit__box::after {
	clear: both;
	content: "";
	display: block; }

.ny-edit__header {
	font-size: 30px;
	text-transform: uppercase;
	color: #202020;
	margin-bottom: 30px; }

.ny-edit__close {
	position: absolute;
	top: 0;
	right: 0;
	width: 16px;
	height: 16px;
	-webkit-transform: translateY(-100%);
	-moz-transform: translateY(-100%);
	-ms-transform: translateY(-100%);
	-o-transform: translateY(-100%);
	transform: translateY(-100%); }

.ny-edit__close--report {
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%); }

.ny-edit__close--followers {
	-webkit-transform: translateY(0);
	-moz-transform: translateY(0);
	-ms-transform: translateY(0);
	-o-transform: translateY(0);
	transform: translateY(0);
	top: 3px; }

.ny-edit__content::after {
	clear: both;
	content: "";
	display: block; }

.ny-edit__col {
	width: -webkit-calc(48.61111%);
	width: -moz-calc(48.61111%);
	width: calc(48.61111%);
	float: left;
	margin-left: 0; }
.ny-edit__col:nth-child(2n) {
	margin-left: -webkit-calc(2.77778% + 0px);
	margin-left: -moz-calc(2.77778% + 0px);
	margin-left: calc(2.77778% + 0px); }
.ny-edit__col:nth-child(2n) > .ny-edit__row:nth-child(3) .ny-edit__group:first-child .ny-edit__title {
	margin-bottom: 20px; }
.ny-edit__col:nth-child(2n) > .ny-edit__row:nth-child(3) .ny-edit__group:last-child .ny-edit__title {
	margin-bottom: 15px;
	margin-top: 30px; }

.ny-edit__group {
	width: -webkit-calc(48.61111%);
	width: -moz-calc(48.61111%);
	width: calc(48.61111%);
	float: left;
	margin-left: 0;
	position: relative; }
.ny-edit__group:nth-child(2n) {
	margin-left: -webkit-calc(2.77778% + 0px);
	margin-left: -moz-calc(2.77778% + 0px);
	margin-left: calc(2.77778% + 0px); }

.ny-edit__group--blur {
	pointer-events: none;
	opacity: .3; }

.ny-edit__error {
	border: 1px solid #eb5757;
	-webkit-box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
	-moz-box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
	box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	font-size: 12px;
	color: #eb5757;
	padding: 8px 15px;
	background: #fff;
	position: relative;
	top: 30px;
	z-index: 2;
	line-height: 1.3; }

.ny-edit__error-label:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	-webkit-transform: translate(-50%, 50%) rotate(-45deg);
	-moz-transform: translate(-50%, 50%) rotate(-45deg);
	-ms-transform: translate(-50%, 50%) rotate(-45deg);
	-o-transform: translate(-50%, 50%) rotate(-45deg);
	transform: translate(-50%, 50%) rotate(-45deg);
	width: 12px;
	height: 12px;
	border: 1px solid transparent;
	border-bottom-color: #eb5757;
	border-left-color: #eb5757;
	background: #fff; }

.ny-edit__error--desc {
	top: 30px; }
.ny-edit__error--desc:before {
	left: 65%; }

.ny-edit__error--price {
	top: 125px; }
.ny-edit__error--price:before {
	left: 15%; }

.ny-edit__info-more {
	position: absolute;
	top: -10px;
	right: 0;
	font-size: 12px;
	color: #4f4f4f;
	padding: 10px;
	background: #fff;
	-webkit-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	-moz-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #e5e5e5;
	-webkit-transform: translateY(-100%);
	-moz-transform: translateY(-100%);
	-ms-transform: translateY(-100%);
	-o-transform: translateY(-100%);
	transform: translateY(-100%);
	display: none; }
.ny-edit__info-more:after {
	content: "";
	position: absolute;
	bottom: 0;
	right: 4px;
	display: block;
	border: 10px solid transparent;
	border-left-width: 6px;
	border-right-width: 6px;
	border-top-width: 12px;
	-webkit-transform: translateY(100%);
	-moz-transform: translateY(100%);
	-ms-transform: translateY(100%);
	-o-transform: translateY(100%);
	transform: translateY(100%); }
.ny-edit__info-more:before {
	content: "";
	position: absolute;
	bottom: 0;
	right: 4px;
	display: block;
	border: 10px solid transparent;
	border-left-width: 6px;
	border-right-width: 6px;
	border-top-width: 12px;
	-webkit-transform: translateY(100%);
	-moz-transform: translateY(100%);
	-ms-transform: translateY(100%);
	-o-transform: translateY(100%);
	transform: translateY(100%);
	border-top-color: #fff;
	z-index: 1; }
.ny-edit__info-more:after {
	border-left-width: 7px;
	border-right-width: 7px;
	border-top-width: 13px;
	border-top-color: #e5e5e5;
	right: 3px; }

.ny-edit__title {
	font-size: 20px;
	color: #202020;
	margin-bottom: 35px; }

.ny-edit__title--blur {
	pointer-events: none;
	opacity: .7; }

.ny-edit__mobile-title {
	display: none; }

.ny-edit__gallery-guid {
	color: #bdbdbd;
	font-size: 12px;
	width: 97%;
	clear: both;
	line-height: 1.5;
	margin-bottom: -10px;
	position: relative;
	top: -25px; }

.ny-edit__gallery {
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 580px;
	overflow: hidden;
	overflow-y: scroll; }
.ny-edit__gallery::-webkit-scrollbar {
	width: 5px;
	margin-left: 15px; }
.ny-edit__gallery::-webkit-scrollbar-track {
	background: #f2f2f2;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	z-index: 10; }
.ny-edit__gallery::-webkit-scrollbar-thumb {
	background-color: #4f4f4f;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	z-index: 10; }

.ny-edit__gallery-item {
	margin-bottom: 25px; }
.ny-edit__gallery-item::after {
	clear: both;
	content: "";
	display: block; }
.ny-edit__gallery-item:last-child {
	margin-bottom: 0; }

.ny-edit__gallery-col {
	width: -webkit-calc(45.83333%);
	width: -moz-calc(45.83333%);
	width: calc(45.83333%);
	float: left;
	margin-left: 0; }
.ny-edit__gallery-col:nth-child(2) {
	margin-left: -webkit-calc(5.55556% + 0px);
	margin-left: -moz-calc(5.55556% + 0px);
	margin-left: calc(5.55556% + 0px); }
.ny-edit__gallery-col:nth-child(2) > .ny-edit__gallery-label {
	opacity: 0; }

.ny-edit__gallery-col--form {
	border: 1.5px dashed #e5e5e5;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	min-height: 170px;
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.ny-edit__gallery-col--form:nth-child(2) {
	margin-left: -webkit-calc(0% + 0px);
	margin-left: -moz-calc(0% + 0px);
	margin-left: calc(0% + 0px);
	margin-bottom: 15px; }
.ny-edit__gallery-col--form:nth-child(3) {
	margin-bottom: 15px; }
.ny-edit__gallery-col--form:nth-child(4) {
	clear: both; }
.ny-edit__gallery-col--form:nth-child(3), .ny-edit__gallery-col--form:nth-child(5) {
	margin-left: -webkit-calc(5.55556% + 0px);
	margin-left: -moz-calc(5.55556% + 0px);
	margin-left: calc(5.55556% + 0px); }

.ny-edit__gallery-addon {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row wrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center; }

.ny-edit__gallery-addon__caption {
	color: #d6d6d8;
	font-size: 16px;
	display: block;
	width: 100%;
	text-align: center; }

.ny-edit__gallery-title {
	font-size: 14px;
	color: #bdbdbd;
	margin-bottom: 15px; }

.ny-edit__gallery-label {
	color: #bdbdbd;
	font-size: 14px;
	margin-bottom: 5px;
	display: inline-block;
	min-height: 15px; }

.ny-edit__gallery-picture {
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	position: relative;
	height: 230px; }

.ny-edit__gallery-picture--error {
	border-color: #eb5757; }

.ny-edit__gallery-caption {
	color: #000;
	font-size: 11px;
	position: absolute;
	bottom: 10px;
	display: block;
	width: 100%;
	text-align: center; }

.ny-edit__fields {
	margin-bottom: 25px;
	position: relative; }
.ny-edit__fields::after {
	clear: both;
	content: "";
	display: block; }

.ny-edit__fields-group {
	width: -webkit-calc(48.61111%);
	width: -moz-calc(48.61111%);
	width: calc(48.61111%);
	float: left;
	margin-left: 0; }
.ny-edit__fields-group:nth-child(2n) {
	margin-left: -webkit-calc(2.77778% + 0px);
	margin-left: -moz-calc(2.77778% + 0px);
	margin-left: calc(2.77778% + 0px); }

.ny-edit__row {
	margin-bottom: 25px; }
.ny-edit__row::after {
	clear: both;
	content: "";
	display: block; }
.ny-edit__row:last-child {
	margin-bottom: 0; }

.ny-edit__fields-row {
	margin-bottom: 23px; }
.ny-edit__fields-row::after {
	clear: both;
	content: "";
	display: block; }
.ny-edit__fields-row:last-child {
	margin-bottom: 0; }

.ny-edit__fields-row--collapse {
	margin-bottom: 10px; }

.ny-edit__fields-row--collapse-company {
	position: relative;
	margin-bottom: 20px; }

.ny-edit__fields-info {
	width: -webkit-calc(83.33333%);
	width: -moz-calc(83.33333%);
	width: calc(83.33333%);
	float: left;
	margin-left: 0; }

.ny-edit__fields-name {
	font-size: 14px;
	color: #202020;
	margin-right: 9px;
	font-weight: 400; }

.ny-edit__fields-caption {
	color: #bdbdbd;
	font-size: 14px; }

.ny-edit__fields-trigger {
	width: -webkit-calc(16.66667%);
	width: -moz-calc(16.66667%);
	width: calc(16.66667%);
	float: left;
	margin-left: 0; }

.ny-edit__fields-col {
	width: -webkit-calc(36.11111%);
	width: -moz-calc(36.11111%);
	width: calc(36.11111%);
	float: left;
	margin-left: 0; }
.ny-edit__fields-col:nth-child(2n) {
	margin-left: -webkit-calc(13.88889% + 0px);
	margin-left: -moz-calc(13.88889% + 0px);
	margin-left: calc(13.88889% + 0px); }

.ny-edit__area {
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 10px 40px 35px 15px;
	width: 100%;
	resize: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 200px;
	line-height: 1.5;
	font-size: 14px;
	color: #202020;
	outline: 0;
	font-family: "Open Sans";
	font-weight: 500; }
.ny-edit__area:focus, .ny-edit__area:hover {
	color: #4f4f4f;
	border-color: #bdbdbd; }

.ny-edit__area--error {
	border-color: #eb5757; }

.ny-edit__area-count {
	font-size: 14px;
	color: #bdbdbd;
	position: absolute;
	bottom: 15px;
	left: 15px; }

.ny-edit__caption {
	font-size: 14px;
	color: #202020;
	line-height: 1.4;
	margin-bottom: 30px; }

.ny-edit__profit {
	font-size: 16px;
	color: #6fcf97;
	margin-top: -6.5px; }

/*@media only screen and (max-width: 1380px) {
	.ny-edit {
		width: 1024px;
		-webkit-align-self: flex-start;
		-ms-flex-item-align: start;
		align-self: flex-start;
		margin-top: 50px;
		margin-bottom: 50px; }

	.ny-edit__title {
		margin-bottom: 25px; }

	.ny-edit__gallery-picture {
		height: 190px; }

	.ny-edit__fields {
		margin-bottom: 15px; }

	.ny-edit__fields-name, .ny-edit__fields-caption {
		font-size: 12px; }

	.ny-edit__fields-row {
		margin-bottom: 15px; }

	.ny-edit__fields-row--collapse {
		margin-bottom: 13px; }

	.ny-edit__fields-col {
		width: -webkit-calc(47.22222%);
		width: -moz-calc(47.22222%);
		width: calc(47.22222%);
		float: left;
		margin-left: 0; }
	.ny-edit__fields-col:nth-child(2n) {
		margin-left: -webkit-calc(5.55556% + 0px);
		margin-left: -moz-calc(5.55556% + 0px);
		margin-left: calc(5.55556% + 0px); }

	.ny-edit__area {
		padding: 10px 25px 15px 15px;
		font-size: 12px;
		height: 190px; }

	.ny-edit__profit {
		font-size: 14px; }

	.ny-edit__gallery {
		max-height: 519px; }

	.ny-edit__gallery-label {
		font-size: 12px;
		min-height: 13px; }

	.ny-edit__gallery-guid {
		top: -15px; } }
@media only screen and (max-width: 1060px) {
	.ny-edit__close, .ny-edit__header {
		display: none; }

	.ny-edit {
		width: 100%;
		margin-top: 0;
		margin-bottom: 0;
		padding: 0; }

	.ny-edit__col {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0; }
	.ny-edit__col > .ny-edit__title {
		display: none; }
	.ny-edit__col:nth-child(2n) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }
	.ny-edit__col:nth-child(2n) > .ny-edit__row:nth-child(3) .ny-edit__group:first-child:not(.ny-edit__group--show) .ny-edit__title {
		margin-bottom: 13px; }

	.ny-edit__fields-group {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		position: relative;
		margin-bottom: 10px; }
	.ny-edit__fields-group:nth-child(2n) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px);
		margin-bottom: 0; }

	.ny-edit__fields-col {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		margin-bottom: 20px; }
	.ny-edit__fields-col:nth-child(2n) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }

	.ny-edit__gallery-addon__caption {
		font-size: 14px; }

	.ny-edit__group {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		border-bottom: .5px solid #efeff4; }
	.ny-edit__group:nth-child(2n) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }
	.ny-edit__group:not(.ny-edit__group--show) > :not(.ny-edit__title) {
		display: none;
		margin-bottom: 0; }

	.ny-edit__group--show {
		margin-top: 30px;
		border-bottom-width: 7px; }
	.ny-edit__group--show > :not(.ny-edit__mobile-title), .ny-edit__group--show > :not(.ny-edit__title) {
		display: block; }
	.ny-edit__group--show > .ny-edit__mobile-title, .ny-edit__group--show > .ny-edit__title {
		margin-bottom: 20px;
		margin-top: 0; }
	.ny-edit__group--show > .ny-edit__mobile-title .ny-icon--edit-nav, .ny-edit__group--show > .ny-edit__title .ny-icon--edit-nav {
		background-image: url(../pictures/icons/arrow-top--bold.svg); }

	.ny-edit__mobile-title, .ny-edit__title {
		font-weight: 700;
		margin-bottom: 13px;
		margin-top: 13px;
		font-size: 14px;
		position: relative;
		cursor: pointer; }

	.ny-edit__fields {
		margin-bottom: 10px;
		position: relative; }
	.ny-edit__fields:nth-child(n+3) .ny-label--edit-field {
		position: absolute;
		top: 5px;
		font-size: 14px;
		color: #202020; }
	.ny-edit__fields:last-child > * {
		border-bottom: 0; }

	.ny-edit__gallery {
		max-height: 100%;
		overflow: hidden;
		clear: both; }
	.ny-edit__gallery::after {
		clear: both;
		content: "";
		display: block; }

	.ny-edit__gallery-item {
		width: -webkit-calc(47.91667%);
		width: -moz-calc(47.91667%);
		width: calc(47.91667%);
		float: left;
		margin-left: 0; }
	.ny-edit__gallery-item:nth-child(2n) {
		margin-left: -webkit-calc(4.16667% + 0px);
		margin-left: -moz-calc(4.16667% + 0px);
		margin-left: calc(4.16667% + 0px); }
	.ny-edit__gallery-item:last-child {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0; }

	.ny-edit__gallery-col {
		width: -webkit-calc(48.61111%);
		width: -moz-calc(48.61111%);
		width: calc(48.61111%);
		float: left;
		margin-left: 0; }
	.ny-edit__gallery-col:nth-child(2) {
		margin-left: -webkit-calc(2.77778% + 0px);
		margin-left: -moz-calc(2.77778% + 0px);
		margin-left: calc(2.77778% + 0px); }

	.ny-edit__gallery-col--form {
		width: -webkit-calc(23.22222%);
		width: -moz-calc(23.22222%);
		width: calc(23.22222%);
		float: left;
		margin-left: 0;
		height: 220px; }
	.ny-edit__gallery-col--form:nth-child(2) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }
	.ny-edit__gallery-col--form:nth-child(4) {
		margin-left: -webkit-calc(4.16667% + 0px);
		margin-left: -moz-calc(4.16667% + 0px);
		margin-left: calc(4.16667% + 0px);
		clear: none; }
	.ny-edit__gallery-col--form:nth-child(3), .ny-edit__gallery-col--form:nth-child(5) {
		margin-left: -webkit-calc(1.38889% + 0px);
		margin-left: -moz-calc(1.38889% + 0px);
		margin-left: calc(1.38889% + 0px); }

	.ny-edit__gallery-info {
		display: none; }

	.ny-edit__gallery-guid {
		width: -webkit-calc(47.22222%);
		width: -moz-calc(47.22222%);
		width: calc(47.22222%);
		float: left;
		margin-left: 0;
		margin-bottom: 20px;
		top: 0; }

	.ny-edit__gallery-picture {
		height: 210px; }

	.ny-edit__gallery-label {
		font-size: 14px;
		min-height: 15px; }

	.ny-edit__gallery-title {
		margin-bottom: 5px; }

	.ny-edit__row {
		margin-bottom: 0; }
	.ny-edit__row:nth-child(2) .ny-edit__group:first-child {
		border-bottom: 0; }
	.ny-edit__row:nth-child(2) .ny-edit__group:first-child .ny-edit__fields-row:last-child {
		margin-bottom: 0;
		border-bottom: 0; }
	.ny-edit__row:nth-child(2) .ny-edit__group:last-child {
		margin-top: 0; }
	.ny-edit__row:nth-child(2) .ny-edit__group:last-child .ny-edit__fields-row {
		padding-bottom: 0;
		margin-bottom: 0;
		border-bottom: 0; }
	.ny-edit__row:nth-child(2) .ny-edit__group:last-child .ny-edit__fields-row:first-child {
		margin-bottom: 20px; }
	.ny-edit__row:nth-child(2) .ny-edit__group:last-child .ny-edit__fields-row:last-child {
		margin-bottom: 30px; }
	.ny-edit__row:nth-child(3) {
		margin-bottom: 75px; }
	.ny-edit__row:nth-child(3) .ny-edit__group--show {
		padding-bottom: 30px;
		margin-bottom: 30px; }
	.ny-edit__row:nth-child(3) .ny-edit__group:first-child {
		margin-bottom: 30px; }
	.ny-edit__row:nth-child(3) .ny-edit__group:last-child {
		width: -webkit-calc(47.22222%);
		width: -moz-calc(47.22222%);
		width: calc(47.22222%);
		float: left;
		margin-left: 0;
		margin: 0 auto;
		float: none;
		border-bottom: 0; }
	.ny-edit__row:nth-child(3) .ny-edit__group:last-child > * {
		display: block; }
	.ny-edit__row:nth-child(4) {
		margin-bottom: 30px; }
	.ny-edit__row:nth-child(4) .ny-edit__group {
		width: -webkit-calc(47.22222%);
		width: -moz-calc(47.22222%);
		width: calc(47.22222%);
		float: left;
		margin-left: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child {
		border-bottom: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child > * {
		display: block; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-notice--edit {
		left: 55px;
		right: auto; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__caption {
		margin-bottom: 25px; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row {
		border-bottom: 0;
		padding-bottom: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col {
		width: -webkit-calc(30.55556%);
		width: -moz-calc(30.55556%);
		width: calc(30.55556%);
		float: left;
		margin-left: 0;
		margin-bottom: 15px; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col:nth-child(2n) {
		margin-left: -webkit-calc(8.33333% + 0px);
		margin-left: -moz-calc(8.33333% + 0px);
		margin-left: calc(8.33333% + 0px); }
	.ny-edit__row:nth-child(4) .ny-edit__group:nth-child(2n) {
		margin-left: -webkit-calc(5.55556% + 0px);
		margin-left: -moz-calc(5.55556% + 0px);
		margin-left: calc(5.55556% + 0px); }
	.ny-edit__row:nth-child(4) .ny-edit__group:nth-child(2n) .ny-edit__caption {
		margin-bottom: 15px;
		margin-top: 35px; }
	.ny-edit__row:nth-child(4) .ny-edit__group > * {
		display: block; }

	.ny-edit__fields-trigger {
		width: auto;
		float: right; }

	.ny-edit__profit {
		font-size: 16px;
		clear: both; }

	.ny-edit__fields-row {
		padding-bottom: 20px;
		margin-bottom: 20px;
		border-bottom: 1px solid #e5e5e5; }
	.ny-edit__fields-row .ny-label--edit-field {
		font-size: 12px; }
	.ny-edit__fields-row .ny-field--edit-field {
		padding-bottom: 5px; }
	.ny-edit__fields-row:last-child {
		margin-bottom: 30px; }

	.ny-edit__fields-name, .ny-edit__fields-caption {
		font-size: 14px; }

	.ny-edit__area {
		height: 95px;
		font-size: 14px;
		padding: 10px; }

	.ny-edit__area-count {
		bottom: 40px;
		left: 10px; }

	.ny-edit__error {
		position: absolute;
		left: 0;
		top: 70px;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		width: 100%; }
	.ny-edit__error ~ .ny-edit__gallery-guid {
		margin-bottom: 50px; }

	.ny-edit__error--desc {
		top: -25px; }
	.ny-edit__error--desc ~ .ny-edit__title {
		margin-bottom: 85px; }

	.ny-edit__error--price {
		top: -17px; }
	.ny-edit__error--price ~ .ny-edit__caption {
		margin-bottom: 50px !important; } }
@media only screen and (max-width: 800px) {
	.ny-edit__gallery-label, .ny-edit__gallery-title {
		font-size: 12px; }

	.ny-edit__area {
		line-height: 1.2;
		font-size: 13px; }

	.ny-edit__error--price {
		top: -30px; }
	.ny-edit__error--price ~ .ny-edit__caption {
		margin-bottom: 60px !important; }

	.ny-edit__caption {
		font-size: 13px; }

	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col {
		width: -webkit-calc(38.88889%);
		width: -moz-calc(38.88889%);
		width: calc(38.88889%);
		float: left;
		margin-left: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col:nth-child(2n) {
		margin-left: -webkit-calc(8.33333% + 0px);
		margin-left: -moz-calc(8.33333% + 0px);
		margin-left: calc(8.33333% + 0px); }
	.ny-edit__row:nth-child(4) .ny-edit__group:nth-child(2n) .ny-edit__caption {
		margin-bottom: 10px; } }
@media only screen and (max-width: 660px) {
	.ny-edit__group--show > .ny-edit__mobile-title, .ny-edit__group--show > .ny-edit__title {
		margin-bottom: 15px; }

	.ny-edit__row:nth-child(3) {
		margin-bottom: 15px; }
	.ny-edit__row:nth-child(3) .ny-edit__group:last-child .ny-edit__title {
		margin-top: 0; }
	.ny-edit__row:nth-child(3) .ny-edit__group:last-child .ny-edit__fields-row .ny-edit__fields-col {
		margin-bottom: 10px; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col {
		width: -webkit-calc(47.22222%);
		width: -moz-calc(47.22222%);
		width: calc(47.22222%);
		float: left;
		margin-left: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col:nth-child(2n) {
		margin-left: -webkit-calc(5.55556% + 0px);
		margin-left: -moz-calc(5.55556% + 0px);
		margin-left: calc(5.55556% + 0px); }

	.ny-edit__gallery-guid {
		margin-bottom: 15px;
		width: -webkit-calc(69.44444%);
		width: -moz-calc(69.44444%);
		width: calc(69.44444%);
		float: left;
		margin-left: 0; }

	.ny-edit__gallery-item {
		margin-bottom: 20px; }

	.ny-edit__gallery-col--form {
		height: 160px;
		min-height: 100%; }

	.ny-edit__error--desc ~ .ny-edit__title {
		margin-bottom: 75px; }

	.ny-edit__error--price {
		top: -45px; }
	.ny-edit__error--price ~ .ny-edit__caption {
		margin-bottom: 80px !important; } }
@media only screen and (max-width: 520px) {
	.ny-edit__gallery-item {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0; }
	.ny-edit__gallery-item:nth-child(2n) {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }

	.ny-edit__gallery {
		margin-bottom: 30px; }

	.ny-edit__gallery-addon__caption {
		font-size: 12px; }

	.ny-edit__gallery-col--form {
		height: 110px; }
	.ny-edit__gallery-col--form:before {
		width: 34px;
		height: 28px; }
	.ny-edit__gallery-col--form:nth-child(2), .ny-edit__gallery-col--form:nth-child(3) {
		margin-bottom: 0; }

	.ny-edit__fields-row {
		padding-bottom: 15px;
		margin-bottom: 15px; }

	.ny-edit__fields-col {
		margin-bottom: 15px; }

	.ny-edit__area {
		height: 110px; }

	.ny-edit__gallery-guid {
		width: 100%; }

	.ny-edit__row:nth-child(4) .ny-edit__group {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		margin-bottom: 15px; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col {
		width: -webkit-calc(44.44444%);
		width: -moz-calc(44.44444%);
		width: calc(44.44444%);
		float: left;
		margin-left: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:first-child .ny-edit__fields-row .ny-edit__fields-col:nth-child(2n) {
		margin-left: -webkit-calc(5.55556% + 0px);
		margin-left: -moz-calc(5.55556% + 0px);
		margin-left: calc(5.55556% + 0px); }
	.ny-edit__row:nth-child(4) .ny-edit__group:last-child {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px);
		margin-bottom: 0; }
	.ny-edit__row:nth-child(4) .ny-edit__group:last-child .ny-edit__caption {
		margin-top: 10px; }

	.ny-edit__error {
		top: 80px; }

	.ny-edit__error--desc {
		top: -40px; }
	.ny-edit__error--desc ~ .ny-edit__title {
		margin-bottom: 90px; }

	.ny-edit__error--price {
		top: -30px; }

	.ny-edit__row:nth-child(3) .ny-edit__group:last-child {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0; } }
@media only screen and (max-width: 380px) {
	.ny-edit__area {
		height: 125px; }

	.ny-edit__gallery-col--form {
		width: 24%;
		margin-left: 0;
		margin-right: 1.3%; }
	.ny-edit__gallery-col--form:nth-child(2), .ny-edit__gallery-col--form:nth-child(3), .ny-edit__gallery-col--form:nth-child(4) {
		margin-left: 0; }
	.ny-edit__gallery-col--form:last-child {
		margin-right: 0;
		margin-left: 0; }

	.ny-edit__gallery-addon__caption {
		font-size: 10px; } }*/
.ny-feeds {
	border: 1px solid #eaeaea; }

.ny-feeds__block {
	padding: 20px 25px 20px 20px;
	border-bottom: 1px solid #eaeaea; }
.ny-feeds__block:last-child {
	border-bottom: 0; }

.ny-feeds__header {
	margin-bottom: 15px;
	font-size: 16px;
	font-family: "Open Sans";
	font-weight: 500;
	color: #202020; }

.ny-feeds__header-name {
	display: inline-block; }

.ny-feeds__header-count {
	color: #bdbdbd;
	margin-right: 5px;
	display: inline-block; }

.ny-feeds__list {
	list-style: none;
	margin: 0;
	padding: 0; }

.ny-feeds__list--followers {
	max-height: 455px;
	overflow: hidden;
	overflow-y: scroll;
	padding-right: 15px; }
.ny-feeds__list--followers::-webkit-scrollbar {
	width: 5px;
	margin-left: 15px; }
.ny-feeds__list--followers::-webkit-scrollbar-track {
	background: #f2f2f2;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	z-index: 10; }
.ny-feeds__list--followers::-webkit-scrollbar-thumb {
	background-color: #4f4f4f;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	z-index: 10; }

.ny-feeds__person {
	margin-bottom: 15px;
	font-family: "Open Sans";
	font-weight: 500; }

.ny-feeds__person--followers {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }
.ny-feeds__person--followers .ny-feeds__link {
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }

.ny-feeds__person:last-child {
	margin-bottom: 0; }

.ny-feeds__link {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	text-decoration: none;
	color: inherit; }
.ny-feeds__link:hover .ny-feeds__info-name {
	color: #4f4f4f; }

.ny-feeds__avatar {
	margin-right: 10px; }

.ny-feeds__avatar--followers {
	width: 40px;
	height: 40px; }

.ny-feeds__info--followers {
	padding-top: 3px; }

.ny-feeds__info-name {
	font-size: 16px;
	margin-bottom: 3px; }

.ny-feeds__info-type {
	font-size: 12px;
	color: #7d7d7d;
	margin-bottom: 3px; }

.ny-feeds__info-type--followers {
	font-size: 16px; }

.ny-feeds__info-total {
	font-size: 12px;
	color: #8c8c8c; }

.ny-feeds__info-total--followers {
	font-size: 16px; }

.ny-feeds__tags {
	margin-bottom: -7px; }

.ny-feeds__actions {
	padding-top: 5px; }

/*@media only screen and (max-width: 1380px) {
	.ny-feeds__block {
		padding: 15px 10px; }

	.ny-feeds__header {
		font-size: 13px;
		margin-bottom: 10px; }

	.ny-feeds__info-name {
		font-size: 13px; }

	.ny-feeds__info-name-followers {
		font-size: 16px; }

	.ny-feeds__person {
		margin-bottom: 10px; }

	.ny-feeds__person--followers {
		margin-bottom: 15px; } }
@media only screen and (max-width: 1060px) {
	.ny-feeds {
		display: none; }

	.ny-feeds__list--followers {
		max-height: 100%;
		overflow-y: hidden;
		padding-right: 0; } }
@media only screen and (max-width: 520px) {
	.ny-feeds__info-name-followers {
		font-size: 14px; }

	.ny-feeds__info-type--followers {
		font-size: 12px; }

	.ny-feeds__info-total--followers {
		font-size: 11px; }

	.ny-feeds__person--followers {
		margin-bottom: 10px; } }*/
.ny-filters {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row wrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	font-family: "Open Sans";
	font-weight: 500;
	margin-bottom: 25px; }

.ny-filters__list {
	list-style: none;
	margin: 0;
	padding: 0;
	width: -webkit-calc(27.77778% - 19.16667px);
	width: -moz-calc(27.77778% - 19.16667px);
	width: calc(27.77778% - 19.16667px);
	float: left;
	margin-left: 15px;
	margin-left: 0; }

.ny-filters__item {
	display: inline-block;
	font-size: 12px;
	margin-right: 25px;
	color: #4f4f4f; }
.ny-filters__item:last-child {
	margin-right: 0; }

.ny-filters__item--active {
	font-weight: 700; }

.ny-search {
	width: -webkit-calc(50% - 22.5px);
	width: -moz-calc(50% - 22.5px);
	width: calc(50% - 22.5px);
	float: left;
	margin-left: 15px;
	margin-left: 0;
	position: relative; }

.ny-search--followers {
	width: 100%;
	float: none;
	margin-bottom: 15px; }

.ny-filters__sorting {
	font-size: 12px; }

.ny-sorting__label {
	color: #bdbdbd;
	margin-right: 5px; }

.ny-filters__sorting-button {
	display: inline-block;
	cursor: pointer; }

.ny-sorting__type {
	color: #202020;
	margin-right: 5px; }
.ny-sorting__type:hover {
	color: #4f4f4f; }

.ny-filters__choose {
	list-style: none;
	margin: 0;
	padding: 0;
	margin-top: 25px; }

.ny-filters__choose-item {
	font-size: 12px;
	margin-right: 25px;
	color: #202020;
	display: inline-block;
	cursor: pointer; }
.ny-filters__choose-item:last-child {
	margin-right: 0; }
.ny-filters__choose-item:hover {
	color: #4f4f4f; }

.ny-filters__choose-type {
	margin-right: 5px; }

/*@media only screen and (max-width: 1380px) {
	.ny-search {
		width: -webkit-calc(38.88889% - 20.83333px);
		width: -moz-calc(38.88889% - 20.83333px);
		width: calc(38.88889% - 20.83333px);
		float: left;
		margin-left: 15px;
		margin-left: 0; }

	.ny-search--followers {
		width: 100%;
		float: none;
		margin-bottom: 15px; }

	.ny-filters__list {
		width: -webkit-calc(38.88889% - 20.83333px);
		width: -moz-calc(38.88889% - 20.83333px);
		width: calc(38.88889% - 20.83333px);
		float: left;
		margin-left: 15px;
		margin-left: 0; }

	.ny-filters__choose {
		margin-top: 15px; }

	.ny-filters {
		margin-bottom: 20px; } }
@media only screen and (max-width: 1060px) {
	.ny-filters__choose, .ny-filters__list, .ny-filters__sorting {
		display: none; }

	.ny-filters {
		margin-bottom: 0; }

	.ny-search {
		width: 100%;
		margin-bottom: 15px; } }*/
.ny-loader {
	background: #fff;
	padding: 25px 20px 50px 25px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	max-width: 670px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-loader__title {
	font-size: 30px;
	color: #202020;
	margin-bottom: 25px;
	text-transform: uppercase; }

.ny-loader__tabs {
	margin-bottom: 25px; }

.ny-loader__tab {
	display: inline-block;
	margin-right: 60px;
	font-size: 16px;
	color: #202020;
	text-decoration: none; }
.ny-loader__tab:hover {
	text-decoration: underline; }
.ny-loader__tab:last-child {
	margin-right: 0; }

.ny-loader__tab--active {
	font-weight: 700; }

.ny-loader__select {
	position: relative;
	overflow: hidden; }
.ny-loader__select:before {
	content: "";
	display: block;
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 0;
	background: #fff;
	-webkit-box-shadow: 0 0 70px 60px #fff;
	-moz-box-shadow: 0 0 70px 60px #fff;
	box-shadow: 0 0 70px 60px #fff;
	z-index: 1; }

.ny-select__list {
	list-style: none;
	padding: 0;
	margin: 0;
	overflow-y: scroll;
	max-height: 460px;
	padding-right: 15px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: relative; }
.ny-select__list::-webkit-scrollbar {
	width: 5px;
	margin-left: 15px; }
.ny-select__list::-webkit-scrollbar-track {
	background: #f2f2f2;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	z-index: 10; }
.ny-select__list::-webkit-scrollbar-thumb {
	background-color: #4f4f4f;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	z-index: 10; }

.ny-select__item {
	margin-bottom: 35px;
	padding: 3px;
	cursor: pointer;
	border: 2px solid transparent; }

.ny-select__item--active, .ny-select__item:hover {
	border-color: #202020; }

.ny-upload__notice {
	border: 1px solid #eb5757;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	padding: 7px 15px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	margin-bottom: 25px; }

.ny-upload__notice--accordion {
	margin-bottom: 0;
	float: left;
	width: 70%; }
.ny-upload__notice--accordion > .ny-upload__notice-content {
	width: -webkit-calc(100% - 170px);
	width: -moz-calc(100% - 170px);
	width: calc(100% - 170px); }

.ny-upload__notice-content {
	margin: 0;
	width: -webkit-calc(100% - 50px);
	width: -moz-calc(100% - 50px);
	width: calc(100% - 50px);
	color: #202020;
	font-size: 12px;
	line-height: 1.4; }

.ny-upload__info {
	color: #202020;
	font-size: 12px;
	line-height: 1.4;
	margin-bottom: 25px; }

.ny-upload__form {
	border: 2px dashed #bdbdbd;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	width: 100%;
	min-height: 300px;
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 30px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.ny-upload__form-info {
	max-width: 230px;
	text-align: center; }

.ny-loader__action {
	padding-top: 20px;
	text-align: center; }

/*@media only screen and (max-width: 1380px) {
	.ny-upload__notice--accordion {
		width: 75%; } }
@media only screen and (max-width: 1060px) {
	.ny-upload__notice--accordion {
		width: 100%; } }
@media only screen and (max-width: 660px) {
	.ny-upload__notice--accordion {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap; }

	.ny-upload__notice > .ny-upload__notice-content {
		-webkit-box-ordinal-group: 2;
		-webkit-order: 1;
		-moz-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1;
		width: 100%;
		margin-top: 15px; } }*/
.ny-nav {
	padding-top: 25px;
	padding-left: 250px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	margin: 0; }

.ny-nav__item {
	display: inline-block; }

/*@media only screen and (max-width: 1380px) {
	.ny-nav {
		padding-top: 20px;
		padding-left: 230px; } }
@media only screen and (max-width: 1060px) {
	.ny-nav {
		display: none; } }*/
.ny-size {
	padding: 40px 30px;
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	min-width: 370px;
	max-width: 370px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-size__box {
	position: relative; }

.ny-size__close {
	position: absolute;
	top: 0;
	right: 0;
	width: 16px;
	height: 16px;
	-webkit-transform: translateX(50%);
	-moz-transform: translateX(50%);
	-ms-transform: translateX(50%);
	-o-transform: translateX(50%);
	transform: translateX(50%); }

.ny-size__title {
	font-size: 30px;
	text-align: center;
	color: #202020;
	margin-bottom: 25px;
	text-transform: uppercase; }

.ny-size__row {
	margin-bottom: 15px; }
.ny-size__row::after {
	clear: both;
	content: "";
	display: block; }
.ny-size__row:nth-last-child(2) {
	margin-bottom: 30px; }

.ny-size__group {
	margin-bottom: 15px;
	width: -webkit-calc(55.55556%);
	width: -moz-calc(55.55556%);
	width: calc(55.55556%);
	float: left;
	margin-left: 0;
	padding-right: 7.5px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.ny-size__group:nth-child(2n) {
	padding-left: 7.5px;
	padding-right: 0;
	width: -webkit-calc(25%);
	width: -moz-calc(25%);
	width: calc(25%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(19.44444% + 0px);
	margin-left: -moz-calc(19.44444% + 0px);
	margin-left: calc(19.44444% + 0px); }

.ny-size__field-row {
	position: relative; }

/*@media only screen and (max-width: 1060px) {
	.ny-size__close, .ny-size__title {
		display: none; }

	.ny-size {
		padding: 0; }

	.ny-size__row {
		margin-bottom: 20px; }
	.ny-size__row:nth-child(3) {
		padding-top: 10px; }
	.ny-size__row:nth-child(3) .ny-label--size--fixed {
		top: 16px; }
	.ny-size__row:nth-last-child(3) {
		margin-bottom: 30px; }
	.ny-size__row:nth-last-child(2) {
		margin-bottom: 50px; }

	.ny-size__group {
		margin-bottom: 0; }
	.ny-size__group:nth-child(2) {
		width: auto;
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px);
		float: right; } }
@media only screen and (max-width: 520px) {
	.ny-size__close, .ny-size__title {
		display: none; }

	.ny-size {
		width: 100%;
		max-width: 100%;
		min-width: 100%;
		-webkit-align-self: flex-start;
		-ms-flex-item-align: start;
		align-self: flex-start; } }*/
.ny-tabs {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-bottom: 35px;
	font-family: "Open Sans";
	font-weight: 500; }

.ny-tabs--invite {
	margin-bottom: 30px; }

.ny-tabs__header--trading {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center; }

.ny-tabs__tabulator--trading {
	display: inline-block; }

.ny-tabs__link {
	font-size: 12px;
	color: #202020;
	margin-right: 35px;
	text-decoration: none; }
.ny-tabs__link:last-child {
	margin-right: 0; }
.ny-tabs__link:hover {
	text-decoration: underline; }

.ny-tabs__link--active {
	font-weight: 700; }
.ny-tabs__link--active:hover {
	text-decoration: none; }

.ny-tabs__profile {
	display: none; }

.ny-tabs__title {
	font-family: "Open Sans";
	font-weight: 500;
	font-size: 30px;
	margin: 0;
	text-transform: uppercase;
	color: #202020; }

.ny-tabs__title--mobile {
	display: none; }

.ny-tabs__title--trading {
	display: inline-block;
	margin-right: 40px; }

a.ny-tabs__title {
	text-decoration: none; }
a.ny-tabs__title:nth-child(2) {
	margin-left: 40px; }
a.ny-tabs__title--disable {
	color: #bdbdbd; }

.ny-tabs__action {
	width: 45%; }
.ny-tabs__action::after {
	clear: both;
	content: "";
	display: block; }
.ny-tabs__action > button {
	width: 50%;
	float: left;
	padding: 5.5px 0; }

.ny-tabs__action-feed {
	margin-left: 10%;
	width: 40%;
	float: left;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: end;
	-webkit-justify-content: flex-end;
	-moz-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	height: 26px; }

.ny-tabs__action-feed__title {
	display: inline-block;
	font-size: 12px;
	color: #000;
	margin-right: 40px;
	line-height: 20px; }

.ny-tabs__action-feed__list {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: end;
	-webkit-justify-content: flex-end;
	-moz-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end;
	-webkit-box-align: stretch;
	-webkit-align-items: stretch;
	-moz-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch;
	list-style: none;
	padding: 0;
	margin: 0; }

.ny-tabs__action-feed__item {
	display: inline-block;
	margin-right: 30px; }
.ny-tabs__action-feed__item:last-child {
	margin-right: 0; }

.ny-tabs__list {
	list-style: none;
	margin: 0;
	padding: 0; }

.ny-tabs__item {
	display: inline-block;
	margin-right: 30px;
	font-family: "Open Sans";
	font-weight: 500;
	font-size: 16px; }
.ny-tabs__item:last-child {
	margin-right: 0; }

.ny-tabs__item--active {
	font-weight: 700; }

.ny-tabs__item--red > a {
	color: #eb5757; }

/*@media only screen and (max-width: 1380px) {
	.ny-tabs__title {
		font-size: 25px; }

	.ny-tabs__item {
		font-size: 13px;
		margin-right: 15px; }

	.ny-tabs {
		margin-bottom: 15px; } }
@media only screen and (max-width: 1060px) {
	.ny-tabs {
		display: none; }

	.ny-tabs--products, .ny-tabs--trading, .ny-tabs--wishlist {
		display: block; }

	.ny-tabs--products > :not(.ny-tabs__header), .ny-tabs--trading > :not(.ny-tabs__header), .ny-tabs--wishlist > :not(.ny-tabs__header), .ny-tabs__title--trading {
		display: none; }

	.ny-tabs__title--mobile {
		display: block;
		font-size: 22px;
		text-align: center;
		color: #000;
		width: 100%;
		margin-bottom: 25px; }

	.ny-tabs__header--trading {
		display: block; }

	.ny-tabs__link {
		width: 33.3%;
		display: block;
		float: left;
		margin-right: 0;
		text-align: center;
		font-weight: 400;
		padding-bottom: 10px;
		border-bottom: 1px solid #cdced3;
		font-size: 14px; }

	.ny-tabs__link--active {
		border-bottom: 2px solid #000;
		font-weight: 700; }

	.ny-tabs__profile {
		display: block;
		width: 355px;
		margin: 0 auto 50px; }

	.ny-tabs__profile-image {
		margin-bottom: 5px;
		position: relative;
		width: 65px;
		height: 65px;
		margin-left: auto;
		margin-right: auto; }

	.ny-tabs__profile-name {
		font-size: 18px;
		color: #000;
		margin-bottom: 5px;
		text-align: center; }

	.ny-tabs__profile-type {
		font-size: 12px;
		color: #7d7d7d;
		margin-bottom: 5px;
		text-align: center; }

	.ny-tabs__profile-date {
		font-size: 11px;
		text-align: center;
		margin-bottom: 15px;
		color: #c7c7cc; }

	.ny-tabs__profile-list {
		list-style: none;
		padding: 0;
		margin: 0;
		margin-bottom: 15px; }
	.ny-tabs__profile-list::after {
		clear: both;
		content: "";
		display: block; }

	.ny-tabs__profile-item {
		float: left;
		width: 33.3%;
		text-align: center;
		position: relative; }
	.ny-tabs__profile-item:before {
		content: "";
		display: block;
		position: absolute;
		top: 50%;
		right: 0;
		width: 1px;
		height: 30px;
		background: #e0e0e0;
		-webkit-transform: translateY(-50%);
		-moz-transform: translateY(-50%);
		-ms-transform: translateY(-50%);
		-o-transform: translateY(-50%);
		transform: translateY(-50%); }
	.ny-tabs__profile-item:last-child:before {
		display: none; }

	.ny-tabs__profile-item__count {
		font-size: 18px;
		font-weight: 700;
		color: #000;
		margin-bottom: 5px; }

	.ny-tabs__profile-item__caption {
		font-size: 12px;
		color: #7d7d7d; }

	.ny-tabs__tabulator {
		width: 100vw;
		left: 50%;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%);
		position: relative;
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: stretch;
		-webkit-align-items: stretch;
		-moz-box-align: stretch;
		-ms-flex-align: stretch;
		align-items: stretch; }

	a.ny-tabs__title {
		display: block;
		width: 100%;
		font-size: 14px;
		font-weight: 700;
		text-align: center;
		padding-bottom: 10px;
		border-bottom: 2px solid #000;
		text-transform: none; }
	a.ny-tabs__title:nth-child(2) {
		margin-left: 0; }
	a.ny-tabs__title--disable {
		border-bottom: 1px solid #cdced3;
		font-weight: 400; } }
@media only screen and (max-width: 520px) {
	a.ny-tabs__title {
		font-size: 12px; }

	.ny-tabs__link {
		font-size: 12px; }
	.ny-tabs__link span:not(.ny-notice) {
		display: none; } }
@media only screen and (max-width: 374px) {
	.ny-tabs__profile {
		width: 100%; } }*/
.ny-share {
	border: 1px solid #e5e5e5;
	margin-bottom: 35px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	justify-conent: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	font-family: "Open Sans";
	font-weight: 500;
	padding: 30px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.ny-share::after {
	clear: both;
	content: "";
	display: block; }

.ny-share__thumb {
	width: -webkit-calc(33.33333%);
	width: -moz-calc(33.33333%);
	width: calc(33.33333%);
	float: left;
	margin-left: 0; }

.ny-share__info {
	width: -webkit-calc(55.55556%);
	width: -moz-calc(55.55556%);
	width: calc(55.55556%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(11.11111% + 0px);
	margin-left: -moz-calc(11.11111% + 0px);
	margin-left: calc(11.11111% + 0px); }

.ny-share__info-title {
	font-size: 24px;
	font-weight: 700;
	margin-bottom: 10px;
	color: #202020; }

.ny-share__info-caption {
	font-size: 16px;
	margin-bottom: 20px;
	color: #202020;
	opacity: .5; }

.ny-share__info-list {
	list-style: none;
	margin: 0;
	padding: 0; }
.ny-share__info-list::after {
	clear: both;
	content: "";
	display: block; }

.ny-share__info-item {
	float: left;
	width: 30px;
	height: 30px;
	background: #202020;
	position: relative;
	margin-right: 20px; }
.ny-share__info-item:last-child {
	margin-right: 0; }

/*@media only screen and (max-width: 1380px) {
	.ny-share__info-title {
		font-size: 21px; }

	.ny-share__info-caption {
		font-size: 14px; }

	.ny-share {
		margin-bottom: 25px; } }
@media only screen and (max-width: 1060px) {
	.ny-share {
		display: none; } }*/
.ny-swipe {
	position: fixed;
	top: 0;
	right: 0;
	width: 100%;
	height: 100%;
	background: #fff;
	z-index: 1;
	font-family: "Open Sans";
	font-weight: 500;
	-webkit-transform: translate(100%);
	-moz-transform: translate(100%);
	-ms-transform: translate(100%);
	-o-transform: translate(100%);
	transform: translate(100%);
	-webkit-transition: all .5s ease-in-out;
	-o-transition: all .5s ease-in-out;
	-moz-transition: all .5s ease-in-out;
	transition: all .5s ease-in-out; }

.ny-swipe--show {
	-webkit-transform: translateX(0);
	-moz-transform: translateX(0);
	-ms-transform: translateX(0);
	-o-transform: translateX(0);
	transform: translateX(0); }

.ny-swipe__box {
	max-width: 768px;
	margin: 0 auto; }

.ny-swipe__header {
	padding: 12px 0;
	text-align: center;
	-webkit-box-shadow: 0 0.5px 0 rgba(0, 0, 0, 0.3);
	-moz-box-shadow: 0 0.5px 0 rgba(0, 0, 0, 0.3);
	box-shadow: 0 0.5px 0 rgba(0, 0, 0, 0.3);
	margin-bottom: 20px;
	position: relative; }

.ny-swipe__backspace {
	background: url(../pictures/icons/backspace.svg) center center/100% 100% no-repeat;
	width: 13px;
	height: 22px;
	left: 0;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	display: inline-block;
	position: absolute;
	cursor: pointer; }

.ny-swipe__title {
	font-size: 17px;
	color: #000; }

.ny-swipe__search {
	margin-bottom: 30px;
	position: relative; }

.ny-swipe__nav {
	margin-bottom: 15px; }

.ny-swipe__tag {
	font-size: 16px;
	font-weight: 700;
	color: #000; }

.ny-swipe__list {
	list-style: none;
	padding: 0;
	margin: 0; }

.ny-swipe__item {
	font-size: 14px;
	color: #000;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
	cursor: pointer;
	position: relative; }

.ny-swipe__link {
	text-decoration: none;
	display: block;
	font-size: 13px; }

.ny-swipe__caption {
	font-size: 12px;
	color: #8c8c8c;
	padding-top: 5px;
	max-width: 80%;
	line-height: 1.3; }

/*@media only screen and (max-width: 800px) {
	.ny-swipe__box {
		max-width: -webkit-calc(100% - 20px);
		max-width: -moz-calc(100% - 20px);
		max-width: calc(100% - 20px); } }*/
.ny-accordion {
	font-family: "Open Sans";
	font-weight: 500;
	margin-bottom: 60px; }

.ny-accordion__list {
	list-style: none;
	padding: 0;
	margin: 0; }

.ny-accordion__notice {
	margin-bottom: 30px; }
.ny-accordion__notice::after {
	clear: both;
	content: "";
	display: block; }

.ny-accordion__notice-text {
	font-size: 12px;
	color: #202020;
	display: inline-block;
	float: right;
	width: 20%;
	text-align: right; }

.ny-accordion__item {
	margin-bottom: 30px; }
.ny-accordion__item:last-child {
	margin-bottom: 0; }

.ny-accordion__item-tab::after {
	clear: both;
	content: "";
	display: block; }

.ny-accordion__item-tab--active {
	margin-bottom: 18px; }

.ny-accordion__item-tab--waiting {
	margin-bottom: 20px; }

.ny-accordion__item-tab__name {
	font-size: 16px;
	font-weight: 700;
	color: #000;
	display: inline-block;
	margin-right: 10px; }

.ny-accordion__item-tab__date {
	font-size: 16px;
	color: #bdbdbd; }

.ny-accordion__item-tab__info {
	float: right;
	color: #bdbdbd;
	font-size: 16px; }

.ny-accordion__item-content {
	display: none;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row wrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: stretch;
	-webkit-align-items: stretch;
	-moz-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch; }
.ny-accordion__item-content::after {
	clear: both;
	content: "";
	display: block; }

.ny-accordion__item-content--show {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex; }
.ny-accordion__item-content--show .ny-button--card:nth-of-type(2) {
	margin-top: 10px; }

/*@media only screen and (max-width: 1380px) {
	.ny-accordion__item-tab__info {
		float: left;
		margin-top: 5px; }

	.ny-accordion__notice-text {
		width: 22%; } }
@media only screen and (max-width: 1060px) {
	.ny-accordion__notice-text {
		width: 100%;
		text-align: left;
		margin-top: 15px; }

	.ny-accordion__item-tab__info {
		float: right;
		margin-top: 0;
		font-size: 12px; } }
@media only screen and (max-width: 800px) {
	.ny-accordion__item-tab__info {
		float: none;
		display: block;
		margin-top: 10px;
		font-size: 12px; }

	.ny-accordion__item-tab--waiting .ny-accordion__item-tab__date {
		float: right; } }
@media only screen and (max-width: 660px) {
	.ny-accordion__item-content {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap; }
	.ny-accordion__item-content .ny-card__image {
		min-height: auto; }
	.ny-accordion__item-content .ny-card__item {
		margin-left: 0;
		width: 48%;
		margin-bottom: 20px; }
	.ny-accordion__item-content .ny-card__item:nth-child(2n) {
		margin-left: 4%; } }
@media only screen and (max-width: 380px) {
	.ny-accordion__item-content .ny-card__item {
		margin-left: 0;
		width: 49.5%;
		margin-bottom: 5px; }
	.ny-accordion__item-content .ny-card__item:nth-child(2n) {
		margin-left: 1%; } }
@media only screen and (max-width: 374px) {
	.ny-accordion__item-content .ny-card__item {
		margin-left: auto;
		margin-right: auto;
		width: 80%;
		margin-bottom: 5px; }
	.ny-accordion__item-content .ny-card__item:nth-child(2n) {
		margin-left: auto; }

	.ny-accordion__item-tab__date {
		float: right; } }*/
.ny-more {
	padding-top: 40px;
	font-family: "Open Sans";
	font-weight: 500;
	padding-bottom: 20px; }

.ny-more__header {
	margin-bottom: 20px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }
.ny-more__header::after {
	clear: both;
	content: "";
	display: block; }

.ny-more__header-label {
	font-size: 14px;
	color: #27ae60;
	line-height: 1.5;
	margin-right: 40px; }

.ny-more__header-caption {
	font-size: 14px;
	line-height: 1.5;
	color: #4f4f4f; }

.ny-more__content {
	padding-bottom: 30px;
	border-bottom: 1px solid #f2f2f2;
	border-right: 2px solid #f2f2f2;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: stretch;
	-webkit-align-items: stretch;
	-moz-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch; }
.ny-more__content::after {
	clear: both;
	content: "";
	display: block; }

.ny-more__group {
	width: -webkit-calc(49.30556%);
	width: -moz-calc(49.30556%);
	width: calc(49.30556%);
	float: left;
	margin-left: 0;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-align: stretch;
	-webkit-align-items: stretch;
	-moz-box-align: stretch;
	-ms-flex-align: stretch;
	align-items: stretch; }
.ny-more__group:last-child {
	margin-left: -webkit-calc(1.38889% + 0px);
	margin-left: -moz-calc(1.38889% + 0px);
	margin-left: calc(1.38889% + 0px); }

.ny-more__content-line {
	width: -webkit-calc(55.55556%);
	width: -moz-calc(55.55556%);
	width: calc(55.55556%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(5.55556% + 0px);
	margin-left: -moz-calc(5.55556% + 0px);
	margin-left: calc(5.55556% + 0px);
	position: relative;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row wrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between; }
.ny-more__content-line:before {
	content: "";
	position: absolute;
	width: 2px;
	height: 100%;
	left: 0;
	top: 0;
	background: #f2f2f2;
	display: block; }

.ny-more__content-line__step {
	padding-bottom: 58px;
	position: relative;
	padding-left: 20px;
	width: 100%; }

.ny-more__content-line__step--error {
	padding-bottom: 15px; }

.ny-more__content-line__step:before {
	content: "";
	position: absolute;
	width: 2px;
	height: 100%;
	left: 0;
	top: 0;
	background: #f2f2f2;
	display: block; }
.ny-more__content-line__step:last-child {
	padding-bottom: 0;
	-webkit-align-self: flex-end;
	-ms-flex-item-align: end;
	align-self: flex-end; }
.ny-more__content-line__step:last-child:before {
	display: none; }

.ny-more__content-line__step--fill:before {
	background: #27ae60; }

.ny-more__content-line__step-title {
	font-size: 14px;
	color: #4f4f4f; }

.ny-more__content-line__step-title--error {
	color: #eb5757; }

.ny-more__content-line__step-caption {
	font-size: 12px;
	color: #4f4f4f;
	line-height: 1.5;
	margin-top: 5px; }

.ny-more__footer {
	padding-bottom: 30px;
	border-bottom: 1px solid #f2f2f2;
	border-right: 2px solid #f2f2f2;
	padding-top: 30px;
	padding-right: 20px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.ny-more__footer::after {
	clear: both;
	content: "";
	display: block; }

.ny-more__footer-group {
	width: -webkit-calc(29.16667%);
	width: -moz-calc(29.16667%);
	width: calc(29.16667%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(6.25% + 0px);
	margin-left: -moz-calc(6.25% + 0px);
	margin-left: calc(6.25% + 0px); }
.ny-more__footer-group:first-child {
	margin-left: -webkit-calc(0% + 0px);
	margin-left: -moz-calc(0% + 0px);
	margin-left: calc(0% + 0px); }

.ny-more__footer-caption {
	font-size: 14px;
	color: #4f4f4f;
	line-height: 1.5; }

.ny-more__footer-label {
	font-size: 14px;
	color: #bdbdbd;
	margin-bottom: 20px; }

.ny-more__footer-row {
	margin-bottom: 14px; }
.ny-more__footer-row::after {
	clear: both;
	content: "";
	display: block; }
.ny-more__footer-row:last-child {
	margin-bottom: 0; }

.ny-more__footer-key {
	font-size: 14px;
	color: #4f4f4f;
	float: left; }

.ny-more__footer-val {
	font-size: 14px;
	color: #4f4f4f;
	float: right; }

.ny-more__footer-val--bold {
	font-weight: 700; }

/*@media only screen and (max-width: 1380px) {
	.ny-more__header-label, .ny-more__header-caption {
		font-size: 13px; }

	.ny-more__content-line__step {
		padding-bottom: 35px; }

	.ny-more__content-line__step--error {
		padding-bottom: 5px; }

	.ny-more__content-line__step-title {
		font-size: 13px; }

	.ny-more__content, .ny-more__footer {
		border-right: 0; } }
@media only screen and (max-width: 1060px) {
	.ny-more__header {
		display: none; }

	.ny-more__footer-row {
		margin-bottom: 10px; }

	.ny-more__footer {
		padding-bottom: 0;
		border-bottom: 0;
		padding-right: 0; }

	.ny-more {
		padding-bottom: 0; } }
@media only screen and (max-width: 800px) {
	.ny-more__content {
		display: block; }

	.ny-more__content-line__step-title {
		font-size: 12px; }

	.ny-more__content-line__step-caption {
		font-size: 10px; }

	.ny-more__footer-caption, .ny-more__footer-key, .ny-more__footer-val {
		font-size: 12px; }

	.ny-more__footer-group {
		width: -webkit-calc(31%);
		width: -moz-calc(31%);
		width: calc(31%);
		float: left;
		margin-left: 0;
		margin-left: -webkit-calc(3.47222% + 0px);
		margin-left: -moz-calc(3.47222% + 0px);
		margin-left: calc(3.47222% + 0px); }
	.ny-more__footer-group:first-child {
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }

	.ny-more__footer-label {
		margin-bottom: 10px; } }
@media only screen and (max-width: 767px) {
	.ny-more__group {
		width: 100%;
		margin-bottom: 30px; }
	.ny-more__group:last-child {
		margin-left: 0;
		margin-bottom: 0; } }
@media only screen and (max-width: 660px) {
	.ny-more__footer-group {
		width: 100%;
		margin-left: 0;
		margin-bottom: 35px; }
	.ny-more__footer-group:last-child {
		margin-bottom: 0; }

	.ny-more__footer-key, .ny-more__footer-val {
		font-size: 14px; }

	.ny-more__footer-label {
		margin-bottom: 15px; }

	.ny-more__content {
		border-bottom: 0;
		padding-bottom: 0; }

	.ny-more__content-line__step {
		padding-bottom: 35px; } }
@media only screen and (max-width: 520px) {
	.ny-more__content-line__step-title {
		font-size: 11px; }

	.ny-more__content-line__step {
		padding-bottom: 15px; }

	.ny-more__content-line__step-caption {
		font-size: 10px;
		line-height: 1.1; } }
@media only screen and (max-width: 374px) {
	.ny-more__content-line__step-caption {
		line-height: 1.2;
		font-size: 10px; }

	.ny-more__content-line__step-title {
		font-size: 11px; } }*/
.ny-orders {
	font-family: "Open Sans";
	font-weight: 500;
	margin-bottom: 60px; }

.ny-orders__title {
	font-size: 16px;
	color: #000;
	margin-bottom: 40px; }

.ny-orders__list {
	list-style: none;
	padding: 0;
	margin: 0; }

.ny-orders__item {
	margin-bottom: 35px; }
.ny-orders__item:last-child {
	margin-bottom: 0; }
/*
@media only screen and (max-width: 1060px) {
	.ny-orders__title {
		display: none; }

	.ny-orders__item {
		padding-bottom: 40px;
		margin-bottom: 40px;
		position: relative; }
	.ny-orders__item:before {
		content: "";
		display: block;
		position: absolute;
		bottom: 0;
		left: 50%;
		width: 100vw;
		height: 7px;
		background: #efeff4;
		-webkit-transform: translate(-50%, 50%);
		-moz-transform: translate(-50%, 50%);
		-ms-transform: translate(-50%, 50%);
		-o-transform: translate(-50%, 50%);
		transform: translate(-50%, 50%); }
	.ny-orders__item:last-child {
		margin-bottom: 0; }
	.ny-orders__item:last-child:before {
		display: none; } }
@media only screen and (max-width: 520px) {
	.ny-orders__item {
		padding-bottom: 20px;
		margin-bottom: 30px; } }*/
.ny-return {
	padding: 30px 25px 35px;
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	width: 1015px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	margin: 60px 0; }

.ny-return__header {
	margin-bottom: 25px;
	position: relative; }

.ny-return__header-name {
	font-size: 30px;
	color: #202020;
	text-transform: uppercase;
	margin-right: 25px;
	display: inline-block;
	font-weight: 400;
	margin-top: 0;
	margin-bottom: 0; }

.ny-return__header-info {
	display: inline-block;
	font-size: 14px;
	color: #4f4f4f;
	padding: 7px 10px;
	background: #f2f2f2;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	position: relative;
	top: -4px; }

.ny-return__profile {
	margin-bottom: 35px; }
.ny-return__profile::after {
	clear: both;
	content: "";
	display: block; }

.ny-return__profile-fields {
	width: -webkit-calc(26.38889%);
	width: -moz-calc(26.38889%);
	width: calc(26.38889%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(6.94444% + 0px);
	margin-left: -moz-calc(6.94444% + 0px);
	margin-left: calc(6.94444% + 0px);
	margin-bottom: 25px; }
.ny-return__profile-fields:first-child, .ny-return__profile-fields:nth-child(4) {
	margin-left: -webkit-calc(0% + 0px);
	margin-left: -moz-calc(0% + 0px);
	margin-left: calc(0% + 0px); }
.ny-return__profile-fields:last-child, .ny-return__profile-fields:nth-child(4) {
	margin-bottom: 0; }

.ny-return__content {
	margin-bottom: 50px; }

.ny-return__content-title {
	font-size: 16px;
	color: #000;
	margin-bottom: 30px; }

.ny-return__content-list {
	list-style: none;
	padding: 0;
	margin: 0;
	max-height: 293px;
	overflow: hidden;
	overflow-y: scroll;
	padding-right: 15px; }
.ny-return__content-list::-webkit-scrollbar {
	width: 5px;
	margin-left: 15px; }
.ny-return__content-list::-webkit-scrollbar-track {
	background: #f2f2f2;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	z-index: 10; }
.ny-return__content-list::-webkit-scrollbar-thumb {
	background-color: #4f4f4f;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	z-index: 10; }

.ny-return__content-item {
	margin-bottom: 40px; }
.ny-return__content-item::after {
	clear: both;
	content: "";
	display: block; }
.ny-return__content-item:last-child {
	margin-bottom: 0; }

.ny-return__content-col {
	width: -webkit-calc(25%);
	width: -moz-calc(25%);
	width: calc(25%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(6.94444% + 0px);
	margin-left: -moz-calc(6.94444% + 0px);
	margin-left: calc(6.94444% + 0px); }
.ny-return__content-col:first-child {
	width: -webkit-calc(20.83333%);
	width: -moz-calc(20.83333%);
	width: calc(20.83333%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(0% + 0px);
	margin-left: -moz-calc(0% + 0px);
	margin-left: calc(0% + 0px); }
.ny-return__content-col:last-child {
	width: -webkit-calc(40.27778%);
	width: -moz-calc(40.27778%);
	width: calc(40.27778%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(6.94444% + 0px);
	margin-left: -moz-calc(6.94444% + 0px);
	margin-left: calc(6.94444% + 0px); }

.ny-return__content-col--blur {
	opacity: .3;
	pointer-events: none; }

.ny-return__content-item__fields {
	padding-top: 10px;
	margin-bottom: 40px; }

.ny-return__content-item__field {
	margin-bottom: 25px;
	position: relative; }
.ny-return__content-item__field:last-child {
	margin-bottom: 0; }

.ny-return__content-item__caption {
	font-size: 14px;
	color: #202020;
	line-height: 1.5;
	margin-bottom: 15px; }

.ny-return__content-item__action {
	position: relative; }

.ny-return__content-item__action-name {
	padding-top: 25px;
	font-size: 14px;
	color: #000;
	display: block; }

.ny-return__content-item__title {
	font-size: 16px;
	margin-bottom: 15px;
	color: #202020; }

.ny-return__content-item__area {
	border: 1px solid #e5e5e5;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 10px 60px 20px 10px;
	width: 100%;
	resize: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 258px;
	line-height: 1.5;
	font-size: 14px;
	color: #202020;
	outline: 0;
	font-family: "Open Sans";
	font-weight: 500; }
.ny-return__content-item__area:focus, .ny-return__content-item__area:hover {
	color: #4f4f4f;
	border-color: #bdbdbd; }

.ny-return__footer::after {
	clear: both;
	content: "";
	display: block; }

.ny-return__footer-status {
	float: left;
	font-size: 14px;
	color: #bdbdbd;
	padding-top: 15px; }

.ny-return__footer-info {
	float: right; }

.ny-return__footer-info__date {
	font-size: 14px;
	color: #bdbdbd;
	margin-right: 20px;
	display: inline-block; }

.ny-return__footer-info__date--mobile {
	display: none; }

/*@media only screen and (max-width: 1060px) {
	.ny-return {
		width: 100%;
		margin-top: 0;
		margin-bottom: 0;
		padding: 0; }

	.ny-return__footer {
		position: fixed;
		bottom: 0;
		left: 50%;
		width: 768px;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%);
		background: #fff; }

	.ny-return__content {
		margin-bottom: 75px; }

	.ny-return__content-list {
		max-height: none;
		overflow: unset;
		padding-right: 0; }

	.ny-return__header {
		text-align: center;
		margin-bottom: 30px; }

	.ny-return__header-name {
		font-size: 22px;
		margin-right: 0;
		text-align: center;
		width: 100%;
		margin-bottom: 30px; }

	.ny-return__header-info {
		top: 0; }

	.ny-return__profile {
		margin-bottom: 40px; }

	.ny-return__content-title {
		font-weight: 700; }

	.ny-return__content-col {
		width: 32.6%;
		margin-left: 4.5%; }
	.ny-return__content-col:first-child {
		width: 27.9%;
		margin-left: 0; }
	.ny-return__content-col:last-child {
		width: 33%;
		margin-left: 2%; }

	.ny-return__content-item {
		margin-bottom: 30px;
		padding-bottom: 30px;
		position: relative; }
	.ny-return__content-item:before {
		content: "";
		display: block;
		position: absolute;
		bottom: 0;
		left: 50%;
		height: 7px;
		width: 100vw;
		-webkit-transform: translate(-50%, 50%);
		-moz-transform: translate(-50%, 50%);
		-ms-transform: translate(-50%, 50%);
		-o-transform: translate(-50%, 50%);
		transform: translate(-50%, 50%);
		background: #efeff4; }
	.ny-return__content-item:last-child {
		padding-bottom: 0;
		margin-bottom: 0; }
	.ny-return__content-item:last-child:before {
		display: none; } }
@media only screen and (max-width: 800px) {
	.ny-return__content-col {
		margin-left: 3%; }
	.ny-return__content-col:first-child {
		width: 30%; }
	.ny-return__content-col:last-child {
		margin-left: 1.4%; }

	.ny-return__footer {
		width: -webkit-calc(100% - 30px);
		width: -moz-calc(100% - 30px);
		width: calc(100% - 30px); }

	.ny-return__content-item__field {
		margin-bottom: 15px; }

	.ny-return__content-item__fields {
		padding-top: 0;
		margin-bottom: 28px; }

	.ny-return__content-item__area {
		line-height: 1.4;
		height: 250px; }

	.ny-return__footer-status {
		font-size: 12px; }

	.ny-return__footer-info__date {
		font-size: 12px;
		margin-right: 0; } }
@media only screen and (max-width: 767px) {
	.ny-return__content-item__area {
		font-size: 12px; } }
@media only screen and (max-width: 660px) {
	.ny-return__content-col {
		margin-left: 10%;
		width: 50%; }
	.ny-return__content-col:first-child {
		width: 40%; }
	.ny-return__content-col:last-child {
		margin-left: 0;
		width: 100%;
		margin-top: 20px; }

	.ny-return__content {
		margin-bottom: 125px; }

	.ny-return__footer {
		padding-top: 10px; }

	.ny-return__content-item__area {
		font-size: 14px;
		line-height: 1.5;
		height: 125px; }

	.ny-return__content-item__field {
		margin-bottom: 25px; }

	.ny-return__content-item__fields {
		padding-top: 10px;
		margin-bottom: 40px; }

	.ny-return__content-item__caption {
		margin-bottom: 20px; }

	.ny-return__profile-fields {
		width: 45%;
		margin-left: 10%; }
	.ny-return__profile-fields:first-child, .ny-return__profile-fields:nth-child(3) {
		margin-left: 0; }
	.ny-return__profile-fields:nth-child(4) {
		margin-left: 10%;
		margin-bottom: 25px; }
	.ny-return__profile-fields:last-child {
		margin-left: 0; }

	.ny-return__footer-info__date {
		display: none; }

	.ny-return__footer-info__date--mobile {
		display: block;
		text-align: center; }

	.ny-return__footer-status {
		float: none;
		text-align: center;
		margin-bottom: 15px;
		padding-top: 10px; }

	.ny-return__footer-info {
		float: none; } }
@media only screen and (max-width: 520px) {
	.ny-return__header-name {
		margin-bottom: 25px;
		font-size: 16px; }

	.ny-return__profile-fields {
		width: 100%;
		margin-left: 0; }
	.ny-return__profile-fields:nth-child(4) {
		margin-left: 0; }

	.ny-return__content-col {
		margin-left: 0;
		width: 100%; }
	.ny-return__content-col:first-child {
		width: 60%;
		margin-bottom: 30px; }
	.ny-return__content-col:last-child {
		margin-top: 30px; }

	.ny-return__content-item__caption {
		margin-bottom: 15px; }

	.ny-return__content-item__area {
		padding: 10px;
		height: 160px; }

	.ny-return__header-info {
		width: 100%;
		display: block;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box; }
	.ny-return__header-info span {
		display: block; } }*/
.ny-trackline::after {
	clear: both;
	content: "";
	display: block; }

.ny-trackline__thumb {
	width: 120px;
	height: 120px;
	float: left;
	margin-right: 20px;
	position: relative;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	overflow: hidden; }
.ny-trackline__thumb:before {
	content: "";
	position: absolute;
	display: block;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(3, 3, 3, 0.5); }

.ny-trackline__thumb-count {
	font-size: 18px;
	color: #fff;
	text-align: center;
	position: absolute;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	width: 100%; }

.ny-trackline__content {
	width: -webkit-calc(100% - 120px - 20px);
	width: -moz-calc(100% - 120px - 20px);
	width: calc(100% - 120px - 20px);
	float: right; }

.ny-trackline__content-info {
	margin-bottom: 20px; }
.ny-trackline__content-info::after {
	clear: both;
	content: "";
	display: block; }

.ny-trackline__content-info__number {
	float: left;
	margin-right: 45px;
	font-size: 18px;
	color: #000; }

.ny-trackline__content-info__caption {
	float: left; }

.ny-trackline__content-info__caption-status {
	font-size: 14px;
	color: #27ae60; }

.ny-trackline__content-info__caption-status--red {
	color: #eb5757; }

.ny-trackline__content-info__caption-link {
	text-decoration: none;
	font-size: 14px;
	color: #4f4f4f;
	margin-left: 15px; }
.ny-trackline__content-info__caption-link:hover {
	text-decoration: underline; }

.ny-trackline__content-info__profile {
	float: right;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center; }

.ny-trackline__content-info__profile-name {
	color: #bdbdbd;
	font-size: 14px;
	margin-right: 10px; }

.ny-trackline__content-info__profile-login {
	font-size: 16px;
	color: #202020;
	margin-left: 5px; }

.ny-trackline__content-line {
	margin-bottom: 25px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }
.ny-trackline__content-line::after {
	clear: both;
	content: "";
	display: block; }

.ny-trackline__content-line__step {
	position: relative;
	-webkit-box-flex: 1;
	-webkit-flex-grow: 1;
	-moz-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1; }
.ny-trackline__content-line__step:before {
	content: "";
	position: absolute;
	display: block;
	height: 5px;
	width: 100%;
	background: #bdbdbd;
	bottom: 0;
	left: 0;
	-webkit-transform: translateY(100%);
	-moz-transform: translateY(100%);
	-ms-transform: translateY(100%);
	-o-transform: translateY(100%);
	transform: translateY(100%); }
.ny-trackline__content-line__step:after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	width: 1px;
	height: 10px;
	background: #bdbdbd;
	-webkit-transform: translateY(50%);
	-moz-transform: translateY(50%);
	-ms-transform: translateY(50%);
	-o-transform: translateY(50%);
	transform: translateY(50%); }
.ny-trackline__content-line__step:last-child {
	-webkit-box-flex: 0;
	-webkit-flex-grow: 0;
	-moz-box-flex: 0;
	-ms-flex-positive: 0;
	flex-grow: 0; }
.ny-trackline__content-line__step:last-child:after {
	right: 0;
	left: auto; }

.ny-trackline__content-line__step--disable:after, .ny-trackline__content-line__step--disable:before {
	background: #bdbdbd; }

.ny-trackline__content-line__step--active:after, .ny-trackline__content-line__step--active:before, .ny-trackline__content-line__step--active + div:not(:last-child):after {
	background: #27ae60; }

.ny-trackline__content-line__step--notfill:after, .ny-trackline__content-line__step--notfill:before {
	background: #bdbdbd; }

.ny-trackline__content-line__step-status {
	color: #4f4f4f;
	font-size: 14px;
	margin-bottom: 2px; }

.ny-trackline__content-line__step-status--disable {
	color: #bdbdbd; }

.ny-trackline__content-line__step-date {
	font-size: 10px;
	color: #bdbdbd;
	padding-bottom: 10px; }

.ny-trackline__content-action::after {
	clear: both;
	content: "";
	display: block; }

.ny-trackline__content-action__info {
	float: left;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	position: relative; }

.ny-trackline__content-action__info-caption {
	font-size: 14px;
	color: #bdbdbd;
	margin-left: 10px;
	display: inline-block; }

.ny-trackline__content-action__info-caption--sales {
	color: #202020; }

.ny-trackline__content-action__info-state {
	font-size: 12px;
	color: #4f4f4f;
	margin-left: 14px;
	display: inline-block; }

.ny-trackline__content-action__info-state--disable {
	color: #bdbdbd; }

.ny-trackline__tablet {
	display: none; }

.ny-trackline__thumb-group {
	position: relative; }

.ny-trackline__thumb-info {
	display: none; }

/*@media only screen and (max-width: 1380px) {
	.ny-trackline__content-info__number {
		font-size: 16px;
		margin-right: 20px; }

	.ny-trackline__content-info__caption-status {
		font-size: 12px; }

	.ny-trackline__content-info__caption-link {
		font-size: 12px;
		margin-left: 10px; }

	.ny-trackline__content-info__profile-name {
		font-size: 12px; }

	.ny-trackline__content-info__profile-login {
		font-size: 14px; }

	.ny-trackline__content-line__step-status {
		font-size: 12px; }

	.ny-trackline__content-action__info-caption {
		line-height: 16px;
		font-size: 12px; } }
@media only screen and (max-width: 1060px) {
	.ny-trackline__tablet {
		display: block; }

	.ny-trackline__main {
		display: none; }

	.ny-trackline__content {
		width: 100%;
		float: none; }

	.ny-trackline__content-action {
		float: left;
		width: -webkit-calc(100% - 145px);
		width: -moz-calc(100% - 145px);
		width: calc(100% - 145px); }

	.ny-trackline__content-info__number {
		font-size: 14px;
		font-weight: 700; }

	.ny-trackline__content-info__profile-name {
		font-size: 14px; }

	.ny-trackline__thumb {
		margin-right: 25px; }

	.ny-trackline__content-info__caption-status {
		font-size: 14px; }

	.ny-trackline__content-info__caption {
		margin-bottom: 13px; }

	.ny-trackline__content-action__info {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		width: 100%; }

	.ny-trackline__content-action__info-caption {
		width: -webkit-calc(100% - 70px);
		width: -moz-calc(100% - 70px);
		width: calc(100% - 70px); }

	.ny-trackline__content-action__info-state {
		font-size: 14px;
		line-height: 16px; }

	.ny-trackline__content-line {
		padding-top: 20px;
		clear: both; }

	.ny-trackline__content-line__step-date {
		display: none; }

	.ny-trackline__content-line__step-status {
		margin-bottom: 15px; }

	.ny-trackline__thumb-info__caption {
		font-size: 14px;
		line-height: 18px;
		color: #4f4f4f; } }
@media only screen and (max-width: 660px) {
	.ny-trackline__content-action__info {
		display: block;
		float: none; }

	.ny-trackline__content-action {
		float: left;
		clear: both;
		width: 48%; }

	.ny-trackline__content-line {
		-webkit-box-align: end;
		-webkit-align-items: flex-end;
		-moz-box-align: end;
		-ms-flex-align: end;
		align-items: flex-end;
		position: relative;
		padding-left: 10px; }
	.ny-trackline__content-line:before {
		content: "";
		display: block;
		position: absolute;
		right: 0;
		bottom: 0;
		background-image: url(../pictures/icons/orders-finish.svg);
		width: 15px;
		height: 20px;
		background-color: transparent;
		-webkit-background-size: 100% 100%;
		-moz-background-size: 100%;
		-o-background-size: 100%;
		background-size: 100%;
		-webkit-transform: translateX(0);
		-moz-transform: translateX(0);
		-ms-transform: translateX(0);
		-o-transform: translateX(0);
		transform: translateX(0); }

	.ny-trackline__content-line--hide:before {
		display: none; }

	.ny-trackline__content-line__step--active span:not(.ny-notice) {
		display: none; }
	.ny-trackline__content-line__step--active i {
		display: inline-block; }
	.ny-trackline__content-line__step--active:last-child span {
		display: inline-block;
		font-size: 14px; }
	.ny-trackline__content-line__step--active:last-child i {
		-webkit-transform: translateX(50%);
		-moz-transform: translateX(50%);
		-ms-transform: translateX(50%);
		-o-transform: translateX(50%);
		transform: translateX(50%); }

	.ny-trackline__content-line__step--disable:first-child:after {
		background: #bdbdbd; }
	.ny-trackline__content-line__step--disable i {
		display: inline-block;
		background-color: #bdbdbd;
		background-image: url(../pictures/icons/close-white--bold.svg);
		-webkit-background-size: 55% 55%;
		-moz-background-size: 55%;
		-o-background-size: 55%;
		background-size: 55%; }
	.ny-trackline__content-line__step--disable span {
		-webkit-box-ordinal-group: 2;
		-webkit-order: 1;
		-moz-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1; }
	.ny-trackline__content-line__step--disable .ny-trackline__content-line__step-status {
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: end;
		-webkit-align-items: flex-end;
		-moz-box-align: end;
		-ms-flex-align: end;
		align-items: flex-end; }

	.ny-trackline__content-info__caption, .ny-trackline__content-action__info-state, .ny-trackline__thumb-info__caption--tablet {
		display: none; }

	.ny-trackline__thumb-info {
		display: block;
		width: -webkit-calc(100% - 80px - 10px);
		width: -moz-calc(100% - 80px - 10px);
		width: calc(100% - 80px - 10px); }

	.ny-trackline__thumb-info__state {
		font-size: 14px;
		line-height: 16px;
		color: #27ae60;
		margin-bottom: 9px; }

	.ny-trackline__thumb-info__state--red {
		color: #eb5757; }

	.ny-trackline__thumb {
		float: none;
		margin-right: 0;
		width: auto;
		height: auto;
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: start;
		-webkit-align-items: flex-start;
		-moz-box-align: start;
		-ms-flex-align: start;
		align-items: flex-start;
		-webkit-border-radius: 0;
		-moz-border-radius: 0;
		border-radius: 0;
		overflow: auto;
		margin-bottom: 20px; }
	.ny-trackline__thumb:before {
		display: none; }

	.ny-trackline__thumb-count {
		font-size: 14px; }

	.ny-trackline__thumb-group {
		width: 80px;
		height: 80px;
		margin-right: 10px;
		-webkit-border-radius: 3px;
		-moz-border-radius: 3px;
		border-radius: 3px;
		overflow: hidden; }
	.ny-trackline__thumb-group:before {
		content: "";
		display: block;
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(3, 3, 3, 0.5); }

	.ny-trackline__content-line--end {
		display: none; } }
@media only screen and (max-width: 520px) {
	.ny-trackline__thumb-info__caption {
		font-size: 10px;
		line-height: 16px; }

	.ny-trackline__thumb-info__state {
		font-size: 12px; }

	.ny-trackline__thumb-group {
		width: 70px;
		height: 70px; }

	.ny-trackline__thumb-info {
		width: -webkit-calc(100% - 70px - 10px);
		width: -moz-calc(100% - 70px - 10px);
		width: calc(100% - 70px - 10px); }

	.ny-trackline__content-action {
		width: 100%; }

	.ny-trackline__content-action__info-caption {
		margin-bottom: 25px; }

	.ny-trackline__content-info__profile-name {
		font-size: 12px; }

	.ny-trackline__content-info {
		line-height: 21px; } }*/
.ny-report {
	font-family: "Open Sans";
	font-weight: 500;
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: 1100px;
	padding: 40px 30px 30px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-report__header {
	position: relative;
	margin-bottom: 30px; }
.ny-report__header::after {
	clear: both;
	content: "";
	display: block; }

.ny-report__header-title {
	font-size: 30px;
	text-transform: uppercase;
	color: #202020;
	margin-right: 35px;
	width: 28%;
	float: left; }

.ny-report__header-notice {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	width: 38%;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	float: left; }

.ny-report__header-notice__text {
	font-size: 12px;
	color: #202020; }

.ny-report__status {
	font-size: 12px;
	line-height: 23px;
	color: #333; }

.ny-report__content::after {
	clear: both;
	content: "";
	display: block; }

.ny-report__group {
	width: 67%;
	float: left; }
.ny-report__group:last-child {
	width: 30%;
	float: right; }

.ny-report__table {
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 30px; }

.ny-report__table-head tr {
	border-bottom-color: transparent; }

.ny-report__table-row {
	border-bottom: 1px solid #f2f2f2; }

.ny-report__table-name {
	color: #bdbdbd;
	font-size: 12px;
	text-align: right;
	font-weight: 400;
	padding: 30px 0 10px; }
.ny-report__table-name:first-child {
	text-align: left; }

.ny-report__table-col {
	font-size: 14px;
	color: #202020;
	text-align: right;
	padding: 20px 0 10px; }
.ny-report__table-col span {
	color: #bdbdbd;
	font-size: 12px; }
.ny-report__table-col:first-child {
	text-align: left; }

.ny-report__table-col__break {
	display: none; }

.ny-report__info {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: end;
	-webkit-justify-content: flex-end;
	-moz-box-pack: end;
	-ms-flex-pack: end;
	justify-content: flex-end; }
.ny-report__info::after {
	clear: both;
	content: "";
	display: block; }

.ny-report__info-text {
	font-size: 12px;
	color: #202020;
	margin-right: 20px;
	width: 30%;
	float: left;
	line-height: 1.4; }

.ny-report__info-price {
	font-size: 30px;
	color: #202020; }

.ny-report__fields {
	border: 1px solid #f2f2f2;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 20px 30px; }

.ny-report__fields--paid {
	border: 0;
	padding: 0; }

.ny-report__fields--address {
	border: 0;
	padding: 0;
	margin-bottom: 30px; }
.ny-report__fields--address .ny-report__fields-row {
	margin-bottom: 15px; }

.ny-report__fields-row {
	margin-bottom: 10px;
	position: relative; }
.ny-report__fields-row::after {
	clear: both;
	content: "";
	display: block; }

.ny-report__fields-col {
	width: -webkit-calc(55.55556%);
	width: -moz-calc(55.55556%);
	width: calc(55.55556%);
	float: left;
	margin-left: 0; }
.ny-report__fields-col:last-child {
	width: -webkit-calc(38.88889%);
	width: -moz-calc(38.88889%);
	width: calc(38.88889%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(5.55556% + 0px);
	margin-left: -moz-calc(5.55556% + 0px);
	margin-left: calc(5.55556% + 0px); }

.ny-report__fields-flex {
	width: 20%;
	float: left;
	margin-right: 10%; }

.ny-report__fields-caption {
	font-size: 12px;
	color: #202020;
	line-height: 1.4; }

.ny-report__fields-actions {
	margin-top: 25px; }

.ny-report__fields-notice {
	position: absolute;
	top: 0;
	right: 0; }

/*@media only screen and (max-width: 1130px) {
	.ny-report {
		width: 1030px; }

	.ny-report__header-title {
		width: 30%; }

	.ny-report__header-notice {
		width: 45%; }

	.ny-report__info-text {
		width: 33%; } }
@media only screen and (max-width: 1060px) {
	.ny-report {
		width: 100%;
		margin-top: 0;
		margin-bottom: 0;
		padding: 0; }

	.ny-report__header-notice {
		display: none; }

	.ny-report__header-title {
		width: 100%;
		margin-right: 0;
		text-align: center;
		font-size: 22px; }

	.ny-report__group {
		width: 100%;
		float: none; }
	.ny-report__group:last-child {
		width: 100%;
		float: none; }

	.ny-report__info {
		margin-bottom: 25px; }

	.ny-report__fields {
		position: relative;
		padding: 20px 25px; }
	.ny-report__fields::after {
		clear: both;
		content: "";
		display: block; }

	.ny-report__fields--address, .ny-report__fields--paid {
		padding: 0; }

	.ny-report__fields--address .ny-report__fields-caption, .ny-report__fields--paid .ny-report__fields-caption {
		width: 100%; }

	.ny-report__fields-group {
		width: 50%;
		float: left; }

	.ny-report__fields-row {
		position: inherit; }
	.ny-report__fields-row:last-child {
		margin-bottom: 0; }

	.ny-report__fields-actions {
		margin-top: 70px;
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		-moz-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between; }

	.ny-report__fields-notice {
		top: 20px;
		right: 25px; }

	.ny-report__fields-caption {
		width: 70%; } }
@media only screen and (max-width: 660px) {
	.ny-report__info-text {
		width: 45%; }

	.ny-report__fields {
		padding: 10px; }

	.ny-report__fields--address, .ny-report__fields--paid {
		padding: 0; }

	.ny-report__fields-group {
		width: 100%;
		float: none; }

	.ny-report__fields-actions {
		margin-top: 20px; } }
@media only screen and (max-width: 520px) {
	.ny-report__info-text {
		width: 100%;
		margin-right: 0;
		margin-bottom: 10px; }

	.ny-report__info {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap; }

	.ny-report__info-price {
		width: 100%; }

	.ny-report__table-head {
		display: none; }

	.ny-report__table-col {
		display: block;
		width: 100%;
		padding: 7.5px 0 7.5px; }
	.ny-report__table-col:first-child {
		padding-top: 30px;
		padding-bottom: 15px;
		line-height: 1.4; }
	.ny-report__table-col:last-child {
		padding-bottom: 30px;
		position: relative; }
	.ny-report__table-col:last-child:before {
		content: "";
		display: block;
		position: absolute;
		bottom: 0;
		left: 50%;
		width: 100vw;
		height: 7px;
		background: #efeff4;
		-webkit-transform: translate(-50%, 50%);
		-moz-transform: translate(-50%, 50%);
		-ms-transform: translate(-50%, 50%);
		-o-transform: translate(-50%, 50%);
		transform: translate(-50%, 50%); }

	.ny-report__table-row {
		border-bottom: 0; }
	.ny-report__table-row:last-child .ny-report__table-col:last-child {
		padding-bottom: 0; }
	.ny-report__table-row:last-child .ny-report__table-col:last-child:before {
		display: none; }

	.ny-report__table-col__break {
		float: left;
		display: block;
		font-size: 12px;
		color: #bdbdbd; } }*/
.ny-send {
	font-family: "Open Sans";
	font-weight: 500;
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	width: 820px;
	padding: 50px 30px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-send__box {
	position: relative; }

.ny-send__title {
	font-size: 30px;
	margin-bottom: 25px;
	text-align: center; }

.ny-send__caption {
	font-size: 14px;
	text-align: center;
	margin-bottom: 20px; }

.ny-send__icons {
	margin-bottom: 26px;
	text-align: center; }

.ny-send__list {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row wrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row wrap;
	flex-flow: row wrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	max-height: 433px;
	overflow-y: visible;
	overflow-x: hidden;
	padding-right: 5px;
	margin-bottom: 40px; }
.ny-send__list::after {
	clear: both;
	content: "";
	display: block; }
.ny-send__list::-webkit-scrollbar {
	width: 5px;
	margin-left: 15px; }
.ny-send__list::-webkit-scrollbar-track {
	background: #f2f2f2;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	z-index: 10; }
.ny-send__list::-webkit-scrollbar-thumb {
	background-color: #4f4f4f;
	-webkit-border-radius: 2px;
	border-radius: 2px;
	z-index: 10; }

.ny-send__info {
	text-align: center;
	margin-bottom: 35px; }

.ny-send__info-text {
	font-size: 14px;
	color: #4f4f4f; }

.ny-send__action {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-align: end;
	-webkit-align-items: flex-end;
	-moz-box-align: end;
	-ms-flex-align: end;
	align-items: flex-end; }
.ny-send__action::after {
	clear: both;
	content: "";
	display: block; }

.ny-send__action-group {
	width: -webkit-calc(58.33333%);
	width: -moz-calc(58.33333%);
	width: calc(58.33333%);
	float: left;
	margin-left: 0; }
.ny-send__action-group:last-child {
	width: -webkit-calc(38.88889%);
	width: -moz-calc(38.88889%);
	width: calc(38.88889%);
	float: left;
	margin-left: 0;
	margin-left: -webkit-calc(2.77778% + 0px);
	margin-left: -moz-calc(2.77778% + 0px);
	margin-left: calc(2.77778% + 0px); }

/*@media only screen and (max-width: 1060px) {
	.ny-send {
		width: 100%;
		margin-top: 0;
		margin-bottom: 0;
		padding: 0;
		padding-bottom: 30px; }

	.ny-send__action-group {
		position: relative; } }
@media only screen and (max-width: 800px) {
	.ny-send__list {
		margin-bottom: 30px;
		max-height: 418px; }

	.ny-send__info {
		margin-bottom: 25px; } }
@media only screen and (max-width: 660px) {
	.ny-send__list {
		max-height: 347px; }

	.ny-send__action-group {
		width: 100%;
		margin-bottom: 20px; }
	.ny-send__action-group:last-child {
		margin-left: 0;
		width: 100%; }

	.ny-send__action {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap; }

	.ny-send__info {
		text-align: left; }

	.ny-send__title {
		font-size: 22px; } }
@media only screen and (max-width: 520px) {
	.ny-send__list {
		max-height: none; }

	.ny-send__info {
		margin-bottom: 130px; }

	.ny-send__action {
		position: fixed;
		bottom: 0;
		left: 15px;
		width: -webkit-calc(100% - 30px);
		width: -moz-calc(100% - 30px);
		width: calc(100% - 30px);
		background: #fff;
		padding-top: 10px; } }*/
.ny-followers {
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	width: 445px;
	padding: 40px 20px 30px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.ny-followers__header {
	position: relative;
	margin-bottom: 20px; }

.ny-followers__header-title {
	font-size: 30px;
	text-transform: uppercase;
	line-height: 1; }

/*@media only screen and (max-width: 1060px) {
	.ny-followers {
		padding: 0 15px; }

	.ny-followers__header-title {
		text-transform: none;
		font-size: 25px;
		text-align: center; } }
@media only screen and (max-width: 520px) {
	.ny-followers__header-title {
		font-size: 16px; }

	.ny-followers {
		width: 100%;
		padding: 0; } }*/
.ny-invite {
	font-family: "Open Sans";
	font-weight: 500;
	margin-bottom: 50px; }

.ny-invite__header {
	margin-bottom: 50px;
	background: url(../pictures/invite.png) left bottom/contain no-repeat;
	position: relative; }
.ny-invite__header::after {
	clear: both;
	content: "";
	display: block; }
.ny-invite__header:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	right: 0;
	width: 65%;
	height: 100%;
	background: -webkit-linear-gradient(179.38deg, #f5f5f5 68.98%, rgba(245, 245, 245, 0) 91.95%);
	background: -moz-linear-gradient(179.38deg, #f5f5f5 68.98%, rgba(245, 245, 245, 0) 91.95%);
	background: -o-linear-gradient(179.38deg, #f5f5f5 68.98%, rgba(245, 245, 245, 0) 91.95%);
	background: linear-gradient(270.62deg, #f5f5f5 68.98%, rgba(245, 245, 245, 0) 91.95%);
	z-index: 0; }

.ny-invite__header-title {
	float: right;
	font-size: 36px;
	line-height: 44px;
	padding: 35px 85px 40px 0;
	color: #202020;
	position: relative;
	z-index: 1; }

.ny-invite__row {
	margin-bottom: 50px; }
.ny-invite__row::after {
	clear: both;
	content: "";
	display: block; }
.ny-invite__row:last-child {
	margin-bottom: 0; }

.ny-invite__col {
	width: -webkit-calc(41.66667%);
	width: -moz-calc(41.66667%);
	width: calc(41.66667%);
	float: left;
	margin-left: 0; }
.ny-invite__col:nth-child(2) {
	margin-left: -webkit-calc(8.33333% + 0px);
	margin-left: -moz-calc(8.33333% + 0px);
	margin-left: calc(8.33333% + 0px); }

.ny-invite__col--full {
	width: -webkit-calc(100%);
	width: -moz-calc(100%);
	width: calc(100%);
	float: left;
	margin-left: 0; }

.ny-invite__price {
	font-size: 36px;
	font-weight: 700;
	line-height: 24px;
	color: #000;
	margin-bottom: 20px; }

.ny-invite__caption {
	font-size: 16px;
	line-height: 24px;
	color: #202020; }

.ny-invite__caption--up {
	padding-top: 10px;
	margin-bottom: 25px; }

.ny-invite__title {
	font-size: 22px;
	line-height: 24px;
	font-weight: 700;
	margin-bottom: 20px; }

.ny-invite__form {
	width: 65%; }

.ny-invite__actions {
	padding-top: 5px; }

/*@media only screen and (max-width: 1380px) {
	.ny-invite__header-title {
		font-size: 28px;
		line-height: 34px;
		padding: 50px 40px 50px 0; }

	.ny-invite__col {
		width: -webkit-calc(44.44444%);
		width: -moz-calc(44.44444%);
		width: calc(44.44444%);
		float: left;
		margin-left: 0; }
	.ny-invite__col:nth-child(2) {
		margin-left: -webkit-calc(11.11111% + 0px);
		margin-left: -moz-calc(11.11111% + 0px);
		margin-left: calc(11.11111% + 0px); }

	.ny-invite__col--full {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0; } }
@media only screen and (max-width: 1060px) {
	.ny-invite__form {
		width: 80%; }

	.ny-invite__col {
		width: -webkit-calc(50%);
		width: -moz-calc(50%);
		width: calc(50%);
		float: left;
		margin-left: 0; }
	.ny-invite__col:nth-child(2) {
		width: -webkit-calc(44.44444%);
		width: -moz-calc(44.44444%);
		width: calc(44.44444%);
		float: left;
		margin-left: 0;
		margin-left: -webkit-calc(5.55556% + 0px);
		margin-left: -moz-calc(5.55556% + 0px);
		margin-left: calc(5.55556% + 0px); }

	.ny-invite__col--full {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0; } }
@media only screen and (max-width: 767px) {
	.ny-invite__price {
		font-size: 30px; }

	.ny-invite__caption {
		font-size: 13px;
		line-height: 1.5; } }
@media only screen and (max-width: 660px) {
	.ny-invite__header-title {
		font-size: 16px;
		line-height: 22px;
		padding: 40px 20px 40px 0; }

	.ny-invite__header {
		margin-bottom: 40px; }

	.ny-invite__price {
		font-size: 28px; }

	.ny-invite__col {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		margin-bottom: 40px; }
	.ny-invite__col:last-child {
		margin-bottom: 0; }
	.ny-invite__col:nth-child(2) {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		margin-left: -webkit-calc(0% + 0px);
		margin-left: -moz-calc(0% + 0px);
		margin-left: calc(0% + 0px); }

	.ny-invite__caption {
		font-size: 16px; }

	.ny-invite__caption--up {
		padding-top: 5px; }

	.ny-invite__row {
		margin-bottom: 40px; }

	.ny-invite__title {
		margin-bottom: 10px;
		font-size: 16px;
		line-height: 1.5; }

	.ny-invite__actions {
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row wrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row wrap;
		flex-flow: row wrap;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: start;
		-webkit-align-items: flex-start;
		-moz-box-align: start;
		-ms-flex-align: start;
		align-items: flex-start; } }
@media only screen and (max-width: 520px) {
	.ny-invite__header-title {
		padding-right: 10px; }

	.ny-invite__header {
		-webkit-background-size: cover;
		-moz-background-size: cover;
		-o-background-size: cover;
		background-size: cover;
		background-position: 140% center; }
	.ny-invite__header:before {
		width: 70%; } }*/
.ny-product {
	font-family: "Open Sans";
	font-weight: 500;
	margin-bottom: 60px; }
.ny-product::after {
	clear: both;
	content: "";
	display: block; }

.ny-product__title {
	font-size: 26px;
	color: #000;
	margin-bottom: 10px; }

.ny-product__caption {
	font-size: 12px;
	color: #bdbdbd;
	margin-bottom: 40px; }

.ny-product__sidebar, .ny-product__popup {
	font-family: "Open Sans";
	font-weight: 500; }

.ny-product__content {
	width: 513px;
	float: left;
	margin-left: 0; }

.ny-product__rightbar {
	width: -webkit-calc(31.94444%);
	width: -moz-calc(31.94444%);
	width: calc(31.94444%);
	float: right;
	margin-left: 0;
	 }

.ny-product__mobile, .ny-mobile {
	display: none; }

/*@media only screen and (max-width: 1380px) {
	.ny-product__content {
		width: -webkit-calc(65.27778%);
		width: -moz-calc(65.27778%);
		width: calc(65.27778%);
		float: left;
		margin-left: 0; } }
@media only screen and (max-width: 1060px) {
	.ny-product__mobile {
		display: block; }

	.ny-product__popup--category, .ny-product__popup--size {
		-webkit-align-self: flex-start;
		-ms-flex-item-align: start;
		align-self: flex-start;
		width: 100%; }

	.ny-product__popup--category, .ny-product__content, .ny-product__rightbar, .ny-product__sidebar {
		display: none; }

	.ny-product__mobile-header {
		text-align: center;
		margin-bottom: 40px; }

	.ny-product__mobile-header__title {
		display: inline-block;
		font-size: 22px;
		color: #000; }

	.ny-product__nav {
		list-style: none;
		padding: 0;
		margin: 0;
		margin-bottom: 60px; }

	.ny-product__nav-item {
		margin-bottom: 40px;
		position: relative; }
	.ny-product__nav-item:before {
		content: "";
		display: block;
		position: absolute;
		bottom: 0;
		left: 50%;
		width: 100vw;
		-webkit-transform: translate(-50%, 100%);
		-moz-transform: translate(-50%, 100%);
		-ms-transform: translate(-50%, 100%);
		-o-transform: translate(-50%, 100%);
		transform: translate(-50%, 100%);
		height: 7px;
		background: #efeff4; }
	.ny-product__nav-item:last-child {
		margin-bottom: 0; }
	.ny-product__nav-item:last-child:before {
		display: none; }

	.ny-product__nav-item__wrap {
		padding-bottom: 40px; }

	.ny-product__title {
		font-size: 14px;
		font-weight: 700;
		margin-bottom: 24px; } }
@media only screen and (max-width: 1060px) {
	.ny-mobile {
		display: block;
		font-family: "Open Sans";
		font-weight: 500; }

	.ny-mobile__header {
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: center;
		-webkit-align-items: center;
		-moz-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		margin-bottom: 30px;
		text-decoration: none;
		color: inherit;
		position: relative; }
	.ny-mobile__header::after {
		clear: both;
		content: "";
		display: block; }

	.ny-mobile__picture {
		margin-right: 20px;
		float: left; }

	.ny-mobile__info {
		float: left; }

	.ny-mobile__info-name {
		font-size: 24px;
		font-weight: 700;
		margin-bottom: 10px;
		color: #202020;
		line-height: 1; }

	.ny-mobile__info-caption {
		font-size: 13px;
		text-decoration: none;
		color: #202020;
		line-height: 1;
		position: relative; }

	.ny-mobile__list {
		list-style: none;
		padding: 0;
		margin: 0;
		border-bottom: 7px solid #efeff4;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		position: relative; }
	.ny-mobile__list:before {
		content: "";
		display: block;
		height: 7px;
		width: 100vw;
		background: #efeff4;
		position: absolute;
		bottom: 0;
		left: 50%;
		-webkit-transform: translate(-50%, 100%);
		-moz-transform: translate(-50%, 100%);
		-ms-transform: translate(-50%, 100%);
		-o-transform: translate(-50%, 100%);
		transform: translate(-50%, 100%); }
	.ny-mobile__list:first-child:not(.ny-mobile__list-sub) > .ny-mobile__item:first-child {
		border-top: .5px solid #cdced3; }
	.ny-mobile__list:last-child {
		border-bottom: 0; }
	.ny-mobile__list:last-child:before {
		background-color: transparent; }

	.ny-mobile__list-sub {
		width: -webkit-calc(100% - 27px);
		width: -moz-calc(100% - 27px);
		width: calc(100% - 27px);
		margin-left: 27px; }
	.ny-mobile__list-sub:before {
		height: 1px;
		left: -webkit-calc(50% - 13.5px);
		left: -moz-calc(50% - 13.5px);
		left: calc(50% - 13.5px);
		background: #cdced3; }

	.ny-mobile__item {
		border-bottom: .5px solid #cdced3;
		font-size: 14px;
		color: #202020; }
	.ny-mobile__item:last-child {
		border-bottom: 0; }

	.ny-mobile__item--show {
		font-weight: 700; }
	.ny-mobile__item--show > .ny-mobile__link {
		padding-bottom: 0; }

	.ny-mobile__item-sub {
		font-weight: 400; }

	.ny-mobile__link {
		text-decoration: none;
		color: inherit;
		display: -webkit-box;
		display: -webkit-flex;
		display: -moz-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-orient: horizontal;
		-webkit-box-direction: normal;
		-webkit-flex-flow: row nowrap;
		-moz-box-orient: horizontal;
		-moz-box-direction: normal;
		-ms-flex-flow: row nowrap;
		flex-flow: row nowrap;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: center;
		-webkit-align-items: center;
		-moz-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		position: relative;
		padding: 10px 0;
		line-height: 21px;
		cursor: pointer; }

	.ny-mobile__count {
		font-size: 13px;
		color: #8c8c8c;
		margin-left: 10px;
		font-weight: 400; } }
@media only screen and (max-width: 520px) {
	.ny-mobile__info-name {
		font-size: 18px; }

	.ny-mobile__item {
		font-size: 13px; }

	.ny-mobile__header {
		margin-bottom: 20px; } }*/
.ny-header {
	margin-bottom: 60px; }

/*@media only screen and (max-width: 1380px) {
	.ny-header {
		margin-bottom: 30px; } }
@media only screen and (max-width: 1060px) {
	.ny-header {
		margin-bottom: 0; } }*/
.ny-content {
	width: -webkit-calc(80.55556% - 27.08333px);
	width: -moz-calc(80.55556% - 27.08333px);
	width: calc(80.55556% - 27.08333px);
	float: left;
	margin-left: 15px;
	margin-left: 0;
	padding-left: 25px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

.ny-content--product {
	padding-left: 105px; }
/*
@media only screen and (max-width: 1380px) {
	.ny-content {
		width: calc(100% - 280px);
		float: left;
		margin-left: 0;
		padding-left: 20px; } }
@media only screen and (max-width: 1024px) {
	.ny-content {
		width: -webkit-calc(100%);
		width: -moz-calc(100%);
		width: calc(100%);
		float: left;
		margin-left: 0;
		padding-left: 0; } }*/

.ny-layer {
	padding: 52px 0 0;
}

.ny-sidebar {
	/*width: -webkit-calc(22.22222% - 18.33333px);
	width: -moz-calc(22.22222% - 18.33333px);
	width: calc(22.22222% - 18.33333px);*/
	width: 280px;
	float: left;
	margin-left: 0; }

/*@media only screen and (max-width: 1024px) {
	.ny-sidebar {
		display: none; } }*/
.ny-popup {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.8);
	z-index: 5;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	overflow-y: scroll; }

.ny-popup--orders {
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }

.ny-popup--sales {
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	padding: 50px 0;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }

/*@media only screen and (max-width: 1024px) {
	.ny-popup {
		position: static;
		width: auto;
		max-width: 768px;
		margin: 0 auto;
		background: 0 0;
		z-index: 0;
		overflow-y: visible; }
	.ny-popup ~ .ny-container {
		display: none; }

	.ny-popup--sales {
		padding: 0; }

	.ny-popup--followers {
		-webkit-box-align: start;
		-webkit-align-items: flex-start;
		-moz-box-align: start;
		-ms-flex-align: start;
		align-items: flex-start; } }
@media only screen and (max-width: 800px) {
	.ny-popup {
		max-width: -webkit-calc(100% - 30px);
		max-width: -moz-calc(100% - 30px);
		max-width: calc(100% - 30px); } }*/
.nyp-af {
	margin-bottom: 50px; }

.nyp-af__form {
	padding-top: 8px; }

.nyp-af__fields {
	margin-bottom: 23px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	position: relative; }
.nyp-af__fields:hover .nyp-tooltip {
	display: block; }
.nyp-af__fields:last-child {
	margin-bottom: 0;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }

.nyp-af__cat {
	width: 365px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between; }

.nyp-af__cat-text {
	font-size: 13px;
	line-height: 27px;
	color: #202020;
	white-space: nowrap;
	overflow: hidden;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis; }

.nyp-af__area {
	border: 1px solid #e6e6e6;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	resize: none;
	min-height: 118px;
	width: 365px;
	display: block;
	padding: 9px 10px;
	font-size: 14px;
	color: #202020;
	outline: 0;
	-webkit-box-shadow: 0 0 0;
	-moz-box-shadow: 0 0 0;
	box-shadow: 0 0 0;
	-webkit-appearance: none; }
.nyp-af__area:focus {
	border-color: #333;
	font-size: 14px; }

/*@media only screen and (max-width: 1060px) {
	.nyp-af__form {
		padding-top: 0;
		margin-top: -12px; }

	.nyp-af {
		margin-bottom: 0;
		padding-bottom: 40px; }

	.nyp-af__area {
		width: 100%;
		font-size: 16px; }
	.nyp-af__area:focus {
		font-size: 14px; }

	.nyp-af__fields {
		margin-bottom: 0; } }
@media only screen and (max-width: 520px) {
	.nyp-af__area {
		min-height: 70px; } }*/
.nyp-calc {
	width: 227px;
	border: 1px solid #b3eac5;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 28px 16px 18px;
	position: absolute;
	top: 0;
	right: -30px;
	-webkit-transform: translateX(100%);
	-moz-transform: translateX(100%);
	-ms-transform: translateX(100%);
	-o-transform: translateX(100%);
	transform: translateX(100%);
	font-family: Roboto;
	font-weight: 500;
	display: none;
	z-index: 2;
	background: #fff; }

.nyp-calc__close {
	background: url(../pictures/icons/close-media.svg) center center/100% 100% no-repeat;
	width: 13px;
	height: 13px;
	position: absolute;
	top: 15px;
	right: 16px; }

.nyp-calc__fields {
	margin-bottom: 15px; }
.nyp-calc__fields:last-child {
	margin-bottom: 0; }

.nyp-calc__info {
	margin-top: 33px; }

.nyp-calc__field {
	position: relative;
	line-height: 23px; }

.nyp-calc__price {
	font-size: 23px;
	color: #00aa25;
	line-height: 1;
	display: inline-block; }

.nyp-calc__currency {
	display: inline-block;
	color: #9b9b9b;
	font-size: 14px;
	position: absolute;
	top: 50%;
	right: 10px;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%); }

.nyp-category {
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	width: 1040px;
	/*overflow: hidden;*/
	position: relative; }

.nyp-category__title {
	font-size: 30px;
	text-transform: uppercase;
	color: #202020;
	margin-left: 10px;
	letter-spacing: .3px;
	margin-bottom: 28px;}

.nyp-category__nav {
	list-style: none;
	padding: 0;
	margin: 0;
	padding-top: 17px;
	padding-right: 40px;
	border-right: 1px solid #eaeaea;
	width: 234px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	min-height: 275px;
	position: relative; }

.nyp-category__item {
	margin-bottom: 12px;}
.nyp-category__item:last-child {
	margin-bottom: 0; }

.nyp-category__link {
	font-size: 14px;
	color: #333;
	cursor: pointer;
	text-decoration: none;
	line-height: 19px;
	padding: 6px 16px 6px 10px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	display: block; }

.nyp-category__link--active {
	background: #eaeaea;
	font-weight: 700;
	position: relative; }
.nyp-category__link--active:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	right: 16px;
	-webkit-transform: translateX(100%);
	-moz-transform: translateX(100%);
	-ms-transform: translateX(100%);
	-o-transform: translateX(100%);
	transform: translateX(100%);
	border: 16px solid #fff;
	border-left-color: #eaeaea;
	border-left-width: 12px; }

.nyp-category__sub {
	list-style: none;
	padding: 0;
	margin: 0;
	position: absolute;
	top: 0;
	right: 0;
	-webkit-transform: translateX(100%);
	-moz-transform: translateX(100%);
	-ms-transform: translateX(100%);
	-o-transform: translateX(100%);
	transform: translateX(100%);
	padding-top: 17px;
	padding-right: 30px;
	padding-left: 20px;
	border-right: 1px solid #eaeaea;
	width: 252px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	min-height: 275px;
	display: none; }

.nyp-category__sub--clear {
	border-right: 0;
	padding-right: 0; }

/*@media only screen and (max-width: 1380px) {
	.nyp-category {
		width: 1030px;
		padding-left: 20px;
		padding-right: 20px; } }
@media only screen and (max-width: 1024px) {
	.nyp-category__title {
		display: none; }

	.nyp-category {
		width: 100%;
		padding: 0; }

	.nyp-category__nav {
		border-right: 0;
		padding: 0;
		width: 100%;
		overflow: hidden; }

	.nyp-category__sub {
		width: 100%;
		-webkit-transition: all .3s ease-in;
		-o-transition: all .3s ease-in;
		-moz-transition: all .3s ease-in;
		transition: all .3s ease-in;
		background: #fff;
		padding: 0;
		z-index: 1;
		border-right: 0;
		display: block; }

	.nyp-category__sub--show {
		-webkit-transform: translateX(0);
		-moz-transform: translateX(0);
		-ms-transform: translateX(0);
		-o-transform: translateX(0);
		transform: translateX(0); }

	.nyp-category__item {
		margin-bottom: 0;
		border-bottom: 1px solid #f0f0f0; }

	.nyp-category__link {
		padding-top: 12px;
		padding-bottom: 12px;
		padding-left: 0;
		width: 100%; } }*/
.nyp-cf {
	margin-bottom: 70px;
}

.nyp-cf__output {
	padding-top: 8px; }

.nyp-cf__field {
	width: 100%;
	position: relative; }

.nyp-cf__field-checkmark {
	position: relative;
	border: 1px solid #e6e6e6;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	padding: 15px;
	width: 100%; }
.nyp-cf__field-checkmark:before {
	content: "";
	display: block;
	position: absolute;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	top: 50%;
	left: 15px;
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background: rgba(196, 196, 196, 0.06);
	border: 1px solid #bdbdbd;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box; }
.nyp-cf__field-checkmark:after {
	content: "";
	display: block;
	position: absolute;
	-webkit-transform: translateY(-50%);
	-moz-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	-o-transform: translateY(-50%);
	transform: translateY(-50%);
	top: 50%;
	left: 20px;
	width: 6px;
	height: 6px;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	background: #333;
	opacity: 0;
	-webkit-transition: opacity .2 ease-in;
	-o-transition: opacity .2 ease-in;
	-moz-transition: opacity .2 ease-in;
	transition: opacity .2 ease-in; }

.nyp-cf__field-info {
	padding-left: 35px; }

.nyp-cf__field-info__name {
	font-size: 14px;
	color: #202020;
	margin-bottom: 8px; }

.nyp-cf__field-info__phone {
	font-size: 14px;
	color: #202020;
	margin-bottom: 6px; }

.nyp-cf__field-info__address {
	font-size: 14px;
	color: #202020; }

.nyp-cf__input {
	margin-top: 30px; }

.nyp-cf__row {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	margin-bottom: 18px;}
.nyp-cf__row:last-child {
	margin-bottom: 0; }

.nyp-cf__row--output {
	margin-bottom: 20px; }

.nyp-cf__col {
	width: calc(50% - 20px);}

.nyp-cf__half {
	width: calc(50% - 18px);
	float: left; }
.nyp-cf__half:last-child {
	float: right; }

.nyp-cf__fields {
	position: relative; }
.nyp-cf__fields:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	width: 0%;
	height: 1px;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	background: #333;
	-webkit-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-o-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-moz-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s; }
.nyp-cf__fields.show:before {
	width: 100%;
	z-index: 1; }
.nyp-cf__fields.error:before {
	background: red; }

/*@media only screen and (max-width: 1024px) {
	.nyp-cf {
		margin-bottom: 0;
		padding-bottom: 40px; }

	.nyp-cf__output {
		padding-top: 0; }

	.nyp-cf__input {
		margin-top: 0; }

	.nyp-cf__field-checkmark {
		border: 0;
		padding-top: 0;
		padding-bottom: 20px;
		border-bottom: 1px solid #f0f0f0 !important; }
	.nyp-cf__field-checkmark:before {
		top: 0;
		-webkit-transform: translateY(0);
		-moz-transform: translateY(0);
		-ms-transform: translateY(0);
		-o-transform: translateY(0);
		transform: translateY(0); }
	.nyp-cf__field-checkmark:after {
		top: 5px;
		-webkit-transform: translateY(0);
		-moz-transform: translateY(0);
		-ms-transform: translateY(0);
		-o-transform: translateY(0);
		transform: translateY(0); }

	.nyp-cf__field-info__name {
		font-size: 13px; }

	.nyp-cf__field-info__address, .nyp-cf__field-info__phone {
		font-size: 13px;
		color: #bdbdbd; } }
@media only screen and (max-width: 520px) {
	.nyp-cf__col {
		width: 100%;
		margin-bottom: 50px; }
	.nyp-cf__col:last-child {
		margin-bottom: 0; }

	.nyp-cf__row {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap; }

	.nyp-cf__field-checkmark {
		padding-left: 0;
		padding-right: 0; }
	.nyp-cf__field-checkmark:before {
		left: auto;
		right: 0; }
	.nyp-cf__field-checkmark:after {
		left: auto;
		right: 5px; }

	.nyp-cf__field-info {
		padding-left: 0;
		padding-right: 35px; } }*/
.nyp-draft {
	border: 1px solid #e5e5e5;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 38px 21px 31px;
	font-family: "Open Sans";
	font-weight: 500; }

.nyp-draft__title {
	padding-left: 14px;
	margin-bottom: 15px;
	font-size: 14px;
	color: #202020; }

.nyp-draft__item {
	padding: 14px 8px 14px 14px;
	margin-bottom: 14px;
	position: relative; }
.nyp-draft__item::after {
	clear: both;
	content: "";
	display: block; }
.nyp-draft__item:last-child {
	margin-bottom: 0; }
.nyp-draft__item:hover {
	background: #f7f7f7; }
.nyp-draft__item:hover .ny-button--nyp-draft--close {
	opacity: 1; }
.nyp-draft__item.active:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -21px;
	height: 100%;
	width: 3px;
	background: #242424; }

.nyp-draft__item-thumb {
	border: 1px solid #e6e6e6;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	margin-right: 10px;
	width: 53px;
	float: left;
	height: 87px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	background: #fff; }

.nyp-draft__item-info {
	float: right;
	width: -webkit-calc(100% - 53px - 10px);
	width: -moz-calc(100% - 53px - 10px);
	width: calc(100% - 53px - 10px);
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-webkit-flex-flow: column nowrap;
	-moz-box-orient: vertical;
	-moz-box-direction: normal;
	-ms-flex-flow: column nowrap;
	flex-flow: column nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	height: 87px; }

.nyp-draft__item-brand {
	font-size: 14px;
	color: #202020;
	white-space: nowrap;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	overflow: hidden;
	text-transform: uppercase;
	width: 100%;
	padding-right: 25px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	position: relative; }

.nyp-draft__item-caption {
	font-size: 12px;
	color: #bdbdbd;
	min-height: 16px;
	white-space: nowrap;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
	overflow: hidden;
	width: 100%;
	line-height: 1.4; }

.nyp-draft__item-price {
	font-family: Roboto;
	font-weight: 500;
	font-size: 14px;
	color: #202020; }

.nyp-of {
	margin-bottom: 50px; }

.nyp-of__title-label {
	display: inline-block;
	font-size: 16px;
	color: #bdbdbd;
	position: relative;
	top: -2px;
	font-weight: 400;
	margin-left: 5px; }

.nyp-of__box {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start; }
/*.nyp-of__box.nyp-of__company > .nyp-of__col:first-child {
	-webkit-box-ordinal-group: 2;
	-webkit-order: 1;
	-moz-box-ordinal-group: 2;
	-ms-flex-order: 1;
	order: 1; }*/
.nyp-of__box.nyp-of__company .nyp-of__options {
	margin-bottom: 17px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	justify-content: space-between;
align-items: center;}
.nyp-of__box.nyp-of__company .nyp-of__group:first-child {
	margin-bottom: 32px; }

.nyp-of__col {
	width: calc(50% - 18px);
	position: relative; }

.nyp-of__fields {
	margin-bottom: 18px;
	position: relative; }
.nyp-of__fields::after {
	clear: both;
	content: "";
	display: block; }
.nyp-of__fields:last-child {
	margin-bottom: 0; }
.nyp-of__fields:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	width: 0%;
	height: 1px;
	-webkit-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-o-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-moz-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	background: #333; }
.nyp-of__fields.show:before {
	width: 100%;
	z-index: 1; }

.nyp-of__half {
	width: -webkit-calc(50% - 10px);
	width: -moz-calc(50% - 10px);
	width: calc(50% - 10px);
	float: left;
	position: relative; }
.nyp-of__half:last-child {
	float: right; }
.nyp-of__half:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	width: 0%;
	height: 1px;
	-webkit-transition: all .2s ease-in;
	-o-transition: all .2s ease-in;
	-moz-transition: all .2s ease-in;
	transition: all .2s ease-in;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	background: #333; }
.nyp-of__half.show:before {
	width: 100%;
	z-index: 1; }
.nyp-of__half.error:before {
	background: red; }

.nyp-of__group {
	margin-bottom: 40px; }
.nyp-of__group:last-child {
	margin-bottom: 0; }

.nyp-of__options {
	margin-bottom: 20px; }

.nyp-of__options:last-child {
	margin-bottom: 0; }

.nyp-of__sized {
	margin-bottom: 20px; }

.nyp-of__info {
	width: -webkit-calc(100% - 50px);
	width: -moz-calc(100% - 50px);
	width: calc(100% - 50px); }

.nyp-of__label {
	font-size: 14px;
	line-height: 22px;
	color: #000; }

.nyp-of__caption {
	font-size: 12px;
	line-height: 14px;
	color: #8e8e93; }

.nyp-of__trigger {
	position: relative;
	width: 40px;
	}

/*@media only screen and (max-width: 1024px) {
	.nyp-of {
		margin-bottom: 0; }

	.nyp-of__title-label {
		font-size: 14px;
		top: 0;
		margin-left: 8px; }

	.nyp-of__caption--inline, .nyp-of__label--inline {
		display: inline-block; }

	.nyp-of__caption {
		color: #bdbdbd;
		font-size: 14px; }

	.nyp-of__caption--inline {
		margin-left: 7px; } }
@media only screen and (max-width: 660px) {
	.nyp-of__col {
		width: -webkit-calc(50% - 5px);
		width: -moz-calc(50% - 5px);
		width: calc(50% - 5px); }
	.nyp-of__col:first-child {
		width: -webkit-calc(47% - 5px);
		width: -moz-calc(47% - 5px);
		width: calc(47% - 5px); } }
@media only screen and (max-width: 520px) {
	.nyp-of__col {
		width: 100%; }
	.nyp-of__col:first-child {
		width: 100%;
		margin-bottom: 40px; }

	.nyp-of__box {
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap; }
	.nyp-of__box.nyp-of__company > .nyp-of__col:first-child {
		-webkit-box-ordinal-group: 1;
		-webkit-order: 0;
		-moz-box-ordinal-group: 1;
		-ms-flex-order: 0;
		order: 0; } }*/
.nyp-photo::after {
	clear: both;
	content: "";
	display: block; }

.nyp-photo__caption {
	display: none;
	padding: 10px 12px 20px 0;
	font-size: 14px;
	color: #202020; }

.nyp-photo__form {
	margin-top: 25px;
	margin-bottom: 19px;
	width: -webkit-calc(83.33333%);
	width: -moz-calc(83.33333%);
	width: calc(83.33333%);
	float: left;
	margin-left: 0; }

.nyp-photo__fields {
	display: none;
	position: relative;
	margin-bottom: 15px; }

.nyp-photo__gallery {
	list-style: none;
	padding: 0;
	margin: 0;
	display: block;
	clear: both; }

.nyp-photo__gallery-item {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: start;
	-webkit-align-items: flex-start;
	-moz-box-align: start;
	-ms-flex-align: start;
	align-items: flex-start;
	margin-bottom: 30px;
	position: relative; }
.nyp-photo__gallery-item:hover .nyp-tooltip {
	display: block; }

.nyp-photo__gallery-item--form {
	-webkit-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap; }

.nyp-photo__gallery-col {
	width: -webkit-calc(50% - 7px);
	width: -moz-calc(50% - 7px);
	width: calc(50% - 7px);
	position: relative; }

.nyp-photo__gallery-col--form {
	width: -webkit-calc(25% - 5.25px);
	width: -moz-calc(25% - 5.25px);
	width: calc(25% - 5.25px);
	border: 1px dashed #e6e6e6;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.nyp-photo__gallery-label {
	font-size: 12px;
	color: #bdbdbd;
	margin-bottom: 5px;
	line-height: 20px; }

.nyp-photo__gallery-picture {
	border: 1px dashed #e6e6e6;
	height: 218px;
	margin-top: 5px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px;
	position: relative; }

.nyp-photo__gallery-picture--fill {
	border-style: solid; }

.nyp-photo__gallery-title {
	font-size: 20px;
	color: #000;
	margin-bottom: 15px; }

.nyp-photo__gallery-addon {
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-webkit-flex-flow: column nowrap;
	-moz-box-orient: vertical;
	-moz-box-direction: normal;
	-ms-flex-flow: column nowrap;
	flex-flow: column nowrap;
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-moz-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	min-height: 88px; }

.nyp-photo__gallery-addon__caption {
	color: #d6d6d8;
	font-size: 11px;
	display: block;
	width: 100%;
	text-align: center; }

/*@media only screen and (max-width: 1380px) {
	.nyp-photo__gallery-picture {
		min-height: 155px; }

	.nyp-photo__gallery-title {
		font-size: 17px; }

	.nyp-photo__gallery-col--form {
		width: -webkit-calc(50% - 5.25px);
		width: -moz-calc(50% - 5.25px);
		width: calc(50% - 5.25px);
		margin-bottom: 6px; } }
@media only screen and (max-width: 1060px) {
	.nyp-photo {
		padding-bottom: 40px; }

	.nyp-photo__gallery-item {
		margin-bottom: 25px;
		width: -webkit-calc(50% - 13.5px);
		width: -moz-calc(50% - 13.5px);
		width: calc(50% - 13.5px);
		float: left; }
	.nyp-photo__gallery-item:nth-child(2n) {
		float: right; }
	.nyp-photo__gallery-item:nth-child(2n+1) {
		clear: both; }

	.nyp-photo__gallery-item--form {
		width: 100%;
		float: none;
		-webkit-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		flex-wrap: wrap;
		margin-bottom: 0; }

	.nyp-photo__gallery-item--app {
		width: 100%;
		float: none;
		margin-bottom: 0;
		-webkit-box-pack: start;
		-webkit-justify-content: flex-start;
		-moz-box-pack: start;
		-ms-flex-pack: start;
		justify-content: flex-start;
		-webkit-box-align: center;
		-webkit-align-items: center;
		-moz-box-align: center;
		-ms-flex-align: center;
		align-items: center; }

	.nyp-photo__gallery-col {
		width: -webkit-calc(50% - 4.5px);
		width: -moz-calc(50% - 4.5px);
		width: calc(50% - 4.5px); }

	.nyp-photo__gallery-col--form {
		width: -webkit-calc(25% - 13px);
		width: -moz-calc(25% - 13px);
		width: calc(25% - 13px);
		float: left;
		margin-right: 8px;
		margin-bottom: 0; }
	.nyp-photo__gallery-col--form:nth-child(3) {
		margin-right: 28px; }
	.nyp-photo__gallery-col--form:last-child {
		margin-right: 0; }

	.nyp-photo__gallery-picture {
		min-height: 220px; }

	.nyp-photo__gallery-picture--app {
		width: 260px;
		min-height: 90px;
		margin-right: 35px; }

	.nyp-photo__gallery-label {
		font-size: 14px;
		margin-bottom: 15px;
		display: block; }

	.nyp-photo__gallery-title {
		font-size: 14px;
		color: #bdbdbd;
		display: block;
		width: 100%; }

	.nyp-photo__gallery-addon {
		min-height: 220px; }

	.nyp-photo__gallery-addon__caption {
		font-size: 14px;
		line-height: 1.5; } }
@media only screen and (max-width: 660px) {
	.nyp-photo__gallery-addon, .nyp-photo__gallery-picture {
		min-height: auto;
		height: 150px; }

	.nyp-photo__gallery-addon--app, .nyp-photo__gallery-picture--app {
		height: 90px;
		margin-right: 15px; }

	.nyp-photo__gallery-label, .nyp-photo__gallery-title {
		font-size: 12px;
		margin-bottom: 5px; }

	.nyp-photo__gallery-label--app, .nyp-photo__gallery-title--app {
		font-size: 14px;
		margin-bottom: 0; } }
@media only screen and (max-width: 520px) {
	.nyp-photo__gallery-item {
		margin-bottom: 15px;
		width: 100%; }

	.nyp-photo__gallery-item--form, .nyp-photo__gallery-item--app {
		margin-bottom: 0; }

	.nyp-photo__gallery-picture {
		height: 220px; }

	.nyp-photo__gallery-picture--app {
		height: 90px; }

	.nyp-photo__gallery-addon {
		height: 105px; }

	.nyp-photo__gallery-col--form {
		width: -webkit-calc(25% - 11px);
		width: -moz-calc(25% - 11px);
		width: calc(25% - 11px);
		margin-right: 12px; }
	.nyp-photo__gallery-col--form:nth-child(3) {
		margin-right: 12px; }

	.nyp-photo__gallery-addon__caption {
		font-size: 10px;
		line-height: 1.2; } }*/
.nyp-pf {
	margin-bottom: 69px;
	position: relative; }
.nyp-pf::after {
	clear: both;
	content: "";
	display: block; }
.nyp-pf:hover .nyp-tooltip {
	display: block; }

.nyp-pf__calc-label {
	background: #f2f2f2;
	/*display: inline-block;*/
	font-size: 16px;
	color: #bdbdbd;
	padding: 5px 7px;
	-webkit-transition: all .17s ease-in;
	-o-transition: all .17s ease-in;
	-moz-transition: all .17s ease-in;
	transition: all .17s ease-in;
	margin-left: 15px;
	cursor: pointer;
	position: relative;
	display: none;
	top: -2px; }
.nyp-pf__calc-label:hover {
	color: #333; }

/*.nyp-pf__fields {
	width: -webkit-calc(50% - 25px);
	width: -moz-calc(50% - 25px);
	width: calc(50% - 25px); }*/
.nyp-pf__fields::after {
	clear: both;
	content: "";
	display: block; }
.nyp-pf__fields{
	margin-top: 30px;
}
.nyp-pf .ny-product__caption{
	display: none;
}
.nyp-pf__field {
	position: relative;
	/*width: -webkit-calc(50% - 15px);
	width: -moz-calc(50% - 15px);
	width: calc(50% - 15px);*/
	float: left;
	width: 122px;
	margin-right: 28px;
}
.nyp-pf__fields .nyp-pf__field:last-child {
	margin-right: 0;
}
.nyp-pf__field:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	width: 0%;
	height: 1px;
	-webkit-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-o-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-moz-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	background: #333; }
.nyp-pf__field.show:before {
	width: 100%;
	z-index: 1; }
.nyp-pf__field.error:before {
	background: red; }

.nyp-pf__field--hidden {
	opacity: 0;
	-webkit-transition: all .2s ease-in;
	-o-transition: all .2s ease-in;
	-moz-transition: all .2s ease-in;
	transition: all .2s ease-in;
	/*float: right; */}
.nyp-pf__field--hidden.active {
	opacity: 1; }

/*@media only screen and (max-width: 1060px) {
	.nyp-pf {
		margin-bottom: 0;
		padding-bottom: 40px; } }
@media only screen and (max-width: 660px) {
	.nyp-pf__fields {
		width: -webkit-calc(53% - 25px);
		width: -moz-calc(53% - 25px);
		width: calc(53% - 25px); } }
@media only screen and (max-width: 520px) {
	.nyp-pf__fields {
		width: 230px; } }*/
.nyp-remove {
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	max-width: 540px;
	padding: 30px 35px 30px 25px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.nyp-remove__title {
	font-size: 18px;
	font-weight: 700;
	color: #333;
	margin-bottom: 20px; }

.nyp-remove__caption {
	font-size: 16px;
	color: #828282;
	margin-bottom: 40px;
	line-height: 1.5; }

.nyp-remove__actions {
	text-align: right; }

/*@media only screen and (max-width: 520px) {
	.nyp-remove {
		width: 100%;
		margin-top: 0;
		margin-bottom: 0;
		padding: 0;
		max-width: 100%;
		min-width: 100%;
		-webkit-align-self: flex-start;
		-ms-flex-item-align: start;
		align-self: flex-start; } }*/
.nyp-sf {
	margin-bottom: 50px; }

.nyp-sf__type {
	margin-bottom: 40px;
	padding-top: 20px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: justify;
	-webkit-justify-content: space-between;
	-moz-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center; }

.nyp-sf__row {
	margin-bottom: 20px;
	position: relative; }
.nyp-sf__row::after {
	clear: both;
	content: "";
	display: block; }
.nyp-sf__row:last-child {
	margin-bottom: 0; }

.nyp-sf__col {
	margin-right: 30px;
	display: -webkit-box;
	display: -webkit-flex;
	display: -moz-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: horizontal;
	-webkit-box-direction: normal;
	-webkit-flex-flow: row nowrap;
	-moz-box-orient: horizontal;
	-moz-box-direction: normal;
	-ms-flex-flow: row nowrap;
	flex-flow: row nowrap;
	-webkit-box-pack: start;
	-webkit-justify-content: flex-start;
	-moz-box-pack: start;
	-ms-flex-pack: start;
	justify-content: flex-start;
	-webkit-box-align: center;
	-webkit-align-items: center;
	-moz-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	width: 30%;
	float: left; }
.nyp-sf__col:nth-child(2) {
	width: auto;
	margin-right: 0; }

/*@media only screen and (max-width: 1060px) {
	.nyp-sf {
		margin-bottom: 0;
		padding-bottom: 40px; }

	.nyp-sf__col {
		width: 100%; }
	.nyp-sf__col:nth-child(2) {
		width: 100%;
		-webkit-box-pack: end;
		-webkit-justify-content: flex-end;
		-moz-box-pack: end;
		-ms-flex-pack: end;
		justify-content: flex-end; }

	.nyp-sf__type {
		padding-top: 0;
		position: relative; }
	.nyp-sf__type:before {
		content: "";
		display: block;
		height: 2px;
		width: 100vw;
		position: absolute;
		bottom: -1px;
		left: 50%;
		-webkit-transform: translateX(-50%);
		-moz-transform: translateX(-50%);
		-ms-transform: translateX(-50%);
		-o-transform: translateX(-50%);
		transform: translateX(-50%);
		background: #efeff4;
		z-index: 1; } }*/
.nyp-size {
	background: #fff;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	font-family: "Open Sans";
	font-weight: 500;
	width: 760px;
	padding: 30px 25px;
	-webkit-border-radius: 2px;
	-moz-border-radius: 2px;
	border-radius: 2px; }

.nyp-size__title {
	font-size: 22px;
	color: #333;
	margin-bottom: 35px; }

.nyp-size__fields {
	margin-bottom: 55px; }

.nyp-size__row {
	margin-bottom: 50px; }
.nyp-size__row::after {
	clear: both;
	content: "";
	display: block; }
.nyp-size__row:last-child {
	margin-bottom: 0; }
.featherlight-content .nyp-size__row{
	margin-bottom: 20px;
}
.nyp-size__col {
	width: calc(50% - 20px);
	float: left; }
.nyp-size__col:nth-child(2) {
	float: right; }

.nyp-size__half {
	width: -webkit-calc(50% - 5px);
	width: -moz-calc(50% - 5px);
	width: calc(50% - 5px);
	float: left; }
.nyp-size__half:nth-child(2) {
	float: right; }

.nyp-size__field {
	position: relative; }
.nyp-size__field:before {
	content: "";
	position: absolute;
	bottom: 0;
	left: 50%;
	width: 0%;
	height: 1px;
	-webkit-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-o-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-moz-transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s;
	-webkit-transform: translateX(-50%);
	-moz-transform: translateX(-50%);
	-ms-transform: translateX(-50%);
	-o-transform: translateX(-50%);
	transform: translateX(-50%);
	background: #333; }
.nyp-size__field.show:before {
	width: 100%;
	z-index: 1; }
.nyp-size__field.error:before {
	background: red; }

.nyp-size__actions {
	text-align: right; }

/*@media only screen and (max-width: 1060px) {
	.nyp-size {
		width: 100%;
		padding: 0;
		padding-top: 40px; }

	.nyp-size__title {
		display: none; }

	.nyp-size__row {
		margin-bottom: 40px; }
	.nyp-size__row:last-child .nyp-size__col {
		width: 50%; }

	.nyp-size__col {
		width: 100%; }
	.nyp-size__col:first-child {
		margin-bottom: 40px; }

	.nyp-size__actions {
		text-align: center; } }
@media only screen and (max-width: 520px) {
	.nyp-size__row {
		margin-bottom: 40px; }
	.nyp-size__row:last-child .nyp-size__col {
		width: 100%; } }*/
.nyp-thumb {
	width: 151px;
	border: 1px solid #b3eac5;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 15px 16px 8px;
	position: absolute;
	top: 0;
	right: -30px;
	-webkit-transform: translateX(100%);
	-moz-transform: translateX(100%);
	-ms-transform: translateX(100%);
	-o-transform: translateX(100%);
	transform: translateX(100%);
	font-family: "Open Sans";
	font-weight: 500;
	display: none;
	background: #fff;
	z-index: 1; }

.nyp-thumb__pic {
	height: 160px;
	display: block;
	margin: 0 auto 7px; }

.nyp-thumb__caption {
	font-size: 12px;
	line-height: 14px;
	color: #000;
	text-align: center; }

/*@media only screen and (max-width: 1060px) {
	.nyp-thumb {
		display: none !important; } }*/
.nyp-tooltip {
	width: 320px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 14px 20px 30px 32px;
	background: #e6fbed;
	position: absolute;
	top: 0;
	right: -30px;
	-webkit-transform: translateX(100%);
	-moz-transform: translateX(100%);
	-ms-transform: translateX(100%);
	-o-transform: translateX(100%);
	transform: translateX(100%);
	display: none;
	z-index: 1; }
.nyp-tooltip:before {
	content: "";
	display: block;
	position: absolute;
	top: 30px;
	left: 0;
	-webkit-transform: translateX(-100%);
	-moz-transform: translateX(-100%);
	-ms-transform: translateX(-100%);
	-o-transform: translateX(-100%);
	transform: translateX(-100%);
	border: 30px solid transparent;
	border-right-color: #e6fbed;
	border-right-width: 12px; }

.nyp-tooltip--disable {
	display: none !important; }

.nyp-tooltip__box {
	font-size: 12px;
	line-height: 16px;
	color: #219653; }
.nyp-tooltip__box p {
	margin-bottom: 10px;
	margin-top: 0; }
.nyp-tooltip__box ul {
	list-style: none;
	padding: 0;
	margin: 0;
	margin-bottom: 10px; }
.nyp-tooltip__box li {
	margin-bottom: 3px; }
.nyp-tooltip__box li:last-child {
	margin-bottom: 0; }
.nyp-tooltip__box li:before {
	content: "";
	display: inline-block;
	width: 4px;
	height: 4px;
	background: #219653;
	-webkit-border-radius: 50%;
	-moz-border-radius: 50%;
	border-radius: 50%;
	margin-right: 5px;
	position: relative;
	top: -2px; }









/*                MY STYLE NEW         */
/*

.publish_original {
	border: 1px solid rgba(189, 189, 189, 0.6);
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding: 30px 60px;
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	align-items: center;
	position: relative;
	margin-bottom: 88px; }
.publish_original .close {
	position: absolute;
	width: 12px;
	height: 12px;
	top: 30px;
	right: 30px;
	cursor: pointer;
	background: url("../pictures/icons/close-media.svg") no-repeat center center;
	background-size: 100%; }
.publish_original .left_publish_img {
	width: 256px; }
.publish_original .left_publish_img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain; }
.publish_original .right_publish_info {
	width: calc(100% - 330px); }
.publish_original .right_publish_info .name_block_publish {
	font-size: 20px;
	font-weight: bold; }
.publish_original .right_publish_info .flex_block_publish {
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	margin-top: 37px; }
.publish_original .right_publish_info .flex_block_publish .block_publish {
	width: 300px;
	margin-right: 101px; }
.publish_original .right_publish_info .flex_block_publish .block_publish:last-child {
	margin-right: 0; }
.publish_original .right_publish_info .flex_block_publish .block_publish .name_block {
	font-size: 14px;
	font-weight: 700;
	margin-top: 4px; }
.publish_original .right_publish_info .flex_block_publish .block_publish .icon {
	height: 22px; }
.publish_original .right_publish_info .flex_block_publish .block_publish p {
	font-size: 13px;
	color: #828282;
	line-height: 20px;
	margin-top: 3px; }

.finish_publishing {
	margin: 0px auto 39px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	background: rgba(242, 242, 242, 0.5);
	border: 1px solid #EB5757;
	width: 710px;
	max-width: 100%;
	position: relative;
	padding: 12px; }
.finish_publishing .close {
	position: absolute;
	width: 12px;
	height: 12px;
	top: 21px;
	right: 16px;
	cursor: pointer;
	background: url("../pictures/icons/close-media.svg") no-repeat center center;
	background-size: 100%; }
.finish_publishing .text_link {
	font-size: 16px;
	line-height: 26px;
	color: #828282;
	position: relative;
	padding-left: 30px; }
.finish_publishing .text_link:before {
	content: "!";
	position: absolute;
	top: 3px;
	left: 0;
	width: 18px;
	height: 18px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	color: #ffffff;
	line-height: 18px;
	text-align: center;
	background: #FA0000;
	font-weight: bold;
	font-size: 12px; }
.finish_publishing .text_link a {
	text-decoration: underline;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	color: #333333;
	font-size: 16px;
	display: inline-block;
	line-height: 24px; }
.finish_publishing .text_link a:hover {
	text-decoration: none; }


.publication_information ul.list_select_tab > li {
	border-bottom: 1px solid #F0F0F0;
	padding: 23px 0; }
.publication_information .prev_filter {
	position: fixed;
	top: 26px;
	left: 15px;
	background: url("../images/arrow_back_prof.svg") no-repeat center center;
	width: 13px;
	height: 20px;
	background-size: 100%;
	cursor: pointer;
	z-index: 2; }
.publication_information .block_toggle {
	padding: 10px 16px 0; }
.publication_information .block_toggle .popup_block_title {
	font-size: 16px;
	text-transform: uppercase;
	color: #000000;
	letter-spacing: 0.3px; }
.publication_information .block_toggle > ul > li {
	position: relative;
	padding: 13.4px 0;
	border-bottom: 1px solid #F0F0F0; }
.publication_information .block_toggle > ul > li:last-child {
	border-bottom: none; }
.publication_information .block_toggle > ul > li > a {
	font-size: 14px;
	position: relative;
	display: block;
	padding-right: 20px; }
.publication_information .block_toggle > ul > li > a:after {
	content: "";
	display: table;
	clear: both; }
.publication_information .block_toggle > ul > li > a .right_selected {
	float: right;
	font-size: 14px;
	color: #BDBDBD;
	text-align: right;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis; }
.publication_information .block_toggle > ul > li > a .arrow_icon {
	position: absolute;
	right: 4px;
	top: 0;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	-moz-transform: rotate(90deg);
	-o-transform: rotate(90deg);
	transform: rotate(90deg); }
.publication_information .block_toggle > ul > li > a.active .arrow_icon {
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg); }
.publication_information .block_toggle > ul > li .plus_minus_li .name_filter {
	padding: 3px 0; }
.publication_information .block_toggle > ul > li .name_filter {
	float: left; }
.publication_information .block_toggle > ul > li .name_filter em {
	color: #202020; }
.publication_information .block_toggle > ul > li .name_filter span {
	color: #BDBDBD;
	padding-left: 3px;
	display: inline-block; }
.publication_information .block_toggle > ul > li .block_styler_input {
	float: right; }
.publication_information .block_toggle > ul > li .block_styler_input .inp_plus_minus {
	position: relative; }
.publication_information .block_toggle > ul > li .block_styler_input .inp_plus_minus input {
	width: 84px;
	height: 20px;
	border: none;
	box-sizing: border-box;
	font-size: 14px;
	text-align: center;
	color: #202020;
	padding: 0 22px;
	outline: none; }
.publication_information .block_toggle > ul > li .block_styler_input .inp_plus_minus .plus, .publication_information .block_toggle > ul > li .block_styler_input .inp_plus_minus .minus {
	position: absolute;
	left: 7px;
	top: 50%;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%);
	cursor: pointer; }
.publication_information .block_toggle > ul > li .block_styler_input .inp_plus_minus .plus {
	left: auto;
	right: 7px;
	background: url("../images/tab_inp_plus.svg") no-repeat center center;
	width: 12px;
	height: 12px; }
.publication_information .block_toggle > ul > li .block_styler_input .inp_plus_minus .minus {
	background: url("../images/tab_inp_minus.svg") no-repeat center center;
	width: 12px;
	height: 2px; }
.publication_information .block_toggle > ul > li .check {
	position: absolute;
	top: 9px;
	right: 7px;
	width: 40px;
	height: 22px; }
.publication_information .block_toggle > ul > li .check label {
	cursor: pointer;
	width: 40px;
	height: 22px; }
.publication_information .block_toggle > ul > li .check label:before {
	content: "";
	position: absolute;
	width: 40px;
	height: 22px;
	background: #F5F5F5;
	-webkit-border-radius: 13.5px;
	-ms-border-radius: 13.5px;
	-moz-border-radius: 13.5px;
	-o-border-radius: 13.5px;
	border-radius: 13.5px;
	top: 0px;
	right: 0;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.publication_information .block_toggle > ul > li .check label:after {
	content: "";
	position: absolute;
	width: 18px;
	height: 18px;
	top: 2px;
	left: 2px;
	background: #FFFFFF;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	-webkit-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-ms-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-o-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.publication_information .block_toggle > ul > li .check input[type='checkbox'] {
	display: none; }
.publication_information .block_toggle > ul > li .check input[type='checkbox']:checked + label:after {
	left: 20px; }
.publication_information .block_toggle > ul > li .check input[type='checkbox']:checked + label:before {
	background: #202020; }
.publication_information .block_toggle > ul > li .radio label {
	color: #202020;
	cursor: pointer;
	display: block;
	position: relative;
	padding-right: 30px; }
.publication_information .block_toggle > ul > li .radio label:before {
	content: "";
	position: absolute;
	top: -3px;
	right: 7px;
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	border: 1px solid #E7E7EA;
	box-sizing: border-box; }
.publication_information .block_toggle > ul > li .radio label:after {
	content: "";
	position: absolute;
	display: none;
	width: 8px;
	height: 8px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #000000;
	right: 11px;
	top: 1px; }
.publication_information .block_toggle > ul > li .radio input[type='radio'] {
	display: none; }
.publication_information .block_toggle > ul > li .radio input[type='radio']:checked + label:after {
	display: block; }
.publication_information .block_toggle .price_filter {
	padding: 20px 0; }
.publication_information .block_toggle .price_filter .jquery_ui_slid {
	border: none;
	-webkit-border-radius: 0;
	-ms-border-radius: 0;
	-moz-border-radius: 0;
	-o-border-radius: 0;
	border-radius: 0;
	height: 3px;
	background: #E5E5E5;
	margin: 3px 8px; }
.publication_information .block_toggle .price_filter .jquery_ui_slid .ui-slider-handle {
	width: 17px;
	height: 17px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #202020;
	outline: none;
	top: -6px;
	border: none; }
.publication_information .block_toggle .price_filter .jquery_ui_slid .ui-slider-range {
	height: 3px;
	background: #4F4F4F; }
.publication_information .block_toggle .price_filter .jquery_ui_slid .last_num {
	position: absolute;
	top: -39px;
	right: -8px;
	font-size: 13px;
	color: #000;
	padding-right: 10px;
	letter-spacing: .11px; }
.publication_information .block_toggle .price_filter .jquery_ui_slid .last_num em {
	position: absolute;
	top: 0;
	right: 0; }
.publication_information .block_toggle .price_filter .jquery_ui_slid .start_num {
	position: absolute;
	top: -39px;
	left: -8px;
	font-size: 13px;
	color: #000;
	padding-right: 10px;
	letter-spacing: .11px; }
.publication_information .block_toggle .price_filter .jquery_ui_slid .start_num em {
	position: absolute;
	top: 0;
	right: 0; }
.publication_information .block_toggle .form_text {
	padding-top: 12px;
	padding-bottom: 15px; }
.publication_information .block_toggle .form_text .inp_block {
	position: relative; }
.publication_information .block_toggle .form_text .inp_block.error_textarea .remained_symbol {
	border: 1px solid #FF0000;
	border-top: none; }
.publication_information .block_toggle .form_text .inp_block.error_textarea .block_textarea {
	border: 1px solid #FF0000;
	border-bottom: none; }
.publication_information .block_toggle .form_text .inp_block .remained_symbol {
	font-size: 12px;
	color: #BDBDBD;
	padding: 10px;
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 0 0 2px 2px;
	-ms-border-radius: 0 0 2px 2px;
	-moz-border-radius: 0 0 2px 2px;
	-o-border-radius: 0 0 2px 2px;
	border-radius: 0 0 2px 2px;
	border-top: none;
	margin-top: -3px; }
.publication_information .block_toggle .form_text .inp_block .block_textarea {
	font-size: 14px;
	width: 100%;
	-webkit-border-radius: 2px 2px 0 0;
	-ms-border-radius: 2px 2px 0 0;
	-moz-border-radius: 2px 2px 0 0;
	-o-border-radius: 2px 2px 0 0;
	border-radius: 2px 2px 0 0;
	border: 1px solid #E5E5E5;
	border-bottom: none;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 94px;
	resize: none;
	outline: none; }
.publication_information .block_toggle .photos_edited {
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start; }
.publication_information .block_toggle .photos_edited > .block {
	width: calc(50% - 14px);
	margin-bottom: 28px;
	margin-right: 27px; }
.publication_information .block_toggle .photos_edited > .block:nth-child(2n) {
	margin-right: 0; }
.publication_information .block_toggle .photos_edited > .block .name_block {
	color: #BDBDBD;
	font-size: 14px; }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex {
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 7px; }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block {
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	position: relative;
	width: calc(50% - 8px);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	height: 221px; }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block a {
	position: absolute;
	right: 9px;
	top: 10px;
	width: 25px;
	height: 25px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-ms-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-moz-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-o-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25); }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block a svg {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block .img {
	height: 100%; }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block .img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain; }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block span {
	text-align: center;
	display: block;
	font-size: 11px;
	letter-spacing: -.3px;
	position: absolute;
	width: 100%;
	left: 0;
	bottom: 7px; }
.publication_information .block_toggle .photos_edited > .block .photos_two_flex .photos_two_block.error_foto {
	border: 1px solid #FF0000;
	background: rgba(255, 0, 0, 0.1); }
.publication_information .block_toggle .photos_add {
	margin-top: 16px;
	padding-bottom: 30px; }
.publication_information .block_toggle .photos_add .name_block {
	color: #BDBDBD;
	font-size: 14px; }
.publication_information .block_toggle .photos_add .photos_add_flex {
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	margin-top: 17px; }
.publication_information .block_toggle .photos_add .photos_add_flex .block {
	width: 132px;
	height: 217px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px dashed #E5E5E5;
	position: relative;
	margin-right: 16px; }
.publication_information .block_toggle .photos_add .photos_add_flex .block .img {
	position: relative;
	background: #fff;
	height: 100%; }
.publication_information .block_toggle .photos_add .photos_add_flex .block .img img {
	width: 100%;
	height: 100%;
	-webkit-object-fit: contain;
	-ms-object-fit: contain;
	-moz-object-fit: contain;
	-o-object-fit: contain;
	object-fit: contain; }
.publication_information .block_toggle .photos_add .photos_add_flex .block .img.active {
	z-index: 1; }
.publication_information .block_toggle .added_photo .attach_label_pop {
	position: absolute;
	right: 9px;
	top: 10px;
	left: auto;
	width: 25px;
	height: 25px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-ms-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-moz-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-o-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	background: #fff url("../images/edit_icon.svg") no-repeat center center; }
.publication_information .block_toggle .added_photo .attach_label_pop .name_icon {
	display: none; }
.publication_information .block_toggle .attach_label_pop {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	cursor: pointer; }
.publication_information .block_toggle .attach_label_pop .attach_input_pop {
	display: none; }
.publication_information .block_toggle .edit_img_attach {
	position: absolute;
	right: 9px;
	top: 10px;
	width: 25px;
	height: 25px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-ms-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-moz-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	-o-box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
	box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25); }
.publication_information .block_toggle .edit_img_attach svg {
	display: block;
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%); }
.publication_information .block_toggle .name_icon {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	-ms-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	-o-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	color: #D6D6D8;
	font-size: 14px;
	line-height: 20px;
	text-align: center; }
.publication_information .block_toggle .name_icon svg {
	margin-bottom: 5px; }
.publication_information .block_toggle .prices_block_edit {
	padding-bottom: 17px;
	padding-top: 11px; }
.publication_information .block_toggle .prices_block_edit .text_top_info {
	font-size: 12px;
	color: #909090;
	background: rgba(241, 241, 241, 0.6);
	padding: 9px 7px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	display: inline-block; }
.publication_information .block_toggle .prices_block_edit .text_top_info img {
	display: inline-block;
	vertical-align: middle;
	margin-right: 3px; }
.publication_information .block_toggle .prices_block_edit .prices_form {
	margin-top: 27px; }
.publication_information .block_toggle .prices_block_edit .prices_form form > div {
	float: left;
	width: 122px;
	margin-right: 30px;
	position: relative; }
.publication_information .block_toggle .prices_block_edit .prices_form form > div:last-child {
	margin-right: 0; }
.publication_information .block_toggle .prices_block_edit .prices_form form > div input {
	display: block;
	width: 100%;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #E5E5E5;
	height: 42px;
	margin-top: 10px;
	padding: 0 25px 0 14px;
	outline: none;
	font-size: 16px; }
.publication_information .block_toggle .prices_block_edit .prices_form form > div input.error_inp {
	border: 1px solid #FF0000; }
.publication_information .block_toggle .prices_block_edit .prices_form form > div span {
	font-size: 14px; }
.publication_information .block_toggle .prices_block_edit .prices_form form > div svg {
	position: absolute;
	right: 14px;
	bottom: 16px; }
.publication_information .block_toggle .prices_block_edit .prices_form form .right_block_price input {
	opacity: .5;
	background: #F9F9F9; }
.publication_information .block_toggle .prices_block_edit .prices_form form:after {
	content: "";
	display: table;
	clear: both; }
.publication_information .block_toggle .block_tab_seller {
	padding-bottom: 19px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block {
	border-bottom: 1px solid #F0F0F0;
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label {
	border: none !important;
	color: #BDBDBD;
	padding: 25px 30px 21px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:before {
	top: 25px;
	left: 1px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:after {
	left: 6px;
	top: 30px;
	width: 6px;
	height: 6px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label > div {
	margin-bottom: 11px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .address_info span:first-child {
	display: block;
	margin-bottom: 5px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul {
	display: block; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon {
	position: static;
	width: auto;
	height: auto;
	-webkit-box-shadow: none;
	-ms-box-shadow: none;
	-moz-box-shadow: none;
	-o-box-shadow: none;
	box-shadow: none;
	margin-top: 27px;
	opacity: 1; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a {
	position: static;
	-webkit-transform: translate(0, 0);
	-ms-transform: translate(0, 0);
	-moz-transform: translate(0, 0);
	-o-transform: translate(0, 0);
	transform: translate(0, 0); }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a span {
	display: inline-block;
	vertical-align: middle; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon a svg {
	display: inline-block;
	vertical-align: middle; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block {
	position: relative;
	border-bottom: 1px solid #F0F0F0;
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block:last-child {
	margin-bottom: 16px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label {
	border: none !important;
	color: #BDBDBD;
	padding: 20px 21px 21px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	font-size: 14px;
	cursor: pointer;
	display: block;
	position: relative;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .name_block {
	font-size: 14px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .name_block span {
	color: #333333;
	padding-left: 0; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon {
	position: static;
	-webkit-transform: translate(0, 0);
	-ms-transform: translate(0, 0);
	-moz-transform: translate(0, 0);
	-o-transform: translate(0, 0);
	transform: translate(0, 0); }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon span {
	display: inline-block;
	vertical-align: middle; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .edited_icon svg {
	display: inline-block;
	vertical-align: middle; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:before {
	content: "";
	position: absolute;
	top: 21px;
	left: -7px;
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	border: 1px solid #BDBDBD;
	box-sizing: border-box; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label:after {
	content: "";
	position: absolute;
	display: none;
	left: -2px;
	top: 26px;
	width: 6px;
	height: 6px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #000000; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul {
	display: inline-block;
	vertical-align: top;
	margin-right: 30px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li {
	margin-bottom: 11px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li strong {
	color: #BDBDBD;
	display: inline-block;
	width: 36px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label .pay_edit_save ul li span {
	color: #202020;
	margin-left: 5px;
	display: inline-block; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label > div {
	margin-bottom: 5px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info label > div:last-child {
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio'] {
	display: none; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label {
	border: 1px solid #4F4F4F; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label .edited_icon {
	opacity: 1; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label .name_block {
	font-size: 14px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label .name_block span {
	color: #333333;
	padding-left: 0; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block .radio_info input[type='radio']:checked + label:after {
	display: block; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block:hover .radio_info label {
	border: 1px solid #4F4F4F; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block:hover .radio_info label .edited_icon {
	opacity: 1; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes {
	position: relative;
	padding-right: 35px; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes .edited_icon {
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	position: static;
	width: auto;
	height: auto;
	-webkit-box-shadow: none;
	-ms-box-shadow: none;
	-moz-box-shadow: none;
	-o-box-shadow: none;
	box-shadow: none;
	margin-top: 27px;
	opacity: 1; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes .edited_icon a {
	position: static;
	-webkit-transform: translate(0, 0);
	-ms-transform: translate(0, 0);
	-moz-transform: translate(0, 0);
	-o-transform: translate(0, 0);
	transform: translate(0, 0); }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes .edited_icon a span {
	display: inline-block;
	vertical-align: middle; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes .edited_icon a svg {
	display: inline-block;
	vertical-align: middle; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes ul {
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: nowrap;
	justify-content: flex-start; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes ul li {
	margin-right: 40px;
	max-width: calc(25% - 35px); }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .edited_saves_all_block .edited_saves_block_sizes ul li .name_block {
	color: #BDBDBD;
	font-size: 14px;
	padding: 5px 0; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .add_address_new a {
	position: relative;
	line-height: 25px;
	color: #202020;
	font-size: 12px;
	padding-left: 38px;
	display: inline-block; }
.publication_information .block_toggle .block_tab_seller .edited_add_all_block .add_address_new a:before {
	width: 25px;
	height: 25px;
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	background: #F2F2F2 url("../images/add_block_icon_plus.svg") no-repeat center center; }
.publication_information .block_toggle .block_tab_additionally {
	padding: 25px 0 0; }
.publication_information .block_toggle .block_tab_additionally .block_additionally {
	margin-bottom: 48px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally:last-child {
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .popup_block_title {
	font-size: 14px;
	color: #000;
	letter-spacing: -.1px;
	text-transform: unset; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally {
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-top: 23px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally.flex_block_additionally_start {
	justify-content: flex-start;
	margin: 29px -15px 0; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally.flex_block_additionally_start .inp_block_pop {
	width: calc(25% - 30px);
	margin: 0 15px 21px;
	position: relative; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop {
	width: calc(50% - 28px); }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li {
	margin-bottom: 16px;
	position: relative;
	min-height: 22px;
	line-height: 20px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li:last-child {
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .name_block {
	font-size: 14px;
	color: #000; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li p {
	font-size: 12px;
	color: #8E8E93; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop {
	!*position: absolute;
	top: 50%;
	right: 0;
	@include prefix-name(transform,translate(0,-50%));*!
	position: absolute;
	top: calc(50% - 11px);
	right: 1px;
	width: 40px;
	height: 22px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop label {
	cursor: pointer;
	width: 40px;
	height: 22px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop label:before {
	content: "";
	position: absolute;
	width: 40px;
	height: 22px;
	background: #F5F5F5;
	-webkit-border-radius: 13.5px;
	-ms-border-radius: 13.5px;
	-moz-border-radius: 13.5px;
	-o-border-radius: 13.5px;
	border-radius: 13.5px;
	top: 0px;
	right: 0;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop label:after {
	content: "";
	position: absolute;
	width: 18px;
	height: 18px;
	top: 2px;
	left: 2px;
	background: #FFFFFF;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	-webkit-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-ms-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-o-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.2);
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop input[type='checkbox'] {
	display: none; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop input[type='checkbox']:checked + label:after {
	left: 20px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_left_pop ul li .check_pop input[type='checkbox']:checked + label:before {
	background: #202020; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_right_pop {
	width: calc(50% - 20px); }
.publication_information .block_toggle .block_tab_additionally .block_additionally .flex_block_additionally .flex_right_pop .form_block_pop {
	margin-top: 3px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .form_block_pop .inp_block_pop {
	margin-bottom: 21px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .form_block_pop .inp_block_pop input {
	display: block;
	width: 100%;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #E5E5E5;
	height: 42px;
	margin-top: 10px;
	padding: 0 14px 0 14px;
	outline: none;
	font-size: 16px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .form_block_pop .inp_block_pop span {
	font-size: 14px; }
.publication_information .block_toggle .block_tab_additionally .block_additionally .form_block_pop .inp_block_pop em {
	position: absolute;
	bottom: 13px;
	right: 16px;
	color: #BDBDBD;
	font-size: 15px; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes {
	margin-top: 44px; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes .form_block_pop {
	margin-top: 31px; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes .flex_block_additionally {
	justify-content: flex-start;
	margin-top: 0;
	margin-bottom: 21px; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes .flex_block_additionally:last-child {
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes .flex_block_additionally .inp_block_pop {
	width: 115px;
	margin-right: 34px;
	position: relative;
	margin-bottom: 0; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes .flex_block_additionally .inp_block_pop em {
	position: absolute;
	bottom: 13px;
	right: 16px;
	color: #BDBDBD;
	font-size: 15px; }
.publication_information .block_toggle .block_tab_additionally .form_block_pop_sizes .flex_block_additionally .inp_block_pop input {
	padding-right: 35px; }
.publication_information .block_toggle .block_sizes_pop_ul {
	padding: 20px 0; }
.publication_information .block_toggle .block_sizes_pop_ul ul li {
	float: left;
	margin-right: 13px;
	margin-bottom: 13px; }
.publication_information .block_toggle .block_sizes_pop_ul ul li a {
	min-width: 30px;
	padding: 0 5px;
	height: 30px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	display: block;
	border: 1px solid #E5E5E5;
	color: #BDBDBD;
	text-align: center;
	line-height: 30px;
	font-size: 14px; }
.publication_information .block_toggle .block_sizes_pop_ul ul li a.active {
	border: 1px solid #333333;
	color: #000; }
.publication_information .block_toggle .block_sizes_pop_ul ul:after {
	content: "";
	display: table;
	clear: both; }
.publication_information .block_toggle .sizes_edit_tab {
	padding-bottom: 30px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size {
	margin-bottom: 18px;
	display: -webkit-box;
	!* OLD - iOS 6-, Safari 3.1-6 *!
	display: -moz-box;
	!* OLD - Firefox 19- (buggy but mostly works) *!
	display: -ms-flexbox;
	!* TWEENER - IE 10 *!
	display: -webkit-flex;
	!* NEW - Chrome *!
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	align-items: center; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size:last-child {
	margin-bottom: 0; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .name_line {
	min-width: 75px;
	font-size: 14px;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding-right: 21px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .block_styler_input {
	width: 74px;
	margin-right: 32px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .block_styler_input:nth-child(4) {
	margin-right: 0; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .inp_plus_minus {
	position: relative; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .inp_plus_minus input {
	width: 100%;
	height: 38px;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	border: 1px solid #E5E5E5;
	box-sizing: border-box;
	font-size: 14px;
	text-align: center;
	color: #202020;
	padding: 0 22px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .inp_plus_minus .plus, .publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .inp_plus_minus .minus {
	position: absolute;
	left: 7px;
	top: 50%;
	-webkit-transform: translate(0, -50%);
	-ms-transform: translate(0, -50%);
	-moz-transform: translate(0, -50%);
	-o-transform: translate(0, -50%);
	transform: translate(0, -50%);
	cursor: pointer; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .inp_plus_minus .plus {
	left: auto;
	right: 7px;
	background: url("../images/tab_inp_plus.svg") no-repeat center center;
	width: 12px;
	height: 12px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .block_line_size .inp_plus_minus .minus {
	background: url("../images/tab_inp_minus.svg") no-repeat center center;
	width: 12px;
	height: 2px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .close_tab_line {
	margin-left: 14px; }
.publication_information .block_toggle .sizes_edit_tab .two_block_sizes .close_tab_line a {
	background: url("../images/close_tab_line.svg") no-repeat center center;
	width: 9px;
	height: 9px;
	display: block; }
.publication_information .all_block_two {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	background: #fff;
	height: 100vh;
	z-index: 2;
	display: none;
	overflow: hidden; }
.publication_information .all_block_two.active {
	display: block; }
.publication_information .all_block_two .block {
	padding: 70px 16px 20px;
	max-height: 100vh;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	overflow: auto; }
.publication_information .all_block_two .block .all_brend_blocks {
	position: relative; }
.publication_information .all_block_two .block .all_brend_blocks .block_brend {
	margin-top: 25px; }
.publication_information .all_block_two .block .all_brend_blocks .block_brend > .name-block-popup {
	padding-bottom: 5px; }
.publication_information .all_block_two .block .mob_prices_filter > div {
	width: calc(50% - 5px);
	float: left; }
.publication_information .all_block_two .block .mob_prices_filter > div span {
	display: block;
	font-size: 12px;
	color: #A8A8A8; }
.publication_information .all_block_two .block .mob_prices_filter > div input {
	width: 100%;
	height: 30px;
	background: none;
	border: none;
	border-bottom: 1px solid #F0F0F0;
	outline: none;
	padding: 0;
	color: #000; }
.publication_information .all_block_two .block .mob_prices_filter .last_price_mob {
	float: right; }
.publication_information .all_block_two .block .price_filter {
	padding-top: 39px;
	margin-top: 40px; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid {
	border: none;
	-webkit-border-radius: 0;
	-ms-border-radius: 0;
	-moz-border-radius: 0;
	-o-border-radius: 0;
	border-radius: 0;
	height: 3px;
	background: #E5E5E5;
	margin: 3px 8px; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid .ui-slider-handle {
	width: 17px;
	height: 17px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%;
	background: #202020;
	outline: none;
	top: -6px;
	border: none; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid .ui-slider-range {
	height: 3px;
	background: #4F4F4F; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid .last_num {
	position: absolute;
	top: -39px;
	right: -8px;
	font-size: 13px;
	color: #000;
	padding-right: 10px;
	letter-spacing: .11px; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid .last_num em {
	position: absolute;
	top: 0;
	right: 0; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid .start_num {
	position: absolute;
	top: -39px;
	left: -8px;
	font-size: 13px;
	color: #000;
	padding-right: 10px;
	letter-spacing: .11px; }
.publication_information .all_block_two .block .price_filter .jquery_ui_slid .start_num em {
	position: absolute;
	top: 0;
	right: 0; }
.publication_information .all_block_two .block .catalog_menu_block .title_popup {
	padding: 18px 0 25px; }
.publication_information .all_block_two .block .catalog_menu_block .prev_filter_list {
	position: absolute;
	left: 15px;
	top: 30px;
	width: 22px;
	height: 26px;
	cursor: pointer;
	z-index: 10; }
.publication_information .all_block_two .block .catalog_menu_block .prev_filter_list svg {
	width: 100%;
	height: 100%; }
.publication_information .all_block_two .block .catalog_menu_block .one_list_block,
.publication_information .all_block_two .block .catalog_menu_block .two_list_block,
.publication_information .all_block_two .block .catalog_menu_block .check_list_block {
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	padding: 10px 16px;
	background: #ffffff;
	height: calc(100vh - 70px);
	z-index: 1;
	overflow: auto; }
.publication_information .all_block_two .block .catalog_menu_block .one_list_block.active,
.publication_information .all_block_two .block .catalog_menu_block .two_list_block.active,
.publication_information .all_block_two .block .catalog_menu_block .check_list_block.active {
	z-index: 2; }
.publication_information .all_block_two .block .catalog_menu_block .one_list_block ul.one_list,
.publication_information .all_block_two .block .catalog_menu_block .two_list_block ul.one_list,
.publication_information .all_block_two .block .catalog_menu_block .check_list_block ul.one_list {
	display: none; }
.publication_information .all_block_two .block .catalog_menu_block .one_list_block ul.one_list > li ul.two_list,
.publication_information .all_block_two .block .catalog_menu_block .two_list_block ul.one_list > li ul.two_list,
.publication_information .all_block_two .block .catalog_menu_block .check_list_block ul.one_list > li ul.two_list {
	display: none; }
.publication_information .all_block_two .block .catalog_menu_block .one_list_block ul.one_list > li ul.two_list > li > a,
.publication_information .all_block_two .block .catalog_menu_block .two_list_block ul.one_list > li ul.two_list > li > a,
.publication_information .all_block_two .block .catalog_menu_block .check_list_block ul.one_list > li ul.two_list > li > a {
	padding: 8px 0px; }
.publication_information .all_block_two .block .catalog_menu_block .one_list_block ul.check_list > li,
.publication_information .all_block_two .block .catalog_menu_block .two_list_block ul.check_list > li,
.publication_information .all_block_two .block .catalog_menu_block .check_list_block ul.check_list > li {
	padding: 3px 0 3px; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li {
	border-bottom: 1px solid #F0F0F0;
	padding: 12px 0; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li:last-child {
	border-bottom: none; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li .check_two label {
	position: relative;
	cursor: pointer;
	display: block; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li .check_two label p {
	max-width: 505px;
	margin-top: 10px;
	font-size: 12px;
	line-height: 16px;
	color: #828282; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li .check_two label:before {
	content: "";
	position: absolute;
	width: 15px;
	height: 10px;
	display: none;
	top: 3px;
	right: 0;
	background: url("../images/check_icon_new.svg") no-repeat center center;
	background-size: 100%; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li .check_two input[type='checkbox'] {
	display: none; }
.publication_information .all_block_two .block .catalog_menu_block ul.new_check li .check_two input[type='checkbox']:checked + label:before {
	display: block; }
.publication_information .all_block_two .block .catalog_menu {
	margin-top: 22px; }
.publication_information .all_block_two .block .catalog_menu > li {
	margin-bottom: 23px; }
.publication_information .all_block_two .block .catalog_menu > li > a {
	font-size: 16px;
	text-transform: uppercase;
	opacity: .4;
	letter-spacing: .4px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	display: block; }
.publication_information .all_block_two .block .catalog_menu > li > a.active {
	opacity: 1; }
.publication_information .all_block_two .block .catalog_menu > li > ul {
	display: none; }
.publication_information .all_block_two .block .catalog_menu > li > ul > li > .check {
	padding: 0; }
.publication_information .all_block_two .block .catalog_menu > li > ul > li > .check label {
	font-size: 14px; }
.publication_information .all_block_two .block ul.one_list {
	padding: 19px 2px 7px; }
.publication_information .all_block_two .block ul.one_list .check {
	padding: 0 10px; }
.publication_information .all_block_two .block ul.one_list .check label {
	position: relative;
	cursor: pointer;
	padding-left: 22px;
	display: block; }
.publication_information .all_block_two .block ul.one_list .check label:before {
	content: "";
	position: absolute;
	width: 14px;
	height: 14px;
	border: 1px solid #E0E0E0;
	top: 0px;
	left: 0px;
	box-sizing: border-box;
	-webkit-border-radius: 1px;
	-ms-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px; }
.publication_information .all_block_two .block ul.one_list .check label:after {
	display: none;
	content: "";
	position: absolute;
	width: 6px;
	height: 6px;
	left: 4px;
	top: calc(50% - 3px);
	background: #333333;
	-webkit-border-radius: 1px;
	-ms-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px; }
.publication_information .all_block_two .block ul.one_list .check input[type='checkbox'] {
	display: none; }
.publication_information .all_block_two .block ul.one_list .check input[type='checkbox']:checked + label:after {
	display: block; }
.publication_information .all_block_two .block ul.one_list > li {
	margin-bottom: 22px; }
.publication_information .all_block_two .block ul.one_list > li:last-child {
	margin-bottom: 0; }
.publication_information .all_block_two .block ul.one_list > li > a {
	font-size: 14px;
	position: relative;
	display: block;
	padding-right: 20px; }
.publication_information .all_block_two .block ul.one_list > li > a svg {
	position: absolute;
	top: 5px;
	right: 0;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-moz-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg); }
.publication_information .all_block_two .block ul.one_list > li > a.active svg {
	-webkit-transform: rotate(0deg);
	-ms-transform: rotate(0deg);
	-moz-transform: rotate(0deg);
	-o-transform: rotate(0deg);
	transform: rotate(0deg); }
.publication_information .all_block_two .block ul.one_list > li ul.two_list {
	padding: 13px 0 0px;
	display: none; }
.publication_information .all_block_two .block ul.one_list > li ul.two_list > li {
	margin-bottom: 2px; }
.publication_information .all_block_two .block ul.one_list > li ul.two_list > li:last-child {
	margin-bottom: 0; }
.publication_information .all_block_two .block ul.one_list > li ul.two_list > li > a {
	padding: 8px 10px;
	font-size: 14px;
	line-height: 16px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	display: block; }
.publication_information .all_block_two .block ul.one_list > li ul.two_list > li > a.active, .publication_information .all_block_two .block ul.one_list > li ul.two_list > li > a:hover {
	background: #F2F2F2; }
.publication_information .all_block_two .block ul.one_list > li ul.two_list > li > ul.check_list {
	padding-bottom: 5px; }
.publication_information .all_block_two .block ul.one_list > li ul.two_list > li > ul.check_list > li {
	padding-left: 30px; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list {
	padding-top: 10px;
	display: none; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li {
	padding: 3px 0 3px 16px;
	margin-bottom: 16px; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li:last-child {
	margin-bottom: 0; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li.check_readonly {
	pointer-events: none;
	opacity: .5; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li .check {
	padding: 0; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li .check label {
	position: relative;
	cursor: pointer;
	padding-left: 22px;
	display: block; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li .check label:before {
	content: "";
	position: absolute;
	width: 14px;
	height: 14px;
	border: 1px solid #E0E0E0;
	top: 0px;
	left: 0px;
	box-sizing: border-box;
	-webkit-border-radius: 1px;
	-ms-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li .check label:after {
	display: none;
	content: "";
	position: absolute;
	width: 6px;
	height: 6px;
	left: 4px;
	top: 4px;
	background: #333333;
	-webkit-border-radius: 1px;
	-ms-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li .check input[type='checkbox'] {
	display: none; }
.publication_information .all_block_two .block ul.one_list > li ul.check_list > li .check input[type='checkbox']:checked + label:after {
	display: block; }
.publication_information .all_block_two .block ul.new_check li {
	border-bottom: 1px solid #F0F0F0;
	padding: 12px 0; }
.publication_information .all_block_two .block ul.new_check li:last-child {
	border-bottom: none; }
.publication_information .all_block_two .block ul.new_check li .check_two label {
	position: relative;
	cursor: pointer;
	display: block; }
.publication_information .all_block_two .block ul.new_check li .check_two label p {
	max-width: 505px;
	margin-top: 10px;
	font-size: 12px;
	line-height: 16px;
	color: #828282; }
.publication_information .all_block_two .block ul.new_check li .check_two label:before {
	content: "";
	position: absolute;
	width: 15px;
	height: 10px;
	display: none;
	top: 3px;
	right: 0;
	background: url("../images/check_icon_new.svg") no-repeat center center;
	background-size: 100%; }
.publication_information .all_block_two .block ul.new_check li .check_two input[type='checkbox'] {
	display: none; }
.publication_information .all_block_two .block ul.new_check li .check_two input[type='checkbox']:checked + label:before {
	display: block; }
.publication_information .all_block_two .block ul.color_list .color_pop {
	display: inline-block;
	vertical-align: middle;
	border: 1px solid #C4C4C4;
	width: 16px;
	height: 16px;
	-webkit-border-radius: 50%;
	-ms-border-radius: 50%;
	-moz-border-radius: 50%;
	-o-border-radius: 50%;
	border-radius: 50%; }
.publication_information .all_block_two .block ul.color_list em {
	display: inline-block;
	vertical-align: middle;
	margin-left: 2px; }
.publication_information .all_block_two .block ul.children_list {
	padding: 13px 0px 4px; }
.publication_information .all_block_two .block ul.children_list > li {
	margin-bottom: 4px; }
.publication_information .all_block_two .block ul.children_list > li .check {
	padding: 8px 10px; }
.publication_information .all_block_two .block ul.children_list > li .check label {
	position: relative;
	cursor: pointer;
	padding-left: 22px;
	display: block; }
.publication_information .all_block_two .block ul.children_list > li .check label:before {
	content: "";
	position: absolute;
	width: 14px;
	height: 14px;
	border: 1px solid #E0E0E0;
	top: 0px;
	left: 0px;
	box-sizing: border-box;
	-webkit-border-radius: 1px;
	-ms-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px; }
.publication_information .all_block_two .block ul.children_list > li .check label:after {
	display: none;
	content: "";
	position: absolute;
	width: 6px;
	height: 6px;
	left: 4px;
	top: 4px;
	background: #333333;
	-webkit-border-radius: 1px;
	-ms-border-radius: 1px;
	-moz-border-radius: 1px;
	-o-border-radius: 1px;
	border-radius: 1px; }
.publication_information .all_block_two .block ul.children_list > li .check input[type='checkbox'] {
	display: none; }
.publication_information .all_block_two .block ul.children_list > li .check input[type='checkbox']:checked + label:after {
	display: block; }
.publication_information .all_block_two .block ul.children_list > li:last-child {
	margin-bottom: 0; }
.publication_information .all_block_two .block ul.children_list > li > a {
	padding: 8px 10px;
	font-size: 14px;
	line-height: 16px;
	-webkit-transition: all 0.3s ease;
	-ms-transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-o-transition: all 0.3s ease;
	transition: all 0.3s ease;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	display: block; }
.publication_information .all_block_two .block ul.children_list > li > a.active, .publication_information .all_block_two .block ul.children_list > li > a:hover {
	background: #F2F2F2; }
.publication_information .all_block_two .block .size_block_top {
	padding-top: 12px; }
.publication_information .all_block_two .block .size_block_top .left_text {
	float: left;
	color: #BDBDBD;
	font-size: 14px; }
.publication_information .all_block_two .block .size_block_top .open_table_size {
	float: right; }
.publication_information .all_block_two .block .size_block_top .open_table_size a {
	font-size: 12px;
	position: relative;
	padding-right: 17px;
	letter-spacing: .11px; }
.publication_information .all_block_two .block .size_block_top .open_table_size a:before {
	content: "";
	position: absolute;
	top: 2px;
	right: 0;
	background: url("../images/arrow_down_form_1.svg") no-repeat center center;
	width: 17px;
	height: 14px; }
.publication_information .all_block_two .block .suggestags {
	position: relative; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area {
	padding-top: 50px;
	overflow: auto; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area .amsify-suggestags-input-area {
	white-space: nowrap; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area .amsify-suggestags-input-area .amsify-suggestags-input {
	position: absolute;
	top: 0;
	left: 0;
	width: 100% !important;
	max-width: 100%;
	height: 40px;
	background: #F5F5F5 url("../images/search_icon_ams.svg") no-repeat 10px center;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px;
	padding: 0 31px; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area .amsify-suggestags-input-area .col-bg {
	background: #FFFFFF;
	border: 1px solid #E5E5E5;
	-webkit-border-radius: 2px;
	-ms-border-radius: 2px;
	-moz-border-radius: 2px;
	-o-border-radius: 2px;
	border-radius: 2px; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area .amsify-suggestags-input-area .amsify-select-tag {
	padding: 0 44px 0 12px;
	position: relative;
	height: 44px;
	margin-bottom: 0;
	display: inline-block;
	line-height: 44px; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area .amsify-suggestags-input-area .amsify-select-tag .fa-times:before {
	content: "";
	position: absolute;
	right: 9px;
	top: 15px;
	background: url("../images/cancel-music.svg") no-repeat center center;
	width: 11px;
	height: 11px; }
.publication_information .all_block_two .block .suggestags .amsify-suggestags-area .amsify-suggestags-input-area.amsify-focus {
	-webkit-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
	-ms-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
	-moz-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
	-o-box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
	box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0); }


@media only screen and (max-width: 1360px) {
	.ny-content--product {
		padding-left: 60px; }

	.ny-content {
		width: calc(100% - 280px); }

	.ny-product__content {
		width: calc(100% - 31.94444% - 80px); } }
@media only screen and (max-width: 1200px) {
	.publish_original {
		padding: 30px; }
	.publish_original .right_publish_info {
		width: calc(100% - 290px); }
	.publish_original .right_publish_info .flex_block_publish .block_publish {
		margin-right: 30px; }

	.ny-content--product {
		padding-left: 20px; }

	.ny-product__content {
		width: calc(100% - 31.94444% - 20px); }

	.nyp-tooltip {
		right: 0; }

	.featherlight .featherlight-content {
		max-width: 97%; }

	.nyp-category {
		max-width: 100%; }

	.nyp-category__nav {
		padding-right: 15px;
		width: 230px; }

	.nyp-category__sub {
		padding-right: 15px;
		padding-left: 10px;
		width: 230px; } }
@media only screen and (max-width: 1024px) {
	.publish_original {
		display: block; }
	.publish_original .left_publish_img {
		display: none; }
	.publish_original .right_publish_info {
		width: 100%; }
	.publish_original .right_publish_info .flex_block_publish .block_publish {
		margin-right: 45px; }
	.mob_none_publication{
		display: none;
	}
	.ny-content{
		padding-left: 0;
		width: 100%;
	}
	.ny-product__content{
		width: 100%;
	}
	.nyp-tooltip{
		display: none!important;
	}
}
@media only screen and (max-width: 767px) {
	.finish_publishing {
		padding-top: 50px; }
	.finish_publishing .text_link {
		padding-left: 0; }
	.finish_publishing .text_link:before {
		top: -36px; }

	.publish_original {
		display: none; } }

*/


/*      -----------------------        */



/*@media only screen and (max-width: 1380px) {
	.nyp-tooltip {
		width: 250px; } }
@media only screen and (max-width: 1060px) {
	.nyp-tooltip {
		display: none !important; } }*/

/*# sourceMappingURL=application_new.css.map */
