$(document).ready(function (){

	$("input.ny-field--nyp-pf").keypress(function(event){
		event = event || window.event;
		if (event.charCode && event.charCode!=0 && event.charCode!=46 && (event.charCode < 48 || event.charCode > 57) )
			return false;
	});



	$ ( ".ny-button--nyp-sf-close" ).on ( "click" , function () {
		var $this = $(this);
		$this.parents("nyp-sf__row").remove();
	} );


	/*search brends*/
	$ ( "body" ).on ( "keyup", "#brandList .ny-field--search--swipe", function () {

		var input, filter, ul, li, a, i, txtValue, label;
		var $this = $(this);
		ul = $this.parents("#brandList");
		li = $this.parents("#brandList").find("li");
		filter =  $this.val().toUpperCase();
		for (i = 0; i < li.length; i++) {
			label = li[i].getElementsByTagName("span")[0];

			txtValue = label.textContent || label.innerText;
			if (txtValue.toUpperCase().indexOf(filter) == 0) {
				li[i].style.display = "";
			} else {
				li[i].style.display = "none";
			}
		}
	});

	$(document).on("click",".show-popup-address",function(t){
		var $phone_maska_input = $('input.phone_maska');

		if ($phone_maska_input.length) {
			$phone_maska_input.simpleMask({
				'mask': ['+# (###) ###-##-##', '+# (###) ###-##-##']
			});
		}
	});


	var $phone_maska_input = $('input.phone_maska');
	if ($phone_maska_input.length) {
		$phone_maska_input.simpleMask ( {
			'mask' : [ '+###########' , '+###########' ]
		} );
	}
	/*	/!*plugin scroll*!/
        if($("div").hasClass("scroll_content")){
            $(".scroll_content").mCustomScrollbar({
                axis:"y",
            });
        }*/

	/*--------------------------- PopUp Script ----------------------------------------*/


	$ ( ".tree_popups .popup_block" ).on ( "click" , function ( e ) {
		e.stopPropagation ();
	} );



	$ ( ".sent_moderation_open" ).on ( "click" , function ( e ) {
		e.preventDefault ();
		$ ( "#sent_moderation" ).fadeIn ( function () {
			setTimeout(function(){$ ( "#sent_moderation" ).fadeOut() }, 1000);
		} );
	} );

	$("body .publication_information ").on('click', ' .block_toggle > ul > li > a', function (e) {
		e.preventDefault();
		var $this = $(this);
		$("body").addClass("active");
		$this.parent().find(".all_block_two").fadeIn(300);
	});

	$("body .publication_information ").on('click', ' .catalog_menu li a', function () {
		var $this = $(this);
		var array = [];
		$this.parent("li").parent("ul").find("li a").removeClass("active");
		$this.addClass("active");
		$this.parents(".all_block_two").parent("li").find(".right_selected").css({'width': 'calc(100% - 20px - ' + $this.parent(".all_block_two").parent("li").find(".name_filter ").innerWidth() + 'px)'});
		if ($this.parent().find("div").children().length === 0) {
			$this.parents(".all_block_two").fadeOut(300);
			$("body").removeClass("active");

			for ( var i = 0; i < $this.parents(".catalog_menu").find(">li a.active").length; i++  ){
				var x = $this.parents(".catalog_menu").find(">li a.active").eq(i).text();
				array.push(" > " + x );

				$this.parents(".all_block_two").parent("li").find(".right_selected").text( array);
				$this.parents(".all_block_two").parent("li").find(".right_selected").text($this.parents(".all_block_two").parent("li").find(".right_selected").text().replace(/,/g, ''));
			}

			$this.parents(".one_list_block,.two_list,.check_list").find(" >ul").slideUp(0);
			$this.parents(".catalog_menu").find(" li>a").slideDown(0);
			$this.parents(".one_list_block,.two_list_block").fadeOut(0);
			$this.parents(".catalog_menu_block").find(".two_list_block, .check_list_block , .one_list_block").removeClass("active");
			$this.parents(".catalog_menu_block").find("> .prev_filter_list_title").show();

		}

	});

	$("body .publication_information ").on('click', ' .last_list_catalog li a', function (e) {
		e.preventDefault();
		var $this = $(this);
		$this.parents(".catalog_menu_block").find(".two_list_block, .check_list_block,.one_list_block ").removeClass("active");
		$this.parent(".all_block_two").fadeOut(300);

	});

	$("body .publication_information ").on('click', ' .block_toggle > ul > li .prev_filter', function (e) {
		e.preventDefault();
		var $this = $(this);
		$("body").removeClass("active");
		$this.parent(".all_block_two").fadeOut(300);
		var $this_index = $this.parent(".all_block_two").parent("li").index();
		var checked_aktive = $this.parent(".all_block_two").find(" li input:checked").parent().length;
		var cheched_name = $this.parent(".all_block_two").find(" li input:checked").parent().text();
		var array = [];
		$(".publication_information  .all_block_two.active").removeClass("active");
		$this.parent(".all_block_two").parent("li").find(".name_filter span").text("(" + checked_aktive + ")");
		for (var i = 0; i < checked_aktive; i++) {
			var x = $this.parent(".all_block_two").find(" li input:checked").parents("li").eq(i).find("label .name-block-popup").html();
			array.push(" " + x);

			$this.parent(".all_block_two").parent("li").find(".right_selected").html(array);
		}
		$this.parent(".all_block_two").parent("li").find(".right_selected ").css({'width': 'calc(100% - 20px - ' + $this.parent(".all_block_two").parent("li").find(".name_filter ").innerWidth() + 'px)'});
		$this.parent(".all_block_two").parent("li").removeClass("no_selected");
	});

	/*search brends  mobile*/
	$ ( "body" ).on ( "keyup", "#brandListMobile .ny-field--search--swipe", function () {
		var input, filter, ul, li, a, i, txtValue, label,label_span,txtValue_span;
		var $this = $(this);
		ul = $this.parents(" #brandListMobile");
		li = $this.parents("#brandListMobile").find("li");
		filter =  $this.val().toUpperCase();
		/*first_letter = $this.val().substring(0, 1);*/
		for (i = 0; i < li.length; i++) {
			label = li[i].getElementsByClassName("name-block-popup")[0];

			txtValue = label.textContent || label.innerText;
			if (txtValue.toUpperCase().indexOf(filter) == 0) {
				li[i].style.display = "";
			} else {
				li[i].style.display = "none";
			}
		}
	});

	/* plus minus */
	$("body .publication_information ").on("click", " .inp_plus_minus .plus", function () {

		if ($(this).prev().val() < 999) {
			$(this).prev().val(+$(this).prev().val() + 1);
		}
	});
	$("body .publication_information ").on("click", " .inp_plus_minus .minus", function () {

		if ($(this).next().val() > 1) {
			if ($(this).next().val() > 1) $(this).next().val(+$(this).next().val() - 1);
		}
	});

	$ ( ".publish_original .close" ).on ( "click" , function () {
		$ ( ".publish_original" ).fadeOut(300);
	} );

	$ ( ".add_photo_defect .popup_block" ).on ( "click" , function ( e ) {
		e.stopPropagation ();
	} );

	$ ( ".condition_blocks .radio_chacked_blocks ul li.excellent_condition_block label .click_add_img" ).on ( "click" , function ( e ) {
		e.preventDefault ();
		$ ( "body" ).css ( "overflow" , "hidden" );

		$ ( "#add_photo_defect_excellent" ).fadeIn ( function () {

		} );

	} );

	$ ( ".condition_blocks .radio_chacked_blocks ul li.good_condition_block label .click_add_img" ).on ( "click" , function ( e ) {
		e.preventDefault ();
		$ ( "body" ).css ( "overflow" , "hidden" );

		$ ( "#add_photo_defect_good" ).fadeIn ( function () {

		} );

	} );


	$ ( ".add_photo_defect .close" ).on ( "click" , function ( e ) {
		e.preventDefault ();
		$ ( ".add_photo_defect" ).fadeOut ( function () {
			$ ( "body" ).css ( "overflow" , "auto" );
		} );
	} );


	$("body").on("change", ".add_photo_defect .popup_block .add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block .add_img_defect .add_file input", function () {

		var $this = $(this);
		$this.parents(".add_photo_defect_block").addClass("img_added_active");
		$this.parents(".add_photo_defect_block").addClass("img_added");

		if (this.files && this.files[0]) {
			var readerImg = new FileReader();
			readerImg.onload = imageIsLoadedPreview;
			readerImg.readAsDataURL(this.files[0]);
		}
		if (   $this.parents(".add_photo_defect_block").find(".add_text_defect textarea").val().length > 1 ){
			$this.parents(".popup_block").find(".save_img_defect").addClass("active");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks .click_add_block").addClass("active");
		} else{
			$this.parents(".popup_block").find(".save_img_defect").removeClass("active");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks .click_add_block").removeClass("active");
		}
	});

	function imageIsLoadedPreview(e) {
		var x = 'img';
		var image = '<img src="' + e.target.result + '" class="' + x + 'thImage">';
		$(".add_photo_defect .popup_block .add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added_active .add_img_defect .img_defect").empty().append(image);

		$(".add_photo_defect .popup_block .add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block ").removeClass("img_added_active ");

	}

	$ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks .click_add_block" ).on ( "click" , function (e) {
		e.preventDefault();
		var block_lenght = $ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).length;

		if ( block_lenght + 1 < 10 ){
			$ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_blosks " ).append('<div class="add_photo_defect_block">'+ $ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).first().html() +'</div>');
			$ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_blosks .add_photo_defect_block " ).last().find(".add_file input").attr("id","img_defect" + $ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).last().index());
			$ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_blosks .add_photo_defect_block " ).last().find(".add_file label").attr("for","img_defect" + $ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).last().index());
			$("#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks .click_add_block").removeClass("active");
		} else{
			$ ( "#add_photo_defect_excellent .popup_block .add_photo_defect_all_blosks.active_img_true .click_add_block" ).hide();
		}
	} );


	$ ( "#add_photo_defect_good .popup_block .add_photo_defect_all_blosks .click_add_block" ).on ( "click" , function (e) {
		e.preventDefault();
		var block_lenght = $ ( "#add_photo_defect_good .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).length;

		if ( block_lenght + 1 < 10 ){
			$ ( "#add_photo_defect_good .popup_block .add_photo_defect_blosks " ).append('<div class="add_photo_defect_block">'+ $ ( "#add_photo_defect_good .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).first().html() +'</div>');
			$ ( "#add_photo_defect_good .popup_block .add_photo_defect_blosks .add_photo_defect_block " ).last().find(".add_file input").attr("id","img_defect_g" + $ ( "#add_photo_defect_good .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).last().index());
			$ ( "#add_photo_defect_good .popup_block .add_photo_defect_blosks .add_photo_defect_block " ).last().find(".add_file label").attr("for","img_defect_g" + $ ( "#add_photo_defect_good .popup_block .add_photo_defect_all_blosks .add_photo_defect_block " ).last().index());
			$("#add_photo_defect_good .popup_block .add_photo_defect_all_blosks .click_add_block").removeClass("active");
		} else{
			$ ( "#add_photo_defect_good .popup_block .add_photo_defect_all_blosks.active_img_true .click_add_block" ).hide();
		}
	} );

	$("body").on("keyup", ".add_photo_defect .popup_block .add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block .add_text_defect textarea", function () {
		var $this = $(this);
		if ( $this.val().length > 1 ){
			if ( $this.parents(".add_photo_defect_block").hasClass("img_added") ){
				$this.parents(".popup_block").find(".save_img_defect").addClass("active");
				$this.parents(".popup_block").find(".add_photo_defect_all_blosks .click_add_block").addClass("active");
			}else{
				$this.parents(".popup_block").find(".save_img_defect").removeClass("active");
				$this.parents(".popup_block").find(".add_photo_defect_all_blosks .click_add_block").removeClass("active");
			}
		}else{
			$this.parents(".popup_block").find(".save_img_defect").removeClass("active");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks .click_add_block").removeClass("active");
		}
	});



	$ ( "#add_photo_defect_excellent .popup_block .bottom_button .save_img_defect" ).on ( "click" , function (e) {
		e.preventDefault();
		var $this = $(this);
		var n = $this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").length;


		$(".excellent_condition_block .added_images").empty().addClass("active");
		$(".excellent_condition_block .added_images").append(new Array(++n).join('<div>  </div>'));
		for ( var i = 0; i < $this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").length; i++){

			$(".excellent_condition_block .added_images div").eq(i).append('<p> '+ $this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").eq(i).find(".add_text_defect textarea").val() +'</p>'+$this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").eq(i).find(".img_defect").html());
		}
		$ ( ".add_photo_defect" ).fadeOut ( function () {
			$ ( "body" ).css ( "overflow" , "auto" );
			$this.removeClass("active");
		} );
		if(n == 1){
			$(".excellent_condition_block .click_add_img").html("Добавить фото дефекта");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks").removeClass("active_edit");
		}else{
			$(".excellent_condition_block .click_add_img").html("Редактировать фото дефекта");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks").addClass("active_edit");
		}
	} );

	$ ( "#add_photo_defect_good .popup_block .bottom_button .save_img_defect" ).on ( "click" , function (e) {
		e.preventDefault();
		var $this = $(this);
		var n = $this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").length;


		$(".good_condition_block .added_images").empty().addClass("active");
		$(".good_condition_block .added_images").append(new Array(++n).join('<div>  </div>'));
		for ( var i = 0; i < $this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").length; i++){

			$(".good_condition_block .added_images div").eq(i).append('<p> '+ $this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").eq(i).find(".add_text_defect textarea").val() +'</p>'+$this.parents(".popup_block").find(".add_photo_defect_all_blosks .add_photo_defect_blosks .add_photo_defect_block.img_added").eq(i).find(".img_defect").html());
		}
		$ ( ".add_photo_defect" ).fadeOut ( function () {
			$ ( "body" ).css ( "overflow" , "auto" );
			$this.removeClass("active");
		} );
		if(n == 1){
			$(".good_condition_block .click_add_img").html("Добавить фото дефекта");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks").removeClass("active_edit");
		}else{
			$(".good_condition_block .click_add_img").html("Редактировать фото дефекта");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks").addClass("active_edit");
		}
	} );


	$("body .add_photo_defect .popup_block").on('click', ' .remove_defect', function (e) {
		e.preventDefault();
		var $this = $(this);
		if (  $this.parents(".popup_block").find(".add_photo_defect_block ").length <= 1 ) {
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks  ").removeClass("active_edit");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks  textarea").val("");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks .click_add_block  ").removeClass("active");
			$this.parents(".popup_block").find(".save_img_defect").addClass("active");
			$this.parents(".popup_block").find(".add_photo_defect_all_blosks  .add_photo_defect_block ").removeClass("img_added");
		}else{
			$this.parents(".add_photo_defect_block ").remove();
			$this.parents(".popup_block").find(" .click_add_block  ").addClass("active");
			$this.parents(".popup_block").find(".save_img_defect").addClass("active");
		}

	});

	/*     add  new   */

	/*--------------------------- PopUp Script ----------------------------------------*/


	$ ( "#popup_private_seller .popup_block" ).on ( "click" , function ( e ) {
		e.stopPropagation ();
	} );

	$ ( ".open_popup_private_seller" ).on ( "click" , function ( e ) {
		e.preventDefault ();
		$ ( "body" ).css ( "overflow" , "hidden" );
		$ ( "#popup_private_seller" ).fadeIn ( function () {

		} );
	} );

	$ ( "#popup_private_seller .close, #popup_private_seller " ).on ( "click" , function ( e ) {
		e.preventDefault ();
		$ ( "#popup_private_seller" ).fadeOut ( function () {
			$ ( "body" ).css ( "overflow" , "auto" );
		} );
	} );


	/*-----------*/

});


