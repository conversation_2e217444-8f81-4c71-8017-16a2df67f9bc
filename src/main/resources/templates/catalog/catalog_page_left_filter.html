<div class='left_filter' th:fragment="left_filter">
    <th:block th:if="${parentCategories != null && parentCategories.size() > 0}" th:with="parentCategory=${parentCategories[0]}">
        <th:block  th:if="${parentCategory != null and parentCategory.urlName == 'zhenskoe'}" th:with="currentCatalog=${catalog.?[urlName == 'zhenskoe'][0].children}" >
            <ul th:replace="catalog/catalog_page_left_filter_category_tree :: category_tree (${currentCatalog})"></ul>
        </th:block>
        <th:block  th:if="${parentCategory != null and parentCategory.urlName == 'muzhskoe'}" th:with="currentCatalog=${catalog.?[urlName == 'muzhskoe'][0].children}" >
            <ul th:replace="catalog/catalog_page_left_filter_category_tree :: category_tree (${currentCatalog})"></ul>
        </th:block>
        <th:block  th:if="${parentCategory != null and parentCategory.urlName == 'detskoe'}" >
            <th:block th:if="${parentCategories.size() == 1}" th:with="currentCatalog=${catalog.?[urlName == 'detskoe'][0].children.?[id == #root.thisCategory.id][0]}" >
                <div th:text="${currentCatalog.displayName}" class='name_block_list'>Девочки (0-3)</div>
                <ul  th:replace="catalog/catalog_page_left_filter_category_tree :: category_child_tree (${currentCatalog})"></ul>
            </th:block>
            <th:block th:if="${parentCategories.size() > 1}" th:with="currentCatalog=${catalog.?[urlName == 'detskoe'][0].children.?[id == #root.parentCategories[1].id][0]}" >
                <div th:text="${currentCatalog.displayName}" class='name_block_list'>Девочки (0-3)</div>
                <ul  th:replace="catalog/catalog_page_left_filter_category_tree :: category_child_tree (${currentCatalog})"></ul>
            </th:block>
        </th:block>
    </th:block>
    <th:block th:if="${thisCategory.urlName == 'zhenskoe'}" th:with="currentCatalog=${catalog.?[urlName == 'zhenskoe'][0].children}" >
            <ul th:replace="catalog/catalog_page_left_filter_category_tree :: category_tree (${currentCatalog})"></ul>
    </th:block>
    <th:block  th:if="${thisCategory.urlName == 'muzhskoe'}" th:with="currentCatalog=${catalog.?[urlName == 'muzhskoe'][0].children}" >
        <ul th:replace="catalog/catalog_page_left_filter_category_tree :: category_tree (${currentCatalog})"></ul>
    </th:block>
    <th:block  th:if="${thisCategory.urlName == 'detskoe'}" th:with="currentCatalog=${catalog.?[urlName == 'detskoe'][0].children}" >
        <ul th:replace="catalog/catalog_page_left_filter_category_tree :: category_tree (${currentCatalog})"></ul>
    </th:block>





</div>