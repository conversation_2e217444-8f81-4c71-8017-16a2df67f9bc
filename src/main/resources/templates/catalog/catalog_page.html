<!DOCTYPE html>
<html
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/current/layout}"
      th:with="
        pageStyle = 'cataloguePage',
        noMetaDescriptionTag = ${request.page > 1},
        isPaginating = ${request != null && request.page > 1},
        isNewCat = ${isJustInCategory != null && isJustInCategory != false},
        isChoiceCat = ${isOurChoiceCategory != null && isOurChoiceCategory != false},
        isMen = ${#strings.contains(thisCategory.urlName, 'muzhskoe')},
        isWomen = ${#strings.contains(thisCategory.urlName, 'zhenskoe')},
        paginationText = ${request != null && request.page > 1 && productsPage != null && productsPage.totalPages > 1 ? ' - страница ' + request.page + ' из ' + productsPage.totalPages : ''},
        catalogTitle = ${brand != null ? (isMen ? 'Мужское ' : isWomen ? 'Женское ' : 'Детское ' ) : ''} + ${thisCategory.fullName}?: ${thisCategory.displayName}
      "
>

<head>
    <title th:if="${!isPaginating}" th:text="${brandTitle} ?: ${brand != null ? (thisCategory.displayName + ' для ' + (isMen ? 'мужчин' : isWomen ? 'женщин' : 'детей' ) + ' - купить оригинал в Москве, свыше ' + productsPage.totalAmount + ' предложений в интернет-магазине Oskelly') : titleText}"></title>

    <th:block th:if="${!isPaginating}">
        <th:block th:if="${hiddenDescription}">
            <meta name="description" th:content="${hiddenDescription}">
        </th:block>
        <th:block th:unless="${hiddenDescription}">
            <meta th:if="${brand == null}" name="description" th:content="|Брендовые ${thisCategory.effectiveName} со скидкой купить по цене от ${#numbers.formatDecimal(availableFilters == null || availableFilters.startPrice == null ? 0 : availableFilters.startPrice, 0, 0)} руб. в интернет-магазине Oskelly. Все наши товары проверены экспертизой. ✔ Оригинальные вещи мировых брендов. ✔ Доставка в течение 5 рабочих дней по Москве и всей России.|">
            <meta th:if="${brand != null}" name="description" th:content="|Купить одежду бренда ${brand.name} (${brand.transliterateName}): ${isWomen ? 'женская' : isMen ? 'мужская' : 'детская'} одежда, обувь и сумки. Свыше ${productsPage.totalAmount} моделей в интернет-магазине Oskelly. Подарочная упаковка и доставка по Москве и всей России.|">
            <link rel="canonical" th:href="@{__${#httpServletRequest.requestURI}__}">
        </th:block>
    </th:block>

    <th:block th:if="${isPaginating}">
        <meta name="description" content="">
        <link rel="canonical" th:href="@{${fullURLWithQuery}}">
        <!--/* Для SEO на страницах с пагинацией title всегда идентичен h1 с добавлением указания "- страница N из totalPages" */-->
        <title th:if="${!isNewCat && !isChoiceCat}" th:text="|${thisCategory.fullName?: thisCategory.displayName}${paginationText}|"></title>
        <title th:if="${isNewCat}" th:text="|Новинки - ${thisCategory.displayName}${paginationText}|"></title>
        <title th:if="${isChoiceCat}" th:text="|Выбор OSKELLY - ${thisCategory.displayName}${paginationText}|"></title>
    </th:block>

    <link rel="stylesheet" th:href="@{/css/catalog_page/catalog.css}">

    <script type="application/ld+json" th:inline="javascript">
        /*[[${breadcrumbs}]]*/
    </script>
</head>

<body>
<div layout:fragment="content" th:remove="tag">
<section class='catalog_page' th:data-catalog-type="${isJustInCategory != null ? 'JUST-IN' : (#root.isOurChoiceCategory != null ? 'OUR_CHOICE' : (#root.brand != null) ? 'BRAND' : _)}" th:data-url-name="${thisCategory.urlName}" th:data-category-id="${thisCategory.id}"
th:data-root-url="${(parentCategories != null and parentCategories.size() > 0 )? parentCategories[0].urlName : thisCategory.urlName}" th:data-root-id="${(parentCategories != null and parentCategories.size() > 0 )? parentCategories[0].id : thisCategory.id}"
th:attrappend="data-brand-id=${(brand != null) ? brand.id : _}, data-brand-url-name=${(brand != null) ? brand.url : _}">
    <div class='wrapper'>
        <div class='navigation_page'>
            <ul th:if="${(isJustInCategory == null or isJustInCategory == false) && (isOurChoiceCategory == null or isOurChoiceCategory == false)}">
                <li>
                    <a href='/'>Главная</a>
                </li>
                <li th:each="parentCategory : ${parentCategories}" th:switch="${parentCategory.displayName}">
                    <a th:case="'Женское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для нее</a>
                    <a th:case="'Мужское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для него</a>
                    <a th:case="*" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       th:text="${parentCategory.displayName}"
                       href='#'>Для детей</a>
                </li>
                <li th:if="${brand == null}">
                    <a th:text="${thisCategory.displayName}" >Джинсы</a>
                </li>
                <li th:if="${brand != null}">
                    <a th:href="@{/catalog/{url}(url=${thisCategory.urlName})}" th:text="${thisCategory.displayName}" >Джинсы</a>
                    <a th:text="${thisCategory.fullName}" >Gucci</a>
                </li>
            </ul>
            <ul th:if="${(isJustInCategory != null and isJustInCategory != false)}">
                <li>
                    <a href='/'>Главная</a>
                </li>
                <li th:each="parentCategory : ${parentCategories}" th:switch="${parentCategory.displayName}">
                    <a th:case="'Женское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для нее</a>
                    <a th:case="'Мужское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для него</a>
                    <a th:case="'Детское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для детей</a>
                    <a th:case="*" th:href="@{/catalog/__${parentCategory.urlName}__(justInCategory=1)}"
                       th:text="${parentCategory.displayName}"
                       href='#'>Для детей</a>
                </li>
                <li>
                    <a th:text="${thisCategory.displayName}" >Джинсы</a>
                </li>
            </ul>
            <ul th:if="${(isOurChoiceCategory != null and isOurChoiceCategory != false)}">
                <li>
                    <a href='/'>Главная</a>
                </li>
                <li th:each="parentCategory : ${parentCategories}" th:switch="${parentCategory.displayName}">
                    <a th:case="'Женское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для нее</a>
                    <a th:case="'Мужское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для него</a>
                    <a th:case="'Детское'" th:href="@{/catalog/{url}(url=${parentCategory.urlName})}"
                       href='#'>Для детей</a>
                    <a th:case="*" th:href="@{/catalog/__${parentCategory.urlName}__(ourChoiceCategory=1)}"
                       th:text="${parentCategory.displayName}"
                       href='#'>Для детей</a>
                </li>
                <li>
                    <a th:text="${thisCategory.displayName}" >Джинсы</a>
                </li>
            </ul>
        </div>
        <div>
            <h1 th:if="${!isNewCat and !isChoiceCat}" class='title_page' th:text="${catalogTitle}">Женские Джинсы</h1>
            <h1 th:if="${isNewCat }" class='title_page' th:text="|Новинки - ${catalogTitle}|">Женские Джинсы</h1>
            <h1 th:if="${isChoiceCat }" class='title_page' th:text="|Выбор OSKELLY - ${catalogTitle}|">Женские Джинсы</h1>


        </div>
        <div th:if="${productsPage != null and productsPage.totalAmount > 0 }" class='amount_goods total_amount' th:data-total-amount="${productsPage.totalAmount}"><span class="products_amount_number" th:text="${productsPage.totalAmount}">2</span> товаров</div>
        <div class='filter_goods clearfix'>
            <div th:replace="catalog/catalog_page_left_filter :: left_filter"></div>
            <div th:replace="catalog/catalog_page_right_block :: all_right_block(${brands}, ${thisCategory}, ${sizesBySizeType}, ${sizeTypes}, ${requestedSizeType},
   ${thisCategoryAttributes}, ${productConditions}, ${tags}, ${newConditionTag}, ${ourChoiceTag}, ${newCollectionTag}, ${officeTag},
     ${vintageTag}, ${productsPage.items
     }, ${request.page}, ${prevPage}, ${nextPage}, ${productsPage.totalPages}, ${seller}, ${isMyAccountPage})"></div>

        </div>
    </div>
</section>
<section class='text_bottom' th:if="${brand != null and brandDescription != null}">
    <div class='wrapper'>
        <div class='all_text'>
            <p>
                <th:block th:text="${brandDescription}"></th:block>
                <a class='hide_show_text' href='#'>Показать</a>
            </p>
        </div>
    </div>
</section>
<div class='click_open_filter'>
    <a href='#'>
        <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5912 0.590379V10.2376C11.9664 10.5131 13 11.7289 13 13.1764C13 14.6283 11.962 15.8397 10.5912 16.1152V20.4096C10.5912 20.7376 10.3285 21 10 21C9.67153 21 9.40876 20.7376 9.40876 20.4096V16.1152C8.03796 15.8397 7 14.6283 7 13.1764C7 11.7245 8.03796 10.5131 9.40876 10.2376V0.590379C9.40876 0.262391 9.67153 0 10 0C10.3285 0 10.5912 0.262391 10.5912 0.590379ZM8.1825 13.1763C8.1825 14.1778 8.99709 14.9912 10 14.9912C11.0029 14.9912 11.8175 14.1778 11.8175 13.1763C11.8175 12.1749 11.0029 11.3615 10 11.3615C8.99709 11.3615 8.1825 12.1749 8.1825 13.1763Z" fill="#333333"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.59124 0.590379V5.28717C4.96204 5.56268 6 6.76968 6 8.22157C6 9.67347 4.96204 10.8848 3.59124 11.1603V20.4096C3.59124 20.7376 3.32847 21 3 21C2.67153 21 2.40876 20.7376 2.40876 20.4096V11.1647C1.03796 10.8892 0 9.67784 0 8.22595C0 6.77405 1.03796 5.56268 2.40876 5.28717V0.590379C2.40876 0.262391 2.67153 0 3 0C3.32847 0 3.59124 0.262391 3.59124 0.590379ZM1.1825 8.22588C1.1825 9.22734 1.99709 10.0407 3.00001 10.0407C3.99855 10.0407 4.81753 9.22734 4.81753 8.22588C4.81753 7.22442 4.00293 6.41101 3.00001 6.41101C1.99709 6.41101 1.1825 7.22442 1.1825 8.22588Z" fill="#333333"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5912 0.590256V5.28607C18.9664 5.56152 20 6.76827 20 8.21986C20 9.67146 18.962 10.8826 17.5912 11.158V20.4097C17.5912 20.7377 17.3285 21 17 21C16.6715 21 16.4088 20.7377 16.4088 20.4097V11.1624C15.038 10.8869 14 9.67583 14 8.22423C14 6.77264 15.038 5.56152 16.4088 5.28607V0.590256C16.4088 0.262336 16.6715 0 17 0C17.3285 0 17.5912 0.262336 17.5912 0.590256ZM15.1825 8.22416C15.1825 9.22541 15.9971 10.0386 17 10.0386C18.0029 10.0386 18.8175 9.22541 18.8175 8.22416C18.8175 7.22291 18.0029 6.40967 17 6.40967C15.9971 6.40967 15.1825 7.22291 15.1825 8.22416Z" fill="#333333"/>
        </svg>

        <em>Фильтр и сортировка</em>
    </a>
</div>
<div class='popup' id='popup_my_size'>
</div>
<div th:replace="catalog/catalog_page_mobile_filter_popup :: mobile_filter_popup(${parentCategories}, ${brands}, ${brandsFirstChars}, ${thisCategory}, ${sizesBySizeType}, ${sizeTypes}, ${requestedSizeType}, ${thisCategoryAttributes}, ${productConditions}, ${ourChoiceTag}, ${newCollectionTag}, ${vintageTag}, ${null})"></div>
</div>
</body>
<div layout:fragment="custom-scripts" th:remove="tag">
    <script th:src="@{/js/catalog_page/api_client.js}"></script>
    <script th:src="@{/js/catalog_page/catalog_page.js}"></script>
    <script th:src="@{/js/event/inVision.js}"></script>
    <script th:src="@{/js/analytics/dataLayer/mainPageProductIsVision.js}"></script>
</div>
</html>
