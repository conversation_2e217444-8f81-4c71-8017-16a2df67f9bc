<div class='filter_top' th:fragment="filter_top(brands, sizesBySizeType, defaultSizeType, sizeTypes, requestedSizeType, thisCategoryAttributes, productConditions, seller)">
    <ul>
        <li class="filter_item" id="brand_filter" th:if="${brands != null}">
            <a href='#'>
                <div class='hide_existing' >
                    Бренд
                    <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 1L4 4L7 1" stroke="#333333"/>
                    </svg>

                </div>
                <div class='show_selected'>
                    <div class='selected_elem'>
                        <em>Бренд:</em>
                        <div></div>
                    </div>
                    <div class='close'>
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                        </svg>

                    </div>
                </div>
            </a>
            <div class='toggle_filter'>
                <div class='search_form'>
                    <form>
                        <input class='search_text' placeholder='Поиск по бренду ' required='' type='text'>
                        <input class='search_submit' type='submit' value=' '>
                    </form>
                </div>
                <div class='scrolling_filter scroll_content'>
                    <ul>
                        <li th:each="brand, brandIter : ${brands}">
                            <div class='check' th:data-filter-value="${brand.id}">
                                <input th:id="|check_top_1_${brandIter.index + 1}|" id='check_top_1_1' type='checkbox'>
                                <label th:for="|check_top_1_${brandIter.index + 1}|" for='check_top_1_1' th:text="${brand.name}"></label>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class='apply'>
                    <a href='#'>Применить</a>
                </div>
            </div>
        </li>
        <li class="filter_item" id="size_filter" th:if="${requestedSizeType != null}">
            <a href='#'>
                <div class='hide_existing'>
                    Размер
                    <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 1L4 4L7 1" stroke="#333333"/>
                    </svg>

                </div>
                <div class='show_selected'>
                    <div class='selected_elem'>
                        <em>Размер:</em>
                        <div></div>
                    </div>
                    <div class='close'>
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                        </svg>

                    </div>
                </div>
            </a>
            <div class='toggle_filter'>
                <div th:with="sizeType=${(sizeTypes.?[name() == #root.requestedSizeType])[0]}" th:text="|${sizeType.description} (${sizeType.abbreviation})|" class='title_filter_toggle'>Европейский размер (EU)</div>
                <div class='scrolling_filter scroll_content'>
                    <ul>
                        <th:block th:each="size : ${sizesBySizeType}">
                            <li th:if="${size.name != 'U'}">
                                <div class='check' th:data-filter-value="${size.id}">
                                    <input th:id='|check_top_size_${size.id}|' id='check_top_2_2' type='checkbox'>
                                    <label th:text="${size.name}" th:for='|check_top_size_${size.id}|' for='check_top_2_2'>30</label>
                                </div>
                            </li>
                        </th:block>

                    </ul>
                </div>
                <div class='apply apply_other'>
                    <a href='#'>Применить</a>
                </div>
            </div>
        </li>
        <th:block th:if="${thisCategoryAttributes}" th:each="attribute, attributeIter : ${thisCategoryAttributes}">
            <li th:if="${attribute.key.name == 'Цвет'}" class="filter_item attribute_filter" id="color_filter">
                <a href='#'>
                    <div class='hide_existing' th:inline="text">
                        [[${attribute.key.name} ]]
                        <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 1L4 4L7 1" stroke="#333333"/>
                        </svg>

                    </div>
                    <div class='show_selected'>
                        <div class='selected_elem'>
                            <em th:text="|${attribute.key.name}:|">Цвет:</em>
                            <div></div>
                        </div>
                        <div class='close'>
                            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                            </svg>

                        </div>
                    </div>
                </a>
                <div class='toggle_filter'>
                    <div class='search_form'>
                        <form>
                            <input class='search_text' placeholder='Поиск ' required='' type='text'>
                            <input class='search_submit' type='submit' value=' '>
                        </form>
                    </div>
                    <div class='scrolling_filter scroll_content'>
                        <ul class='color_list'>
                            <li th:each="value, valueIter : ${attribute.value}">
                                <div class='check' th:data-filter-value="${value.id}">
                                    <input th:id="|check_top_${attributeIter.index + 3}_${valueIter.index + 1}|" id='check_top_3_1' type='checkbox'>
                                    <label th:for="|check_top_${attributeIter.index + 3}_${valueIter.index + 1}|" for='check_top_3_1'>
                                        <div th:style='|background:no-repeat center url(@{|/imgs/colors/${value.transliterateValue}.png|})|' style='background:#E4BD8F'></div>
                                        <span th:text="${value.value}">бежевый</span>
                                    </label>
                                </div>
                            </li>

                        </ul>
                    </div>
                    <div class='apply'>
                        <a href='#'>Применить</a>
                    </div>
                </div>
            </li>
            <li th:unless="${attribute.key.name == 'Цвет'}" th:data-attribute-id="${attribute.key.id}" class="filter_item attribute_filter">
                <a href='#'>
                    <div class='hide_existing' th:inline="text">
                        [[${attribute.key.name} ]]
                        <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 1L4 4L7 1" stroke="#333333"/>
                        </svg>

                    </div>
                    <div class='show_selected'>
                        <div class='selected_elem'>
                            <em th:text="|${attribute.key.name}:|">Материал:</em>
                            <div></div>
                        </div>
                        <div class='close'>
                            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                            </svg>

                        </div>
                    </div>
                </a>
                <div class='toggle_filter'>
                    <div class='scrolling_filter scroll_content'>
                        <ul>
                            <li th:each="value, valueIter : ${attribute.value}">
                                <div class='check' th:data-filter-value="${value.id}">
                                    <input th:id="|check_top_${attributeIter.index + 3}_${valueIter.index + 1}|" id='check_top_4_1' type='checkbox'>
                                    <label th:for="|check_top_${attributeIter.index + 3}_${valueIter.index + 1}|" th:text="${value.value}" for='check_top_4_1'>искусственная кожа</label>
                                </div>
                            </li>

                        </ul>
                    </div>
                    <div class='apply'>
                        <a href='#'>Применить</a>
                    </div>
                </div>
            </li>
        </th:block>
        <li th:with="index=${(thisCategoryAttributes != null) ? thisCategoryAttributes.size() + 3 : 3}" class="filter_item" id="condition_filter">
            <a href='#'>
                <div class='hide_existing'>
                    Состояние товара
                    <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 1L4 4L7 1" stroke="#333333"/>
                    </svg>

                </div>
                <div class='show_selected'>
                    <div class='selected_elem'>
                        <em>Состояние товара:</em>
                        <div></div>
                    </div>
                    <div class='close'>
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                        </svg>

                    </div>
                </div>
            </a>
            <div class='toggle_filter'>
                <div class='scrolling_filter scroll_content'>
                    <ul>
                        <li th:if="productConditions != null" th:each="condition, conditionIter : ${productConditions}">
                            <div class='check' th:data-filter-value="${condition.id}">
                                <input th:id="|check_top_${index}_${conditionIter.index + 1}|" id='check_top_5_1' type='checkbox'>
                                <label th:for="|check_top_${index}_${conditionIter.index + 1}|" th:text="${condition.name}" for='check_top_5_1'>Новое, с биркой</label>
                            </div>
                        </li>

                    </ul>
                </div>
                <div class='apply'>
                    <a href='#'>Применить</a>
                </div>
            </div>
        </li>
        <li th:if="${seller == null}" th:with="index=${(thisCategoryAttributes != null) ? thisCategoryAttributes.size() + 4 : 4}" class="filter_item" id="seller-type_filter">
            <a href='#'>
                <div class='hide_existing'>
                    Тип продавца
                    <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 1L4 4L7 1" stroke="#333333"/>
                    </svg>

                </div>
                <div class='show_selected'>
                    <div class='selected_elem'>
                        <em>Тип продавца:</em>
                        <div></div>
                    </div>
                    <div class='close'>
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                        </svg>

                    </div>
                </div>
            </a>
            <div class='toggle_filter'>
                <div class='scrolling_filter scroll_content'>
                    <ul>
                        <li>
                            <div class='check' data-filter-value="simple">
                                <input th:id="|check_top_${index}_1|" id='check_top_6_1' type='checkbox'>
                                <label th:for="|check_top_${index}_1|" for='check_top_6_1'>Частный продавец</label>
                            </div>
                        </li>
                        <li>
                            <div class='check' data-filter-value="pro">
                                <input th:id="|check_top_${index}_2|" id='check_top_6_2' type='checkbox'>
                                <label th:for="|check_top_${index}_2|" for='check_top_6_2'>Бутик</label>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class='apply'>
                    <a href='#'>Применить</a>
                </div>
            </div>
        </li>
        <li id="price_filter">
            <a href='#'>
                <div class='hide_existing'>
                    Цена
                    <svg width="8" height="5" viewBox="0 0 8 5" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 1L4 4L7 1" stroke="#333333"/>
                    </svg>

                </div>
                <div class='show_selected'>
                    <div class='selected_elem'>
                        <em>Цена:</em>
                        <div class="prices_div">
                            <span class="first_price_span"></span>
                            <span>-</span>
                            <span class="last_price_span"></span>
                        </div>
                    </div>
                    <div class='close'>
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9M1 1L9 9" stroke="#333333"/>
                        </svg>

                    </div>
                </div>
            </a>
            <div class='toggle_filter'>
                <div class='filter_price'>
                    <form>
                        <span>от</span>
                        <input class='first_price text_input' placeholder='1000' required='' type='text'>
                        <span>-</span>
                        <input class='last_price text_input' placeholder='999000' required='' type='text'>
                        <span><i class="fa-rub fa"></i></span>
                    </form>
                </div>
                <div class='apply apply_price'>
                    <a href='#'>Применить</a>
                </div>
            </div>
        </li>
        <li>
            <div class='clear_filter'>
                <a href='#'>Очистить фильтры</a>
            </div>
        </li>
    </ul>
</div>