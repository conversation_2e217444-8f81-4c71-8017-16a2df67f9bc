<!DOCTYPE html>
<html>
<head>
<meta content='text/html; charset=UTF-8' http-equiv='Content-Type'>
<meta charset='UTF-8'>
<meta content='width=device-width, initial-scale=1, height=device-height' name='viewport'>
<title>Публикация товаров</title>
<meta content='Публикация товаров' name='keywords'>
<meta content='Публикация товаров' name='description'>
<link href='../css/normalize.css' rel='stylesheet'>
<link href='../css/init.css' rel='stylesheet'>
<link href='../css/application.css' rel='stylesheet'>
<link href='../css/style_p.css' rel='stylesheet'>
    <script type='application/ld+json'>
		{
			"@context": "http://www.schema.org",
			"@type": "LocalBusiness",
			"name": "Oskelly Group",
			"url": "https://oskelly.ru/",
			"logo": "https://static.oskelly.ru/images/icons/insta_logo.png?v=2.1.2_c0753d93593d6441d0dd4f9eaecf0d8f",
			"image": "https://static.oskelly.ru/images/icons/insta_img_1.jpg?v=2.1.2_0a83dbe2155b3a0373b2dc3eb9a5a5ba",
			"description": "Первый сервис для покупки и перепродажи брендовых вещей в России. Бутики и частные продавцы размещают у нас новую и почти новую брендовую одежду со скидками до -90%.\n\nКаждый день мы добавляем 100+ новых товаров, а наши эксперты собирают лучшие из них в специальные подборки.",
			"address": {
				"@type": "PostalAddress",
				"streetAddress": "Бережковская набережная 16а",
				"postOfficeBoxNumber": "<EMAIL>",
				"addressLocality": "Москва",
				"addressRegion": "Москва",
				"postalCode": "121059",
				"addressCountry": "Россия"
			},
			"geo": {
				"@type": "GeoCoordinates",
				"latitude": "55.737054",
				"longitude": "37.563080"
			},
			"hasMap": "https://goo.gl/maps/x94wA7cnGmzmkt4v9",
			"openingHours": "Mo, Tu, We, Th, Fr, Sa, Su -",
			"contactPoint": {
				"@type": "ContactPoint",
				"telephone": "8 (800) 707-53-08",
				"contactType": "shop"
			}
		}
	</script>
</head>
<body>
<div class='ny-layer ny-layer--product'>
<div class='ny-container'>
<div class='publish_original'>
<div class='close'></div>
<div class='left_publish_img'>
    <img src='/images/pictures/alert.png'>
</div>
<div class='right_publish_info'>
<div class='name_block_publish'>Публикуйте только оригинал!</div>
<div class='flex_block_publish'>
<div class='block_publish'>
<div class='icon_block'>
<svg width="22" height="16" viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.4941 2.93333L14.7441 1.33333L7.25585 1.33333L4.50585 2.93333H1.375V14.6667L20.625 14.6667V2.93333H17.4941ZM4.125 1.6H1.375C0.615609 1.6 0 2.19696 0 2.93334V14.6667C0 15.403 0.615609 16 1.375 16H20.625C21.3844 16 22 15.403 22 14.6667V2.93333C22 2.19695 21.3844 1.6 20.625 1.6H17.875L15.4516 0.190009C15.2379 0.0656771 14.9934 0 14.7441 0H7.25585C7.00664 0 6.76212 0.065677 6.54842 0.190009L4.125 1.6Z" fill="#8E8E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11 12.0013C13.2782 12.0013 15.125 10.2104 15.125 8.0013C15.125 5.79216 13.2782 4.0013 11 4.0013C8.72183 4.0013 6.875 5.79216 6.875 8.0013C6.875 10.2104 8.72183 12.0013 11 12.0013ZM11 13.3346C14.0376 13.3346 16.5 10.9468 16.5 8.0013C16.5 5.05578 14.0376 2.66797 11 2.66797C7.96243 2.66797 5.5 5.05578 5.5 8.0013C5.5 10.9468 7.96243 13.3346 11 13.3346Z" fill="#8E8E93"/>
</svg>

</div>
<div class='name_block'>Экспертиза фото перед публикацией</div>
<p>Мы проверяем размер, состояние, стоимость и подлинность товара по фотографии продавца. При прохождении экспертизы - размещаем его на белом фоне</p>
</div>
<div class='block_publish'>
<div class='icon_block'>
<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.58301 13.9029C11.073 13.9029 13.9022 11.0736 13.9022 7.58342C13.9022 4.09325 11.073 1.2639 7.58301 1.2639C4.09303 1.2639 1.26383 4.09325 1.26383 7.58342C1.26383 11.0736 4.09303 13.9029 7.58301 13.9029ZM7.58301 15.1668C11.771 15.1668 15.166 11.7716 15.166 7.58342C15.166 3.39521 11.771 0 7.58301 0C3.39503 0 0 3.39521 0 7.58342C0 11.7716 3.39503 15.1668 7.58301 15.1668Z" fill="#8E8E93"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.191 12.1899C12.4378 11.9431 12.8379 11.9431 13.0846 12.1899L17.553 16.6584C17.7997 16.9052 17.7997 17.3054 17.553 17.5521C17.3062 17.7989 16.9061 17.7989 16.6593 17.5521L12.191 13.0836C11.9442 12.8368 11.9442 12.4367 12.191 12.1899Z" fill="#8E8E93"/>
</svg>

</div>
<div class='name_block'>Экспертиза в офисе OSKELLY</div>
<p>После продажи товара, мы заберем его у вас и сами доставим покупателю, проверив перед этим в нашем офисе на подлинность и соответствие вашего описания</p>
</div>
</div>
</div>
</div>
<div class='finish_publishing'>
<div class='text_link'>
У вас есть
<span>5</span>
неопубликованных товаров.
<a href='#'>Закончить публикацию</a>
</div>
</div>
<ul class='catalog_menu'>
<li>
<a href='#'>Для неё</a>
<div class='one_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
<ul class='one_list'>
<li>
<a href='#'>
Одежда
<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
</svg>

</a>
<div class='two_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
<ul class='two_list'>
<li>
<a href='#'>Брюки</a>
</li>
<li>
<a href='#'>Верхняя одежда</a>
</li>
<li>
<a href='#'>Водолазки</a>
</li>
<li>
<a href='#'>Джемперы, кардиганы и трикотаж</a>
</li>
<li>
<a href='#'>Джинсы</a>
<div class='check_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
<ul class='check_list'>
<li>
<div class='check'>
<input id='check_pop_1_1' type='checkbox'>
<label for='check_pop_1_1'>Все джинсы</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_2' type='checkbox'>
<label for='check_pop_1_2'>Прямые джинсы</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_3' type='checkbox'>
<label for='check_pop_1_3'>Узкие джинсы</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4' type='checkbox'>
<label for='check_pop_1_4'>Другие джинсы</label>
</div>
</li>
</ul>
</div>
</li>
<li>
<a href='#'>Костюмы</a>
</li>
<li>
<a href='#'>Пиджаки</a>
</li>
<li>
<a href='#'>Поло</a>
</li>
<li>
<a href='#'>Рубашки</a>
</li>
<li>
<a href='#'>Спортивная одежда</a>
</li>
<li>
<a href='#'>Футболки</a>
</li>
<li>
<a href='#'>Шорты</a>
</li>
</ul>
</div>
</li>
<li>
<a href='#'>
Сумки
<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
</svg>

</a>
<div class='two_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
</div>
</li>
<li>
<a href='#'>
Обувь
<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
</svg>

</a>
<div class='two_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
</div>
</li>
<li>
<a href='#'>
Аксессуары
<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
</svg>

</a>
<div class='two_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
<ul class='check_list'>
<li>
<div class='check'>
<input id='check_pop_1_4_1' type='checkbox'>
<label for='check_pop_1_4_1'>Все аксессуары</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_2' type='checkbox'>
<label for='check_pop_1_4_2'>Визитницы</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_3' type='checkbox'>
<label for='check_pop_1_4_3'>Галстуки и бабочки</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_4' type='checkbox'>
<label for='check_pop_1_4_4'>Головные уборы</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_5' type='checkbox'>
<label for='check_pop_1_4_5'>Кепки</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_6' type='checkbox'>
<label for='check_pop_1_4_6'>Шляпы</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_7' type='checkbox'>
<label for='check_pop_1_4_7'>Запонки</label>
</div>
</li>
<li>
<div class='check'>
<input id='check_pop_1_4_8' type='checkbox'>
<label for='check_pop_1_4_8'>Кошельки</label>
</div>
</li>
</ul>
</div>
</li>
</ul>
</div>
</li>
<li>
<a href='#'>Для него</a>
<div class='one_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
</div>
</li>
<li>
<a href='#'>Для детей</a>
<div class='one_list_block'>
<div class='prev_filter_list_title'>
<div class='prev_filter_list'>
<svg width="28" height="24" viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3398 1L0.999844 12L12.3398 23" stroke="#333333"/>
<line x1="1" y1="11.9399" x2="27.04" y2="11.9399" stroke="#333333"/>
</svg>

</div>
<div class='title_popup'>Категория</div>
</div>
</div>
</li>
</ul>
</div>
</div>
</body>
</html>
