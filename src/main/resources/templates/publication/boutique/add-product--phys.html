<!DOCTYPE html>
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:th="http://www.thymeleaf.org"
      layout:decorate="~{layout/current/layout}" th:with="pageStyle='publication'">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, height=device-height">
    <title>Публикация товаров [[${headTitle}]]</title>
    <meta name="keywords" content="Публикация товаров">
    <meta name="description" content="Публикация товаров">
    <!-- build:css app/dist/build/css/vendor.css -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:400,700|Roboto">
    <!-- bower:css-->
    <link rel="stylesheet" th:href="@{/publication/css/normalize.css}">
    <link rel="stylesheet" th:href="@{/publication/css/jquery.mCustomScrollbar.min.css}">
    <!-- endbower-->
    <!-- endbuild -->
    <!-- build:css app/dist/build/css/main.css -->
    <link rel="stylesheet" th:href="@{/publication/css/init.css}">
    <link rel="stylesheet" th:href="@{/publication/css/application.css}">
    <link rel="stylesheet" th:href="@{/publication/css/style_p.css}">
    <link rel="stylesheet" th:href="@{/publication/css/publish_validation.css}">
    <link rel="stylesheet" th:href="@{/publication/css/publication.css}">
    <link rel="stylesheet" th:href="@{/css/address/address.css}">
    <script th:if="${isPro == false}">var isPro = false;</script>
    <!-- endbuild -->
</head>
<body>
<div layout:fragment="content">
    <div class="ny-layer ny-layer--product">
        <div class="wrapper">
            <div class='finish_publishing' style="display: none">
                <div class='text_link'>
                    У вас есть
                    <span class="finish_publishing_count">0</span>
                    неопубликованных товаров.
                    <a href='/account/products?state=DRAFT' class="finish_publishing_end_draft" style="display: none">Закончить публикацию</a>
                </div>
            </div>
        </div>
        <div class="ny-container">

            <!-- left panel photo -->
            <aside class="ny-sidebar ny-sidebar--product mob_none_publication">
                <div id="draftId" th:attr="data-draft-id=${draftId != null ? draftId : 'none'}" style="display: none"></div>
                <div class="ny-product__sidebar">
                    <div class="nyp-photo">
                        <div class="ny-product__title">Фото</div>
                        <div class="nyp-photo__caption">Чтобы загрузить фотографии выберите категорию товара. Вы можете
                            ввести артикул товара и подгрузить фотографии, если они у нас есть
                        </div>
                        <ul class="nyp-photo__gallery downloadPhotoPlace">
                            <button class="ny-button ny-button--edit-picture"
                                    title="Заменить фото"
                                    style="display: none;"
                                    id="photoChangeButton">
                                <i class="ny-icon ny-icon--edit-picture"></i>
                            </button>
                            <button class="ny-button ny-button--edit-remove"
                                    title="Удалить фото"
                                    style="display: none;"
                                    id="photoDeleteButton">
                                <i class="ny-icon ny-icon--edit-remove"></i>
                            </button>
                            <button class="ny-button ny-button--nyp-photo-add"
                                    title="Добавить фото"
                                    style="display: none;"
                                    id="photoAddButton">
                                <i class="ny-icon ny-icon--nyp-photo-add"></i>
                            </button>
                            <li data-id="1"
                                class="nyp-photo__gallery-item empty_photo example-photo-list-item photo-list-item">
                                <div class="nyp-photo__gallery-col main-upload-photo-one-click-div"><span
                                        class="nyp-photo__gallery-label">Основное фото</span>
                                    <input type="file" class="upload-photo-one-input"  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG"
                                           style="display:none;"/>
                                    <div class="nyp-photo__gallery-picture upload-photo-one-click-div">
                                        <img src="" class="ny-picture ny-picture--edit realPhoto img-show"
                                             data-photo-order="1" data-photo-id="" style="display: none">
                                        <!--<input type="file" placeholder="" id="ny-field&#45;&#45;gallery-upload" name="gallery-upload" class="ny-field ny-field&#45;&#45;gallery-upload">-->
                                        <i class="ny-icon ny-icon--nyp-photo-load photo-icon"></i>
                                    </div>
                                </div>
                                <div class="nyp-photo__gallery-col"><span
                                        class="nyp-photo__gallery-label">Пример фото</span>
                                    <div class="nyp-photo__gallery-picture"><i
                                            class="ny-icon ny-icon--nyp-photo-logo"></i>
                                    </div>
                                </div>
                                <div class="nyp-tooltip"><i class="ny-icon ny-icon--nyp-tooltip"></i>
                                    <div class="nyp-tooltip__box">
                                        <p>Качественная фотография - залог успешной продажи</p>
                                        <ul>
                                            <li> Фотографируйте вещи в<strong>хорошо освещенном помещении,</strong>
                                                располагая изделия в необходимом ракурсе (представленном на примере).
                                            </li>
                                            <li>Наши специалисты отретушируют <strong>главную фотографию</strong>и
                                                поместят ее на белый фон.
                                            </li>
                                            <li>Следуйте подсказкам на <strong>«Пример фото»</strong> возле каждой
                                                позиции.
                                            </li>
                                            <li>Не забывайте о<strong>дополнительных фотографиях.</strong> Чем больше
                                                изображений будет загружено, тем проще покупателю составить
                                                представление о товаре и принять нужное решение.
                                            </li>
                                        </ul>

                                    </div>
                                </div>
                            </li>
                            <li class="nyp-photo__gallery-item" style="display: none;">
                                <div class="nyp-photo__gallery-col">
                                    <span class="nyp-photo__gallery-label">Основное фото</span>
                                    <div class="nyp-photo__gallery-picture nyp-photo__gallery-picture--fill">
                                        <input type="file" placeholder="" id="ny-field--edit-picture-1"
                                               name="edit-picture-1" class="ny-field ny-field--edit-picture">
                                        <img src="" data-photo-order="1" class="ny-picture ny-picture--edit realPhoto">
                                    </div>
                                </div>
                                <div class="nyp-photo__gallery-col"><span
                                        class="nyp-photo__gallery-label">Пример фото</span>
                                    <div class="nyp-photo__gallery-picture"><i
                                            class="ny-icon ny-icon--nyp-photo-logo"></i>
                                    </div>
                                </div>
                            </li>

                            <th:block th:each="i: ${#numbers.sequence(2, 9)}">
                                <li class="nyp-photo__gallery-item" style="display: none;">
                                    <div class="nyp-photo__gallery-col"><span class="nyp-photo__gallery-label"
                                                                              th:text="|Фото №${i}|">Фото №2</span>
                                        <div class="nyp-photo__gallery-picture nyp-photo__gallery-picture--fill">
                                            <input type="file" placeholder="" th:id="|ny-field--edit-picture-${i}|"
                                                   id="ny-field--edit-picture-2" th:name="|edit-picture-${i}|"
                                                   name="edit-picture-2" class="ny-field ny-field--edit-picture">
                                            <img src="" th:data-photo-order="${i}" data-photo-order="2"
                                                 class="ny-picture ny-picture--edit realPhoto">
                                        </div>
                                    </div>
                                    <div class="nyp-photo__gallery-col"><span class="nyp-photo__gallery-label">Пример фото</span>
                                        <div class="nyp-photo__gallery-picture"><i
                                                class="ny-icon ny-icon--nyp-photo-logo"></i>
                                        </div>
                                    </div>
                                </li>
                            </th:block>


                            <!--<li class="nyp-photo__gallery-item nyp-photo__gallery-item--form" style="display: none;">
                                <div class="nyp-photo__gallery-title">Дополнительные фото</div>
                                <div class="nyp-photo__gallery-col nyp-photo__gallery-col--form">
                                    <div class="nyp-photo__gallery-addon">
                                        <i class="ny-icon ny-icon--gallery-addon--nyp-photo ny-icon--gallery-addon"></i>
                                        <span class="nyp-photo__gallery-addon__caption">Добавить<br>фото</span>
                                        <img src="" data-photo-order="5" class="ny-picture ny-picture--nyp-draft realPhoto">
                                    </div>
                                    <input type="file" placeholder="" id="ny-field--gallery-upload-1" name="gallery-upload-1" class="ny-field ny-field--gallery-upload">
                                </div>
                                <div class="nyp-photo__gallery-col nyp-photo__gallery-col--form">
                                    <div class="nyp-photo__gallery-addon">
                                        <i class="ny-icon ny-icon--gallery-addon--nyp-photo ny-icon--gallery-addon"></i>
                                        <span class="nyp-photo__gallery-addon__caption">Добавить<br>фото</span>
                                        <img src="" data-photo-order="6" class="ny-picture ny-picture--nyp-draft realPhoto">
                                    </div>
                                    <input type="file" placeholder="" id="ny-field--gallery-upload-2" name="gallery-upload-2" class="ny-field ny-field--gallery-upload">
                                </div>
                                <div class="nyp-photo__gallery-col nyp-photo__gallery-col--form">
                                    <div class="nyp-photo__gallery-addon">
                                        <i class="ny-icon ny-icon--gallery-addon--nyp-photo ny-icon--gallery-addon"></i>
                                        <span class="nyp-photo__gallery-addon__caption">Добавить<br>фото</span>
                                        <img src="" data-photo-order="7" class="ny-picture ny-picture--nyp-draft realPhoto">
                                    </div>
                                    <input type="file" placeholder="" id="ny-field--gallery-upload-3" name="gallery-upload-3" class="ny-field ny-field--gallery-upload">
                                </div>
                                <div class="nyp-photo__gallery-col nyp-photo__gallery-col--form">
                                    <div class="nyp-photo__gallery-addon">
                                        <i class="ny-icon ny-icon--gallery-addon--nyp-photo ny-icon--gallery-addon"></i>
                                        <span class="nyp-photo__gallery-addon__caption">Добавить<br>фото</span>
                                        <img src="" data-photo-order="8" class="ny-picture ny-picture--nyp-draft realPhoto">
                                    </div>
                                    <input type="file" placeholder="" id="ny-field--gallery-upload-4" name="gallery-upload-4" class="ny-field ny-field--gallery-upload">
                                </div>
                            </li>-->
                        </ul>
                    </div>
                </div>
            </aside>

            <!-- main content -->
            <main class="ny-content ny-content--product">
                <div class="ny-product">
                    <div class="ny-product__content">
                        <!--Main info-->
                        <div class="nyp-af mob_none_publication">
                            <div class="ny-product__title">Добавление товара</div>
                            <form class="nyp-af__form">
                                <div class="nyp-af__fields">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-af category-scroll">Категория</label>
                                    <ul class="ny-list ny-list--nyp-af show-category-popup">
                                        <li class="ny-list__item ny-list__item--nyp-af"><span
                                                class="ny-list__name ny-list__name--nyp-af"></span><i
                                                class="ny-icon ny-icon--nyp-af"></i>
                                        </li>
                                    </ul>
                                    <div class="nyp-af__cat" style="display: none;">
                                        <div class="nyp-af__cat-text" id="categoryId">Category</div>
                                        <button class="ny-button ny-button--nyp-af show-category-popup">Изменить
                                        </button>
                                    </div>
                                </div>
                                <div class="nyp-af__fields brand-field-div">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-af">Бренд</label>
                                    <ul class="ny-list ny-list--nyp-af--search ny-list--nyp-af">
                                        <li class="ny-list__item ny-list__item--nyp-af--search ny-list__item--nyp-af">
                                            <span class="ny-list__name ny-list__name--nyp-af--search ny-list__name--nyp-af"
                                                  id="brandId"></span>
                                            <i class="ny-icon ny-icon--nyp-af--search ny-icon--nyp-af"></i>
                                            <ul class="ny-list__sub ny-list__sub--nyp-af--search ny-list__sub--nyp-af"
                                                id="brandList">
                                                <div class="ny-swipe__search">
                                                    <input type="text" placeholder="Поиск по бренду"
                                                           id="ny-field--ny-search" name="ny-search"
                                                           class="ny-field ny-field--search--swipe ny-field--search">
                                                    <i class="ny-icon ny-icon--search--swipe ny-icon--search"></i>
                                                </div>
                                                <li data-item='1'
                                                    class="ny-list__item-sub ny-list__item-sub--nyp-af--search ny-list__item-sub--nyp-af"
                                                    style="display: none;"
                                                    id="brandExample">
                                                    <span class="ny-list__name-sub ny-list__name-sub--nyp-af--search ny-list__name-sub--nyp-af">Alaia</span>
                                                </li>

                                            </ul>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nyp-af__fields">
                                    <label for="ny-field--" class="ny-label ny-label--nyp-sf ny-label--visible">Тип
                                        размера</label>
                                    <ul class="ny-list ny-list--nyp-af">
                                        <li class="ny-list__item ny-list__item--nyp-sf">
                                            <span class="ny-list__name ny-list__name--nyp-sf" id="sizeTypeId"></span><i
                                                class="ny-icon ny-icon--nyp-sf"></i>
                                            <ul class="ny-list__sub ny-list__sub--nyp-sf" id="sizeTypeList">
                                            </ul>
                                        </li>
                                        <li data-item='1' class="ny-list__item-sub ny-list__item-sub--nyp-sf"
                                            id="sizeTypeExample" style="display:none;"><span
                                                class="ny-list__name-sub ny-list__name-sub--nyp-sf">Китайский</span>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nyp-af__fields" id="sizeAdder">
                                    <label for="ny-field--"
                                           class="ny-label ny-label--nyp-sf ny-label--visible">Размер</label>
                                    <ul class="ny-list ny-list--nyp-af selected-size-ul-wk" data-size-id="">
                                        <li class="ny-list__item ny-list__item--nyp-sf"><span
                                                class="ny-list__name ny-list__name--nyp-sf select-size-span-wk"></span><i
                                                class="ny-icon ny-icon--nyp-sf"></i>
                                            <ul class="ny-list__sub ny-list__sub--nyp-sf">
                                                <!--<li data-item='1' class="ny-list__item-sub ny-list__item-sub--nyp-sf"><span class="ny-list__name-sub ny-list__name-sub--nyp-sf">38</span></li>-->
                                            </ul>
                                        </li>
                                    </ul>
                                </div>

<!--                                <div class="nyp-af__fields">-->
<!--                                    <label for="ny-field&#45;&#45;undefined" class="ny-label ny-label&#45;&#45;nyp-af">Состояние</label>-->
<!--                                    <ul class="ny-list ny-list&#45;&#45;nyp-af">-->
<!--                                        <li class="ny-list__item ny-list__item&#45;&#45;nyp-af">-->
<!--                                            <span class="ny-list__name ny-list__name&#45;&#45;nyp-af" id="conditionId"></span>-->
<!--                                            <i class="ny-icon ny-icon&#45;&#45;nyp-af"></i>-->
<!--                                            <ul class="ny-list__sub ny-list__sub&#45;&#45;nyp-af">-->
<!--                                                <li data-item='1'-->
<!--                                                    class="ny-list__item-sub ny-list__item-sub&#45;&#45;nyp-af ny-list__item-sub&#45;&#45;top">-->
<!--                                                    <span class="ny-list__name-sub ny-list__name-sub&#45;&#45;nyp-af">Новое с биркой</span><span-->
<!--                                                        class="ny-list__name-desc ny-list__name-desc&#45;&#45;nyp-af">Товар с биркой. Вещь ни разу не надевали</span>-->
<!--                                                </li>-->
<!--                                                <li data-item='2'-->
<!--                                                    class="ny-list__item-sub ny-list__item-sub&#45;&#45;nyp-af ny-list__item-sub&#45;&#45;top">-->
<!--                                                    <span class="ny-list__name-sub ny-list__name-sub&#45;&#45;nyp-af">Отличное состояние</span><span-->
<!--                                                        class="ny-list__name-desc ny-list__name-desc&#45;&#45;nyp-af">Надевали пару раз, в отличном состоянии</span>-->
<!--                                                </li>-->
<!--                                                <li data-item='3'-->
<!--                                                    class="ny-list__item-sub ny-list__item-sub&#45;&#45;nyp-af ny-list__item-sub&#45;&#45;top">-->
<!--                                                    <span class="ny-list__name-sub ny-list__name-sub&#45;&#45;nyp-af">Хорошее состояние</span><span-->
<!--                                                        class="ny-list__name-desc ny-list__name-desc&#45;&#45;nyp-af">Незначительные следы носки, но без видимых дефектов</span>-->
<!--                                                </li>-->
<!--                                            </ul>-->
<!--                                        </li>-->
<!--                                    </ul>-->
<!--                                </div>-->

                                <div class="nyp-af__fields">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-af">Описание</label>
                                    <textarea name="area" maxlength="" class="nyp-af__area"
                                              id="descriptionId"></textarea>

                                    <!--Tooltip-->
                                    <div class="nyp-tooltip"><i class="ny-icon ny-icon--nyp-tooltip"></i>
                                        <div class="nyp-tooltip__box">
                                            <p>Чтобы помочь покупателю принять решение, сделайте подробное описание
                                                вашего товара. Укажите следующие характеристики:</p>
                                            <ul>
                                                <li><strong>Вид товара</strong> (например, коктейльное платье, зимняя
                                                    парка).
                                                </li>
                                                <li><strong>Состав материала</strong> (как указано на бирке), цвет.</li>
                                                <li><strong>Имеющиеся дефекты</strong> – подробно, не скрывая, так как
                                                    при экспертизе они все равно будут выявлены.
                                                </li>
                                            </ul>
                                            <p>По нашей статистике товар, имеющий подробное описание, продается гораздо
                                                быстрее.</p>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!--Sizes-->
                        <!--                        <div class="nyp-sf">-->
                        <!--                            <div class="ny-product__title">Размеры-->
                        <!--                                <button class="ny-button ny-button&#45;&#45;nyp-sf-add"><span>Добавить еще размер</span><i class="ny-icon ny-icon&#45;&#45;nyp-sf-add"></i>-->
                        <!--                                </button>-->
                        <!--                            </div>-->
                        <!--                            <div class="nyp-sf__type">-->
                        <!--                                <label for="ny-field&#45;&#45;" class="ny-label ny-label&#45;&#45;nyp-sf&#45;&#45;type ny-label&#45;&#45;nyp-sf ny-label&#45;&#45;visible">Тип размера</label>-->
                        <!--                                <ul class="ny-list ny-list&#45;&#45;nyp-sf">-->
                        <!--                                    <li class="ny-list__item ny-list__item&#45;&#45;nyp-sf">-->
                        <!--                                        <span class="ny-list__name ny-list__name&#45;&#45;nyp-sf" id="sizeTypeId"></span><i class="ny-icon ny-icon&#45;&#45;nyp-sf"></i>-->
                        <!--                                        <ul class="ny-list__sub ny-list__sub&#45;&#45;nyp-sf" id="sizeTypeList">-->
                        <!--                                        </ul>-->
                        <!--                                    </li>-->
                        <!--                                    <li data-item='1' class="ny-list__item-sub ny-list__item-sub&#45;&#45;nyp-sf" id="sizeTypeExample" style="display:none;"><span class="ny-list__name-sub ny-list__name-sub&#45;&#45;nyp-sf">Китайский</span></li>-->
                        <!--                                </ul>-->
                        <!--                            </div>-->
                        <!--                            <div class="nyp-sf__row" id="sizeAdder">-->
                        <!--                                <div class="nyp-sf__col">-->
                        <!--                                    <label for="ny-field&#45;&#45;" class="ny-label ny-label&#45;&#45;nyp-sf ny-label&#45;&#45;visible">Размер</label>-->
                        <!--                                    <ul class="ny-list ny-list&#45;&#45;nyp-sf">-->
                        <!--                                        <li class="ny-list__item ny-list__item&#45;&#45;nyp-sf"><span class="ny-list__name ny-list__name&#45;&#45;nyp-sf"></span><i class="ny-icon ny-icon&#45;&#45;nyp-sf"></i>-->
                        <!--                                            <ul class="ny-list__sub ny-list__sub&#45;&#45;nyp-sf">-->
                        <!--                                                &lt;!&ndash;<li data-item='1' class="ny-list__item-sub ny-list__item-sub&#45;&#45;nyp-sf"><span class="ny-list__name-sub ny-list__name-sub&#45;&#45;nyp-sf">38</span></li>&ndash;&gt;-->
                        <!--                                            </ul>-->
                        <!--                                        </li>-->
                        <!--                                    </ul>-->
                        <!--                                </div>-->
                        <!--                                <div class="nyp-sf__col">-->
                        <!--                                    <label for="ny-field&#45;&#45;" class="ny-label ny-label&#45;&#45;nyp-sf ny-label&#45;&#45;visible">Количество</label>-->
                        <!--                                    <div class="ny-counter ny-counter&#45;&#45;nyp-sf"><i class="ny-icon ny-icon&#45;&#45;ny-counter&#45;&#45;minus ny-icon&#45;&#45;ny-counter"></i>-->
                        <!--                                        <input type="number" placeholder="" value="1" readonly="true" id="ny-field&#45;&#45;" name="" class="ny-field ny-field&#45;&#45;ny-counter"><i class="ny-icon ny-icon&#45;&#45;ny-counter&#45;&#45;plus ny-icon&#45;&#45;ny-counter"></i>-->
                        <!--                                    </div>-->
                        <!--                                </div>-->
                        <!--                            </div>-->
                        <!--                        </div>-->


                        <div class="nyp-sf__row" id="sizeExample" style="display:none;">
                            <div class="nyp-sf__col">
                                <label for="ny-field--"
                                       class="ny-label ny-label--nyp-sf ny-label--visible">Размер</label>
                                <ul class="ny-list ny-list--nyp-sf">
                                    <li class="ny-list__item ny-list__item--nyp-sf"><span
                                            class="ny-list__name ny-list__name--nyp-sf">36</span><i
                                            class="ny-icon ny-icon--nyp-sf"></i>
                                        <ul class="ny-list__sub ny-list__sub--nyp-sf">
                                            <li data-item='1' class="ny-list__item-sub ny-list__item-sub--nyp-sf"><span
                                                    class="ny-list__name-sub ny-list__name-sub--nyp-sf">38</span></li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                            <div class="nyp-sf__col">
                                <label for="ny-field--"
                                       class="ny-label ny-label--nyp-sf ny-label--visible">Количество</label>
                                <div class="ny-counter ny-counter--nyp-sf"><i
                                        class="ny-icon ny-icon--ny-counter--minus ny-icon--ny-counter"></i>
                                    <input type="number" placeholder="" value="2" readonly="true" id="ny-field--"
                                           name="" class="ny-field ny-field--ny-counter"><i
                                            class="ny-icon ny-icon--ny-counter--plus ny-icon--ny-counter"></i>
                                </div>
                            </div>
                            <button class="ny-button ny-button--nyp-sf-close" title="Удалить"></button>
                        </div>

                        <li data-item='1' class="ny-list__item-sub ny-list__item-sub--nyp-sf" id="sizeVariantExample"
                            style="display:none;"><span class="ny-list__name-sub ny-list__name-sub--nyp-sf">38</span>
                        </li>

                        <!--mobile information-->
                        <div class="mob_show_publication publication_information">
                            <br>
                            <div class="title_page_pub">Продажа товара</div>
                            <ul class='list_select_tab'>
                                <li>
                                    <div class="ny-product__title">Информация</div>
                                    <div class='block_toggle'>
                                        <ul id="attribute-list-ul">
                                            <li class="no_selected">
                                                <a class="item-category-a" data-item-category-id="1" href='#'>
                                                    <div class='name_filter'>
                                                        <em>Категория</em>

                                                    </div>
                                                    <div class='right_selected first_letter_remove'></div>
                                                    <div class='arrow_icon'>
                                                        <svg width="12" height="7" viewBox="0 0 12 7" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
                                                        </svg>

                                                    </div>
                                                </a>
                                            </li>
                                            <li class="no_selected">
                                                <a href='#'>
                                                    <div class='name_filter'>
                                                        <em>Бренд</em>

                                                    </div>
                                                    <div class='right_selected brand-selected-text-div'></div>
                                                    <div class='arrow_icon'>
                                                        <svg width="12" height="7" viewBox="0 0 12 7" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
                                                        </svg>

                                                    </div>
                                                </a>
                                                <div class='all_block_two'>
                                                    <div class='prev_filter'></div>
                                                    <div class='title_popup'>Выбор бренда</div>
                                                    <div class='block ' id="brandListMobile">
                                                        <div class="ny-swipe__search">
                                                            <input type="text" placeholder="Поиск по бренду"
                                                                   id="ny-field--ny-search_mob" name="ny-search"
                                                                   class="ny-field ny-field--search--swipe ny-field--search">
                                                            <i class="ny-icon ny-icon--search--swipe ny-icon--search"></i>
                                                        </div>
                                                        <div class='all_brend_blocks all-brand-block-div'>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            <li style="display: none" class="attr-add-flag-li"></li>
                                            <li class="no_selected show-after-change-category-li size-type-attr-li" style="display: none">
                                                <a href='#'>
                                                    <div class='name_filter'>
                                                        <em>Тип размера</em>
                                                    </div>
                                                    <div class='right_selected'></div>
                                                    <div class='arrow_icon'>
                                                        <svg width="12" height="7" viewBox="0 0 12 7" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
                                                        </svg>

                                                    </div>
                                                </a>
                                                <div class='all_block_two'>
                                                    <div class='prev_filter'></div>
                                                    <div class='title_popup'>Выбор типа размера</div>
                                                    <div class='block'>
                                                        <ul class='new_check size-type-phys-ul'>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="no_selected show-after-change-size-type-li size-attr-li" style="display: none">
                                                <a href='#'>
                                                    <div class='name_filter'>
                                                        <em>Размер</em>
                                                    </div>
                                                    <div class='right_selected'></div>
                                                    <div class='arrow_icon'>
                                                        <svg width="12" height="7" viewBox="0 0 12 7" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>
                                                        </svg>

                                                    </div>
                                                </a>
                                                <div class='all_block_two'>
                                                    <div class='prev_filter'></div>
                                                    <div class='title_popup'>Выбор размера</div>
                                                    <div class='block'>
                                                        <ul class='new_check size-list-phys-ul'>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </li>
<!--                                            <li class="no_selected show-after-change-category-li condition-attr-li" style="display: none">-->
<!--                                                <a href='#'>-->
<!--                                                    <div class='name_filter'>-->
<!--                                                        <em>Состояние</em>-->
<!--                                                    </div>-->
<!--                                                    <div class='right_selected' id="conditionMobileId"></div>-->
<!--                                                    <div class='arrow_icon'>-->
<!--                                                        <svg width="12" height="7" viewBox="0 0 12 7" fill="none"-->
<!--                                                             xmlns="http://www.w3.org/2000/svg">-->
<!--                                                            <path d="M11 6L6 1L1 6" stroke="#4F4F4F"/>-->
<!--                                                        </svg>-->

<!--                                                    </div>-->
<!--                                                </a>-->
<!--                                                <div class='all_block_two'>-->
<!--                                                    <div class='prev_filter'></div>-->
<!--                                                    <div class='title_popup'>Выбор состояния</div>-->
<!--                                                    <div class='block'>-->
<!--                                                        <ul class='new_check'>-->
<!--                                                            <li>-->
<!--                                                                <div class='check_two'>-->
<!--                                                                    <input id='check_info_mob_7_1' type='radio'-->
<!--                                                                           name="radio_mob_conditions">-->
<!--                                                                    <label for='check_info_mob_7_1'>-->
<!--                                                                        <div class='name-block-popup'>Новое с биркой-->
<!--                                                                        </div>-->
<!--                                                                        <p>Абсолютно новая вещь с полным комплектом-->
<!--                                                                            документов и фирменной упаковкой.</p>-->
<!--                                                                    </label>-->
<!--                                                                </div>-->
<!--                                                            </li>-->
<!--                                                            <li>-->
<!--                                                                <div class='check_two'>-->
<!--                                                                    <input id='check_info_mob_7_2' type='radio'-->
<!--                                                                           name="radio_mob_conditions">-->
<!--                                                                    <label for='check_info_mob_7_2'>-->
<!--                                                                        <div class='name-block-popup'>Отличное состояние-->
<!--                                                                        </div>-->
<!--                                                                        <p>Вещь в прекрасном состоянии, без явных следов-->
<!--                                                                            носки и дефектов. К ней может быть приложен-->
<!--                                                                            не весь комлект документов, а также-->
<!--                                                                            отсутствовать фирменная упаковка.</p>-->
<!--                                                                    </label>-->
<!--                                                                </div>-->
<!--                                                            </li>-->
<!--                                                            <li>-->
<!--                                                                <div class='check_two'>-->
<!--                                                                    <input id='check_info_mob_7_3' type='radio'-->
<!--                                                                           name="radio_mob_conditions">-->
<!--                                                                    <label for='check_info_mob_7_3'>-->
<!--                                                                        <div class='name-block-popup'>Хорошее состояние-->
<!--                                                                        </div>-->
<!--                                                                        <p>Вещь бывшая в употреблении, но без видимых-->
<!--                                                                            дефектов - сколов, царапин, дырок и-->
<!--                                                                            зацепок</p>-->
<!--                                                                    </label>-->
<!--                                                                </div>-->
<!--                                                            </li>-->
<!--                                                        </ul>-->
<!--                                                    </div>-->
<!--                                                </div>-->
<!--                                            </li>-->
                                        </ul>
                                    </div>
                                </li>
                                <li id="sizes-logic-block-li" style="display:none">
                                    <div class="ny-product__title">Размеры</div>
                                    <div class="block_toggle">
                                        <ul class="list_sizes size-logic-block-ul" id="size-type-root-ul" data-num="1">
                                            <li>
                                                <a href="#">
                                                    <div class="name_filter">
                                                        <em>Тип размера</em>
                                                    </div>
                                                    <div class="right_selected"></div>
                                                    <div class="arrow_icon">
                                                        <svg width="12" height="7" viewBox="0 0 12 7" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M11 6L6 1L1 6" stroke="#4F4F4F"></path>
                                                        </svg>
                                                    </div>
                                                </a>
                                                <div class="all_block_two">
                                                    <div class="prev_filter"></div>
                                                    <div class="title_popup">Выбор размера</div>
                                                    <div class="block">
                                                        <ul class="new_check" id="size-type-list-ul">
                                                            <li>

                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </li>

                                        </ul>

                                        <div class="add_size_new add-new-size-block-div">
                                            <a>Добавить еще размер</a>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="ny-product__title">Фото</div>
                                    <div class='block_toggle root-photo-block-div'>
                                        <div class="photo_empty" style="display: block">
                                            <div class="flex_block">
                                                <div class="left_add_photo">
                                                    <label class='attach_label_pop'>
                                                    </label>
                                                </div>
                                                <div class="right_info_text">
                                                    Чтобы загрузить фотографии, выберите категорию товара
                                                </div>
                                            </div>
                                        </div>
                                        <div class="photo_added_block photo_block_active" style="display: none">
                                            <div class='photos_edited'>
                                                <div class='block'>
                                                    <div class='name_block'>1-ая фотография*</div>
                                                    <div class='photos_two_flex'>
                                                        <div class='photos_two_block'>
                                                            <div class='block added_photo'>
                                                                <!--                                                                   attach_label_pop-->
                                                                <label class=''>
                                                                    <input class='attach_input_pop photo-change-mobile-input photo-upload-mobile-input'
                                                                           type='file' style="display:none"  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                                    <div class='name_icon'>
                                                                        <svg width="42" height="33" viewBox="0 0 42 33"
                                                                             fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                                  fill="#E6E6E6"/>
                                                                            <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                            <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                        </svg>

                                                                        Добавить фото
                                                                    </div>
                                                                </label>
                                                            </div>
                                                            <div class='img ny-picture' style="display:none">
                                                                <img th:src='@{../pictures/photo_pop_1.png}'>
                                                            </div>
                                                        </div>
                                                        <div class='photos_two_block photo-sample-mobile-root-div'>
                                                            <div class='img'>
                                                                <img class="photo-sample-mobile-img"
                                                                     style="display:none">
                                                                <i class="ny-icon ny-icon--nyp-photo-logo photo-sample-mobile-logo-i"
                                                                   style="display:none"></i>
                                                            </div>
                                                            <span>Пример фото</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class='block'>
                                                    <div class='name_block'>2-ая фотография*</div>
                                                    <div class='photos_two_flex'>
                                                        <div class='photos_two_block'>
                                                            <div class='block added_photo'>
                                                                <!--                                                                   attach_label_pop-->
                                                                <label class=''>
                                                                    <input class='attach_input_pop photo-change-mobile-input photo-upload-mobile-input'
                                                                           type='file' style="display:none"  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                                    <div class='name_icon'>
                                                                        <svg width="42" height="33" viewBox="0 0 42 33"
                                                                             fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                                  fill="#E6E6E6"/>
                                                                            <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                            <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                        </svg>

                                                                        Добавить фото
                                                                    </div>
                                                                </label>
                                                            </div>
                                                            <div class='img ny-picture' style="display:none">
                                                                <img th:src='@{../pictures/photo_pop_1.png}'>
                                                            </div>
                                                        </div>
                                                        <div class='photos_two_block photo-sample-mobile-root-div'>
                                                            <div class='img'>
                                                                <img class="photo-sample-mobile-img"
                                                                     style="display:none">
                                                                <i class="ny-icon ny-icon--nyp-photo-logo photo-sample-mobile-logo-i"
                                                                   style="display:none"></i>
                                                            </div>
                                                            <span>Пример фото</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class='block'>
                                                    <div class='name_block'>3-ая фотография*</div>
                                                    <div class='photos_two_flex'>
                                                        <div class='photos_two_block'>
                                                            <div class='block added_photo'>
                                                                <!--                                                                   attach_label_pop-->
                                                                <label class=''>
                                                                    <input class='attach_input_pop photo-change-mobile-input photo-upload-mobile-input'
                                                                           type='file' style="display:none"  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                                    <div class='name_icon'>
                                                                        <svg width="42" height="33" viewBox="0 0 42 33"
                                                                             fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                                  fill="#E6E6E6"/>
                                                                            <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                            <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                        </svg>

                                                                        Добавить фото
                                                                    </div>
                                                                </label>
                                                            </div>
                                                            <div class='img ny-picture' style="display:none">
                                                                <img th:src='@{../pictures/photo_pop_1.png}'>
                                                            </div>
                                                        </div>
                                                        <div class='photos_two_block photo-sample-mobile-root-div'>
                                                            <div class='img'>
                                                                <img class="photo-sample-mobile-img"
                                                                     style="display:none">
                                                                <i class="ny-icon ny-icon--nyp-photo-logo photo-sample-mobile-logo-i"
                                                                   style="display:none"></i>
                                                            </div>
                                                            <span>Пример фото</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class='block'>
                                                    <div class='name_block'>4-ая фотография*</div>
                                                    <div class='photos_two_flex'>
                                                        <div class='photos_two_block'>
                                                            <div class='block added_photo'>
                                                                <!--                                                                   attach_label_pop-->
                                                                <label class=''>
                                                                    <input class='attach_input_pop photo-change-mobile-input photo-upload-mobile-input'
                                                                           type='file' style="display:none"  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                                    <div class='name_icon'>
                                                                        <svg width="42" height="33" viewBox="0 0 42 33"
                                                                             fill="none"
                                                                             xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                                  fill="#E6E6E6"/>
                                                                            <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                            <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                                  stroke="#E6E6E6"
                                                                                  stroke-width="1.5788"/>
                                                                        </svg>

                                                                        Добавить фото
                                                                    </div>
                                                                </label>
                                                            </div>
                                                            <div class='img ny-picture' style="display:none">
                                                                <img th:src='@{../pictures/photo_pop_1.png}'>
                                                            </div>
                                                        </div>
                                                        <div class='photos_two_block photo-sample-mobile-root-div'>
                                                            <div class='img'>
                                                                <img class="photo-sample-mobile-img"
                                                                     style="display:none">
                                                                <i class="ny-icon ny-icon--nyp-photo-logo photo-sample-mobile-logo-i"
                                                                   style="display:none"></i>
                                                            </div>
                                                            <span>Пример фото</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='photos_add'>
                                                <div class='name_block'>Дополнительные фотографии</div>
                                                <div class='photos_add_flex'>
                                                    <div class='block'>
                                                        <div class='img' style="display:none">
                                                            <img th:src='@{../pictures/photo_pop_1.png}'>
                                                        </div>
                                                        <label class='attach_label_pop'>
                                                            <input class='attach_input_pop photo-added-change-mobile-input photo-upload-mobile-input'
                                                                   type='file'  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                            <div class='name_icon'>
                                                                <svg width="42" height="33" viewBox="0 0 42 33"
                                                                     fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                          fill="#E6E6E6"/>
                                                                    <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                    <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                </svg>

                                                                Добавить фото
                                                            </div>
                                                        </label>
                                                    </div>
                                                    <div class='block'>
                                                        <div class='img' style="display:none">
                                                            <img th:src='@{../pictures/photo_pop_1.png}'>
                                                        </div>
                                                        <label class='attach_label_pop'>
                                                            <input class='attach_input_pop photo-added-change-mobile-input photo-upload-mobile-input'
                                                                   type='file'  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                            <div class='name_icon'>
                                                                <svg width="42" height="33" viewBox="0 0 42 33"
                                                                     fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                          fill="#E6E6E6"/>
                                                                    <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                    <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                </svg>

                                                                Добавить фото
                                                            </div>
                                                        </label>
                                                    </div>
                                                    <div class='block'>
                                                        <div class='img' style="display:none">
                                                            <img th:src='@{../pictures/photo_pop_1.png}'>
                                                        </div>
                                                        <label class='attach_label_pop'>
                                                            <input class='attach_input_pop photo-added-change-mobile-input photo-upload-mobile-input'
                                                                   type='file'  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                            <div class='name_icon'>
                                                                <svg width="42" height="33" viewBox="0 0 42 33"
                                                                     fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                          fill="#E6E6E6"/>
                                                                    <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                    <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                </svg>

                                                                Добавить фото
                                                            </div>
                                                        </label>
                                                    </div>
                                                    <div class='block'>
                                                        <div class='img' style="display:none">
                                                            <img th:src='@{../pictures/photo_pop_1.png}'>
                                                        </div>
                                                        <label class='attach_label_pop'>
                                                            <input class='attach_input_pop photo-added-change-mobile-input photo-upload-mobile-input'
                                                                   type='file'  accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                                            <div class='name_icon'>
                                                                <svg width="42" height="33" viewBox="0 0 42 33"
                                                                     fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M38.9572 4.67084H28.0987L26.9897 1.53958C26.6635 0.618654 25.8086 0 24.8623 0H17.1369C16.1905 0 15.3357 0.618737 15.0095 1.53958L13.9004 4.67076H10.2108V3.44364C10.2108 2.45424 9.43437 1.64922 8.47978 1.64922H5.92474C4.97031 1.64922 4.19373 2.45424 4.19373 3.44364V4.67084H3.04196C1.57354 4.67084 0.378906 5.90924 0.378906 7.43145V30.2394C0.378906 31.7616 1.57354 33 3.04196 33H38.9572C40.4257 33 41.6203 31.7616 41.6203 30.2394V7.43145C41.6203 5.90924 40.4257 4.67084 38.9572 4.67084ZM16.676 2.17393C16.7467 1.97445 16.9318 1.84043 17.1368 1.84043H24.8622C25.0672 1.84043 25.2524 1.97445 25.323 2.17393L26.2074 4.67084H15.7917L16.676 2.17393ZM5.88044 3.44364C5.88044 3.41834 5.90034 3.39763 5.92482 3.39763H8.47986C8.50434 3.39763 8.52424 3.41834 8.52424 3.44364V4.67084H5.88044V3.44364ZM2.1543 11.6347H4.78368V26.036H2.1543V11.6347ZM39.8448 30.2394C39.8449 30.7468 39.4467 31.1596 38.9572 31.1596H3.04196C2.55246 31.1596 2.1543 30.7467 2.1543 30.2394V27.8764H5.67134C6.16156 27.8764 6.55899 27.4643 6.55899 26.9562V10.7145C6.55899 10.2064 6.16156 9.79434 5.67134 9.79434H2.1543V7.43136C2.1543 6.92393 2.55254 6.51119 3.04196 6.51119H27.4723C27.4747 6.51119 27.4768 6.51144 27.4791 6.51144C27.481 6.51144 27.4831 6.51119 27.485 6.51119H38.9572C39.4467 6.51119 39.8449 6.92401 39.8449 7.43136V9.79434H36.3279C35.8376 9.79434 35.4402 10.2064 35.4402 10.7145V26.9562C35.4402 27.4643 35.8376 27.8764 36.3279 27.8764H39.8449V30.2394H39.8448ZM39.8449 26.036H37.2155V11.6347H39.8449V26.036Z"
                                                                          fill="#E6E6E6"/>
                                                                    <path d="M28.141 18.8571C28.141 22.7536 24.9506 25.9249 20.9994 25.9249C17.0482 25.9249 13.8578 22.7536 13.8578 18.8571C13.8578 14.9607 17.0482 11.7894 20.9994 11.7894C24.9506 11.7894 28.141 14.9607 28.141 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                    <path d="M23.3826 18.8571C23.3826 20.1499 22.3226 21.2105 20.9996 21.2105C19.6765 21.2105 18.6165 20.1499 18.6165 18.8571C18.6165 17.5643 19.6765 16.5036 20.9996 16.5036C22.3226 16.5036 23.3826 17.5643 23.3826 18.8571Z"
                                                                          stroke="#E6E6E6" stroke-width="1.5788"/>
                                                                </svg>

                                                                Добавить фото
                                                            </div>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li>
                                    <div class="ny-product__title">Описание</div>
                                    <div class='block_toggle'>
                                        <div class='form_text'>

                                            <div class='inp_block'>
                                                <textarea class='block_textarea' id="descriptionMobileId"
                                                          placeholder='' style=" -webkit-appearance: none;"></textarea>
                                                <div class='remained_symbol'>
                                                    Осталось
                                                    <span>0</span>
                                                    символов
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <!--Condition-->
                        <div class="nyp-pf">
                            <div class="condition_blocks" style="padding: 0">
                                <div class="ny-product__title">Состояние</div>
                                <div class="radio_chacked_blocks">
                                    <ul>
                                        <li>
                                            <input type="radio" checked name="condition_radio" id="condition_radio_d_1" value="1">
                                            <label for="condition_radio_d_1">
                                                <div class="name-block">Новое - с биркой</div>
                                                <p>Абсолютно новая вещь, которая ни разу не была в носке. Все фирменные бирки и упаковка прилагаются.</p>
                                            </label>
                                        </li>
                                        <li class="excellent_condition_block">
                                            <input type="radio" name="condition_radio" id="condition_radio_d_2" value="2">
                                            <label for="condition_radio_d_2">
                                                <div class="name-block">Отличное состояние</div>
                                                <p>Вещь в прекрасном состоянии, без явных внешних следов носки и дефектов. К ней может быть приложен не весь комлект документов, а также отсутствовать фирменная упаковка. Допускаются небольшие внутренние дефекты.</p>
                                                <div class="added_images">

                                                </div>
                                                <a href="#" class="click_add_img">Добавить фото дефекта</a>
                                            </label>
                                        </li>
                                        <li class="good_condition_block">
                                            <input type="radio" name="condition_radio" id="condition_radio_d_3"  value="3">
                                            <label for="condition_radio_d_3">
                                                <div class="name-block">Хорошее состояние</div>
                                                <p>Вещь бывшая в употреблении, за которой хорошо ухаживали. Возможны небольшие дефекты в виде сколов, царапинок, дырочек и зацепок.</p>
                                                <div class="added_images">
                                                    <div></div>
                                                </div>
                                                <a href="#" class="click_add_img">Добавить фото дефекта</a>
                                            </label>
                                        </li>
                                    </ul>
                                </div>

                            </div>
                        </div>

                        <!--Price area-->
                        <div class="nyp-pf">

                            <!--Calculator-->
                            <div class="nyp-calc">
                                <a href="#" title="Закрыть" class="nyp-calc__close"></a>
                                <div class="nyp-calc__fields">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-calc">Сумма
                                        приобритения</label>
                                    <div class="nyp-calc__field">
                                        <input type="text" placeholder="" value="23 445" id="ny-field--" name=""
                                               class="ny-field ny-field--nyp-calc"><span
                                            class="nyp-calc__currency">₽</span>
                                    </div>
                                </div>
                                <div class="nyp-calc__fields">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-calc">Год
                                        приобритения</label>
                                    <div class="nyp-calc__field">
                                        <ul class="ny-list ny-list--nyp-calc">
                                            <li class="ny-list__item ny-list__item--nyp-calc"><span
                                                    class="ny-list__name ny-list__name--nyp-calc">2018</span><i
                                                    class="ny-icon ny-icon--nyp-calc"></i>
                                                <ul class="ny-list__sub ny-list__sub--nyp-calc">
                                                    <li data-item='1'
                                                        class="ny-list__item-sub ny-list__item-sub--nyp-calc"><span
                                                            class="ny-list__name-sub ny-list__name-sub--nyp-calc">2019</span>
                                                    </li>
                                                    <li data-item='2'
                                                        class="ny-list__item-sub ny-list__item-sub--nyp-calc"><span
                                                            class="ny-list__name-sub ny-list__name-sub--nyp-calc">2020</span>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="nyp-calc__fields">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-calc">Состояние
                                        товара</label>
                                    <div class="nyp-calc__field">
                                        <ul class="ny-list ny-list--nyp-calc">
                                            <li class="ny-list__item ny-list__item--nyp-calc"><span
                                                    class="ny-list__name ny-list__name--nyp-calc">Хорошее состояние</span><i
                                                    class="ny-icon ny-icon--nyp-calc"></i>
                                                <ul class="ny-list__sub ny-list__sub--nyp-calc">
                                                    <li data-item='1'
                                                        class="ny-list__item-sub ny-list__item-sub--nyp-calc"><span
                                                            class="ny-list__name-sub ny-list__name-sub--nyp-calc">Хорошее состояние 2</span>
                                                    </li>
                                                    <li data-item='2'
                                                        class="ny-list__item-sub ny-list__item-sub--nyp-calc"><span
                                                            class="ny-list__name-sub ny-list__name-sub--nyp-calc">Хорошее состояние 3</span>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="nyp-calc__info">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-calc">Расчетная
                                        цена</label>
                                    <div class="nyp-calc__field">
                                        <div class="nyp-calc__price">900 ₽</div>
                                        <a href="#" class="ny-link ny-link--nyp-calc">Применить</a>
                                    </div>
                                </div>
                            </div>

                            <div class="ny-product__title">Цена<span class="nyp-pf__calc-label">Калькулятор</span>
                            </div>
                            <div class="ny-product__caption">Чем ниже цена, тем больше шансов продать товар</div>


                            <!--Price-->
                            <div class="nyp-pf__fields">
                                <div class="nyp-pf__field">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-pf">Цена на
                                        сайте</label>
                                    <input type="text" id="ny-field--priceOnSite" inputmode="numeric" name=""
                                           class="ny-field ny-field--nyp-pf">

                                </div>
                                <div class="nyp-pf__field nyp-pf__field--hidden active">
                                    <label for="ny-field--undefined" class="ny-label ny-label--nyp-pf">Ваша
                                        прибыль</label>
                                    <input type="text" placeholder="" inputmode="numeric" id="ny-field--priceToHand" name=""
                                           class="ny-field ny-field--nyp-pf--hidden ny-field--nyp-pf">
                                </div>
                            </div>
                            <div class="text__price_add_pop">
                                <p>
                                    Комиссия Oskelly варьируется от 16 до 25%.
                                    <br >
                                    Еe размер зависит от заявленной стоимости товара.
                                    <a href = "#" class="open_popup_private_seller">Подробнее</a >
                                </p>
                            </div>

                            <!--Tooltip-->
                            <div class="nyp-tooltip"><i class="ny-icon ny-icon--nyp-tooltip"></i>
                                <div class="nyp-tooltip__box">
                                    <p>Стоимость товара на ресейл-платформе зависит от нескольких факторов: популярности бренда,
                                        даты выпуска изделия, актуальности лота, его первоначальной цены в ритейле, состояния товара.
                                        При выставлении цены, постарайтесь учесть каждый из этих параметров.
                                        Для этого изучите стоимость на похожие издедия у нас на платформе и оцените конкурентоспособность вашего предложения.
                                        Будьте объективны, чтобы выбрать оптимальную стоимость и как можно быстрее привлечь покупателя!
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!--Address-->
                        <div class="nyp-cf">
                            <div class="ny-product__title">Продавец
                                <button class="ny-button ny-button--nyp-cf-add show-popup-address"
                                        id="add-new-address-button" style="display: none;">
                                    <span>Добавить новый адрес</span>
                                    <i class="ny-icon ny-icon--nyp-cf-add"></i>
                                </button>
                            </div>

                            <!--Multiple address-->
                            <div class="nyp-cf__output" id="multipleAddressList" style="display: none;">
                                <div class="nyp-cf__row nyp-cf__row--output" id="multipleAddressExample"
                                     style="display: none;">
                                    <div class="nyp-cf__field">
                                        <input type="radio" placeholder="" value="" id="ny-field--radio-1"
                                               name="radio-1" class="ny-field ny-field--nyp-cf-radio">
                                        <div class="nyp-cf__field-checkmark">
                                            <div class="nyp-cf__field-info">
                                                <div class="nyp-cf__field-info__name">Иванов Сергей Васильевич</div>
                                                <div class="nyp-cf__field-info__phone">+78989483943</div>
                                                <div class="nyp-cf__field-info__address">Москва, ул. Длинноименная им.
                                                    Ленина, д. 1026, кв 50
                                                </div>
                                            </div>
                                            <button class="ny-button ny-button--nyp-cf-edit" style="display:none;">
                                                <i class="ny-icon ny-icon--nyp-cf-edit"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!--Single address-->
                            <div class="nyp-cf__input" id="singleAddress" style="display: none;">
                                <div class="nyp-cf__row">
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields">
                                            <label for="ny-field--firstName"
                                                   class="ny-label ny-label--nyp-cf">Имя</label>
                                            <input type="text" id="ny-field--firstName" name=""
                                                   class="ny-field ny-field--nyp-cf">

                                        </div>
                                    </div>
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields">
                                            <label for="ny-field--lastName"
                                                   class="ny-label ny-label--nyp-cf">Фамилия</label>
                                            <input type="text" id="ny-field--lastName" name=""
                                                   class="ny-field ny-field--nyp-cf">

                                        </div>
                                    </div>
                                </div>
                                <div class="nyp-cf__row">
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields">
                                            <label for="ny-field--patronymicName" class="ny-label ny-label--nyp-cf">Отчество</label>
                                            <input type="text" id="ny-field--patronymicName" name=""
                                                   class="ny-field ny-field--nyp-cf">

                                        </div>
                                    </div>
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields">
                                            <label for="ny-field--phone" class="ny-label ny-label--nyp-cf">Номер
                                                телефона</label>
                                            <input type="text" id="ny-field--phone" name=""
                                                   class="ny-field ny-field--phone ny-field--nyp-cf phone_maska">
                                            <!-- <span class="ny-validate">В поле допустимы только цифры</span>-->
                                        </div>
                                    </div>
                                </div>
                                <div class="nyp-cf__row">
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields">
                                            <label for="ny-field--region"
                                                   class="ny-label ny-label--nyp-cf">Регион</label>
                                            <input type="text" id="ny-field--region" name="Московская область"
                                                   class="ny-field ny-field--nyp-cf">

                                        </div>
                                    </div>
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields non-unfocus-city-div">
                                            <label for="ny-field--city" class="ny-label ny-label--nyp-cf">Город</label>
                                            <input type="text" id="ny-field--city" name="Москва"
                                                   class="ny-field ny-field--nyp-cf">

                                            <div class="show_list_city city-popup"  style="top:auto;display: none;">
                                                <div class="empty_block--city empty-search-city hide_address">Вашего города нет в базе данных.
                                                    Мы перезвоним вам для уточнения условий доставки.
                                                </div>
                                                <div class="list_all_block list-all-city">
                                                    <div class="enter_address_tooltip">
                                                        Подсказка появится после ввода 3-х символов
                                                    </div>
                                                    <div class="list_block list_block_latest list-city-block">
                                                        <div class="name_block-list name-list-city-block">
                                                            Недавний поиск
                                                        </div>
                                                        <ul>

                                                        </ul>
                                                    </div>
                                                    <div class="list_block list_block_popular list-city-block">
                                                        <div class="name_block-list name-list-city-block">
                                                            Популярные города
                                                        </div>
                                                        <ul>

                                                        </ul>
                                                    </div>
                                                    <div class="list_block list_block_search list-city-block" style="display: none">
                                                        <ul>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="nyp-cf__row">
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__fields">
                                            <label for="ny-field--street"
                                                   class="ny-label ny-label--nyp-cf">Адрес</label>
                                            <input type="text" id="ny-field--street" name=""
                                                   class="ny-field ny-field--nyp-cf">

                                        </div>
                                    </div>
                                    <div class="nyp-cf__col">
                                        <div class="nyp-cf__half">
                                            <div class="nyp-cf__fields">
                                                <label for="ny-field--building"
                                                       class="ny-label ny-label--nyp-cf">Дом</label>
                                                <input type="text" id="ny-field--building" name=""
                                                       class="ny-field ny-field--nyp-cf">

                                            </div>
                                        </div>
                                        <div class="nyp-cf__half">
                                            <div class="nyp-cf__fields">
                                                <label for="ny-field--flat"
                                                       class="ny-label ny-label--nyp-cf">Квартира</label>
                                                <input type="text" id="ny-field--flat" name=""
                                                       class="ny-field ny-field--nyp-cf">

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--Additional info-->
                        <div class="nyp-of">
                            <div class="ny-product__title">Дополнительно<span style="display: inline-block;font-size: 16px;color: #bdbdbd;position: relative;top: -2px;font-weight: 400;margin-left: 5px;">(необязательно)</span>
                            </div>
                            <div class="ny-product__caption">Чем больше информации о товаре тем проще клиенту<br>решить
                                покупать товар или нет
                            </div>
                            <div class="nyp-of__box nyp-of__box--margin-top-24 nyp-of__company">
                                <div class="nyp-of__col nyp-of__col--padding-top-16">
                                    <div class="nyp-of__group">
                                        <div class="nyp-of__fields">
                                            <label for="ny-field--serialNumber" class="ny-label ny-label--nyp-of">Серийный
                                                номер</label>
                                            <input type="text" id="ny-field--serialNumber" name=""
                                                   class="ny-field ny-field--nyp-of">
                                        </div>
                                        <div class="nyp-of__fields">
                                            <label for="ny-field--model"
                                                   class="ny-label ny-label--nyp-of">Модель</label>
                                            <input type="text" id="ny-field--model" name=""
                                                   class="ny-field ny-field--nyp-of">
                                        </div>
                                        <div class="nyp-of__fields">
                                            <label for="ny-field--vendorCode"
                                                   class="ny-label ny-label--nyp-of">Артикул производителя</label>
                                            <input type="text" id="ny-field--vendorCode" name=""
                                                   class="ny-field ny-field--nyp-of">
                                        </div>
                                    </div>
                                </div>
                                <div class="nyp-of__col">
                                    <div class="nyp-of__group">
                                        <div class="nyp-of__fields">
                                            <label for="ny-field--rrp" class="ny-label ny-label--nyp-pf">РРЦ</label>
                                            <input type="text" inputmode="numeric" id="ny-field--rrp" name="" class="ny-field ny-field--nyp-pf">
                                        </div>
                                        <div class="nyp-of__options vintage-field-div">
                                            <div class="nyp-of__info">
                                                <div class="nyp-of__label">Винтаж</div>
                                                <div class="nyp-of__caption">Если товару более 15 лет</div>
                                            </div>
                                            <div class="nyp-of__trigger">
                                                <input type="checkbox" placeholder="" id="ny-field--trigger-vintage"
                                                       name="trigger-vintage"
                                                       class="ny-field ny-field--edit-trigger--nyp-of ny-field--edit-trigger">
                                            </div>
                                        </div>
                                        <!--                                        <div class="nyp-of__options" id="boxAttribute" >-->
                                        <!--                                            <div class="nyp-of__info">-->
                                        <!--                                                <div class="nyp-of__label">Наличие коробки</div>-->
                                        <!--                                            </div>-->
                                        <!--                                            <div class="nyp-of__trigger">-->
                                        <!--                                                <input type="checkbox" placeholder="" id="ny-field&#45;&#45;trigger-box" name="trigger-box" class="ny-field ny-field&#45;&#45;edit-trigger&#45;&#45;nyp-of ny-field&#45;&#45;edit-trigger field-attr-trigger">-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <!--                                        <div class="nyp-of__options" id="dusterAttribute" >-->
                                        <!--                                            <div class="nyp-of__info">-->
                                        <!--                                                <div class="nyp-of__label">Наличие пыльника</div>-->
                                        <!--                                            </div>-->
                                        <!--                                            <div class="nyp-of__trigger">-->
                                        <!--                                                <input type="checkbox" placeholder="" id="ny-field&#45;&#45;trigger-duster" name="trigger-duster" class="ny-field ny-field&#45;&#45;edit-trigger&#45;&#45;nyp-of ny-field&#45;&#45;edit-trigger field-attr-trigger">-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <!--                                        <div class="nyp-of__options" id="docAttribute" >-->
                                        <!--                                            <div class="nyp-of__info">-->
                                        <!--                                                <div class="nyp-of__label">Наличие сертификата</div>-->
                                        <!--                                            </div>-->
                                        <!--                                            <div class="nyp-of__trigger">-->
                                        <!--                                                <input type="checkbox" placeholder="" id="ny-field&#45;&#45;trigger-doc" name="trigger-doc" class="ny-field ny-field&#45;&#45;edit-trigger&#45;&#45;nyp-of ny-field&#45;&#45;edit-trigger field-attr-trigger">-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <!--                                        <div class="nyp-of__options" id="coverAttribute" style="display: none;">-->
                                        <!--                                            <div class="nyp-of__info">-->
                                        <!--                                                <div class="nyp-of__label">Наличие чехла</div>-->
                                        <!--                                            </div>-->
                                        <!--                                            <div class="nyp-of__trigger">-->
                                        <!--                                                <input type="checkbox" placeholder="" id="ny-field&#45;&#45;trigger-cover" name="trigger-doc" class="ny-field ny-field&#45;&#45;edit-trigger&#45;&#45;nyp-of ny-field&#45;&#45;edit-trigger field-attr-trigger">-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                        <!--                                        <div class="nyp-of__options" id="napkinAttribute" style="display: none;">-->
                                        <!--                                            <div class="nyp-of__info">-->
                                        <!--                                                <div class="nyp-of__label">Наличие салфетки</div>-->
                                        <!--                                            </div>-->
                                        <!--                                            <div class="nyp-of__trigger">-->
                                        <!--                                                <input type="checkbox" placeholder="" id="ny-field&#45;&#45;trigger-napkin" name="trigger-doc" class="ny-field ny-field&#45;&#45;edit-trigger&#45;&#45;nyp-of ny-field&#45;&#45;edit-trigger field-attr-trigger">-->
                                        <!--                                            </div>-->
                                        <!--                                        </div>-->
                                    </div>
                                </div>
                            </div>

                            <!--Additional sizes-->
                            <div class="a-title a-title--margin-top-66 a-title--open-sans a-title--font-size-20 a-title--black"
                                 style="display: none">
                                Дополнительные размеры
                            </div>
                            <div class="nyp-of__box nyp-of__company nyp-of__box--margin-top-26" style="display: none">
                                <div class="nyp-of__col nyp-of__col--full-width nyp-of__col--thumb">
                                    <div class="nyp-thumb">
                                        <div class="nyp-thumb__pic"><img th:src="@{/img/publication/pictures/add-thumb.png}"
                                                                         class="ny-picture ny-picture--nyp-thumb">
                                        </div>
                                        <div class="nyp-thumb__caption">Пример замера</div>
                                    </div>
                                    <div class="nyp-of__sized">
                                        <button class="ny-button ny-button--new ny-button--nyp-of--active ny-button--nyp-of">
                                            37
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">38
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">39
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">40
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">41
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">42
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">43
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">44
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">45
                                        </button>
                                        <button class="ny-button ny-button--new ny-button--nyp-of">46
                                        </button>
                                    </div>
                                    <div class="nyp-of__fields nyp-of__fields--flex">
                                        <div class="nyp-of__3-115">
                                            <label for="ny-field--undefined" class="ny-label ny-label--nyp-of">Ширина
                                                груди</label>
                                            <input type="text" id="ny-field--" name=""
                                                   class="ny-field ny-field--padding-right-33 ny-field--nyp-of">
                                            <span class="nyp-of__input-badge">
                                            cм
                                        </span>

                                        </div>
                                        <div class="nyp-of__3-115">
                                            <label for="ny-field--undefined" class="ny-label ny-label--nyp-of">Длина
                                                груди</label>
                                            <input type="text" id="ny-field--" name=""
                                                   class="ny-field ny-field--padding-right-33 ny-field--nyp-of">
                                            <span class="nyp-of__input-badge">
                                            cм
                                        </span>

                                        </div>
                                        <div class="nyp-of__3-115">
                                            <label for="ny-field--undefined" class="ny-label ny-label--nyp-of">Длина
                                                руки</label>
                                            <input type="text" id="ny-field--" name=""
                                                   class="ny-field ny-field--padding-right-33 ny-field--nyp-of">
                                            <span class="nyp-of__input-badge">
                                            cм
                                        </span>

                                        </div>
                                    </div>
                                    <div class="nyp-of__fields nyp-of__fields--flex">
                                        <div class="nyp-of__3-115">
                                            <label for="ny-field--undefined" class="ny-label ny-label--nyp-of">Длина
                                                ноги</label>
                                            <input type="text" id="ny-field--" name=""
                                                   class="ny-field ny-field--padding-right-33 ny-field--nyp-of">
                                            <span class="nyp-of__input-badge">
                                            cм
                                        </span>

                                        </div>
                                        <div class="nyp-of__3-115">
                                            <label for="ny-field--undefined" class="ny-label ny-label--nyp-of">Обхват
                                                груди</label>
                                            <input type="text" id="ny-field--" name=""
                                                   class="ny-field ny-field--padding-right-33 ny-field--nyp-of">
                                            <span class="nyp-of__input-badge">
                                            cм
                                        </span>

                                        </div>
                                        <div class="nyp-of__3-115">
                                            <label for="ny-field--undefined" class="ny-label ny-label--nyp-of">Обхват
                                                талии</label>
                                            <input type="text" id="ny-field--" name=""
                                                   class="ny-field ny-field--padding-right-33 ny-field--nyp-of">
                                            <span class="nyp-of__input-badge">
                                            cм
                                        </span>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--Buttons-->
                        <div class="ny-product__content-row ny-product__content-row--flex">
                            <button class="ny-button ny-button--no-side-padding ny-button--flex-1 ny-button--product-inline sent_publication_open mob_none_publication" id="buttonAddProduct" style="margin-right: 15px;"><i class="ny-icon ny-icon--right-42-negative ny-icon--product-inline"></i><span>Добавить еще товар</span></button>
                            <button class="ny-button ny-button--no-side-padding ny-button--margin-left-15 ny-button--flex-1 ny-button--product" id="buttonPostProduct" style="margin-left: 0px;">Опубликовать этот товар</button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        <!--<div th:replace="publication/boutique/chose_photo :: choose_photo()"></div>-->
    </div>

    <!--popup category-->
    <div class="popup-wrap popup-wrap--add-photo">
        <div class="ny-product__popup ny-product__popup--category" id="select-category">
            <div class="nyp-category">
                <div class="nyp-category__title">Выберите категорию</div>
                <div class="nyp-category_all"  style="display: flex;
                                                        flex-wrap: nowrap;
                                                        overflow-x: auto;">

                    <ul class="nyp-category__nav"  style="overflow: initial;">
                        <li class="nyp-category__item" id="boutiqueCategoryTemplate" style="display: none;">
                            <a href="#" class="nyp-category__link">Мужское<i class="ny-icon ny-icon--nyp-category"></i></a>
                        </li>
                    </ul>
                    <div class="scroll_content scroll_content_publication">
                        <ul class="nyp-category__sub level1">
                            <li class="nyp-category__item" id="boutiqueCategoryTemplate1" style="display: none;">
                                <a href="#" class="nyp-category__link">Одежда<i
                                        class="ny-icon ny-icon--nyp-category"></i></a>
                            </li>
                        </ul>
                    </div>

                    <div class="scroll_content scroll_content_publication">
                        <ul class="nyp-category__sub level2">
                            <li class="nyp-category__item" id="boutiqueCategoryTemplate2" style="display: none;">
                                <a href="#" class="nyp-category__link">Ботинки<i
                                        class="ny-icon ny-icon--nyp-category"></i></a>
                            </li>
                        </ul>
                    </div>

                    <div class="scroll_content scroll_content_publication">
                        <ul class="nyp-category__sub nyp-category__sub--clear level3">
                            <li class="nyp-category__item" id="boutiqueCategoryTemplate3" style="display: none;">
                                <span class="nyp-category__link">Туфли на высоком каблуке</span>
                            </li>
                        </ul>
                    </div>
                    <div class="scroll_content scroll_content_publication">
                        <ul class="nyp-category__sub nyp-category__sub--clear level4">
                            <li class="nyp-category__item" id="boutiqueCategoryTemplate4" style="display: none;">
                                <span class="nyp-category__link">Туфли на высоком каблуке</span>
                            </li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!--popup photo-->
    <div class="popup-wrap popup-wrap--add-photo">
        <div class="popup popup--add-photo" id="add-photo">
            <div class="popup__title popup__title--align-left popup__title--font-weight-400 popup__title--font-size-22">
                Выбор фотографий
            </div>
            <div class="popup__content popup__content--margin-top-30">
                <div class="tabs tabs--add-photo">

                    <!--Разделы-->
                    <ul class="tabs__nav tabs__nav--opacity tabs__nav--border-bottom">
                        <li class="tabs__nav-item tabs__nav-item--flex-regular" style="display: none;"
                            id="photoSection">
                            <a class="tabs__link tabs__link--padding-side-4-mobile tabs__link--padding-side-20 tabs__link--font-size-18"
                               href="javascript:void(0);">
                                <span class="tabs__link-text tabs__link-text--font-weight-300 tabs__link-text--font-size-20"></span></a>
                        </li>
                    </ul>

                    <!--Контент раздела-->
                    <div class="tabs__content-wrap">
                        <div class="tabs__content" data-id="0" style="display: block;">
                            <div class="tabs__content-row tabs__content-row--flex">

                                <!--Список категорий-->
                                <div class="tabs__content-row-block tabs__content-row-block--padding-right-6 tabs__content-row-block--padding-top-22 tabs__content-row-block--border-right tabs__content-row-block--width-224">
                                    <div class="a-text a-text--font-weight-700 a-text--font-size-12 a-text--grey">
                                        Категория
                                    </div>
                                    <ul class="a-list a-list--margin-top-15">
                                        <li class="a-list__item a-list__item--show-folders a-list__item--whitespace-nowrap a-list__item--with-triangle a-list__item--triangle-side"
                                            style="display: none;" id="photoSectionCategory">
                                            <span class="a-text a-text--color-dark"></span>
                                        </li>
                                    </ul>
                                </div>

                                <!--Список папок с фото-->
                                <div class="tabs__content-row-block tabs__content-row-block--folders tabs__content-row-block--hidden tabs__content-row-block--padding-top-22 tabs__content-row-block--padding-side-20 tabs__content-row-block--width-346 tabs__content-row-block--border-right">
                                    <div class="tabs__content-row tabs__content-row--space-between tabs__content-row--flex tabs__content-row--padding-bottom-10 tabs__content-row--border-bottom">
                                        <div class="tabs__content-row-block">
                                            <span class="a-text a-text--font-weight-700 a-text--font-size-12 a-text--grey">Артикул</span>
                                        </div>
                                        <div class="tabs__content-row-block tabs__content-row-block--cursor-pointer">
                                            <span class="a-text a-text--font-weight-700 a-text--font-size-12 a-text--grey">Дата создания</span><span
                                                class="icon icon--margin-left-5 icon--arrow-down-wide"></span>
                                        </div>
                                    </div>
                                    <div class="tabs__content-row">
                                        <ul class="photo-folders a-list a-list--overflow-scroll a-list--height-345">
                                            <li class="a-list__item a-list__item--show-photos a-list__item--padding-side-20 a-list__item--vertical-padding-11 a-list__item--space-between a-list__item--flex a-list__item--hover-grey a-list__item--border-top"
                                                style="display: none;" id="photoFolders">
                                                <div class="a-list__item-block">
                                                    <span class="icon icon--folder"></span>
                                                    <span class="folder-name a-text a-text--letter-spacing-1 a-text--margin-left-10">012</span>
                                                </div>
                                                <div class="a-list__item-block">
                                                    <span class="folder-date a-text a-text--listgrey a-text--font-size-12">12.11.19 в 22:30</span>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!--Список фото-->
                                <div class="tabs__content-row-block tabs__content-row-block--gallery tabs__content-row-block--flex-1">
                                    <div class="tabs__content-row tabs__content-row--empty-gallery tabs__content-row--height-404">
                                        <div class="tabs__content-row-block tabs__content-row-block--align-center">
                                            <img alt="image" class="a-img a-img--width-187"
                                                 th:src="@{/imgs/empty_gallery.png}"/>
                                            <div class="a-text a-text--categories a-text--width-355 a-text--margin-top-20 a-text--font-size-16 a-text--listgrey">
                                                Выберите<span class="a-text a-text--font-weight-700">&nbsp;Категорию&nbsp;</span>слева
                                                для того, чтобы посмотреть папки с фотографиями
                                            </div>
                                            <div class="a-text a-text--folders a-text--width-355 a-text--margin-top-20 a-text--font-size-16 a-text--listgrey">
                                                Выберите<span
                                                    class="a-text a-text--font-weight-700">&nbsp;Папку&nbsp;</span>для
                                                того, чтобы посмотреть фотографии
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tabs__content-row tabs__content-row--gallery tabs__content-row--height-404">
                                        <form action="#" class="form form--gallery" method="POST">
                                            <div class="tabs__gallery-item" id="photoList" style="display: none;">
                                                <img alt="photo" class="a-img a-img--border-radius-3 a-img--add-photo"
                                                     th:src="@{imgs/08.jpg}"/>
                                                <div class="tabs__gallery-item-shadow"></div>
                                                <label class="badge badge--check-photo">
                                                    <input class="form__checkbox" name="" type="checkbox" value="1"/>
                                                    <div class="form__label form__label--checkbox form__label--checkbox-round"></div>
                                                </label>
                                                <div class="photo-name a-text a-text--align-center a-text--text-overflow-ellipsis a-text--listgrey a-text--font-size-12">
                                                    photoname.jpg
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="tabs__content-row tabs__content-row--full-width tabs__content-row--padding-top-14 tabs__content-row--border-top tabs__content-row--justify-flex-end tabs__content-row--flex">
                                <button class="button button--hidden button--margin-right-27" id="select-photos">
                                    <span class="button__text button__text--font-size-12 button__text--grey">Выбрать фотографии</span>
                                </button>
                                <button class="button button--hidden button--margin-right-27"
                                        id="reset-photo-selection">
                                    <span class="button__text button__text--font-size-12 button__text--grey">Отменить выбранное</span>
                                </button>
                                <button class="button button--disabled button--width-154 button--no-transition button--border-radius-2 button--hover-opacity button--height-37 button--black-regular"
                                        id="upload-photos">
                                    <span class="button__text button__text--font-size-12">Загрузить</span>
                                </button>
                                <button class="button button--hidden button--width-154 button--no-transition button--border-radius-2 button--hover-opacity button--height-37 button--black-regular"
                                        id="upload-all-photos">
                                    <span class="button__text button__text--font-size-12">Загрузить все</span>
                                </button>
                            </div>
                        </div>
                        <div class="tabs__content" data-id="1" style="display: none;">
                            <h1>
                                content1
                            </h1>
                        </div>
                        <div class="tabs__content" data-id="2" style="display: none;">
                            <h1>
                                content2
                            </h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add address popup -->
    <div class="popup-wrap popup-wrap--add-address">
        <div class="nyp-size popup popup--add-address" id="add-address">
            <div class="nyp-size__title">Добавление нового адреса</div>
            <div class="nyp-size__fields">
                <div class="nyp-size__row">
                    <div class="nyp-size__col">
                        <div class="nyp-size__field">
                            <label for="ny-field--popUp-lastName" class="ny-label ny-label--nyp-size">Фамилия</label>
                            <input type="text" placeholder="" id="ny-field--popUp-lastName" name=""
                                   class="ny-field ny-field--nyp-size lastName" style="font-weight: normal;">

                        </div>
                    </div>
                    <div class="nyp-size__col">
                        <div class="nyp-size__field">
                            <label for="ny-field--popUp-firstName" class="ny-label ny-label--nyp-size">Имя</label>
                            <input type="text" placeholder="" id="ny-field--popUp-firstName" name=""
                                   class="ny-field ny-field--nyp-size firstName">

                        </div>
                    </div>
                </div>
                <div class="nyp-size__row">
                    <div class="nyp-size__col">
                        <div class="nyp-size__field">
                            <label for="ny-field--popUp-patronymicName"
                                   class="ny-label ny-label--nyp-size">Отчество</label>
                            <input type="text" placeholder="" id="ny-field--popUp-patronymicName" name=""
                                   class="ny-field ny-field--nyp-size patronymicName" style="font-weight: normal;">

                        </div>
                    </div>
                    <div class="nyp-size__col">
                        <div class="nyp-size__field">
                            <label for="ny-field--popUp-phone" class="ny-label ny-label--nyp-size">Номер
                                телефона</label>
                            <input type="text" placeholder="" id="ny-field--popUp-phone" name=""
                                   class="ny-field ny-field--nyp-size ny-field--phone phone" style="font-weight: normal;">

                        </div>
                    </div>
                </div>
                <div class="nyp-size__row">
                    <div class="nyp-size__col">
                        <div class="nyp-size__field">
                            <label for="ny-field--popUp-region" class="ny-label ny-label--nyp-size">Регион</label>
                            <input type="text" placeholder="" id="ny-field--popUp-region" name=""
                                   class="ny-field ny-field--nyp-size region" style="font-weight: normal;">

                        </div>
                    </div>
                    <div class="nyp-size__col">
                        <div class="nyp-size__field non-unfocus-city-div">
                            <label for="ny-field--popUp-city" class="ny-label ny-label--nyp-size">Город</label>
                            <input type="text" placeholder="" id="ny-field--popUp-city" name=""
                                   class="ny-field ny-field--nyp-size city" style="font-weight: normal;">
                            <div class="show_list_city city-popup"  style="top:auto;display: none;">
                                <div class="empty_block--city empty-search-city hide_address">Вашего города нет в базе данных.
                                    Мы перезвоним вам для уточнения условий доставки.
                                </div>
                                <div class="list_all_block list-all-city">
                                    <div class="enter_address_tooltip">
                                        Подсказка появится после ввода 3-х символов
                                    </div>
                                    <div class="list_block list_block_latest list-city-block">
                                        <div class="name_block-list name-list-city-block">
                                            Недавний поиск
                                        </div>
                                        <ul>

                                        </ul>
                                    </div>
                                    <div class="list_block list_block_popular list-city-block">
                                        <div class="name_block-list name-list-city-block">
                                            Популярные города
                                        </div>
                                        <ul>

                                        </ul>
                                    </div>
                                    <div class="list_block list_block_search list-city-block" style="display: none">
                                        <ul>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="nyp-size__row">
                    <div class="nyp-size__col">
                        <div class="nyp-size__field">
                            <label for="ny-field--popUp-street" class="ny-label ny-label--nyp-size">Улица</label>
                            <input type="text" placeholder="" id="ny-field--popUp-street" name=""
                                   class="ny-field ny-field--nyp-size street" style="font-weight: normal;">

                        </div>
                    </div>
                    <div class="nyp-size__col">
                        <div class="nyp-size__half">
                            <div class="nyp-size__field">
                                <label for="ny-field--popUp-building" class="ny-label ny-label--nyp-size">Дом</label>
                                <input type="text" placeholder="" id="ny-field--popUp-building" name=""
                                       class="ny-field ny-field--nyp-size building" style="font-weight: normal;">

                            </div>
                        </div>
                        <div class="nyp-size__half">
                            <div class="nyp-size__field">
                                <label for="ny-field--popUp-flat" class="ny-label ny-label--nyp-size">Квартира</label>
                                <input type="text" placeholder="" id="ny-field--popUp-flat" name=""
                                       class="ny-field ny-field--nyp-size flat" style="font-weight: normal;">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="nyp-size__actions">
                <button class="ny-button ny-button--nyp-size--clear ny-button--nyp-size" id="cancelAddressButton">Отмена
                </button>
                <button class="ny-button ny-button--nyp-size" id="saveAddressButton">Добавить
                </button>
            </div>
        </div>
    </div>
    <!--отправлен на модерацию-->
    <div class='tree_popups' id='sent_moderation'>
        <div class='popup_block'>
            <div class='popup_icon'>
                <svg width="159" height="145" viewBox="0 0 159 145" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g opacity="0.3">
                        <g clip-path="url(#clip0)">
                            <path d="M143.5 125C140.462 125 138 122.538 138 119.5C138 116.462 140.462 114 143.5 114C146.538 114 149 116.462 149 119.5C149 122.538 146.538 125 143.5 125ZM143.5 123C145.433 123 147 121.433 147 119.5C147 117.567 145.433 116 143.5 116C141.567 116 140 117.567 140 119.5C140 121.433 141.567 123 143.5 123Z" fill="#E5E5E5"/>
                            <path d="M137.658 70.3956C136.932 70.1313 136.768 69.1782 137.363 68.6855L147.3 60.4614C147.889 59.9745 148.784 60.3002 148.922 61.0515L151.248 73.7392C151.387 74.4994 150.648 75.1236 149.922 74.8592L137.658 70.3956ZM139.983 69.1133L148.966 72.3828L147.262 63.0894L139.983 69.1133Z" fill="#E5E5E5"/>
                            <path d="M148.5 27C144.358 27 141 23.6421 141 19.5C141 15.3579 144.358 12 148.5 12C152.642 12 156 15.3579 156 19.5C156 23.6421 152.642 27 148.5 27ZM148.5 25C151.538 25 154 22.5376 154 19.5C154 16.4624 151.538 14 148.5 14C145.462 14 143 16.4624 143 19.5C143 22.5376 145.462 25 148.5 25Z" fill="#E5E5E5"/>
                            <path d="M8 101C5.23858 101 3 98.7614 3 96C3 93.2386 5.23858 91 8 91C10.7614 91 13 93.2386 13 96C13 98.7614 10.7614 101 8 101ZM8 99C9.65685 99 11 97.6569 11 96C11 94.3431 9.65685 93 8 93C6.34315 93 5 94.3431 5 96C5 97.6569 6.34315 99 8 99Z" fill="#E5E5E5"/>
                            <path d="M26.5 57C23.4624 57 21 54.5376 21 51.5C21 48.4624 23.4624 46 26.5 46C29.5376 46 32 48.4624 32 51.5C32 54.5376 29.5376 57 26.5 57ZM26.5 55C28.433 55 30 53.433 30 51.5C30 49.567 28.433 48 26.5 48C24.567 48 23 49.567 23 51.5C23 53.433 24.567 55 26.5 55Z" fill="#E5E5E5"/>
                            <path d="M111 33C108.791 33 107 31.2091 107 29C107 26.7909 108.791 25 111 25C113.209 25 115 26.7909 115 29C115 31.2091 113.209 33 111 33ZM111 31C112.105 31 113 30.1046 113 29C113 27.8954 112.105 27 111 27C109.895 27 109 27.8954 109 29C109 30.1046 109.895 31 111 31Z" fill="#E5E5E5"/>
                            <path d="M158 92C155.791 92 154 90.2091 154 88C154 85.7909 155.791 84 158 84C160.209 84 162 85.7909 162 88C162 90.2091 160.209 92 158 92ZM158 90C159.105 90 160 89.1046 160 88C160 86.8954 159.105 86 158 86C156.895 86 156 86.8954 156 88C156 89.1046 156.895 90 158 90Z" fill="#E5E5E5"/>
                            <path d="M31.2283 78.5813C31.4172 78.0623 31.991 77.7947 32.51 77.9836C33.029 78.1725 33.2966 78.7464 33.1077 79.2653L30.3093 86.9537C30.1204 87.4727 29.5466 87.7403 29.0276 87.5514C28.5086 87.3625 28.2411 86.7887 28.4299 86.2697L31.2283 78.5813Z" fill="#E5E5E5"/>
                            <path d="M34.9548 83.2273C35.4738 83.4162 35.7414 83.9901 35.5525 84.509C35.3636 85.028 34.7898 85.2956 34.2708 85.1067L26.5824 82.3084C26.0634 82.1195 25.7958 81.5456 25.9847 81.0266C26.1736 80.5077 26.7475 80.2401 27.2664 80.429L34.9548 83.2273Z" fill="#E5E5E5"/>
                            <path d="M131.591 14.5C132.143 14.5 132.591 14.9477 132.591 15.5C132.591 16.0523 132.143 16.5 131.591 16.5H123.409C122.857 16.5 122.409 16.0523 122.409 15.5C122.409 14.9477 122.857 14.5 123.409 14.5H131.591Z" fill="#E5E5E5"/>
                            <path d="M128.5 19.5908C128.5 20.1431 128.052 20.5908 127.5 20.5908C126.948 20.5908 126.5 20.1431 126.5 19.5908V11.409C126.5 10.8567 126.948 10.409 127.5 10.409C128.052 10.409 128.5 10.8567 128.5 11.409V19.5908Z" fill="#E5E5E5"/>
                            <path d="M53.3789 8.2424L57.2422 13.7448L61.1055 8.2424L57.2422 2.74003L53.3789 8.2424ZM58.0606 0.425137L63.1458 7.66778C63.3879 8.01261 63.3879 8.4722 63.1458 8.81703L58.0606 16.0597C57.6624 16.6268 56.822 16.6268 56.4238 16.0597L51.3386 8.81703C51.0965 8.4722 51.0965 8.01261 51.3386 7.66778L56.4238 0.425137C56.822 -0.14203 57.6624 -0.14203 58.0606 0.425137Z" fill="#E5E5E5"/>
                            <path d="M25.3683 117.742L27.7422 121.123L30.1161 117.742L27.7422 114.361L25.3683 117.742ZM28.5606 112.046L32.1564 117.168C32.3985 117.512 32.3985 117.972 32.1564 118.317L28.5606 123.438C28.1624 124.005 27.322 124.005 26.9238 123.438L23.328 118.317C23.0859 117.972 23.0859 117.512 23.328 117.168L26.9238 112.046C27.322 111.479 28.1624 111.479 28.5606 112.046Z" fill="#E5E5E5"/>
                        </g>
                    </g>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M41 31C41 29.3431 42.3431 28 44 28H115C116.657 28 118 29.3431 118 31V68.125C118 68.6773 117.552 69.125 117 69.125C116.448 69.125 116 68.6773 116 68.125V31C116 30.4477 115.552 30 115 30H44C43.4477 30 43 30.4477 43 31V112C43 112.552 43.4477 113 44 113H115C115.552 113 116 112.552 116 112V80.375C116 79.8227 116.448 79.375 117 79.375C117.552 79.375 118 79.8227 118 80.375V112C118 113.657 116.657 115 115 115H44C42.3431 115 41 113.657 41 112V31Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M41 31C41 29.3431 42.3431 28 44 28H115C116.657 28 118 29.3431 118 31V37H41V31ZM44 30C43.4477 30 43 30.4477 43 31V35H116V31C116 30.4477 115.552 30 115 30H44Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M50 45C50 43.3431 51.3431 42 53 42H76C77.6569 42 79 43.3431 79 45V68C79 69.6569 77.6569 71 76 71H53C51.3431 71 50 69.6569 50 68V45ZM53 44C52.4477 44 52 44.4477 52 45V68C52 68.5523 52.4477 69 53 69H76C76.5523 69 77 68.5523 77 68V45C77 44.4477 76.5523 44 76 44H53Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 52C86 51.4477 86.4477 51 87 51H108C108.552 51 109 51.4477 109 52C109 52.5523 108.552 53 108 53H87C86.4477 53 86 52.5523 86 52Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M51 32.5C51 31.9477 51.4477 31.5 52 31.5H54C54.5523 31.5 55 31.9477 55 32.5C55 33.0523 54.5523 33.5 54 33.5H52C51.4477 33.5 51 33.0523 51 32.5Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M58 32.5C58 31.9477 58.4477 31.5 59 31.5H61C61.5523 31.5 62 31.9477 62 32.5C62 33.0523 61.5523 33.5 61 33.5H59C58.4477 33.5 58 33.0523 58 32.5Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M65 32.5C65 31.9477 65.4477 31.5 66 31.5H68C68.5523 31.5 69 31.9477 69 32.5C69 33.0523 68.5523 33.5 68 33.5H66C65.4477 33.5 65 33.0523 65 32.5Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 63C86 62.4477 86.4477 62 87 62H108C108.552 62 109 62.4477 109 63C109 63.5523 108.552 64 108 64H87C86.4477 64 86 63.5523 86 63Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M50 84C50 83.4477 50.4477 83 51 83H108C108.552 83 109 83.4477 109 84C109 84.5523 108.552 85 108 85H51C50.4477 85 50 84.5523 50 84Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M50 99C50 98.4477 50.4477 98 51 98H108C108.552 98 109 98.4477 109 99C109 99.5523 108.552 100 108 100H51C50.4477 100 50 99.5523 50 99Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M56.293 56.0214C56.6836 55.6309 57.3167 55.6309 57.7073 56.0214L61.9499 60.264L71.1423 51.0717C71.5328 50.6811 72.166 50.6811 72.5565 51.0717C72.947 51.4622 72.947 52.0954 72.5565 52.4859L63.3641 61.6783C62.5831 62.4593 61.3167 62.4593 60.5357 61.6783L56.293 57.4356C55.9025 57.0451 55.9025 56.4119 56.293 56.0214Z" fill="#B3B3B3"/>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="159" height="145" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>

            </div>
            <div class='name_popup'>Ваш товар отправлен на модерацию</div>
            <p>Мы проверяем размер, состояние, стоимость и подлинность фотографий вашего товара. В случае успешной
                модерации мы опубликуем товар в течение 48 часов</p>
        </div>
    </div>
    <!--добавлен в очередь-->
    <div class='tree_popups' id='queue_publication'>
        <div class='popup_block'>
            <div class='popup_icon'>
                <svg width="159" height="145" viewBox="0 0 159 145" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g opacity="0.3">
                        <g clip-path="url(#clip0)">
                            <path d="M143.5 125C140.462 125 138 122.538 138 119.5C138 116.462 140.462 114 143.5 114C146.538 114 149 116.462 149 119.5C149 122.538 146.538 125 143.5 125ZM143.5 123C145.433 123 147 121.433 147 119.5C147 117.567 145.433 116 143.5 116C141.567 116 140 117.567 140 119.5C140 121.433 141.567 123 143.5 123Z" fill="#E5E5E5"/>
                            <path d="M137.658 70.3956C136.932 70.1313 136.768 69.1782 137.363 68.6855L147.3 60.4614C147.889 59.9745 148.784 60.3002 148.922 61.0515L151.248 73.7392C151.387 74.4994 150.648 75.1236 149.922 74.8592L137.658 70.3956ZM139.983 69.1133L148.966 72.3828L147.262 63.0894L139.983 69.1133Z" fill="#E5E5E5"/>
                            <path d="M148.5 27C144.358 27 141 23.6421 141 19.5C141 15.3579 144.358 12 148.5 12C152.642 12 156 15.3579 156 19.5C156 23.6421 152.642 27 148.5 27ZM148.5 25C151.538 25 154 22.5376 154 19.5C154 16.4624 151.538 14 148.5 14C145.462 14 143 16.4624 143 19.5C143 22.5376 145.462 25 148.5 25Z" fill="#E5E5E5"/>
                            <path d="M8 101C5.23858 101 3 98.7614 3 96C3 93.2386 5.23858 91 8 91C10.7614 91 13 93.2386 13 96C13 98.7614 10.7614 101 8 101ZM8 99C9.65685 99 11 97.6569 11 96C11 94.3431 9.65685 93 8 93C6.34315 93 5 94.3431 5 96C5 97.6569 6.34315 99 8 99Z" fill="#E5E5E5"/>
                            <path d="M26.5 57C23.4624 57 21 54.5376 21 51.5C21 48.4624 23.4624 46 26.5 46C29.5376 46 32 48.4624 32 51.5C32 54.5376 29.5376 57 26.5 57ZM26.5 55C28.433 55 30 53.433 30 51.5C30 49.567 28.433 48 26.5 48C24.567 48 23 49.567 23 51.5C23 53.433 24.567 55 26.5 55Z" fill="#E5E5E5"/>
                            <path d="M111 33C108.791 33 107 31.2091 107 29C107 26.7909 108.791 25 111 25C113.209 25 115 26.7909 115 29C115 31.2091 113.209 33 111 33ZM111 31C112.105 31 113 30.1046 113 29C113 27.8954 112.105 27 111 27C109.895 27 109 27.8954 109 29C109 30.1046 109.895 31 111 31Z" fill="#E5E5E5"/>
                            <path d="M158 92C155.791 92 154 90.2091 154 88C154 85.7909 155.791 84 158 84C160.209 84 162 85.7909 162 88C162 90.2091 160.209 92 158 92ZM158 90C159.105 90 160 89.1046 160 88C160 86.8954 159.105 86 158 86C156.895 86 156 86.8954 156 88C156 89.1046 156.895 90 158 90Z" fill="#E5E5E5"/>
                            <path d="M31.2273 78.5813C31.4162 78.0623 31.9901 77.7947 32.509 77.9836C33.028 78.1725 33.2956 78.7464 33.1067 79.2653L30.3084 86.9537C30.1195 87.4727 29.5456 87.7403 29.0266 87.5514C28.5077 87.3625 28.2401 86.7887 28.429 86.2697L31.2273 78.5813Z" fill="#E5E5E5"/>
                            <path d="M34.9539 83.2273C35.4728 83.4162 35.7404 83.9901 35.5515 84.509C35.3626 85.028 34.7888 85.2956 34.2698 85.1067L26.5814 82.3084C26.0624 82.1195 25.7949 81.5456 25.9837 81.0266C26.1726 80.5077 26.7465 80.2401 27.2655 80.429L34.9539 83.2273Z" fill="#E5E5E5"/>
                            <path d="M131.591 14.5C132.143 14.5 132.591 14.9477 132.591 15.5C132.591 16.0523 132.143 16.5 131.591 16.5H123.409C122.857 16.5 122.409 16.0523 122.409 15.5C122.409 14.9477 122.857 14.5 123.409 14.5H131.591Z" fill="#E5E5E5"/>
                            <path d="M128.5 19.5908C128.5 20.1431 128.052 20.5908 127.5 20.5908C126.948 20.5908 126.5 20.1431 126.5 19.5908V11.409C126.5 10.8567 126.948 10.409 127.5 10.409C128.052 10.409 128.5 10.8567 128.5 11.409V19.5908Z" fill="#E5E5E5"/>
                            <path d="M53.3789 8.2424L57.2422 13.7448L61.1055 8.2424L57.2422 2.74003L53.3789 8.2424ZM58.0606 0.425137L63.1458 7.66778C63.3879 8.01261 63.3879 8.4722 63.1458 8.81703L58.0606 16.0597C57.6624 16.6268 56.822 16.6268 56.4238 16.0597L51.3386 8.81703C51.0965 8.4722 51.0965 8.01261 51.3386 7.66778L56.4238 0.425137C56.822 -0.14203 57.6624 -0.14203 58.0606 0.425137Z" fill="#E5E5E5"/>
                            <path d="M25.3683 117.742L27.7422 121.123L30.1161 117.742L27.7422 114.361L25.3683 117.742ZM28.5606 112.046L32.1564 117.168C32.3985 117.512 32.3985 117.972 32.1564 118.317L28.5606 123.438C28.1624 124.005 27.322 124.005 26.9238 123.438L23.328 118.317C23.0859 117.972 23.0859 117.512 23.328 117.168L26.9238 112.046C27.322 111.479 28.1624 111.479 28.5606 112.046Z" fill="#E5E5E5"/>
                        </g>
                    </g>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M41 31C41 29.3431 42.3431 28 44 28H115C116.657 28 118 29.3431 118 31V68.125C118 68.6773 117.552 69.125 117 69.125C116.448 69.125 116 68.6773 116 68.125V31C116 30.4477 115.552 30 115 30H44C43.4477 30 43 30.4477 43 31V112C43 112.552 43.4477 113 44 113H115C115.552 113 116 112.552 116 112V80.375C116 79.8227 116.448 79.375 117 79.375C117.552 79.375 118 79.8227 118 80.375V112C118 113.657 116.657 115 115 115H44C42.3431 115 41 113.657 41 112V31Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M41 31C41 29.3431 42.3431 28 44 28H115C116.657 28 118 29.3431 118 31V37H41V31ZM44 30C43.4477 30 43 30.4477 43 31V35H116V31C116 30.4477 115.552 30 115 30H44Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M53 48.2759C53 47.0189 54.0189 46 55.2759 46H72.7241C73.9811 46 75 47.0189 75 48.2759V65.7241C75 66.9811 73.9811 68 72.7241 68H55.2759C54.0189 68 53 66.9811 53 65.7241V48.2759ZM55.2759 47.5172C54.8569 47.5172 54.5172 47.8569 54.5172 48.2759V65.7241C54.5172 66.1431 54.8569 66.4828 55.2759 66.4828H72.7241C73.1431 66.4828 73.4828 66.1431 73.4828 65.7241V48.2759C73.4828 47.8569 73.1431 47.5172 72.7241 47.5172H55.2759Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M53 83.2759C53 82.0189 54.0189 81 55.2759 81H72.7241C73.9811 81 75 82.0189 75 83.2759V100.724C75 101.981 73.9811 103 72.7241 103H55.2759C54.0189 103 53 101.981 53 100.724V83.2759ZM55.2759 82.5172C54.8569 82.5172 54.5172 82.8569 54.5172 83.2759V100.724C54.5172 101.143 54.8569 101.483 55.2759 101.483H72.7241C73.1431 101.483 73.4828 101.143 73.4828 100.724V83.2759C73.4828 82.8569 73.1431 82.5172 72.7241 82.5172H55.2759Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 47C86 46.4477 86.4477 46 87 46H108C108.552 46 109 46.4477 109 47C109 47.5523 108.552 48 108 48H87C86.4477 48 86 47.5523 86 47Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M51 32.5C51 31.9477 51.4477 31.5 52 31.5H54C54.5523 31.5 55 31.9477 55 32.5C55 33.0523 54.5523 33.5 54 33.5H52C51.4477 33.5 51 33.0523 51 32.5Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M58 32.5C58 31.9477 58.4477 31.5 59 31.5H61C61.5523 31.5 62 31.9477 62 32.5C62 33.0523 61.5523 33.5 61 33.5H59C58.4477 33.5 58 33.0523 58 32.5Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M65 32.5C65 31.9477 65.4477 31.5 66 31.5H68C68.5523 31.5 69 31.9477 69 32.5C69 33.0523 68.5523 33.5 68 33.5H66C65.4477 33.5 65 33.0523 65 32.5Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 57C86 56.4477 86.4477 56 87 56H108C108.552 56 109 56.4477 109 57C109 57.5523 108.552 58 108 58H87C86.4477 58 86 57.5523 86 57Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 69C86 68.4477 86.4477 68 87 68H108C108.552 68 109 68.4477 109 69C109 69.5523 108.552 70 108 70H87C86.4477 70 86 69.5523 86 69Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 80C86 79.4477 86.4477 79 87 79H108C108.552 79 109 79.4477 109 80C109 80.5523 108.552 81 108 81H87C86.4477 81 86 80.5523 86 80Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 91C86 90.4477 86.4477 90 87 90H108C108.552 90 109 90.4477 109 91C109 91.5523 108.552 92 108 92H87C86.4477 92 86 91.5523 86 91Z" fill="#B3B3B3"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M86 102C86 101.448 86.4477 101 87 101H108C108.552 101 109 101.448 109 102C109 102.552 108.552 103 108 103H87C86.4477 103 86 102.552 86 102Z" fill="#B3B3B3"/>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="159" height="145" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </div>
            <div class='name_popup'>Ваш товар добавлен в очередь на публикацию</div>
            <p>Добавьте несколько товаров, затем опубликуйте их. Так они быстрее пройдут модерацию OSKELLY</p>
        </div>
    </div>
    <!--Черновик удален-->
    <div class='tree_popups' id='draft_deleted'>
        <div class='popup_block'>
            <div class='popup_icon'>
                <img th:src='@{/img/publication/pictures/ic_published.svg}'>
            </div>
            <div class='name_popup name_popup_color'>Черновик удален</div>
        </div>
    </div>
    <div id="popup_private_seller">
        <div class="flex_block--popup">
            <div class="popup_block">
                <div class="close"></div>
                <div class="title_block-popup">
                    <div class="title_block-icon">
                        <img src="/images/pictures/offer_1.png?v=182720876098d22b88aef04f33387c82">
                    </div>
                    <div class="title_block-text">Что включает в себя комиссия OSKELLY</div>
                </div>
                <div class='popup__list'>
                    <ul>
                        <li>
                            <div class='popup__list--name'>Безопасность сделки</div>
                            <p>Мы предоставляем гарантию, что средства за проданный вами товар будут зачислены на ваш счет.</p>
                        </li>
                        <li>
                            <div class='popup__list--name'>Экспертиза аутентификации Oskelly и контроль качества</div>
                            <p>Наша профессиональная команда экспертов проверяет абсолютно каждый товар на подлинность и
                                соответствие заявленным характеристикам. Согласно нашей внутренней статистике,
                                именно наличие сертификата подлинности Oskelly является главным триггером к покупке и
                                большинства наших пользователей.</p>
                        </li>
                        <li>
                            <div class='popup__list--name'>Доставка от двери до двери</div>
                            <p>Курьер из Службы доставки заберет у вас изделие, чтобы доставить на экспертизу в офис Oskelly,
                                а затем доставит проверенный товар в руки покупателя.</p>
                        </li>
                        <li>
                            <div class='popup__list--name'>Маркетинговые инструменты</div>
                            <p>Функционал нашей платформы позволяет использовать самые различные инструменты для привлечения покупателей,
                                продвижения товаров и их скорейшей продажи.</p>
                        </li>
                        <li>
                            <div class='popup__list--name'>Предпродажная подготовка и упаковка</div>
                            <p>Перед тем, как передать товар покупателю, мы приводим его в порядок и упаковываем в фирменную коробку Oskelly.</p>
                        </li>
                    </ul>
                </div>
                <div class="title_block-popup">
                    <div class="title_block-icon">
                        <img src="/images/pictures/offer_1.png?v=182720876098d22b88aef04f33387c82">
                    </div>
                    <div class="title_block-text">Почему это выгодно?</div>
                    <p>
                        Создайте собственный виртуальный бутик и продавайте ваши вещи другим ценителям моды из комьюнити OSKELLY по всей России в 2 клика.
                        <br>
                        Вы не несете никаких затрат. Комиссия OSKELLY взымается только после доставки товара покупателю
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!--Добавить фото дефекта-->
    <div class="add_photo_defect" id="add_photo_defect_excellent">
        <div class="popup_block">
            <div class="popup_title">Добавить фото дефекта</div>
            <div class="add_photo_defect_all_blosks">
                <div class="add_photo_defect_blosks">
                    <div class="add_photo_defect_block">
                        <div class="add_img_defect">
                            <div class="add_file">
                                <input type="file" id="img_defect" accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                <label for="img_defect"></label >
                            </div>
                            <div class="img_defect">

                            </div>
                        </div>
                        <div class="add_text_defect">
                            <span>Описание дефекта</span>
                            <textarea  maxlength = "60" ></textarea >
                            <a href = "#" class="remove_defect" style="display: block;">Удалить дефект</a >
                        </div>
                    </div>

                </div>
                <a href = "#" class = "click_add_block" >Добавить фото дефекта</a >
            </div>

            <div class="bottom_button">
                <a href = "#" class="close">Отмена</a >
                <a href = "#" class="save_img_defect">Сохранить</a >
            </div>
        </div>
    </div>
    <div class="add_photo_defect" id="add_photo_defect_good">
        <div class="popup_block">
            <div class="popup_title">Добавить фото дефекта</div>
            <div class="add_photo_defect_all_blosks">
                <div class="add_photo_defect_blosks">
                    <div class="add_photo_defect_block">
                        <div class="add_img_defect">
                            <div class="add_file">
                                <input type = "file" id="img_defect_g" accept=".png,.jpeg,.jpg,.PNG,.JPEG,.JPG">
                                <label for = "img_defect_g" ></label >
                            </div>
                            <div class="img_defect">

                            </div>
                        </div>
                        <div class="add_text_defect">
                            <span>Описание дефекта</span>
                            <textarea  maxlength = "60" ></textarea >
                            <a href = "#" class="remove_defect" style="display: block;">Удалить дефект</a >
                        </div>
                    </div>

                </div>
                <a href = "#" class = "click_add_block" >Добавить фото дефекта</a >
            </div>

            <div class="bottom_button">
                <a href = "#" class="close">Отмена</a >
                <a href = "#" class="save_img_defect">Сохранить</a >
            </div>
        </div>
    </div>
</div>

<!-- build:js app/dist/build/js/vendor.js -->
<!-- bower:js -->
<!--<script type="text/javascript" src="app/bower/jquery/dist/jquery.js"></script>-->
<!-- endbower-->
<!-- endbuild -->
<!-- build:js app/dist/build/js/main.js -->
<div layout:fragment="custom-scripts">
    <script th:src="@{/publication/js/boutique/vendor.js}" src="/publication/js/boutique/vendor.js"></script>
    <script th:src="@{/publication/js/boutique/main.js}" src="/publication/js/boutique/main.js"></script>
    <script th:src="@{/publication/js/boutique/application.js}" src="/publication/js/boutique/application.js"></script>
    <script th:src="@{/js/address/address_service.js}"></script>
    <script th:src="@{/js/address/address_api.js}"></script>

    <script th:src="@{/publication/js/boutique/mainModule.js}" src="/publication/js/boutique/mainModule.js"></script>
    <script th:src="@{/publication/js/boutique/attributesService.js}" src="/publication/js/boutique/attributesService.js"></script>
    <script th:src="@{/publication/js/boutique/sizeService.js}" src="/publication/js/boutique/sizeService.js"></script>
    <script th:src="@{/publication/js/boutique/sampleService.js}" src="/publication/js/boutique/sampleService.js"></script>
    <script th:src="@{/publication/js/boutique/priceService.js}" src="/publication/js/boutique/priceService.js"></script>
    <script th:src="@{/publication/js/boutique/photoService.js}" src="/publication/js/boutique/photoService.js"></script>
    <script th:src="@{/publication/js/boutique/modelService.js}" src="/publication/js/boutique/modelService.js"></script>
    <script th:src="@{/publication/js/boutique/brandService.js}" src="/publication/js/boutique/brandService.js"></script>
    <script th:src="@{/publication/js/boutique/draftService.js}" src="/publication/js/boutique/draftService.js"></script>
    <script th:src="@{/publication/js/boutique/photoPopupService.js}" src="/publication/js/boutique/photoPopupService.js"></script>
    <script th:src="@{/publication/js/boutique/categoryPopupService.js}" src="/publication/js/boutique/categoryPopupService.js"></script>
    <script th:src="@{/publication/js/boutique/addressService.js}" src="/publication/js/boutique/addressService.js"></script>
    <script th:src="@{/publication/js/boutique/mainPageService.js}" src="/publication/js/boutique/mainPageService.js"></script>
    <script th:src="@{/publication/js/boutique/conditionService.js}" src="/publication/js/boutique/conditionService.js"></script>
    <script th:src="@{/publication/js/boutique/validateService.js}" src="/publication/js/boutique/validateService.js"></script>
    <script th:src="@{/publication/js/boutique/main_js_new_v.js}" src="/publication/js/boutique/main_js_new_v.js"></script>
    <script th:src="@{/publication/js/boutique/jquery.mCustomScrollbar.concat.min.js}" src="/publication/js/boutique/jquery.mCustomScrollbar.concat.min.js"></script>
    <script th:src="@{/publication/js/boutique/jQuery.SimpleMask.min.js}" src="/publication/js/boutique/jQuery.SimpleMask.min.js"></script>


</div>
<!-- endbuild -->

</body>
</html>
