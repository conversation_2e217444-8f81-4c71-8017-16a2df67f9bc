<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<!--<div class="tabs__content g-banners" data-id="7">-->
    <!-- Section Women's Banner Catalog-->
    <div class="tabs__content-row">
        <div class="table__row--head">
            <h1 class="g-tab_title">
                Подборки
            </h1>
        </div>
        <div class="g-grid--three">
            <th:block th:each="collection : ${collections}" th:with="item=${collectionStat.index+1}">
                <div class="g-grid_item">
                <div class="created_collection item_collaction" th:data-hashid="${collection.id}">
                    <div class="crud_btns">
                        <button type="button" class="crud_copy"
                                th:data-setlink="|https://oskelly.ru/catalog/set/${collection.id}|">
                            <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="18" height="18" fill="#165ADF"/>
                                <path d="M10.0789 5.92383H4.96525C4.48542 5.92383 4.0957 6.31355 4.0957 6.79338V14.1305C4.0957 14.6103 4.48542 15 4.96525 15H10.0789C10.5587 15 10.9485 14.6103 10.9485 14.1305V6.79338C10.9462 6.31355 10.5565 5.92383 10.0789 5.92383ZM10.338 14.1282C10.338 14.2724 10.2208 14.3895 10.0767 14.3895H4.963C4.81882 14.3895 4.70168 14.2724 4.70168 14.1282V6.79338C4.70168 6.6492 4.81882 6.53206 4.963 6.53206H10.0767C10.2208 6.53206 10.338 6.6492 10.338 6.79338V14.1282Z" fill="white"/>
                                <path d="M12.0345 4H6.92082C6.44099 4 6.05127 4.38972 6.05127 4.86955C6.05127 5.0385 6.18643 5.17366 6.35539 5.17366C6.52434 5.17366 6.6595 5.0385 6.6595 4.86955C6.6595 4.72537 6.77664 4.60823 6.92082 4.60823H12.0345C12.1786 4.60823 12.2958 4.72537 12.2958 4.86955V12.2066C12.2958 12.3508 12.1786 12.4679 12.0345 12.4679C11.8655 12.4679 11.7304 12.6031 11.7304 12.7721C11.7304 12.941 11.8655 13.0762 12.0345 13.0762C12.5143 13.0762 12.904 12.6865 12.904 12.2066V4.86955C12.904 4.38972 12.5143 4 12.0345 4Z" fill="white"/>
                            </svg>
                        </button>

                        <button type="button" th:data-index="${collectionStat.index}" th:data-hashid="${collection.id}" class="crud_edit" data-toggle="modal" th:data-target="|#collection_created_${item}|" data-target="#collection_created_1">
                            <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="18" height="18" fill="#165ADF"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M14.0385 6.40966L11.5908 3.96198L5.38117 10.1716L4.85667 13.1438L7.82885 12.6193L14.0385 6.40966ZM12.0804 3.47245C11.81 3.20208 11.3716 3.20208 11.1013 3.47245L4.89164 9.68209C4.7912 9.78253 4.72408 9.91144 4.6994 10.0513L4.1749 13.0235C4.09137 13.4968 4.50369 13.9091 4.97698 13.8256L7.94916 13.3011C8.08904 13.2764 8.21794 13.2093 8.31838 13.1088L14.528 6.89919C14.7984 6.62883 14.7984 6.19049 14.528 5.92012L12.0804 3.47245Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.40889 13.0183L4.98214 9.59159L5.47168 9.10205L8.89843 12.5288L8.40889 13.0183Z" fill="white"/>
                            </svg>
                        </button>

                        <button type="button" th:data-index="${collectionStat.index}" th:data-hashid="${collection.id}" class="crud_delate">
                            <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="18" height="18" fill="#EF2121"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.52611 5.21045V13.4209C5.52611 13.9442 5.95026 14.3683 6.47347 14.3683H11.5261C12.0493 14.3683 12.4735 13.9442 12.4735 13.4209V5.21045H13.105V13.4209C13.105 14.293 12.3981 14.9999 11.5261 14.9999H6.47347C5.60145 14.9999 4.89453 14.293 4.89453 13.4209V5.21045H5.52611Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M14.0525 5.52611H3.94727V4.89453H14.0525V5.52611Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.78906 3.94737C6.78906 3.42415 7.21321 3 7.73643 3H10.2627C10.786 3 11.2101 3.42415 11.2101 3.94737V5.21052H10.5785V3.94737C10.5785 3.77296 10.4371 3.63158 10.2627 3.63158H7.73643C7.56202 3.63158 7.42064 3.77296 7.42064 3.94737V5.21052H6.78906V3.94737Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.68408 12.1579V7.73682H9.31566V12.1579H8.68408Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5786 12.1579V7.73682H11.2102V12.1579H10.5786Z" fill="white"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.78906 12.1579V7.73682H7.42064V12.1579H6.78906Z" fill="white"/>
                            </svg>
                        </button>
                    </div>

                    <div class="collection_info">
                        <p class="collection_created_name">
                            <a href="#" th:text="${collection.title}">Гардероб Меладзу и Ротару</a>
                        </p>
                        <div class="count_val_coll"><p>Товаров:</p><span class="count_prod_coll" th:text="${collection.productsTotal}">120</span></div>
                    </div>

                    <div class="modal fade modal-big" th:id="|collection_created_${item}|" id="collection_created_1">
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content">

                                <!-- Modal Header -->
                                <div class="modal-header" th:data-setsindex="${collectionStat.index}">
                                    <div class="popup_header">
                                        <h4 class="modal-title sets_heading_value" th:id="|modal-title_${collection.id}|">Подборка<small>[[${collection.productsTotal}]] товаров</small></h4>
                                        <label class="reset_all">
                                            <button class="resetCopy">Очистить</button>
                                        </label>
                                    </div>
                                    <button type="button" class="close g-close_modale" data-dismiss="modal">&times;</button>
                                </div>

                                <!-- Modal body -->
                                <div class="modal-body">
                                    <form action="#" method="#">
                                        <div class="banner_right_bar collaction_bar">
                                            <div class="collaction_description">
                                                <label>Название (будет указано в каталоге)
                                                    <input th:data-index="${collectionStat.index}" th:data-hashid="${collection.id}" type="text" class="name_collaction padborki-input" th:value="${collection.title}" th:name="|name_collaction_${item}|" name="name_collaction">
                                                </label>

                                                <div th:id="|all_product_elements_${collection.id}|" id="all_product_elements" class="sets_all_product_elements">
                                                <th:block th:each="product: ${collection.products}" >
                                                    <div th:id="|g-product_${collection.id}-${productStat.index}|" th:data-id="|product_element_${productStat.index}|" data-id="product_element_1" class="g-product">
                                                        <img th:src="${staticResourceBalancer.transformImage(product.image)}" src="img/image.png" th:alt="|product ${product.id}|" alt="product 1" style="height: 40px;">
                                                        <label th:id="|product_element_text_${collection.id}-${productStat.index}|" class="product_element_text_1" th:text="|${product.category.name} ${product.brand.name}|">Кеды Valentino</label>
                                                        <button th:data-index="${collectionStat.index}" th:data-hashid="${collection.id}" type="button" th:id="|delete_product_btn_${collection.id}-${productStat.index}|" id="delete_product_btn" th:data-product-index="${productStat.index}" class="sets_product delate_product_btn close" aria-label="Close">
                                                            <span aria-hidden="true">&times;</span>
                                                        </button>
                                                    </div>
                                                </th:block>
                                                </div>

                                                <div class="g-add_product" data-toggle="modal" th:data-target="|#add-collection_link_${collection.id}|" data-target="#add-collection_link_2">
                                                    <button type="button" th:id="|add-collection_name${item}|" id="add-collection_name" class="g-add_ic">+</button>
                                                    <label th:for="|add-collection_name${item}|" for="add-collection_name" th:id="|collaction_label_${collection.id}|" id="add-collaction_label_2">Добавьте товар</label>
                                                </div>
                                                <div class="modal fade" th:id="|add-collection_link_${collection.id}|" id="add-collection_link_2">
                                                    <div class="modal-dialog modal-dialog-centered modal-sm">
                                                        <div class="modal-content">

                                                            <!-- Modal body -->
                                                            <div class="modal-body g-second--modal">
                                                                <label>Вставьте ссылку на товар
                                                                    <input type="text" th:name="|add_product_link_${item}|" name="add_product_link_2" th:id="|add_link_input_${collection.id}|" id="add_link_input_2" class="padborki-link-input padborki-input">
                                                                </label>
                                                            </div>

                                                            <!-- Modal footer -->
                                                            <div class="modal-footer">
                                                                <button th:data-index="${collectionStat.index}" th:data-hashid="${collection.id}" type="button" class="btn btn-primary padborki-link-succsess  add_link_toggle" th:data-id="|#collection_link_${item}|" data-id="#collection_link_1" >
                                                                    Добавить товар
                                                                </button>
                                                            </div>
                                                            <div class="alert alert-danger add_link_error w-100" style="display:none; height: auto;width: auto;font-size: 12px;">
                                                                <strong>Error!</strong> Failed msg
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Modal footer -->
                                        <div class="modal-footer g-modal_footer_col">
                                            <input th:data-index="${collectionStat.index}" th:data-hashid="${collection.id}" type="submit" class="btn btn-primary padborki-succsess" value="Сохранить">
                                            <input type="reset" class="resetPopup">
                                        </div>
                                    </form>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </th:block>

            <div class="g-grid_item" th:with="id='new', item=${0}">
                <div th:data-index="${item}" th:data-hashid="${id}" class="image-item_all img-background item_collaction" data-toggle="modal" th:data-target="|#collection_created_${item}|" data-target="#collection_new">
                    <span class="g-image_text">Добавьте подборку</span>
                </div>

                <div class="modal fade modal-big" th:id="|collection_created_${item}|" id="collection_new">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content">
                            <!-- Modal Header -->
                            <div class="modal-header" data-setsindex="0">
                                <div class="popup_header">
                                    <h4 class="modal-title sets_heading_value" th:id="|modal-title_${id}|">Подборка<small>0 товаров</small></h4>
                                    <label class="reset_all">
                                        <button class="resetCopy">Очистить</button>
                                    </label>
                                </div>
                                <button type="button" class="close g-close_modale" data-dismiss="modal">&times;</button>
                            </div>

                            <!-- Modal body -->
                            <div class="modal-body">

                                <form action="#" method="#">
                                    <div class="banner_right_bar collaction_bar">
                                        <div class="collaction_description">
                                            <label>Название (будет указано в каталоге)
                                                <input th:data-index="${0}" th:data-hashid="${id}" type="text" class="name_collaction padborki-input" th:name="|name_collaction_${item}|" name="name_collaction">
                                            </label>

                                            <div th:id="|all_product_elements_${id}|" id="all_product_elements"  class="sets_all_product_elements">
                                            </div>

                                            <div class="g-add_product" data-toggle="modal" th:data-target="|#add-collection_link_${id}|" data-target="#add-collection_link_2">
                                                <button type="button" th:id="|add-collection_name${item}|" id="add-collection_name" class="g-add_ic">+</button>
                                                <label th:for="|add-collection_name${item}|" for="add-collection_name" th:id="|collaction_label_${id}|" id="add-collaction_label_2">Добавьте товар</label>
                                            </div>
                                            <div class="g-add_product" data-toggle="modal" data-target="#add-multiply-collection_link_new">
                                                <button type="button"  id="add-multiply-collection_name_new" class="g-add_ic">+</button>
                                                <label  for="add-multiply-collection_name_new" id="add-collaction_label_3">Добавьте несколько товаров</label>
                                            </div>

                                            <div class="modal fade" th:id="|add-collection_link_${id}|" id="add-collection_link_2">
                                                <div class="modal-dialog modal-dialog-centered modal-sm">
                                                    <div class="modal-content">

                                                        <!-- Modal body -->
                                                        <div class="modal-body g-second--modal">
                                                            <label>Вставьте ссылку на товар
                                                                <input type="text" th:name="|add_product_link_${item}|" name="add_product_link_2" th:id="|add_link_input_${id}|" id="add_link_input_2" class="padborki-link-input padborki-input">
                                                            </label>
                                                        </div>

                                                        <!-- Modal footer -->
                                                        <div class="modal-footer">
                                                            <button data-index="0" th:data-hashid="${id}" type="button" class="btn btn-primary padborki-link-succsess  add_link_toggle" th:data-id="|#collection_link_${item}|" data-id="#collection_link_1" >
                                                                Добавить товар
                                                            </button>
                                                        </div>
                                                        <div class="alert alert-danger add_link_error w-100" style="display:none; height: auto;width: auto;font-size: 12px;">
                                                            <strong>Error!</strong> Failed msg
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="modal fade" id="add-multiply-collection_link_new">
                                                <div class="modal-dialog modal-dialog-centered modal-sm">
                                                    <div class="modal-content">

                                                        <!-- Modal body -->
                                                        <div class="modal-body g-second--modal">
                                                            <label>Вставьте ссылки на товары
                                                                <input type="text" th:name="|add_product_link_${item}|" name="add_product_link_2" th:id="|add_link_input_${id}|" id="add_link_input_2" class="padborki-link-input padborki-input">
                                                            </label>
                                                        </div>

                                                        <!-- Modal footer -->
                                                        <div class="modal-footer">
                                                            <button data-index="0" th:data-hashid="${id}" type="button" class="btn btn-primary padborki-multiply-link-succsess  add_link_toggle" th:data-id="|#collection_link_${item}|" data-id="#collection_link_1" >
                                                                Добавить товар
                                                            </button>
                                                        </div>
                                                        <div class="alert alert-danger add_link_error w-100" style="display:none; height: auto;width: auto;font-size: 12px;">
                                                            <strong>Error!</strong> Failed msg
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Modal footer -->
                                    <div class="modal-footer g-modal_footer_col">
                                        <input th:data-index="${0}" th:data-hashid="${id}" type="submit" class="btn btn-primary padborki-succsess" value="Сохранить">
                                        <input type="reset" class="resetPopup">
                                    </div>
                                </form>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
<!--</div>-->

</html>