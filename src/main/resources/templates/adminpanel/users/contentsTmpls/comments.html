<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html">

<div class="tabs__content" data-id="5" th:data-context="${T(su.reddot.presentation.adminpanel.users.UserTab).COMMENTS.name().toLowerCase()}"
     th:if="${activeTab == T(su.reddot.presentation.adminpanel.users.UserTab).COMMENTS}">
    <h2 class="tabs__title">
        <span class="tabs__title-text">[[${commentsPage.totalItemsCount}]] комметария</span>
    </h2>
    <div class="table table--comments table--margin-top-20" data-id="5">
        <div class="table__row table__row--head">
            <div class="table__cell table__cell--width-95">
                <span class="table__text table__text--font-size-12">ID Товара</span><span
                    class="icon icon--triangle-down icon--triangle-down-table-head"></span>
            </div>
            <div class="table__cell table__cell-width--95">
                <span class="table__text table__text--font-size-12">Фото</span>
            </div>
            <div class="table__cell">
                <span class="table__text table__text--font-size-12">Комментьарий</span>
            </div>
            <div class="table__cell table__cell--width-110">
                <span class="table__text table__text--font-size-12">Дата</span><span
                    class="icon icon--triangle-down icon--triangle-down-table-head"></span>
            </div>
        </div>
        <div class="table__row" th:each="comment : ${commentsPage.items}"
             th:with="commentId=${comment.id},productId=${comment.productId}">
            <div class="table__delete-row">
                <div class="table__delete-row-wrap">
                    <div class="table__text table__text--font-size-16">
                        Уверены, что хотите удалить комментарий пользователя?
                    </div>
                    <div class="table__wrap table__wrap--accept-btns">
                        <button class="button button--show button--comments button--accept" th:data-action="${'/adminpanel/comments/' + commentId}">
                            <span class="icon icon--save"></span>
                        </button>
                        <button class="button button--show button--comments button--decline">
                            <span class="icon icon--cancel"></span>
                        </button>
                    </div>
                </div>
            </div>
            <div class="table__cell table__cell--width-95">
                <a th:href="${'/adminpanel/products/edit?productId=' + productId}">
                    <span class="table__text table__text--font-size-12 table__text--font-size-12" th:text="${productId}">123456</span>
                </a>
            </div>
            <div class="table__cell table__cell-width--95">
                <a th:href="${'/products/' + productId}">
                    <img alt="photo" class="table__img table__img--product-photo"
                         src="img/product_photo.png"  th:src="${comment.imagePreview}"/>
                </a>
            </div>
            <div
                    class="table__cell table__cell--flex table__cell--padding-right-100 table__cell--align-left table__cell-vertical-align-middle">
                <form action="#" class="form form--comment" method="POST">
                    <div class="form__control">
                  <textarea class="form__textarea form__textarea--comment" name="comment" readonly="readonly" th:text="${comment.text}">
                    Здравствуйте, сделайте пожалуйста скидку!
                  </textarea>
                        <input class="temp-storage" type="hidden"/>
                    </div>
                </form>
                <div class="table__cell-block">
                    <button class="button button--show-on-hover button--comments button--edit-comment">
                        <span class="icon icon--edit"></span></button>
                    <button class="button button--show-on-hover button--comments button--delete-comment">
                        <span class="icon icon--delete"></span></button>
                    <button class="button button--comments button--save-comment" th:data-comment-id="${commentId}" data-action="/adminpanel/comments/edit">
                        <span class="icon icon--save"></span>
                    </button>
                    <button class="button button--comments button--cancel-comment"><span class="icon icon--cancel"></span>
                    </button>
                </div>
            </div>
            <div class="table__cell table__cell--width-110">
                <div class="table__text table__text--font-size-12" th:include="adminpanel/common/date_time :: date_time_russian_month (${comment.publishedAtTime})">
                    2 декабря в 23:46
                </div>
            </div>
        </div>
    </div>
    <div th:if="${pageNavigator}" th:replace="adminpanel/common/page_navigator :: page_navigator (${pageNavigator})"></div>
</div>

</html>