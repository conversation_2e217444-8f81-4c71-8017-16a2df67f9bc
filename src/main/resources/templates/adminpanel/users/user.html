<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{adminpanel/layout/layout}"
      th:with="pageName='users'">
<head>
  <meta charset="UTF-8">
  <title>Администрирование | Пользователи</title>
  <link th:href="@{/adminpanel/users.css}" href="/adminpanel/users.css" rel="stylesheet" />
  <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
</head>
<body>
<div class="content" layout:fragment="content">

  <div class="content__nav-row content__nav-row--with-line">
    <a class="content__link content__link--back" href="/adminpanel/users">
      <span class="icon icon--arrow-left icon--margin-right-9"></span>
      <span class="content__text">Все пользователи</span>
    </a>
  </div>
  <div class="title title--flex title--space-between title--margin-top-40">
    <div class="title__block title__block--flex">
      <a href="#" th:href="@{/profile/{id}(id=${userView.id})}" class="title__block title__block--flex">
      <img alt="photo" class="title__img title__img--avatar" src="img/ava.png" th:src="${userView.avatarPath}" onError="this.onerror=null;this.src='/images/no-photo.jpg';"/>
      <div class="title__sub-block title__sub-block--margin-left-14">
        <h1 class="title__text" th:inline="text">
          [[${userView.nickName}]]
          <span th:if="${userView.isDeleted}">[Пользователь удалён [[${userView.deleteTime}]]]</span>
          <span th:if="${userView.isTrusted}" class="icon icon--approve icon--margin-left-8 icon--font-size-18 icon--top-1"></span>
        </h1>
      </div>
      </a>
      <div class="table__text table__text--font-size-12 table__text--ellipsis is-baned-show" style="color:red; cursor: pointer; text-decoration-line: underline;font-size: 16px;padding-left: 15px;display: none;">Нарушитель</div>
    </div>
    <div class="title__block title__block--flex">
      <div class="title__sub-block title__sub-block--margin-left-62">
        <div class="title__text title__text--font-size-21 title__text--align-right">
          [[${userView.subscribersCount}]]
        </div>
        <div class="title__text title__text--font-size-13 title__text--align-right title__text--nargin-top-5">
          Подписчики
        </div>
      </div>
      <div class="title__sub-block title__sub-block--margin-left-62">
        <div class="title__text title__text--font-size-21 title__text--align-right">
          [[${userView.userSubscriptionsCount}]]
        </div>
        <div class="title__text title__text--font-size-13 title__text--align-right title__text--nargin-top-5">
          Подписки
        </div>
      </div>
      <div class="title__sub-block title__sub-block--margin-left-62">
        <div class="title__text title__text--font-size-21 title__text--align-right">
          [[${userView.likedProductsCount}]]
        </div>
        <div class="title__text title__text--font-size-13 title__text--align-right title__text--nargin-top-5">
          Wish list
        </div>
      </div>
      <div class="title__sub-block title__sub-block--margin-left-62">
        <div class="title__text title__text--font-size-21 title__text--align-right">
          [[${userView.priceSubscriptionsCount}]]
        </div>
        <div class="title__text title__text--font-size-13 title__text--align-right title__text--nargin-top-5">
          Снижение цены
        </div>
      </div>
    </div>
  </div>

  <div class="tabs" id="user-profile-tabs">

    <ul class="tabs__nav">
      <li class="tabs__nav-item">
        <a class="tabs__link tabs__link--nav tabs__link--font-size-20" th:classappend="${activeTab == T(su.reddot.presentation.adminpanel.users.UserTab).PROFILE} ? 'tabs__link--active' : _"
           th:href="${'/adminpanel/users/'+userView.id+'?tab=' + T(su.reddot.presentation.adminpanel.users.UserTab).PROFILE.name().toLowerCase()}"
           id="1">Профиль</a>
      </li>
      <li class="tabs__nav-item">
        <a class="tabs__link tabs__link--nav tabs__link--font-size-20" th:classappend="${activeTab == T(su.reddot.presentation.adminpanel.users.UserTab).PRODUCTS} ? 'tabs__link--active' : _"
           th:href="${'/adminpanel/users/'+userView.id+'?tab=' + T(su.reddot.presentation.adminpanel.users.UserTab).PRODUCTS.name().toLowerCase()}"
           id="2">Товары<sup class="tabs__amount" th:text="${userView.productsCount}">15</sup></a>
      </li>
      <li class="tabs__nav-item">
        <a class="tabs__link tabs__link--nav tabs__link--font-size-20" th:classappend="${activeTab == T(su.reddot.presentation.adminpanel.users.UserTab).ORDERS} ? 'tabs__link--active' : _"
           th:href="${'/adminpanel/users/'+userView.id+'?tab=' + T(su.reddot.presentation.adminpanel.users.UserTab).ORDERS.name().toLowerCase()}"
           id="3">Заказы<sup class="tabs__amount" th:text="${userView.ordersCount}">3</sup></a>
      </li>
      <li class="tabs__nav-item">
        <a class="tabs__link tabs__link--nav tabs__link--font-size-20" th:classappend="${activeTab == T(su.reddot.presentation.adminpanel.users.UserTab).SALES} ? 'tabs__link--active' : _"
           th:href="${'/adminpanel/users/'+userView.id+'?tab=' + T(su.reddot.presentation.adminpanel.users.UserTab).SALES.name().toLowerCase()}"
           id="4">Продажи<sup class="tabs__amount" th:text="${userView.salesCount}">22</sup></a>
      </li>
      <li class="tabs__nav-item">
        <a class="tabs__link tabs__link--nav tabs__link--font-size-20" th:classappend="${activeTab == T(su.reddot.presentation.adminpanel.users.UserTab).COMMENTS} ? 'tabs__link--active' : _"
           th:href="${'/adminpanel/users/'+userView.id+'?tab=' + T(su.reddot.presentation.adminpanel.users.UserTab).COMMENTS.name().toLowerCase()}"
           id="5">Комментарии<sup class="tabs__amount" th:text="${userView.visibleCommentsCount}">12</sup></a>
      </li>
      <li class="tabs__nav-item">
        <a class="tabs__link tabs__link--nav tabs__link--font-size-20"
           target="_blank" rel="noopener noreferrer"
           th:href="${config.adminpanelBaseUrl + '/requests/sale?ownerUserIds[]=' + userView.id}"
           id="6">Заявки</a>
      </li>
      <li class="tabs__nav-item tabs__nav-item--fake-nav">
        <button class="button button--regular button--hide" id="save-user-profile_users" type="button">
          <span class="button__text">Сохранить</span>
        </button>
      </li>
    </ul>

    <div class="tabs__content-wrap">
        <div th:replace="adminpanel/users/contentsTmpls/profile"></div>

        <div th:replace="adminpanel/users/contentsTmpls/products"></div>

        <div th:replace="adminpanel/users/contentsTmpls/orders"></div>

        <div th:replace="adminpanel/users/contentsTmpls/sales"></div>

        <div th:replace="adminpanel/users/contentsTmpls/comments"></div>
    </div>

  </div>
    <div layout:fragment="custom-scripts">
        <script th:src="@{/adminpanel/js/messages.js}" src="/adminpanel/js/messages.js"></script>
        <script th:src="@{/adminpanel/js/page_navigator.js}" src="/adminpanel/js/page_navigator.js"></script>
        <script th:src="@{/adminpanel/js/common_tools.js}" src="/adminpanel/js/common_tools.js"></script>
        <script th:src="@{/adminpanel/js/filters.js}" src="/adminpanel/js/filters.js"></script>
        <script th:src="@{/adminpanel/js/users.js}" src="/adminpanel/js/users.js"></script>
<!--        <script th:src="@{/adminpanel/js/users.js}" src="/adminpanel/js/users.js"></script>-->


      <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
      <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

        <script th:src="@{/adminpanel/js/user/user_ban_profile.js}" src="/adminpanel/js/user/user_ban_profile.js"></script>
        <script th:src="@{/adminpanel/js/user/user_edit.js}" src="/adminpanel/js/user/user_edit.js"></script>
    </div>
</div>
</body>
</html>
