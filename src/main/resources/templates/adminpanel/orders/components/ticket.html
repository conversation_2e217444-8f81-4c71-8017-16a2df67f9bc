<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<!--<div th:replace="adminpanel/orders/components/edit_address_form2 :: editAddressForm(${order})" />-->

<th:block th:fragment="createOrderTicket(orderId)">
    <div class="popup__title">
        <span class="popup__title-text">Оставить комментарий</span>
    </div>
    <div class="popup__row  popup__row--margin-top-16">
        <div class="popup__text popup__text--grey">
            Комменатрий
        </div>
        <div class="popup__form">
            <form th:data-create-order-ticket="${orderId}">
                <textarea class="form__textarea--create-order-comment" name="comment"></textarea>
                <div class="popup__btns">
                    <button class="button button--fully-stripped button--close-popup" type="button">
                        <span class="button__text button__text--blue button__text--font-size-13">Отмена</span>
                    </button>
                    <button class="button  button--padding-side-12 button--height-30 button--blue" type="submit">
                        <span class="button__text button__text--font-size-13">Сохранить</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</th:block>

<th:block th:fragment="ticketComment(tickets)" th:if="${tickets}">
    <div class="popup__title">
        <span class="popup__title-text font_20">Комменатрий</span>
    </div>
    <div class="popup__row  popup__row--margin-top-24" th:each="ticket : ${tickets}">
        <div class="popup__text" th:text="${ticket.comment}">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
            dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex
            ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat
            nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit
            anim id est laborum.
        </div>
        <div class="flex_block__new data__name">
            <div>
                <span>Дата:</span>
                <th:block th:text="${ticket.createTime}">2 октября в 23:45</th:block>
            </div>
            <div>
                <span>Автор:</span>
                <th:block th:text="${ticket.author.nickname}">Name</th:block>
            </div>
        </div>
        <div class="popup__btns">
            <a th:if="${!ticket.isClosed()}" class="button__text button__text--blue button__text--font-size-12" href="javascript:void(0);" th:data-close-order-ticket="${ticket.id}">Закрыть коммент</a>
        </div>
    </div>
</th:block>
</html>