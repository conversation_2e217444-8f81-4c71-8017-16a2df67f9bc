CREATE TABLE IF NOT EXISTS user_balance_change (
    id BIGSERIAL PRIMARY KEY,

    user_id BIGINT NOT NULL REFERENCES public.user(id) ON DELETE CASCADE,
    operation_type TEXT NOT NULL,
    operation_mode TEXT NOT NULL,
    operation_id TEXT NOT NULL,
    operation_time TIMESTAMP NOT NULL,
    amount NUMERIC(20, 8) NOT NULL,
    comment TEXT NOT NULL
);

-- To allow only one operation with same ID
CREATE UNIQUE INDEX IF NOT EXISTS user_balance_change_operation_id_uindex ON user_balance_change(operation_id);
-- To load fast operations of user
CREATE INDEX IF NOT EXISTS user_balance_change_user_id ON user_balance_change(user_id);

ALTER TABLE user_balance_change OWNER TO oskelly;

CREATE TABLE IF NOT EXISTS user_balance_change_aud (
    id bigint NOT NULL,
    rev INTEGER NOT NULL,
    revtype SMALLINT,

    user_id BIGINT REFERENCES public.user(id) ON DELETE CASCADE,
    operation_type TEXT,
    operation_mode TEXT,
    operation_id TEXT,
    operation_time TIMESTAMP,
    amount NUMERIC(20, 8),
    comment TEXT
);

ALTER TABLE user_balance_change_aud OWNER TO oskelly;

ALTER TABLE user_balance_change_aud DROP CONSTRAINT IF EXISTS user_balance_change_aud_pkey;
ALTER TABLE ONLY user_balance_change_aud
    ADD CONSTRAINT user_balance_change_aud_pkey
    PRIMARY KEY (id, rev);

ALTER TABLE user_balance_change_aud DROP CONSTRAINT IF EXISTS user_balance_change_aud_rev_fkey;
ALTER TABLE ONLY user_balance_change_aud
    ADD CONSTRAINT user_balance_change_aud_rev_fkey
    FOREIGN KEY (rev)
    REFERENCES public.revinfo(rev);