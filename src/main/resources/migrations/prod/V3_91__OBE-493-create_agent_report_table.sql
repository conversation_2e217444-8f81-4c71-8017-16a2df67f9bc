CREATE TABLE IF NOT EXISTS agent_report (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT REFERENCES "order"(id) UNIQUE,
    create_time TIMESTAMP WITH TIME ZONE NOT NULL,
    user_type TEXT NOT NULL DEFAULT 'SIMPLE_USER',
    name TEXT,
    first_name TEXT,
    second_name TEXT,
    patronymic TEXT,
    bik TEXT,
    inn TEXT,
    payment_account TEXT,
    number_contract TEXT,
    date_contract TIMESTAMP WITH TIME ZONE NOT NULL,
    is_confirmed BOOLEAN
);

ALTER TABLE "order"
    ADD COLUMN agent_report_id BIGINT REFERENCES agent_report(id) UNIQUE;