DO $$
DECLARE
    OSOURCEA_USER_ID BIGINT = 231441; -- This ID is temporary, will change in next steps
BEGIN
    IF NOT EXISTS (SELECT * FROM "user" u WHERE u.id = OSOURCEA_USER_ID) THEN -- TEST DB
        OSOURCEA_USER_ID = 24;
    END IF;

    INSERT INTO order_source_info(id, "type", "name", master_user_id, display_name, is_hidden, is_marketplace_location)
    VALUES(10, 'BOUTIQUE', 'Boutique-2', OSOURCEA_USER_ID, 'Бутик 2', false, false)
    ON CONFLICT DO NOTHING;

    INSERT INTO stock (onec_uuid, stock_title, order_source_info_id)
    VALUES('1d3a227a-8ada-11ee-bd77-44a842327aae', 'СТОЛЕШНИКОВ 5 этаж', 1)
    ON CONFLICT DO NOTHING;

    INSERT INTO stock (onec_uuid, stock_title, order_source_info_id)
    VALUES('9720bdba-8ebd-11ee-9a27-90b11c3d7a47', 'ОФИС ТРАНЗИТ ИЗ БУТИКОВ', 4)
    ON CONFLICT DO NOTHING;

    INSERT INTO stock (onec_uuid, stock_title, order_source_info_id)
    VALUES('979a74e1-8ebc-11ee-9a27-90b11c3d7a47', 'КУЗНЕЦКИЙ МОСТ Склад бутика', 10)
    ON CONFLICT DO NOTHING;

    INSERT INTO stock (onec_uuid, stock_title, order_source_info_id)
    VALUES('e4ccdcfc-8ebd-11ee-9a27-90b11c3d7a47', 'КУЗНЕЦКИЙ МОСТ Отложенные товары (бутик)', 10)
    ON CONFLICT DO NOTHING;

    INSERT INTO stock (onec_uuid, stock_title, order_source_info_id)
    VALUES('01c091d8-8ebd-11ee-9a27-90b11c3d7a47', 'КУЗНЕЦКИЙ МОСТ БУТИК ТРАНЗИТ', 4)
    ON CONFLICT DO NOTHING;

END $$;