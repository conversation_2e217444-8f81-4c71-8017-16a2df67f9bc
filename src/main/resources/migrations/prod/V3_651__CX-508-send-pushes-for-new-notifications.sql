insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'QualityControlStartedBuyerNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'AuthenticationStartedBuyerNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'DefectReconciliationStartedBuyerNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderExpertisePassedWithDefectWithoutDiscountNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderExpertisePassedWithDefectMultipleNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderExpertiseFakeNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderExpertiseImpossibleDetermineAuthenticityNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderExpertiseReconciliationFailedNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderExpertiseFailedMultipleNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'QualityControlStartedSellerNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'AuthenticationStartedSellerNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'DefectReconciliationStartedSellerNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'SaleExpertisePassedWithDefectWithoutDiscountNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'SaleExpertisePassedWithDefectMultipleNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'SaleExpertiseFakeNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'SaleExpertiseImpossibleDetermineAuthenticityNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'SaleExpertiseReconciliationFailedNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'SaleExpertiseFailedMultipleNotification');

insert into notification_group_dtype_binding(notification_group_id, notification_dtype)
values(5, 'OrderCancelledByBuyerNotification');
