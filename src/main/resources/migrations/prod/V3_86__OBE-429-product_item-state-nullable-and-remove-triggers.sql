DROP TRIGGER IF EXISTS on_product_item_update ON public.product_item CASCADE;
DROP TRIGGER IF EXISTS on_new_product_item ON public.product_item CASCADE;
DROP FUNCTION IF EXISTS public.update_state_history();
ALTER TABLE public.product_item ALTER COLUMN state DROP NOT NULL;

CREATE OR REPLACE FUNCTION public.reset_schema_version_3_86() RETURNS void LANGUAGE SQL AS $_$
  CREATE OR REPLACE FUNCTION public.update_state_history() RETURNS trigger
      LANGUAGE plpgsql
      AS $$
  BEGIN
    INSERT INTO state_change (product_item_id, new_state, at)
    VALUES (NEW.id, NEW.state, now());
    RETURN NEW;
  END;
  $$;
  ALTER FUNCTION public.update_state_history() OWNER TO oskelly;
  CREATE TRIGGER on_new_product_item BEFORE INSERT ON public.product_item FOR EACH ROW EXECUTE PROCEDURE public.update_state_history();
	CREATE TRIGGER on_product_item_update AFTER UPDATE OF state ON public.product_item FOR EACH ROW WHEN ((new.state <> old.state)) EXECUTE PROCEDURE public.update_state_history();
	DELETE FROM public.schema_version WHERE version='3.86';
	DROP FUNCTION public.reset_schema_version_3_86();
$_$;
ALTER FUNCTION public.reset_schema_version_3_86() OWNER TO oskelly;