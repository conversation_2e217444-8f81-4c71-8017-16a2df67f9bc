ALTER TABLE category
		ADD COLUMN IF NOT EXISTS default_size_type TEXT;

UPDATE category
    SET default_size_type = 'IT' WHERE url_name LIKE 'zhenskoe/odezhda%';

UPDATE category
    SET default_size_type = 'JEANS' WHERE url_name LIKE 'zhenskoe/odezhda/dzhinsy%';

UPDATE category
    SET default_size_type = 'IT' WHERE url_name LIKE 'zhenskoe/obuv%';

UPDATE category
    SET default_size_type = 'RU' WHERE url_name LIKE 'zhenskoe/odezhda/plyazhnaya-odezhda/kupalniki%';

UPDATE category
    SET default_size_type = 'RU' WHERE url_name LIKE 'zhenskoe/aksessuary/ukrasheniya/kolca%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'zhenskoe/aksessuary/perchatki%';

UPDATE category
    SET default_size_type = 'EU' WHERE url_name LIKE 'zhenskoe/aksessuary/remni%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'zhenskoe/aksessuary/golovnye-ubory%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'muzhskoe/odezhda%';

UPDATE category
    SET default_size_type = 'JEANS' WHERE url_name LIKE 'muzhskoe/odezhda/dzhinsy%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'muzhskoe/odezhda/rubashki%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'muzhskoe/obuv%';

UPDATE category
    SET default_size_type = 'EU' WHERE url_name LIKE 'muzhskoe/aksessuary/remni%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'muzhskoe/aksessuary/perchatki%';

UPDATE category
    SET default_size_type = 'RU' WHERE url_name LIKE 'muzhskoe/aksessuary/ukrasheniya%';

UPDATE category
    SET default_size_type = 'INT' WHERE url_name LIKE 'muzhskoe/aksessuary/golovnye-ubory%';

UPDATE category
    SET default_size_type = 'AGE' WHERE url_name LIKE 'detskoe/%0-3%';

UPDATE category
    SET default_size_type = 'AGE' WHERE url_name LIKE 'detskoe/%4-14%';

UPDATE category
    SET default_size_type = 'EU' WHERE url_name LIKE 'detskoe/%obuv%';