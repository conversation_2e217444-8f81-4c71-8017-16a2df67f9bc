--SPAIN
INSERT INTO duty_calculator_config (pickup_country_id,
                                    delivery_country_id,
                                    dtype,
                                    params,
                                    params_sample,
                                    is_active)
VALUES ((SELECT id FROM country WHERE iso_code_alpha2 = 'ES'),
        (SELECT id FROM country WHERE iso_code_alpha2 = 'RU'),
        'RU_CUSTOM_DUTY',
        '{"customFreeAmountLimits":200,"customFreeCurrencyCode":"EUR","customPercent":15}',
        '{"customFreeAmountLimits":200,"customFreeCurrencyCode":"EUR","customPercent":15}',
        false)
ON CONFLICT DO NOTHING;

--CZECHIA
INSERT INTO duty_calculator_config (pickup_country_id,
                                    delivery_country_id,
                                    dtype,
                                    params,
                                    params_sample,
                                    is_active)
VALUES ((SELECT id FROM country WHERE iso_code_alpha2 = 'CZ'),
        (SELECT id FROM country WHERE iso_code_alpha2 = 'RU'),
        'RU_CUSTOM_DUTY',
        '{"customFreeAmountLimits":200,"customFreeCurrencyCode":"EUR","customPercent":15}',
        '{"customFreeAmountLimits":200,"customFreeCurrencyCode":"EUR","customPercent":15}',
        false)
ON CONFLICT DO NOTHING;

--CROATIA
INSERT INTO duty_calculator_config (pickup_country_id,
                                    delivery_country_id,
                                    dtype,
                                    params,
                                    params_sample,
                                    is_active)
VALUES ((SELECT id FROM country WHERE iso_code_alpha2 = 'HR'),
        (SELECT id FROM country WHERE iso_code_alpha2 = 'RU'),
        'RU_CUSTOM_DUTY',
        '{"customFreeAmountLimits":200,"customFreeCurrencyCode":"EUR","customPercent":15}',
        '{"customFreeAmountLimits":200,"customFreeCurrencyCode":"EUR","customPercent":15}',
        false)
ON CONFLICT DO NOTHING;
