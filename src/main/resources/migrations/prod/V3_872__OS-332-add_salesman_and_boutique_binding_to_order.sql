ALTER TABLE IF EXISTS "order"
    ADD COLUMN IF NOT EXISTS salesman_id BIGINT,
    ADD COLUMN IF NOT EXISTS boutique_id BIGINT,
    DROP CONSTRAINT IF EXISTS fk_order_salesman,
    DROP CONSTRAINT IF EXISTS fk_order_boutique_info,
    DROP CONSTRAINT IF EXISTS fk_order_product_item_location,
    ADD CONSTRAINT fk_order_salesman FOREIGN KEY (salesman_id) REFERENCES "user" (id),
    ADD CONSTRAINT fk_order_product_item_location FOREIGN KEY (boutique_id) REFERENCES oskelly.public.product_item_location (id);

ALTER TABLE IF EXISTS order_aud
    ADD COLUMN IF NOT EXISTS salesman_id BIGINT,
    ADD COLUMN IF NOT EXISTS boutique_id BIGINT;

