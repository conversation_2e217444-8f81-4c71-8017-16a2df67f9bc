-- обновим/поправим сиквенсы, чтобы не было ошибок при добавлении записей
select setval('commission_grid_id_seq', (SELECT max(id) FROM commission_grid));
select setval('commission_id_seq', (SELECT max(id) FROM commission));
select setval('config_param_id_seq', (SELECT max(id) FROM config_param));

WITH inserted_grid AS (
    -- добавим сетку новую с проверкой нет ли уже сетки с таким же названием
    INSERT INTO commission_grid (name, type)
        SELECT 'Комиссионная сетка для консьержа для продавцов' as name,
               'CUSTOM'                                         as type
        WHERE NOT EXISTS (SELECT * FROM commission_grid WHERE name = 'Комиссионная сетка для консьержа для продавцов')
        RETURNING id),
     inserted_comms AS (
     -- инсерт делается из кросс джойна с inserted_grid, который мб пустым, если сетка ранее существовала
     -- таким образом, новые комиссии не добавятся, если ранее не добавилась сетка
     INSERT INTO commission (public_price, value, boutique_value, commission_grid_id)
         SELECT comms.public_price, comms.value, comms.boutique_value, grid.id
         FROM inserted_grid grid
                  CROSS JOIN (SELECT 30000.00 as public_price, 0.28 as value, 0.07 as boutique_value
                              UNION
                              SELECT 100000.00 as public_price, 0.23 as value, 0.12 as boutique_value
                              UNION
                              SELECT 9999999999.00 as public_price, 0.19 as value, 0.16 as boutique_value) as comms
         RETURNING id)
-- ну и добавляем в конфиг ссылку на новую сетку, если она была создана
INSERT INTO config_param(name, value)
SELECT 'saleRequest.commission.grid.id' as name, grid.id::text as value
FROM inserted_grid grid
ON CONFLICT DO NOTHING;
