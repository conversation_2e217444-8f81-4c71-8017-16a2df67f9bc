create table if not exists price_discount_cycle
(
    id                 BIGSERIAL   NOT NULL PRIMARY KEY,
    product_id         BIGINT      NOT NULL REFERENCES product (id),
    start_moderator_id BIGINT REFERENCES "user" (id),
    stop_moderator_id  BIGINT REFERENCES "user" (id),
    create_date        timestamptz NOT NULL,
    stop_date          timestamptz,
    "count"            INT         NOT NULL DEFAULT 0,
    -- в том числе будет работать, как защита от случайного запуска шкедулера на 2 инстансах
    version            INT         NOT NULL DEFAULT 0
);

CREATE INDEX IF NOT EXISTS price_discount_cycle_product_idx on price_discount_cycle (product_id);
CREATE INDEX IF NOT EXISTS price_discount_cycle_create_date_idx on price_discount_cycle (create_date);
CREATE INDEX IF NOT EXISTS price_discount_cycle_stop_date_idx on price_discount_cycle (stop_date);
CREATE UNIQUE INDEX IF NOT EXISTS price_discount_cycle_active_idx
    ON price_discount_cycle (product_id)
    WHERE stop_date IS NULL;

CREATE TABLE IF NOT EXISTS price_discount
(
    id                                   BIGSERIAL   NOT NULL PRIMARY KEY,
    cycle_id                             BIGINT      NOT NULL REFERENCES price_discount_cycle (id),
    create_date                          timestamptz NOT NULL,
    old_current_price                    numeric     not null,
    new_current_price                    numeric,
    old_current_price_in_currency        numeric,
    new_current_price_in_currency        numeric,
    current_price_currency_id            BIGINT,
    old_current_price_without_commission numeric,
    new_current_price_without_commission numeric
);

CREATE INDEX IF NOT EXISTS price_discount_cycle_id_idx on price_discount (cycle_id);
CREATE INDEX IF NOT EXISTS price_discount_create_date_idx on price_discount (create_date);
