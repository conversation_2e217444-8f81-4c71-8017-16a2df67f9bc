DO $$
BEGIN
    IF NOT EXISTS (SELECT * FROM counterparty c WHERE user_id = 1 and card_ref_id = 'noonpayments-1.0/card') THEN
    	INSERT INTO counterparty (dtype,create_time,change_time,user_id,card_ref_id,card_number,card_bind_time,card_bind_bank,card_brand,card_expire_time)
		VALUES ('CardCounterparty',now(),now(),1,'noonpayments-1.0/card','4404 00** **** 4404',now(),'PAYOPTION:NOON_CARD','NOONPAYMENTS',now() + interval '25 year');
    END IF;
END $$;