CREATE TABLE IF NOT EXISTS user_trade_stat
(
    user_id        BIGINT PRIMARY KEY REFERENCES public.user(id),
    purchase_count INT NOT NULL default 0,
    sell_count     INT NOT NULL default 0,
    product_count  INT NOT NULL default 0,
    product_count_oskelly  INT NOT NULL default 0,
    product_count_seller  INT NOT NULL default 0
);

CREATE INDEX IF NOT EXISTS user_trade_stat_purchaseCount_idx on user_trade_stat (purchase_count);
CREATE INDEX IF NOT EXISTS user_trade_stat_sellCount_idx on user_trade_stat (sell_count);
CREATE INDEX IF NOT EXISTS user_trade_stat_productCount_idx on user_trade_stat (product_count);
CREATE INDEX IF NOT EXISTS user_trade_stat_productCountOskelly_idx on user_trade_stat (product_count_oskelly);
CREATE INDEX IF NOT EXISTS user_trade_stat_productCountSeller_idx on user_trade_stat (product_count_seller);