alter table order_bonuses_transaction alter column params type jsonb using params::jsonb;

alter table order_bonuses_transaction_aud alter column params type jsonb using params::jsonb;

insert into authority (id, name, type)
select (select max(id) + 1 from authority), 'BONUS_INTEGRATION ', 'MODERATOR'
where not exists (
		select id from authority where name = 'BONUS_INTEGRATION ' and type = 'MODERATOR');

