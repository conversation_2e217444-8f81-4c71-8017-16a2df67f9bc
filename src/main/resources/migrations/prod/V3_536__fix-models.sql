INSERT INTO product_model_brand_binding (model_id, brand_id) (
    SELECT
        (SELECT id FROM product_model WHERE name='Basket' LIMIT 1) as model_id,
        brand_id
    FROM product_model_brand_binding
    WHERE model_id=(SELECT id FROM product_model WHERE name='Baskets' LIMIT 1))
ON CONFLICT DO NOTHING;
UPDATE product SET product_model_id=(SELECT id FROM product_model WHERE name='Basket' LIMIT 1) WHERE product_model_id=(SELECT id FROM product_model WHERE name='Baskets' LIMIT 1);
DELETE FROM product_model_brand_binding WHERE model_id=(SELECT id FROM product_model WHERE name='Baskets' LIMIT 1);
DELETE FROM product_model WHERE name='Baskets';

INSERT INTO product_model_brand_binding (model_id, brand_id) (
    SELECT
        (SELECT id FROM product_model WHERE name='<PERSON>e' LIMIT 1) as model_id,
        brand_id
    FROM product_model_brand_binding
    WHERE model_id=(SELECT id FROM product_model WHERE name='Evelyn' LIMIT 1))
ON CONFLICT DO NOTHING;
UPDATE product SET product_model_id=(SELECT id FROM product_model WHERE name='Evelyne' LIMIT 1) WHERE product_model_id=(SELECT id FROM product_model WHERE name='Evelyn' LIMIT 1);
DELETE FROM product_model_brand_binding WHERE model_id=(SELECT id FROM product_model WHERE name='Evelyn' LIMIT 1);
DELETE FROM product_model WHERE name='Evelyn';