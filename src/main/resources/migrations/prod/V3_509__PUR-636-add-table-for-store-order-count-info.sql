CREATE TABLE IF NOT EXISTS user_segmentation_vector(
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE REFERENCES "user"(id),
    order_count INTEGER,
    last_update_time TIMESTAMP WITH TIME ZONE NOT NULL
);

CREATE TABLE IF NOT EXISTS user_segmentation_calculation_log(
    id BIGSERIAL PRIMARY KEY,
    max_order_change_time TIMESTAMP WITH TIME ZONE,
    last_check_time TIMESTAMP WITH TIME ZONE
);