ALTER TABLE public.product_reject_reason ADD COLUMN IF NOT EXISTS "old_price" NUMERIC(19,2);
ALTER TABLE public.product_reject_reason ADD COLUMN IF NOT EXISTS "old_description" TEXT;

CREATE OR REPLACE FUNCTION public.reset_schema_version_3_81() <PERSON><PERSON><PERSON><PERSON> void LANGUAGE SQL AS $$
	ALTER TABLE public.product_reject_reason DROP COLUMN IF EXISTS "old_price";
	ALTER TABLE public.product_reject_reason DROP COLUMN IF EXISTS "old_description";
	DELETE FROM public.schema_version WHERE version='3.81';
	DROP FUNCTION public.reset_schema_version_3_81();
$$;
ALTER FUNCTION public.reset_schema_version_3_81() OWNER TO oskelly;