INSERT INTO region(iso_code, name) VALUES('RU-AMU','Амурская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ARK','Архангельская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-AST','Астраханская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-BEL','Белгородская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-BRY','Брянская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-VLA','Владимирская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-VGG','Волгоградская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-VLG','Вологодская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-VOR','Воронежская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-IVA','Ивановская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-IRK','Иркутская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KGD','Калининградская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KLU','Калужская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KEM','Кемеровская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KIR','Кировская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KOS','Костромская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KGN','Курганская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KRS','Курская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-LEN','Ленинградская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-LIP','Липецкая область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-MAG','Магаданская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-MOS','Московская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-MUR','Мурманская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-NIZ','Нижегородская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-NGR','Новгородская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-NVS','Новосибирская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-OMS','Омская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ORE','Оренбургская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ORL','Орловская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-PNZ','Пензенская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-PSK','Псковская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ROS','Ростовская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-RYA','Рязанская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SAM','Самарская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SAR','Саратовская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SAK','Сахалинская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SVE','Свердловская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SMO','Смоленская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TAM','Тамбовская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TVE','Тверская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TOM','Томская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TUL','Тульская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TYU','Тюменская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ULY','Ульяновская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-CHE','Челябинская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-YAR','Ярославская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-AD','Адыгея') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-BA','Башкортостан') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-BU','Бурятия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-DA','Дагестан') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-IN','Ингушетия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KB','Кабардино-Балкария') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KL','Калмыкия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KC','Карачаево-Черкесия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KR','Карелия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ME','Марий Эл') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-MO','Мордовия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-AL','Республика Алтай') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KO','Республика Коми') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SA','Якутия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SE','Северная Осетия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TA','Татарстан') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-TY','Тыва') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-UD','Удмуртия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KK','Хакасия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-CE','Чечня') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-CU','Чувашия') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ALT','Алтайский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-ZAB','Забайкальский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KAM','Камчатский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KDA','Краснодарский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KYA','Красноярский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-PER','Пермский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-PRI','Приморский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-STA','Ставропольский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KHA','Хабаровский край') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-NEN','Ненецкий автономный округ') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-KHM','Ханты-Мансийский автономный округ — Югра') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-CHU','Чукотский автономный округ') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-YAN','Ямало-Ненецкий автономный округ') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-SPE','Санкт-Петербург') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-MOW','Москва') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('RU-YEV','Еврейская автономная область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-BR','Брестская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-HO','Гомельская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-HR','Гродненская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-MA','Могилёвская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-MI','Минская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-HM','Минск') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('BY-VI','Витебская область') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-AKM','Акмолинская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-AKT','Актюбинская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-ALA','Алматы') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-ALM','Алматинская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-AST','Астана') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-ATY','Атырауская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-BAY','Байконур') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-KAR','Карагандинская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-KUS','Костанайская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-KZY','Кызылординская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-MAN','Мангистауская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-PAV','Павлодарская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-SEV','Северо-Казахстанская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-SHY','Шымкент') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-VOS','Восточно-Казахстанская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-YUZ','Туркестанская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-ZAP','Западно-Казахстанская') ON CONFLICT DO NOTHING;
INSERT INTO region(iso_code, name) VALUES('KZ-ZHA','Жамбылская') ON CONFLICT DO NOTHING;

UPDATE region
SET country_id = (SELECT ID FROM country WHERE iso_code_alpha2 ='RU')
WHERE iso_code LIKE 'RU-%';

UPDATE region
SET country_id = (SELECT ID FROM country WHERE iso_code_alpha2 ='BY')
WHERE iso_code LIKE 'BY-%';

UPDATE region
SET country_id = (SELECT ID FROM country WHERE iso_code_alpha2 ='KZ')
WHERE iso_code LIKE 'KZ-%';
