CREATE TABLE IF NOT EXISTS comment_translated(
                                   id BIGSERIAL PRIMARY KEY,
                                   text TEXT NOT NULL,
                                   comment_id BIGINT NOT NULL REFERENCES public.comment(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS product_translated(
                                   id BIGSERIAL PRIMARY KEY,
                                   description TEXT NOT NULL,
                                   product_id BIGINT NOT NULL REFERENCES public.product(id) ON DELETE SET NULL
);