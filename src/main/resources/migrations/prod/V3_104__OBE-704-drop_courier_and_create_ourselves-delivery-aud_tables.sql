ALTER TABLE public.ourselves_delivery
    ADD COLUMN name TEXT NOT NULL,
    ADD COLUMN phone TEXT NOT NULL,
    DROP COLUMN IF EXISTS courier_id,
    DROP CONSTRAINT IF EXISTS ourselves_delivery_courier_id_fkey;

DROP TABLE IF EXISTS public.courier;

CREATE TABLE IF NOT EXISTS public.ourselves_delivery_aud (
    id bigint NOT NULL,
    rev integer NOT NULL,
    revtype smallint,

    order_id BIGINT,
    create_time TIMESTAMP WITH TIME ZONE,
    pickup_from TEXT,
    delivery_to TEXT,
    address_endpoint_id BIGINT,
    name TEXT,
    phone TEXT
);
ALTER TABLE public.ourselves_delivery_aud OWNER TO oskelly;
ALTER TABLE ONLY public.ourselves_delivery_aud ADD CONSTRAINT ourselves_delivery_aud_pkey PRIMARY KEY (id, rev);
ALTER TABLE ONLY public.ourselves_delivery_aud ADD CONSTRAINT ourselves_delivery_aud_rev_fkey FOREIGN KEY (rev) REFERENCES public.revinfo(rev);