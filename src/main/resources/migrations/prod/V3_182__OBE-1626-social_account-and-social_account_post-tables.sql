CREATE TABLE IF NOT EXISTS social_account (
	id BIGSERIAL PRIMARY KEY,
	user_id BIGINT REFERENCES public.user(id) ON DELETE CASCADE,
	social_network VARCHAR(100),
	name VARCHAR(100),
	description VARCHAR(255),
	url VARCHAR(255),
	create_time TIMESTAMP WITH TIME ZONE NOT NULL,
	update_time TIMESTAMP WITH TIME ZONE NOT NULL,
	publications_count_str VARCHAR(20) DEFAULT '0',
	publications_count INTEGER DEFAULT 0,
	subscribers_count_str VARCHAR(20) DEFAULT '0',
	subscribers_count INTEGER DEFAULT 0
);

DELETE FROM social_account;

INSERT INTO social_account(user_id, social_network, name, description, url, create_time, update_time) VALUES
(12091, 'INSTAGRAM', 'Инга Меладзе', 'Дизайнер собственной линии украшений и старшая дочь Валерия Меладзе', 'https://instagram.com/ingameladze', NOW(), NOW()),
(17053, 'INSTAGRAM', 'Тамуна Циклаури', 'It-girl, одна из главных модниц Москвы, талантливый дизайнер одежды, совладелица бренда Simplify', 'https://instagram.com/tamunatsiklauri', NOW(), NOW()),
(18737, 'INSTAGRAM', 'Мария Михайлова', 'Креативный директор ЦУМ, Стилист Vogue Russia, Инфлюенсер', 'https://instagram.com/marimikh', NOW(), NOW()),
(549, 'INSTAGRAM', 'Сарина Турцецкая', 'Продюсер, It-girl, дочь музыканта продюсера Михаила Турецкого', 'https://instagram.com/sorrysorik', NOW(), NOW()),
(8509, 'INSTAGRAM', 'Ольга Калаева', 'It-girl, основательница российских брендов OKbeauty и The Rebel', 'https://instagram.com/oliak', NOW(), NOW()),
(19553, 'INSTAGRAM', 'Филипп Киркоров', 'Народный артист России, коллекционер одежды и аксессуаров премиальных брендов, настоящий ценитель люкса', 'https://instagram.com/fkirkorov', NOW(), NOW()),
(19554, 'INSTAGRAM', 'Тамара Дзуцева (Тамара MP3)', 'Инфлюенсер, певица, коллекционер', 'https://instagram.com/tamara.mp3', NOW(), NOW()),
(260, 'INSTAGRAM', 'Анна Худоян', 'Инфлюенсер, актиса, коллекционер одежды и аксессуаров', 'https://instagram.com/ann_khudoyan', NOW(), NOW()),
(198, 'INSTAGRAM', 'Лидия Иванькова', 'Предприниматель, стилист, московская it-girl, инфлюенсер', 'https://instagram.com/lidiaivankova', NOW(), NOW()),
(42187, 'INSTAGRAM', 'Яна Рудковская', 'Продюссер, Народный артист России, коллекционер одежды и аксессуаров премиальных брендов, настоящий ценитель люкса', 'https://instagram.com/rudkovskayaofficial', NOW(), NOW()),
(48527, 'INSTAGRAM', 'Полина Аскери', 'Топ-модель, журналист, акстриса, галерист', 'https://instagram.com/polina_askeri', NOW(), NOW()),
(33295, 'INSTAGRAM', 'Александр Розенбаум', 'Народный артист России', 'https://instagram.com/rozenbaum.ru', NOW(), NOW()),
(25981, 'INSTAGRAM', 'Александр Рогов', 'Стилист, ведущий, модный эксперт', 'https://instagram.com/alexandrrogov', NOW(), NOW()),
(19379, 'INSTAGRAM', 'Татьяна Азатян', 'Совладелица издательского дома (Independent Media (Harpers Bazaar, Cosmopolitan, Esquire)', 'https://instagram.com/tazatyan', NOW(), NOW()),
(32726, 'INSTAGRAM', 'Юлианна Караулова', 'Певица', 'https://instagram.com/yulianna_karaulova', NOW(), NOW()),
(36427, 'INSTAGRAM', 'Дарина Краснова', 'Дочь художника-сценографа и продюсера Бориса Краснова, основательница благотворительного дома "Дари Надежду"', 'https://instagram.com/dorakrsnva', NOW(), NOW()),
(42358, 'INSTAGRAM', 'Мелания Кондрахина', 'Дочь Тины Канделаки', 'https://instagram.com/melaniakondrahina', NOW(), NOW()),
--Oskelly support user
(25952, 'INSTAGRAM', 'Oskelly', 'Oskelly official instagram account', 'https://www.instagram.com/oskellyofficial', NOW(), NOW())
;

CREATE TABLE IF NOT EXISTS social_account_post (
	id BIGSERIAL PRIMARY KEY,
	social_account_id BIGINT REFERENCES public.social_account(id) ON DELETE CASCADE,
	image VARCHAR(255),
	url VARCHAR(255),
	create_time TIMESTAMP WITH TIME ZONE NOT NULL,
	update_time TIMESTAMP WITH TIME ZONE NOT NULL
);