ALTER TABLE category
    ADD COLUMN IF NOT EXISTS indent_top INTEGER;
ALTER TABLE category
    ADD COLUMN IF NOT EXISTS indent_bottom INTEGER;
ALTER TABLE category
    ADD COLUMN IF NOT EXISTS indent_left INTEGER;
ALTER TABLE category
    ADD COLUMN IF NOT EXISTS indent_right INTEGER;
ALTER TABLE category
    ADD COLUMN IF NOT EXISTS alignment VARCHAR(255) CHECK (alignment IN ('CENTER', 'DOWN'));

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (105, 2, 188, 366, 609);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (107, 108, 109, 110, 111, 112, 113, 114, 488, 489, 490, 289, 280, 283, 282, 279, 277);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (115, 116, 117, 491);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (118, 119, 120, 121);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (122, 123, 125, 126, 288);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id = 124;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id = 127;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (130, 131, 132);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (134, 135, 136, 137, 138);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (139, 128, 129, 140, 141, 494);


UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (142, 143, 144, 486, 487);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id = 145;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id = 146;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (147, 492, 493);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (497, 498);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (499, 500);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (149, 150, 151, 152, 153, 154, 157, 155, 501, 502, 156, 503, 504, 158, 505);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (160, 162, 164, 165, 166, 506, 507, 508, 161, 163, 543, 547, 544, 16, 595, 238);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (171, 177, 178, 521, 183);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN
      (168, 169, 512, 513, 170, 172, 173, 174, 175, 291, 179, 520, 180, 519, 181, 517, 518,
       182, 514, 515, 516, 184, 185, 186, 187, 509, 510, 511);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id BETWEEN 355 AND 365;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (603, 604, 605, 606, 608);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (607, 551, 287);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (204, 479, 480, 481, 482, 483, 484, 485);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (205, 471, 472, 473, 474, 475, 476, 477);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id = 203;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id BETWEEN 190 AND 202;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (220, 465, 466, 467, 468, 469, 470);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (221, 457, 458, 459, 460, 461, 462, 463);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id = 219;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id BETWEEN 207 AND 218;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (236, 442, 443, 444, 445, 446, 447, 448);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (237, 449, 450, 451, 452, 453);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id BETWEEN 223 AND 235;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (454, 455, 456);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (251, 428, 429, 430, 431, 432);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (252, 433, 434, 435, 436, 437);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id = 250;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id BETWEEN 239 AND 249;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (438, 439, 440);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id BETWEEN 10 AND 35
   OR id IN (284, 548, 549, 550, 20, 545, 546);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id BETWEEN 292 AND 354;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (3, 4, 5, 552, 553, 554, 555, 7, 556, 557, 9, 597);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE (id BETWEEN 37 AND 81)
   OR id IN
      (281, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 538, 539, 540, 541, 542);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (590, 591, 592, 600);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE (id BETWEEN 588 AND 599 AND id NOT IN (589, 590, 591, 592))
   OR id = 601;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id BETWEEN 85 AND 91
   OR id IN
      (95, 100, 566, 567, 101, 102, 578, 579, 580, 103, 572, 573, 285, 286, 290, 562, 563, 564, 565, 577, 8, 86,
       87, 574, 575, 576, 568, 569, 570, 571, 558, 559, 560, 561, 599, 585, 584, 582, 583, 581);

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 24,
    indent_bottom = 24,
    indent_left   = 72,
    indent_right  = 72
WHERE id IN (104, 586, 587, 93, 94, 97, 98);

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE (id BETWEEN 92 AND 104 AND id NOT IN (93, 94, 97, 98, 104));

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id BETWEEN 367 AND 414;

UPDATE category
SET alignment     = 'DOWN',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id BETWEEN 415 AND 424;

UPDATE category
SET alignment     = 'CENTER',
    indent_top    = 56,
    indent_bottom = 144,
    indent_left   = 88,
    indent_right  = 88
WHERE id IN (83, 537, 84, 82, 495, 133, 496, 589);
