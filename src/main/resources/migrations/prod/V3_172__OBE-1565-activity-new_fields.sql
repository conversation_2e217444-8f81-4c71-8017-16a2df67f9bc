ALTER TABLE activity
	ADD COLUMN IF NOT EXISTS idfa VARCHAR(50) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS idfv VARCHAR(50) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS android_id VARCHAR(50) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS advertising_id VARCHAR(50) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS appsflyer_id VARCHAR(50) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS mac VARCHAR(20) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS appsflyer_sent_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
	DROP CONSTRAINT IF EXISTS activity_user_id_fkey
;