ALTER TABLE public.product_item ADD COLUMN IF NOT EXISTS "is_hidden" BOOLEAN NOT NULL DEFAULT FALSE;

CREATE OR REPLACE FUNCTION public.reset_schema_version_3_118() RETURNS void LANGUAGE SQL AS $$
	ALTER TABLE public.product_item DROP COLUMN IF EXISTS "is_hidden";
	DELETE FROM public.schema_version WHERE version='3.118';
	DROP FUNCTION public.reset_schema_version_3_118();
$$;
ALTER FUNCTION public.reset_schema_version_3_118() OWNER TO oskelly;