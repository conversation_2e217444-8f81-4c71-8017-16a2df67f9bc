-- связка с брендами --
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('25')), (SELECT id FROM brand WHERE lower(name) = lower('CHANEL'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Coco Crush')), (SELECT id FROM brand WHERE lower(name) = lower('CHANEL'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('J12')), (SELECT id FROM brand WHERE lower(name) = lower('CHANEL'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Premier')), (SELECT id FROM brand WHERE lower(name) = lower('CHANEL'))) ON CONFLICT DO NOTHING;