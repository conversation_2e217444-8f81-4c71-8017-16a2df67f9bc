CREATE TABLE IF NOT EXISTS sale_rejection_reason_type
(
    id           BIGSERIAL PRIMARY KEY,
    name         TEXT    NOT NULL,
    is_for_admin BOOLEAN NOT NULL DEFAULT false
);

INSERT INTO sale_rejection_reason_type (id, name)
VALUES (1, 'CHANGE_DECISION'),
       (2, 'SOLD_ON_OTHER_PLATFORM'),
       (3, 'GOING_TO_CHANGE_PRICE'),
       (4, 'PRODUCT_CONDITION_CHANGED'),
       (5, 'OTHER')
ON CONFLICT DO NOTHING;

INSERT INTO sale_rejection_reason_type (id, name, is_for_admin)
VALUES (6, 'DONT_ANSWER', true),
       (7, 'CAN_NOT_SEND', true)
ON CONFLICT DO NOTHING;

CREATE TABLE IF NOT EXISTS sale_rejection_reason
(
    id             BIGSERIAL PRIMARY KEY,
    reason_type_id BIGINT NOT NULL REFERENCES sale_rejection_reason_type (id),
    sale_platform  TEXT,
    new_price      NUMERIC,
    comment        TEXT
);

CREATE TABLE IF NOT EXISTS sale_rejection_reason_aud
(
    id             BIGINT  NOT NULL,
    rev            INTEGER NOT NULL,
    revtype        SMALLINT,
    reason_type_id BIGINT,
    sale_platform  TEXT,
    new_price      NUMERIC,
    comment        TEXT
);

ALTER TABLE order_position
    ADD COLUMN IF NOT EXISTS
        sale_rejection_reason_id BIGINT NULL REFERENCES sale_rejection_reason (id);
ALTER TABLE order_position_aud
    ADD COLUMN IF NOT EXISTS sale_rejection_reason_id BIGINT NULL;