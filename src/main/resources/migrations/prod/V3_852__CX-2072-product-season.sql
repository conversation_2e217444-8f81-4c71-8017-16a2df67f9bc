create table if not exists season
(
    id          bigserial primary key,
    season_type varchar(2) not null,
    year        integer    not null check (year >= 1900 and year <= 2100)
);

create unique index if not exists season_season_type_year_idx on season (season_type, year);

alter table product
    add column if not exists season_id bigint references season (id);

alter table product_aud
    add column if not exists season_id bigint;

alter table product
    add column if not exists carry_over_time timestamp;

alter table product_aud
    add column if not exists carry_over_time timestamp;

alter table product
    add column if not exists investment_time timestamp;

alter table product_aud
    add column if not exists investment_time timestamp;

-- Spring-Summer (SS) сезоны 2000–2040
insert into season (season_type, year)
values
       ('SS', 2000),
       ('SS', 2001),
       ('SS', 2002),
       ('SS', 2003),
       ('SS', 2004),
       ('SS', 2005),
       ('SS', 2006),
       ('SS', 2007),
       ('SS', 2008),
       ('SS', 2009),
       ('SS', 2010),
       ('SS', 2011),
       ('SS', 2012),
       ('SS', 2013),
       ('SS', 2014),
       ('SS', 2015),
       ('SS', 2016),
       ('SS', 2017),
       ('SS', 2018),
       ('SS', 2019),
       ('SS', 2020),
       ('SS', 2021),
       ('SS', 2022),
       ('SS', 2023),
       ('SS', 2024),
       ('SS', 2025),
       ('SS', 2026),
       ('SS', 2027),
       ('SS', 2028),
       ('SS', 2029),
       ('SS', 2030),
       ('SS', 2031),
       ('SS', 2032),
       ('SS', 2033),
       ('SS', 2034),
       ('SS', 2035),
       ('SS', 2036),
       ('SS', 2037),
       ('SS', 2038),
       ('SS', 2039),
       ('SS', 2040)
on conflict (season_type, year) do nothing;

-- Autumn-Winter (AW) сезоны 2000–2040
insert into season (season_type, year)
values
       ('AW', 2000),
       ('AW', 2001),
       ('AW', 2002),
       ('AW', 2003),
       ('AW', 2004),
       ('AW', 2005),
       ('AW', 2006),
       ('AW', 2007),
       ('AW', 2008),
       ('AW', 2009),
       ('AW', 2010),
       ('AW', 2011),
       ('AW', 2012),
       ('AW', 2013),
       ('AW', 2014),
       ('AW', 2015),
       ('AW', 2016),
       ('AW', 2017),
       ('AW', 2018),
       ('AW', 2019),
       ('AW', 2020),
       ('AW', 2021),
       ('AW', 2022),
       ('AW', 2023),
       ('AW', 2024),
       ('AW', 2025),
       ('AW', 2026),
       ('AW', 2027),
       ('AW', 2028),
       ('AW', 2029),
       ('AW', 2030),
       ('AW', 2031),
       ('AW', 2032),
       ('AW', 2033),
       ('AW', 2034),
       ('AW', 2035),
       ('AW', 2036),
       ('AW', 2037),
       ('AW', 2038),
       ('AW', 2039),
       ('AW', 2040)
on conflict (season_type, year) do nothing;