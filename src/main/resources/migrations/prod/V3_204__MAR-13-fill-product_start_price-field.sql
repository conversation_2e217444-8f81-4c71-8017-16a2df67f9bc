/*Для опубликованных товаров с start_price IS NULL проставляем стартовую цену из аудита*/
UPDATE product
SET start_price = aud.current_price
FROM (SELECT DISTINCT ON (id) id, current_price FROM product_aud WHERE product_state = 'PUBLISHED' ORDER BY id, change_time ASC) AS aud
WHERE product.start_price IS NULL AND product.product_state = 'PUBLISHED' AND product.id = aud.id;

/*Если по каким-то причинам остались опубликованные товары без стартовой цены, проставляем стартовую цену равную текущей*/
UPDATE product SET start_price = current_price WHERE product_state = 'PUBLISHED' AND start_price IS NULL;