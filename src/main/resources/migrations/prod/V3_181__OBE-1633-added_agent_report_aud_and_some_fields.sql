ALTER TABLE public.agent_report ADD COLUMN IF NOT EXISTS state TEXT;
ALTER TABLE public.agent_report ADD COLUMN IF NOT EXISTS state_time TIMESTAMPTZ;

CREATE TABLE IF NOT EXISTS public.agent_report_aud (
  id bigint NOT NULL,
  rev integer NOT NULL,
  revtype smallint,

  order_id BIGINT,
  create_time TIMESTAMPTZ,
  name                    text,
  first_name              text,
  second_name             text,
  patronymic              text,
  bik                     text,
  inn                     text,
  payment_account         text,
  number_contract         text,
  date_contract           TIMESTAMPTZ,
  is_confirmed            boolean,
  kpp                     text,
  payment_details         text,
  payment_amount          numeric,
  cleaning_amount         numeric,
  defects_discount_amount numeric,

  payment_lock BOOLEAN,
  payment_lock_time TIMESTAMPTZ,

  confirmed_time TIMESTAMPTZ,

  state text,
  state_time TIMESTAMPTZ
);

ALTER TABLE public.agent_report_aud OWNER TO oskelly;
ALTER TABLE ONLY public.agent_report_aud ADD CONSTRAINT agent_report_aud_pkey PRIMARY KEY (id, rev);
ALTER TABLE ONLY public.agent_report_aud ADD CONSTRAINT agent_report_aud_rev_fkey FOREIGN KEY (rev) REFERENCES public.revinfo(rev);

ALTER TABLE bank_payment ALTER COLUMN bank DROP NOT NULL;
ALTER TABLE bank_payment ALTER COLUMN bank_transaction_id DROP NOT NULL;

UPDATE
  agent_report
SET
  state = 'PAYMENT_INPROGRESS',
  state_time = now()
WHERE
  (SELECT o.state FROM public."order" o WHERE o.id = agent_report.order_id) IN ('MONEY_PAYMENT_WAIT', 'MONEY_PAYMENT_TECHNICAL_ERROR', 'MONEY_PAYMENT_NOT_ENOUGH')
;

UPDATE
  agent_report
SET
  state = 'PAYMENT_COMPLETED',
  state_time = now()
WHERE
  (SELECT o.state FROM public."order" o WHERE o.id = agent_report.order_id) = 'COMPLETED'
;

ALTER TABLE public.agent_report
  DROP COLUMN IF EXISTS payment_lock,
  DROP COLUMN IF EXISTS payment_lock_time;