/*Сначала удаляем order_position дубликатов*/
DELETE FROM public.order_position WHERE order_id IN (
	SELECT id FROM public.order WHERE state='CREATED' AND buyer_id IS NOT NULL AND id NOT IN(
		SELECT DISTINCT ON(buyer_id) id FROM public.order WHERE state='CREATED' ORDER BY buyer_id, create_time DESC
	)
);
/*Затем удаляем сами дубликаты*/
DELETE FROM public.order WHERE state='CREATED' AND buyer_id IS NOT NULL AND id NOT IN(
	SELECT DISTINCT ON(buyer_id) id FROM public.order WHERE state='CREATED' ORDER BY buyer_id, create_time DESC
);
