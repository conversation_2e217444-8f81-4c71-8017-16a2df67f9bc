create table if not exists user_contract
(
    id          bigserial primary key,
    user_id     bigint references public.user (id) not null,
    key         varchar(50)                        not null,
    filename    varchar(255)                       not null,
    upload_time timestamptz                        not null
);

create unique index if not exists user_contract_key_idx on user_contract (key);
create index if not exists user_contract_user_id_idx on user_contract (user_id);


create table if not exists user_contract_aud
(
    id          bigint  not null,
    rev         integer not null,
    revtype     smallint,

    user_id     bigint,
    key         varchar(50),
    filename    varchar(255),
    upload_time timestamptz,

    primary key (id, rev)
);