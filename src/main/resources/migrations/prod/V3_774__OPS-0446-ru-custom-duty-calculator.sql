INSERT INTO duty_calculator_config (pickup_country_id, delivery_country_id, dtype, params, params_sample)
VALUES (
           (SELECT id FROM country WHERE iso_code_alpha2='CN'),
           (SELECT id FROM country WHERE iso_code_alpha2='RU'),
           'RU_CUSTOM_DUTY',
           '{}',
           '{}'
       )
    ON CONFLICT DO NOTHING;

DO $$
BEGIN
UPDATE duty_calculator_config
SET is_active = UPPER('${internationalVersion}') != 'TRUE'
WHERE dtype = 'RU_CUSTOM_DUTY';
END $$;