CREATE TABLE IF NOT EXISTS order_position_bitrix
(
    id                              BIGSERIAL PRIMARY KEY,
    order_position_id               BIGINT  NOT NULL REFERENCES order_position (id) ON DELETE NO ACTION,
    bitrix_deal_id                  BIGINT  NOT NULL UNIQUE,
    bitrix_category_id              TEXT,
    state                           VARCHAR(255),
    last_bitrix_deal_stage          VARCHAR(255),
    update_bitrix_stage_retry_count INTEGER NOT NULL DEFAULT 0,
    update_bitrix_stage_errors      TEXT,
    create_time                     TIMESTAMPTZ
);

CREATE INDEX IF NOT EXISTS order_position_bitrix_order_position_id_idx on order_position_bitrix (order_position_id);
CREATE INDEX IF NOT EXISTS order_position_bitrix_bitrix_deal_id_idx on order_position_bitrix (bitrix_deal_id);

CREATE TABLE IF NOT EXISTS order_position_bitrix_aud
(
    id                              BIGINT  NOT NULL,
    rev                             INTEGER NOT NULL,
    revtype                         SMALLINT,
    order_position_id               BIGINT,
    bitrix_deal_id                  BIGINT,
    bitrix_category_id              TEXT,
    state                           VA<PERSON><PERSON><PERSON>(255),
    last_bitrix_deal_stage          VARCHAR(255),
    update_bitrix_stage_retry_count INTEGER,
    update_bitrix_stage_errors      TEXT,
    create_time                     TIMESTAMPTZ
);

ALTER TABLE order_position_bitrix_aud
    DROP CONSTRAINT IF EXISTS order_position_bitrix_aud_pkey;
ALTER TABLE ONLY order_position_bitrix_aud
    ADD CONSTRAINT order_position_bitrix_aud_pkey
        PRIMARY KEY (id, rev);

ALTER TABLE IF EXISTS concierge_form
    ADD COLUMN IF NOT EXISTS order_position_id BIGINT REFERENCES order_position(id) ON DELETE NO ACTION;