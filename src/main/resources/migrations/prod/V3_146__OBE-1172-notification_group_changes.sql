ALTER TABLE notification_group
	ADD COLUMN IF NOT EXISTS description TEXT DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS hint VARCHAR(50) DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT NULL,
	ADD COLUMN IF NOT EXISTS is_general BOOLEAN DEFAULT FALSE;

INSERT INTO notification_group
(id, name, is_hidden, description, sort_order, is_general) VALUES
(5, 'PUSH уведомления', FALSE, 'Включите Push уведомления и будьте в курсе всех новых событий в аккаунте', 1, TRUE);

UPDATE notification_group SET name='Требующие внимания', sort_order=2, hint='Рекомендуется',
description='Сообщения о ваших товарах, покупках и комментариях, тре<PERSON><PERSON>ющих вашего незамедлительного ответа' WHERE id=1;

UPDATE notification_group SET sort_order=3,
description='Отслеживайте заказ, подтверждайте товар вовремя, торгуйтесь, и другие важные сообщения от OSKELLY' WHERE id=2;

UPDATE notification_group SET sort_order=4,
description='Новости, подсказки и советы OSKELLY. Будьте в курсе всех событий' WHERE id=3;

UPDATE notification_group SET sort_order=100,
description='Уведомления, привязанные к этой группе не передаются посредством пушей вообще' WHERE id=4;