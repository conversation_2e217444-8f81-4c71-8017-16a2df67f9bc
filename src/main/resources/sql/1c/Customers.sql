SELECT
    u.id AS u_id,
    CONCAT(u.company_name, u.last_name, ' ', u.first_name) AS u_description,
    u.nickname AS u_nickname,
    u.email AS u_email,
    u.registration_time AS u_registration_time,
    u.pro_status_time AS u_pro_status_time,
    u.vip_status_time AS u_vip_status_time,
    u.phone AS u_phone,
    CASE c.dtype
        WHEN 'JurCounterparty' THEN CONCAT(c.company_form, ' "', c.company_name, '"')
        WHEN 'IpCounterparty' THEN CONCAT('Индивидуальный предприниматель ', c.last_name, ' ', c.first_name, ' ', c.patronymic_name)
        ELSE CONCAT(c.last_name, ' ', c.first_name, ' ', c.patronymic_name)
    END AS c_name,
    CONCAT(c.last_name, ' ', c.first_name, ' ', c.patronymic_name) AS c_fullname,
    c.id AS c_id,
    c.first_name AS c_first_name,
    c.last_name AS c_last_name,
    c.patronymic_name AS c_patronymic_name,
    c.inn AS c_inn,
    c.kpp AS c_kpp,
    c.ogrn AS c_ogrn,
    c.dtype AS c_dtype,
    a.id AS a_id,
    a.zip_code AS a_zip_code,
    a.city AS a_city,
    a.address AS a_address
FROM public.user u
LEFT JOIN counterparty c ON c.user_id = u.id
LEFT JOIN address a ON a.user_id = u.id
WHERE u.id IN (
    SELECT o.buyer_id
    FROM "order" o
    JOIN agent_report ag ON ag.order_id = o.id
    WHERE o.delivery_state = 'DELIVERED_TO_BUYER'
        AND COALESCE(o.order_source, '') <> 'BOUTIQUE'
        AND ag.sent_time >= '%startTime%' AND ag.sent_time <= '%endTime%'
)
OR c.id IN (
    SELECT o.seller_counterparty_id
    FROM "order" o
    JOIN agent_report ag ON ag.order_id = o.id
    WHERE o.delivery_state = 'DELIVERED_TO_BUYER'
        AND COALESCE(o.order_source, '') <> 'BOUTIQUE'
        AND ag.sent_time >= '%startTime%' AND ag.sent_time <= '%endTime%'
)
OR u.id IN (
    SELECT o.seller_id
    FROM "order" o
    JOIN agent_report ag ON ag.order_id = o.id
    WHERE o.delivery_state = 'DELIVERED_TO_BUYER'
        AND COALESCE(o.order_source, '') <> 'BOUTIQUE'
        AND ag.sent_time >= '%startTime%' AND ag.sent_time <= '%endTime%'
)
ORDER BY u.id, c.id, a.id
/*LIMIT*/