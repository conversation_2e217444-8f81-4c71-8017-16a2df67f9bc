package su.reddot.infrastructure.logistic.dalli.model.basket.request;

import lombok.Builder;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@Builder
public class DalliReceiver {
    @XmlElement(name = "fias")
    private String fias;
    @XmlElement(name = "town")
    private String town;
    @XmlElement(name = "address")
    private String address;
    @XmlElement(name = "person")
    private String person;
    @XmlElement(name = "phone")
    private String phone;
    @XmlElement(name = "date")
    private String date;
    @XmlElement(name = "time_min")
    private String timeMin;
    @XmlElement(name = "time_max")
    private String timeMax;
}
