package su.reddot.infrastructure.logistic;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import su.reddot.domain.dao.logistic.LogisticsInfoRepository;
import su.reddot.domain.model.logistic.LogisticsInfo;
import su.reddot.domain.model.logistic.LogisticsInfoState;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.service.dto.delivery.DeliveryParamRequestDTO;
import su.reddot.infrastructure.delivery.DeliveryService;

import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;

@Service
@RequiredArgsConstructor
@Slf4j
public class LogisticsInfoService {

    private final DeliveryService deliveryService;
    private final ProcessOrderPickupProperties properties;
    private final LogisticsInfoRepository repository;

    @Transactional
    public LogisticsInfo markAsPending(@NonNull LogisticsInfo logisticsInfo) {
        logisticsInfo.setState(LogisticsInfoState.PENDING);
        return repository.save(logisticsInfo);
    }

    @Transactional
    public List<LogisticsInfo> processOrdersPickup() {
        // extract data to process
        int limit = properties.getProcessingLimit();
        List<LogisticsInfo> logisticsInfos = repository.findAllForProcessing(limit);

        // feed to processor
        List<LogisticsInfo> processed = logisticsInfos.stream()
                .map(this::processOrderPickup)
                .collect(Collectors.toList());

        // persist changed states
        return repository.saveAll(processed);
    }

    private LogisticsInfo processOrderPickup(@NonNull LogisticsInfo logisticsInfo) {
        log.debug("Process order pickup by logisticsInfo {}", logisticsInfo);
        int currentProcessingAttempt = logisticsInfo.getProcessingAttempt() + 1;
        int maxProcessingAttempts = properties.getRetryMaxCount() + 1;

        Order order = logisticsInfo.getOrder();
        if (order == null) {
            // Иногда для одного заказа создается две записи logistics_info, одна из которых в итоге отвязывается от
            // заказа. Подробности тут - https://tracker.yandex.ru/CX-472
            return logisticsInfo
                    .toBuilder()
                    .state(LogisticsInfoState.INITIAL)
                    .build();
        }

        // execute business logic
        StopWatch watch = new StopWatch();
        try {
            watch.start();
            if (isNull(order.getLastWaybillFromSeller(true))) {
                deliveryService.sendConfirmedOrderToPickup(
                        DeliveryParamRequestDTO
                                .builder()
                                .orderId(order.getId())
                                .isOrderProcessingNotificationRequired(true)
                                .build()
                );
            } else {
                log.debug("Order has waybill from seller");
            }
            watch.stop();

            log.debug("Sending confirmed order to pickup executed in {} ms", watch.getTotalTimeMillis());
        } catch (Exception e) {
            log.error("Failed sending confirmed order with id {} to pickup: {}", order.getId(), e.getMessage(), e);
            if (currentProcessingAttempt < maxProcessingAttempts) {
                // retry candidate
                int failsCount = logisticsInfo.getProcessingAttempt();
                int initialInterval = properties.getRetryInitialStepSec();
                int multiplier = properties.getRetryMultiplier();
                LocalDateTime nextProcessingTime = calculateNextProcessingTime(
                        failsCount, initialInterval, multiplier
                );

                return logisticsInfo
                        .toBuilder()
                        .state(LogisticsInfoState.RETRY)
                        .nextProcessingTime(nextProcessingTime)
                        .processingAttempt(currentProcessingAttempt)
                        .errorMessage(e.getMessage())
                        .build();
            }

            // terminal error
            return logisticsInfo
                    .toBuilder()
                    .state(LogisticsInfoState.ERROR)
                    .errorMessage(e.getMessage())
                    .processingAttempt(currentProcessingAttempt)
                    .build();
        }

        return logisticsInfo
                .toBuilder()
                .state(LogisticsInfoState.SUCCESS)
                .processingAttempt(currentProcessingAttempt)
                .build();
    }

    public static LocalDateTime calculateNextProcessingTime(int failsCount, int initialIntervalSec, int multiplier) {
        long seconds = initialIntervalSec * (long) Math.pow(multiplier, failsCount);
        return LocalDateTime.now().plusSeconds(seconds);
    }
}
