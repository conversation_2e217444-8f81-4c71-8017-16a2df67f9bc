package su.reddot.infrastructure.logistic;

import java.time.OffsetDateTime;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import su.reddot.domain.service.dto.delivery.TimeIntervalDTO;

@RequiredArgsConstructor
@Getter
public class DateWithIntervalAndDays {
    private final OffsetDateTime date;
    private final TimeIntervalDTO timeInterval;
    private final int minDeliveryDays;
    private final int maxDeliveryDays;
}
