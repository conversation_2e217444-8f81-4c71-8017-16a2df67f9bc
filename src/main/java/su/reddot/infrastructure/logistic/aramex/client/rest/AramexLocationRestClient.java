package su.reddot.infrastructure.logistic.aramex.client.rest;

import java.util.List;
import java.util.stream.Collectors;

import lombok.NonNull;
import su.reddot.infrastructure.logistic.aramex.AramexConfiguration;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientException;
import su.reddot.infrastructure.logistic.aramex.client.AramexCountry;
import su.reddot.infrastructure.logistic.aramex.client.AramexLocationClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexState;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.Country;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.FetchCitiesRequest;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.FetchCitiesResponse;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.FetchCountriesRequest;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.FetchCountriesResponse;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.FetchStatesRequest;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.FetchStatesResponse;
import su.reddot.infrastructure.logistic.aramex.client.rest.model.State;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty("app.integration.aramex.rest-enabled")
public class AramexLocationRestClient extends AramexBaseRestClient implements AramexLocationClient {

    public AramexLocationRestClient(@NonNull final AramexConfiguration configuration) {
        super(configuration);
    }

    @Override
    @NonNull
    protected String getEndpoint() {
        return configuration.getLocationEndpoint();
    }

    @Override
    @NonNull
    public List<AramexCountry> fetchCountries() throws AramexClientException {
        final FetchCountriesRequest restRequest = new FetchCountriesRequest();

        // Set auth properties
        restRequest.setClientInfo(createClientInfo());

        final FetchCountriesResponse restResponse = sendRequest("FetchCountries", restRequest, FetchCountriesResponse.class);

        checkErrors(restResponse.getNotifications());

        return restResponse.getCountries().stream()
                .map(AramexLocationRestClient::convertCountry)
                .collect(Collectors.toList());
    }

    @NonNull
    private static AramexCountry convertCountry(@NonNull final Country country) {
        return AramexCountry.builder()
                .code(country.getCode())
                .name(country.getName())
                .isoCode(country.getIsoCode())
                .stateRequired(country.isStateRequired())
                .postCodeRequired(country.isPostCodeRequired())
                .internationalCallingNumber(country.getInternationalCallingNumber())
                .build();
    }

    @Override
    @NonNull
    public List<AramexState> fetchStates(@NonNull final String countryCode) throws AramexClientException {
        final FetchStatesRequest restRequest = new FetchStatesRequest();

        // Set auth properties
        restRequest.setClientInfo(createClientInfo());

        restRequest.setCountryCode(countryCode);

        final FetchStatesResponse restResponse = sendRequest("FetchStates", restRequest, FetchStatesResponse.class);

        checkErrors(restResponse.getNotifications());

        return restResponse.getStates().stream()
                .map(AramexLocationRestClient::convertState)
                .collect(Collectors.toList());
    }

    @NonNull
    private static AramexState convertState(@NonNull final State state) {
        return AramexState.builder()
                .code(state.getCode())
                .name(state.getName())
                .build();
    }

    @Override
    @NonNull
    public List<String> fetchCities(
            @NonNull final String countryCode,
            final String state,
            final String nameStartsWith) throws AramexClientException {
        final FetchCitiesRequest restRequest = new FetchCitiesRequest();

        // Set auth properties
        restRequest.setClientInfo(createClientInfo());

        restRequest.setCountryCode(countryCode);
        restRequest.setState(state);
        restRequest.setNameStartsWith(nameStartsWith);

        final FetchCitiesResponse restResponse = sendRequest("FetchCities", restRequest, FetchCitiesResponse.class);

        checkErrors(restResponse.getNotifications());

        return restResponse.getCities();
    }
}
