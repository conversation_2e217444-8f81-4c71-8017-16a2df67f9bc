package su.reddot.infrastructure.logistic.aramex.client.rest.model;

import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class CreateShipmentsResponse {

    private boolean hasErrors;

    private List<Notification> notifications;

    private List<ProcessedShipment> shipments;

    private Transaction transaction;
}
