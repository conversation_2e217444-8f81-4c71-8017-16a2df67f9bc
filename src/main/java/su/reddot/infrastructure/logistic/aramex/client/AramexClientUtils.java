package su.reddot.infrastructure.logistic.aramex.client;

import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import lombok.NonNull;

/**
 * Utility methods used by Aramex SOAP and REST clients.
 */
public final class AramexClientUtils {

    /**
     * Aramex API expects date-time fields in GMT.
     */
    private static final ZoneId API_ZONE_ID = ZoneId.of("Etc/UTC");

    /**
     * Convert local date to {@code ZonedDateTime} in GMT (Aramex expects GMT).
     */
    @NonNull
    public static ZonedDateTime toZonedDateTime(@NonNull final LocalDate date) {
        // "Time" part doesn't matter here. Aramex only considers "date" part in this case.
        final LocalDateTime ldt = LocalDateTime.of(date, LocalTime.of(9, 0, 0));
        return ZonedDateTime.of(ldt, API_ZONE_ID);
    }

    /**
     * Convert local time to {@code ZonedDateTime} in GMT (Aramex expects GMT).
     */
    @NonNull
    public static ZonedDateTime toZonedDateTime(
            @NonNull final LocalDate localDate,
            @NonNull final LocalTime time) {
        // "Date" part doesn't matter here. Aramex only considers "time" part in this case.
        final LocalDateTime ldt = LocalDateTime.of(localDate, time);
        return ZonedDateTime.of(ldt, API_ZONE_ID);
    }

    /**
     * Get current time in GMT (Aramex expects GMT).
     */
    @NonNull
    public static ZonedDateTime getCurrentTime() {
        return ZonedDateTime.now(Clock.system(API_ZONE_ID));
    }

    @NonNull
    public static String cutDescriptionString(@NonNull final String description) {
        return description.length() > 100
                ? description.substring(0, 100)
                : description;
    }

    private AramexClientUtils() {
    }
}
