package su.reddot.infrastructure.logistic.aramex.client.soap;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBElement;

import aramex.location.wsdl.ArrayOfNotification;
import aramex.location.wsdl.CitiesFetchingRequest;
import aramex.location.wsdl.CitiesFetchingResponse;
import aramex.location.wsdl.ClientInfo;
import aramex.location.wsdl.CountriesFetchingRequest;
import aramex.location.wsdl.CountriesFetchingResponse;
import aramex.location.wsdl.Country;
import aramex.location.wsdl.Notification;
import aramex.location.wsdl.ObjectFactory;
import aramex.location.wsdl.State;
import aramex.location.wsdl.StatesFetchingRequest;
import aramex.location.wsdl.StatesFetchingResponse;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import su.reddot.infrastructure.logistic.aramex.AramexConfiguration;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientException;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientException.AramexError;
import su.reddot.infrastructure.logistic.aramex.client.AramexCountry;
import su.reddot.infrastructure.logistic.aramex.client.AramexLocationClient;
import su.reddot.infrastructure.logistic.aramex.client.AramexState;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "app.integration.aramex.rest-enabled", havingValue = "false")
public class AramexLocationSoapClient extends WebServiceGatewaySupport implements AramexLocationClient {

    private final AramexConfiguration configuration;

    private final ObjectFactory objectFactory = new ObjectFactory();

    @PostConstruct
    public void configure() {
        final Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
        marshaller.setContextPath("aramex.location.wsdl");
        setDefaultUri(configuration.getLocationEndpoint());
        setMarshaller(marshaller);
        setUnmarshaller(marshaller);
    }

    @Override
    @NonNull
    public List<AramexCountry> fetchCountries() throws AramexClientException {
        final CountriesFetchingRequest soapRequest = objectFactory.createCountriesFetchingRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        final CountriesFetchingResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/FetchCountries",
                CountriesFetchingResponse.class);

        checkErrors(soapResponse.getNotifications());

        return soapResponse.getCountries().getValue().getCountry().stream()
                .map(AramexLocationSoapClient::convertCountry)
                .collect(Collectors.toList());
    }

    @NonNull
    private static AramexCountry convertCountry(@NonNull final Country country) {
        return AramexCountry.builder()
                .code(country.getCode())
                .name(country.getName())
                .isoCode(country.getIsoCode())
                .stateRequired(country.isStateRequired())
                .postCodeRequired(country.isPostCodeRequired())
                .internationalCallingNumber(country.getInternationalCallingNumber())
                .build();
    }

    @Override
    @NonNull
    public List<AramexState> fetchStates(@NonNull final String countryCode) throws AramexClientException {
        final StatesFetchingRequest soapRequest = objectFactory.createStatesFetchingRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        soapRequest.setCountryCode(objectFactory.createStatesFetchingRequestCountryCode(countryCode));

        final StatesFetchingResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/FetchStates",
                StatesFetchingResponse.class);

        checkErrors(soapResponse.getNotifications());

        return soapResponse.getStates().getValue().getState().stream()
                .map(AramexLocationSoapClient::convertState)
                .collect(Collectors.toList());
    }

    @NonNull
    private static AramexState convertState(@NonNull final State state) {
        return AramexState.builder()
                .code(state.getCode())
                .name(state.getName())
                .build();
    }

    @Override
    @NonNull
    public List<String> fetchCities(
            @NonNull final String countryCode,
            final String state,
            final String nameStartsWith) throws AramexClientException {
        final CitiesFetchingRequest soapRequest = objectFactory.createCitiesFetchingRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        soapRequest.setCountryCode(objectFactory.createCitiesFetchingRequestCountryCode(countryCode));

        if (state != null) {
            soapRequest.setState(objectFactory.createCitiesFetchingRequestState(state));
        }

        if (nameStartsWith != null) {
            soapRequest.setNameStartsWith(objectFactory.createCitiesFetchingRequestNameStartsWith(nameStartsWith));
        }

        final CitiesFetchingResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/FetchCities",
                CitiesFetchingResponse.class);

        checkErrors(soapResponse.getNotifications());

        return soapResponse.getCities().getValue().getString();
    }

    @NonNull
    private JAXBElement<ClientInfo> createClientInfo() {
        final ClientInfo clientInfo = objectFactory.createClientInfo();
        clientInfo.setUserName(configuration.getUsername());
        clientInfo.setPassword(configuration.getPassword());
        clientInfo.setAccountEntity(configuration.getAccountEntity());
        clientInfo.setAccountNumber(configuration.getAccountNumber());
        clientInfo.setAccountPin(configuration.getAccountPin());
        clientInfo.setAccountCountryCode(configuration.getAccountCountryCode());
        clientInfo.setVersion(configuration.getVersion());
        return objectFactory.createClientInfo(clientInfo);
    }

    private static void checkErrors(@NonNull final JAXBElement<ArrayOfNotification> notifications)
            throws AramexClientException {
        final List<AramexError> errors = notifications.getValue().getNotification()
                .stream()
                .map(AramexLocationSoapClient::toAramexError)
                .collect(Collectors.toList());
        if (!errors.isEmpty()) {
            throw new AramexClientException(errors);
        }
    }

    @NonNull
    private static AramexError toAramexError(@NonNull final Notification notification) {
        return new AramexError(notification.getCode(), notification.getMessage());
    }

    @NonNull
    private <T> T sendRequest(
            @NonNull final Object request,
            @NonNull final String action,
            @NonNull final Class<T> responseClass) throws AramexClientException {
        final SoapActionCallback actionCallback = new SoapActionCallback(action);
        final Object response = getWebServiceTemplate().marshalSendAndReceive(request, actionCallback);
        if (response == null) {
            throw new AramexClientException("Empty response: " + responseClass.getSimpleName());
        }
        return responseClass.cast(response);
    }
}
