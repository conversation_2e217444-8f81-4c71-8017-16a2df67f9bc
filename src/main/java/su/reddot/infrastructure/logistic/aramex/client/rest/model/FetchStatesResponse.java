package su.reddot.infrastructure.logistic.aramex.client.rest.model;

import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class FetchStatesResponse {

    private Transaction transaction;

    private List<Notification> notifications;

    private boolean hasErrors;

    private List<State> states;
}
