package su.reddot.infrastructure.logistic.aramex.client.soap;

import java.time.ZonedDateTime;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.PostConstruct;
import javax.xml.bind.JAXBElement;
import javax.xml.datatype.DatatypeConstants;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;

import aramex.shipping.wsdl.Address;
import aramex.shipping.wsdl.ArrayOfAttachment;
import aramex.shipping.wsdl.ArrayOfNotification;
import aramex.shipping.wsdl.ArrayOfPickupItemDetail;
import aramex.shipping.wsdl.ArrayOfShipment;
import aramex.shipping.wsdl.ArrayOfShipmentItem;
import aramex.shipping.wsdl.Attachment;
import aramex.shipping.wsdl.ClientInfo;
import aramex.shipping.wsdl.Contact;
import aramex.shipping.wsdl.LabelInfo;
import aramex.shipping.wsdl.LabelPrintingRequest;
import aramex.shipping.wsdl.LabelPrintingResponse;
import aramex.shipping.wsdl.Money;
import aramex.shipping.wsdl.Notification;
import aramex.shipping.wsdl.ObjectFactory;
import aramex.shipping.wsdl.Party;
import aramex.shipping.wsdl.Pickup;
import aramex.shipping.wsdl.PickupCancelationRequest;
import aramex.shipping.wsdl.PickupCancelationResponse;
import aramex.shipping.wsdl.PickupCreationRequest;
import aramex.shipping.wsdl.PickupCreationResponse;
import aramex.shipping.wsdl.PickupItemDetail;
import aramex.shipping.wsdl.ProcessedPickup;
import aramex.shipping.wsdl.ProcessedShipment;
import aramex.shipping.wsdl.Shipment;
import aramex.shipping.wsdl.ShipmentCreationRequest;
import aramex.shipping.wsdl.ShipmentCreationResponse;
import aramex.shipping.wsdl.ShipmentDetails;
import aramex.shipping.wsdl.ShipmentItem;
import aramex.shipping.wsdl.Weight;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import su.reddot.infrastructure.logistic.aramex.AramexConfiguration;
import su.reddot.infrastructure.logistic.aramex.client.AramexAddress;
import su.reddot.infrastructure.logistic.aramex.client.AramexAttachment;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientException;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientException.AramexError;
import su.reddot.infrastructure.logistic.aramex.client.AramexClientUtils;
import su.reddot.infrastructure.logistic.aramex.client.AramexConstants;
import su.reddot.infrastructure.logistic.aramex.client.AramexMoney;
import su.reddot.infrastructure.logistic.aramex.client.AramexPickupRequest;
import su.reddot.infrastructure.logistic.aramex.client.AramexPickupResponse;
import su.reddot.infrastructure.logistic.aramex.client.AramexShipmentRequest;
import su.reddot.infrastructure.logistic.aramex.client.AramexShippingClient;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.soap.client.core.SoapActionCallback;

@RequiredArgsConstructor
@Component
@ConditionalOnProperty(name = "app.integration.aramex.rest-enabled", havingValue = "false")
public class AramexShippingSoapClient extends WebServiceGatewaySupport implements AramexShippingClient {

    private final AramexConfiguration configuration;

    private final ObjectFactory objectFactory = new ObjectFactory();

    @PostConstruct
    public void configure() {
        final Jaxb2Marshaller marshaller = new Jaxb2Marshaller();
		marshaller.setContextPath("aramex.shipping.wsdl");
		setDefaultUri(configuration.getShippingEndpoint());
		setMarshaller(marshaller);
		setUnmarshaller(marshaller);
    }

    @Override
    @NonNull
    public AramexPickupResponse createPickup(@NonNull final AramexPickupRequest request) throws AramexClientException {
        final PickupCreationRequest soapRequest = objectFactory.createPickupCreationRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        final Pickup pickup = objectFactory.createPickup();
        pickup.setReference1(objectFactory.createPickupReference1(UUID.randomUUID().toString()));
        pickup.setStatus(objectFactory.createPickupStatus(AramexConstants.PICKUP_STATE_READY));
        final AramexAddress address = request.getAddress();
        pickup.setPickupAddress(createAddress(address));
        pickup.setPickupContact(createContact(address));
        pickup.setPickupLocation(address.getPickupLocation());
        pickup.setPickupDate(convertToXmlCalendar(AramexClientUtils.toZonedDateTime(request.getDate())));
        pickup.setReadyTime(convertToXmlCalendar(AramexClientUtils.toZonedDateTime(request.getDate(), request.getFromTime())));
        pickup.setLastPickupTime(convertToXmlCalendar(AramexClientUtils.toZonedDateTime(request.getDate(), request.getToTime())));
        pickup.setClosingTime(convertToXmlCalendar(AramexClientUtils.toZonedDateTime(request.getDate(), request.getClosingTime())));

        final ArrayOfPickupItemDetail pickupItemDetails = objectFactory.createArrayOfPickupItemDetail();
        final PickupItemDetail pickupItemDetail = objectFactory.createPickupItemDetail();
        final boolean isInternational = address.isInternational(configuration);
        pickupItemDetail.setProductGroup(AramexConstants.getProductGroup(isInternational));
        pickupItemDetail.setProductType(AramexConstants.getProductType(isInternational));
        pickupItemDetail.setPayment(request.isFromSellerToOffice()
                ? AramexConstants.PAYMENT_TYPE_FROM_SELLER
                : AramexConstants.PAYMENT_TYPE_TO_BUYER);
        pickupItemDetail.setNumberOfShipments(1);
        pickupItemDetails.getPickupItemDetail().add(pickupItemDetail);
        pickup.setPickupItems(pickupItemDetails);

        soapRequest.setPickup(objectFactory.createPickup(pickup));

        final PickupCreationResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/CreatePickup",
                PickupCreationResponse.class);

        checkErrors(soapResponse.getNotifications());

        final ProcessedPickup processedPickup = soapResponse.getProcessedPickup().getValue();

        return AramexPickupResponse.builder()
                .id(processedPickup.getID())
                .guid(processedPickup.getGUID())
                .build();
    }

    @Override
    public void cancelPickup(@NonNull final String pickupGuid) throws AramexClientException {
        final PickupCancelationRequest soapRequest = objectFactory.createPickupCancelationRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        soapRequest.setPickupGUID(objectFactory.createShipmentPickupGUID(pickupGuid));

        final PickupCancelationResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/CancelPickup",
                PickupCancelationResponse.class);

        checkErrors(soapResponse.getNotifications());
    }

    @Override
    @NonNull
    public String createShipment(@NonNull final AramexShipmentRequest request) throws AramexClientException {
        final ShipmentCreationRequest soapRequest = objectFactory.createShipmentCreationRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        final Shipment shipment = objectFactory.createShipment();
        shipment.setReference1(objectFactory.createShipmentReference1(request.getReference()));
        shipment.setComments(objectFactory.createShipmentComments(request.getComment()));

        final ShipmentDetails shipmentDetails = objectFactory.createShipmentDetails();
        shipmentDetails.setNumberOfPieces(1);
        shipmentDetails.setActualWeight(createWeight(request.getTotalWeightKg()));

        final AramexAddress pickupAddress = request.getPickupAddress();
        final boolean isInternational = pickupAddress.isInternational(configuration);
        if (isInternational) {
            final AramexMoney customsMoney = request.getCustomsValueAmount();
            if (customsMoney == null) {
                throw new AramexClientException("Customs amount is mandatory for international shipments");
            }
            final Money customsValueAmount = objectFactory.createMoney();
            customsValueAmount.setValue(customsMoney.getValue());
            customsValueAmount.setCurrencyCode(customsMoney.getCurrencyCode());
            shipmentDetails.setCustomsValueAmount(objectFactory.createShipmentDetailsCustomsValueAmount(customsValueAmount));
        }

        shipmentDetails.setProductGroup(AramexConstants.getProductGroup(isInternational));
        shipmentDetails.setProductType(AramexConstants.getProductType(isInternational));
        shipmentDetails.setPaymentType(request.isFromSellerToOffice()
                ? AramexConstants.PAYMENT_TYPE_FROM_SELLER
                : AramexConstants.PAYMENT_TYPE_TO_BUYER);
        shipmentDetails.setPaymentOptions(request.isFromSellerToOffice()
                ? AramexConstants.PAYMENT_OPTIONS_FROM_SELLER
                : AramexConstants.PAYMENT_OPTIONS_TO_BUYER);
        shipmentDetails.setDescriptionOfGoods(AramexClientUtils.cutDescriptionString(request.getDescription()));
        shipmentDetails.setGoodsOriginCountry(configuration.getAccountCountryCode());
        final ArrayOfShipmentItem arrayOfShipmentItem = objectFactory.createArrayOfShipmentItem();
        request.getItems().stream()
                .map(this::createShipmentItem)
                .forEach(arrayOfShipmentItem.getShipmentItem()::add);
        shipmentDetails.setItems(objectFactory.createArrayOfShipmentItem(arrayOfShipmentItem));
        shipment.setDetails(shipmentDetails);

        shipment.setShipper(createParty(pickupAddress));
        shipment.setConsignee(createParty(request.getDeliveryAddress()));

        final ZonedDateTime now = AramexClientUtils.getCurrentTime();
        shipment.setShippingDateTime(convertToXmlCalendar(now));
        shipment.setDueDate(convertToXmlCalendar(now.plusDays(AramexConstants.SHIPMENT_DUE_DAYS)));

        shipment.setPickupGUID(objectFactory.createShipmentPickupGUID(request.getPickupGuid()));

        // Attach document
        final AramexAttachment aramexAttachment = request.getAttachment();
        if (aramexAttachment != null) {
            final Attachment attachment = objectFactory.createAttachment();
            attachment.setFileName(aramexAttachment.getFileName());
            attachment.setFileExtension(aramexAttachment.getFileExtension());
            attachment.setFileContents(aramexAttachment.getFileContents());
            final ArrayOfAttachment arrayOfAttachment = objectFactory.createArrayOfAttachment();
            arrayOfAttachment.getAttachment().add(attachment);
            shipment.setAttachments(objectFactory.createShipmentAttachments(arrayOfAttachment));
        }

        final ArrayOfShipment arrayOfShipment = objectFactory.createArrayOfShipment();
        arrayOfShipment.getShipment().add(shipment);
        soapRequest.setShipments(arrayOfShipment);

        final ShipmentCreationResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/CreateShipments",
                ShipmentCreationResponse.class);

        checkErrors(soapResponse);

        return soapResponse.getShipments().getValue().getProcessedShipment().get(0).getID();
    }

    @Override
    public byte[] getLabelPdf(@NonNull final String shipmentNumber) throws AramexClientException {
        final LabelPrintingRequest soapRequest = objectFactory.createLabelPrintingRequest();

        // Set auth properties
        soapRequest.setClientInfo(createClientInfo());

        // Set label report parameters
        soapRequest.setLabelInfo(createLabelInfo());

        soapRequest.setShipmentNumber(objectFactory.createLabelPrintingRequestShipmentNumber(shipmentNumber));

        final LabelPrintingResponse soapResponse = sendRequest(soapRequest,
                "http://ws.aramex.net/ShippingAPI/v1/Service_1_0/PrintLabel",
                LabelPrintingResponse.class);

        checkErrors(soapResponse.getNotifications());

        return soapResponse.getShipmentLabel().getValue().getLabelFileContents();
    }

    @NonNull
    private JAXBElement<ClientInfo> createClientInfo() {
        final ClientInfo clientInfo = objectFactory.createClientInfo();
        clientInfo.setUserName(configuration.getUsername());
        clientInfo.setPassword(configuration.getPassword());
        clientInfo.setAccountEntity(configuration.getAccountEntity());
        clientInfo.setAccountNumber(configuration.getAccountNumber());
        clientInfo.setAccountPin(configuration.getAccountPin());
        clientInfo.setAccountCountryCode(configuration.getAccountCountryCode());
        clientInfo.setVersion(configuration.getVersion());
        return objectFactory.createClientInfo(clientInfo);
    }

    @NonNull
    private JAXBElement<LabelInfo> createLabelInfo() {
        final LabelInfo labelInfo = objectFactory.createLabelInfo();
        labelInfo.setReportID(AramexConstants.LABEL_REPORT_ID);
        labelInfo.setReportType(AramexConstants.LABEL_REPORT_TYPE);
        return objectFactory.createLabelInfo(labelInfo);
    }

    @NonNull
    private Weight createWeight(final double weightKg) {
        final Weight weight = objectFactory.createWeight();
        weight.setUnit("kg");
        weight.setValue(weightKg);
        return weight;
    }

    @NonNull
    private ShipmentItem createShipmentItem(final @NonNull AramexShipmentRequest.Item item) {
        final ShipmentItem shipmentItem = objectFactory.createShipmentItem();
        shipmentItem.setPackageType(AramexConstants.PACKAGE_TYPE);
        shipmentItem.setQuantity(1);
        shipmentItem.setGoodsDescription(objectFactory.createShipmentItemGoodsDescription(
                AramexClientUtils.cutDescriptionString(item.getDescription())));
        shipmentItem.setWeight(createWeight(item.getWeightKg()));
        return shipmentItem;
    }

    @NonNull
    private Party createParty(@NonNull final AramexAddress aramexAddress) {
        final Party party = objectFactory.createParty();
        party.setReference1(objectFactory.createPartyReference1(aramexAddress.getPersonName()));
        party.setAccountNumber(objectFactory.createPartyAccountNumber(configuration.getAccountNumber()));
        party.setContact(createContact(aramexAddress));
        party.setPartyAddress(createAddress(aramexAddress));
        return party;
    }

    @NonNull
    private Address createAddress(@NonNull final AramexAddress aramexAddress) {
        final Address address = objectFactory.createAddress();
        address.setLine1(aramexAddress.getLine1());
        address.setLine2(aramexAddress.getLine2());
        address.setLine3(aramexAddress.getLine3());
        address.setCity(aramexAddress.getCity());
        address.setStateOrProvinceCode(objectFactory.createAddressStateOrProvinceCode(
                aramexAddress.getStateOrProvinceCode()));
        address.setPostCode(aramexAddress.getPostCode());
        address.setCountryCode(aramexAddress.getCountryCode());
        address.setBuildingNumber(objectFactory.createAddressBuildingNumber(aramexAddress.getBuildingNumber()));
        address.setBuildingName(objectFactory.createAddressBuildingName(aramexAddress.getBuildingName()));
        address.setFloor(objectFactory.createAddressFloor(aramexAddress.getFloor()));
        address.setApartment(objectFactory.createAddressApartment(aramexAddress.getApartment()));
        return address;
    }

    @NonNull
    private Contact createContact(@NonNull final AramexAddress address) {
        final Contact contact = objectFactory.createContact();
        contact.setPersonName(address.getPersonName());
        contact.setCompanyName(objectFactory.createContactCompanyName(address.getCompanyName()));
        final String phone = address.getPhoneNumber();
        contact.setPhoneNumber1(phone);
        contact.setCellPhone(objectFactory.createContactCellPhone(phone));
        return contact;
    }

    @SneakyThrows
    @NonNull
    private static XMLGregorianCalendar convertToXmlCalendar(@NonNull final ZonedDateTime zdt) {
        final XMLGregorianCalendar calendar = DatatypeFactory.newInstance()
                .newXMLGregorianCalendar(GregorianCalendar.from(zdt));
        calendar.setTimezone(DatatypeConstants.FIELD_UNDEFINED);
        calendar.setMillisecond(DatatypeConstants.FIELD_UNDEFINED);
        return calendar;
    }

    private static void checkErrors(@NonNull final JAXBElement<ArrayOfNotification> notifications)
            throws AramexClientException {
        checkErrors(notifications.getValue().getNotification());
    }

    private static void checkErrors(@NonNull final List<Notification> notifications)
            throws AramexClientException {
        final List<AramexError> errors = notifications
                .stream()
                .map(AramexShippingSoapClient::toAramexError)
                .collect(Collectors.toList());
        if (!errors.isEmpty()) {
            throw new AramexClientException(errors);
        }
    }

    private static void checkErrors(@NonNull final ShipmentCreationResponse soapResponse)
            throws AramexClientException {
        final Stream<Notification> globalNotifications = soapResponse.getNotifications().getValue()
                .getNotification().stream();
        final Stream<Notification> shipmentsNotifications = soapResponse.getShipments().getValue()
                .getProcessedShipment().stream()
                .flatMap(processedShipment -> processedShipment.getNotifications().getNotification().stream());
        final List<Notification> notifications = Stream.concat(globalNotifications, shipmentsNotifications)
                .collect(Collectors.toList());
        checkErrors(notifications);

        final List<ProcessedShipment> processedShipments = soapResponse.getShipments().getValue()
                .getProcessedShipment();
        if (processedShipments.size() != 1) {
            throw new AramexClientException("Unexpected number of processed shipments: " + processedShipments.size());
        }
    }

    private static AramexError toAramexError(@NonNull final Notification notification) {
        return new AramexError(notification.getCode(), notification.getMessage());
    }

    @NonNull
    private <T> T sendRequest(
            @NonNull final Object request,
            @NonNull final String action,
            @NonNull final Class<T> responseClass) throws AramexClientException {
        final SoapActionCallback actionCallback = new SoapActionCallback(action);
        final Object response = getWebServiceTemplate().marshalSendAndReceive(request, actionCallback);
        if (response == null) {
            throw new AramexClientException("Empty response: " + responseClass.getSimpleName());
        }
        return responseClass.cast(response);
    }
}
