package su.reddot.infrastructure.logistic.aramex.client.rest.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class Shipment {

    private String reference1;

    private String reference2;

    private String reference3;

    private Party shipper;

    private Party consignee;

    private Party thirdParty;

    private String shippingDateTime;

    private String dueDate;

    private String comments;

    private String pickupLocation;

    private String operationsInstructions;

    private ShipmentDetails details;

    private String foreignHAWB;

    @JsonProperty("PickupGUID")
    private String pickupGUID;

    private String number;
}
