package su.reddot.infrastructure.logistic.aramex.client;

import java.time.LocalDate;
import java.time.LocalTime;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Builder
@Getter
public class AramexPickupRequest {

    @NonNull
    private final AramexAddress address;

    private final boolean fromSellerToOffice;

    @NonNull
    private final LocalDate date;

    @NonNull
    private final LocalTime fromTime;

    @NonNull
    private final LocalTime toTime;

    @NonNull
    private final LocalTime closingTime;
}
