package su.reddot.infrastructure.logistic.aramex.client;

import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Builder
@Getter
public class AramexShipmentsTrackingResponse {

    @NonNull
    private final List<AramexWaybill> existingWaybills;

    @NonNull
    private final List<String> nonExistingWaybills;

}
