package su.reddot.infrastructure.logistic;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.common.collect.ImmutableList;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import su.reddot.domain.service.integration.logistic.api.TimeIntervalsProcessorApi;
import su.reddot.domain.service.integration.logistic.model.TimeIntervalDTO;
import su.reddot.infrastructure.logistic.service.LogisticServiceMapper;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class TimeIntervalsService {

    private static final List<TimeIntervalDTO> DEFAULT_INTERVALS = ImmutableList.of(
            new TimeIntervalDTO().id(1L).fromHour(9).toHour(14),
            new TimeIntervalDTO().id(2L).fromHour(14).toHour(18),
            new TimeIntervalDTO().id(3L).fromHour(9).toHour(18));

    private final TimeIntervalsProcessorApi timeIntervalsProcessorApi;

    private final LogisticServiceMapper logisticServiceMapper;

    @Value("${app.integration.logistic.isEnabled}")
    private boolean isLogisticsServiceEnabled;

    @NonNull
    @Cacheable(value = "TimeIntervalsService.getDefaultIntervals")
    public List<su.reddot.domain.service.dto.delivery.TimeIntervalDTO> getDefaultIntervals() {
        Stream<TimeIntervalDTO> timeIntervalsStream = DEFAULT_INTERVALS.stream();
        if (isLogisticsServiceEnabled) {
            // Сервис Логистики доступен
            final List<TimeIntervalDTO> timeIntervals = timeIntervalsProcessorApi.getTimeIntervals().getItems();
            timeIntervalsStream = timeIntervalsStream
                    .map(defaultTimeInterval -> findTimeInterval(timeIntervals, defaultTimeInterval));
        }
        return timeIntervalsStream
                .map(logisticServiceMapper::convertToApiTimeInterval)
                .collect(Collectors.toList());
    }

    @NonNull
    @Cacheable(value = "TimeIntervalsService.getInterval", key = "#timeIntervalId")
    public TimeIntervalDTO getInterval(final long timeIntervalId) {
        if (isLogisticsServiceEnabled) {
            // Сервис Логистики доступен
            return timeIntervalsProcessorApi.getTimeInterval(timeIntervalId);
        } else {
            // Сервис Логистики недоступен
            return DEFAULT_INTERVALS.stream()
                    .filter(timeInterval -> timeInterval.getId() == timeIntervalId)
                    .findAny()
                    .orElseThrow(() -> new IllegalStateException("Cannot find time interval with ID " + timeIntervalId));
        }
    }

    @NonNull
    private static TimeIntervalDTO findTimeInterval(
            @NonNull final  List<TimeIntervalDTO> timeIntervals,
            @NonNull final TimeIntervalDTO timeIntervalToFind) {
        final Integer fromHour = timeIntervalToFind.getFromHour();
        final Integer toHour = timeIntervalToFind.getToHour();
        return timeIntervals.stream()
                .filter(timeInterval -> Objects.equals(timeInterval.getFromHour(), fromHour))
                .filter(timeInterval -> Objects.equals(timeInterval.getToHour(), toHour))
                .findAny().orElseThrow(() -> new IllegalStateException(String.format(
                        "Cannot find time interval: %s - %s", fromHour, toHour)));
    }
}
