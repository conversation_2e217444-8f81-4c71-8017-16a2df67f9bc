package su.reddot.infrastructure.export.feed;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import com.opencsv.CSVWriter;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import ru.oskelly.monolith.dto.event.OrderStateDTO;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.ourselvesdelivery.OurselvesDelivery;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.messaging.SyncOrderService;
import su.reddot.infrastructure.util.CallInTransaction;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class SyncedOrderExporter {

    private static final String FILE_NAME = "SyncedOrders.csv";

    private static final String SKLAD_OSKELLY = "Oskelly";

    @Value("${app.feedExport.oneass.defaultPathToExportFile}")
    private String exportPath;

    private final OrderRepository orderRepository;

    private final SyncOrderService syncOrderService;

    private final ConfigParamService configParamService;

    private final CallInTransaction callInTransaction;

    @NonNull
    public String getExportFilePath() {
        final String exportPathWithSeparator = exportPath.endsWith("/") ? exportPath : exportPath + "/";
        return exportPathWithSeparator + FILE_NAME;
    }

    @NonNull
    public String getExportFileName() {
        return FILE_NAME;
    }

    public void exportSyncedOrders() throws IOException {
        final List<Long> orderIds = orderRepository.getSyncedOrderIds();

        new File(exportPath).mkdirs();
        final Writer writer = new FileWriter(getExportFilePath());

        exportSyncedOrders(orderIds, writer);
    }

    @NonNull
    public String exportSyncedOrders(@NonNull final List<Long> orderIds) throws IOException {
        final Writer writer = new StringWriter();

        exportSyncedOrders(orderIds, writer);

        return writer.toString();
    }

    private void exportSyncedOrders(
            @NonNull final List<Long> orderIds,
            @NonNull final Writer writer) throws IOException {
        final long startTimeMs = System.currentTimeMillis();

        final CSVWriter csvWriter = new CSVWriter(writer);

        // Write header
        csvWriter.writeNext(new String[] {
                "Заказ",
                "Статус",
                "Дата",
                "Склад"
        });

        final Set<Long> blackList = getBlackList();

        final AtomicLong orderCount = new AtomicLong(0);
        final AtomicLong stateCount = new AtomicLong(0);
        for (final long orderId : orderIds) {
            if (blackList.contains(orderId)) {
                continue;
            }
            callInTransaction.runInNewTransaction(() -> {
                final Order order = orderRepository.getOne(orderId);
                long previousTimestamp = 0;
                OrderStateDTO previousState = null;
                for (final OrderStateDTO state : syncOrderService.getSyncedStates(order)) {
                    final long stateTimestamp = syncOrderService.getOrderStateTimestamp(orderId, state) * 1000;
                    final String stateLdtString = toDateTimeString(stateTimestamp);
                    if (stateTimestamp < previousTimestamp) {
                        final String previousStateLdtString = toDateTimeString(previousTimestamp);
                        log.error("Unexpected times in state change from {} (time - {}) to {} (time - {}) for order {}",
                                previousState, previousStateLdtString, state, stateLdtString, orderId);
                    }
                    csvWriter.writeNext(new String[]{
                            String.valueOf(orderId),
                            convertOrderState(order, state),
                            stateLdtString,
                            getSklad(order, state)});
                    previousTimestamp = stateTimestamp;
                    previousState = state;
                    stateCount.incrementAndGet();
                }

                orderCount.incrementAndGet();
            });
            if (orderCount.get() % 100 == 0) {
                csvWriter.flush();
            }
            if (orderCount.get() % 10_000 == 0) {
                log.info("Export in progress: exported {} orders", orderCount);
            }
        }

        csvWriter.flush();

        final long elapsedSec = (System.currentTimeMillis() - startTimeMs) / 1000;
        log.info("Exported {} states for {} orders in {} sec", stateCount, orderCount, elapsedSec);
    }

    @NonNull
    private Set<Long> getBlackList() {
        return configParamService.getValueAsList(ConfigParamService.CONFIG_PARAM_SYNCED_ORDERS_EXPORT_BLACK_LIST)
                .stream()
                .map(Long::parseLong)
                .collect(Collectors.toSet());
    }

    private static String toDateTimeString(long timestamp) {
        final LocalDateTime stateLdt = LocalDateTime
                .ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of("Europe/Moscow"))
                .truncatedTo(ChronoUnit.SECONDS);
        return DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(stateLdt);
    }

    @NonNull
    private String convertOrderState(
            @NonNull final Order order,
            @NonNull final OrderStateDTO orderState) {
        switch (orderState) {
            case DELIVERY_FROM_SELLER:
                return "Забор товара у Продавца";
            case EXPERTISE_STARTED:
                return"Экспертиза начата";
            case EXPERTISE_FINISHED:
                return syncOrderService.isExpertiseFailed(order)
                        ? "Не прошел экспертизу"
                        : "Экспертиза завершена";
            case WAITING_FOR_DELIVERY_TO_BUYER:
                return "Ожидает КС";
            case DELIVERY_TO_BUYER:
                return "Передан в КС";
            case DELIVERED_TO_BUYER:
                return "Доставлен";
            case SALE_REPORT_CREATED:
                return "Создан отчет о продаже";
            case PAID_OUT:
                return "Выплачен";
            case RETURN:
                return "Возврат поставщику";
            default:
                throw new IllegalStateException();
        }
    }

    @NonNull
    private static String getSklad(
            @NonNull final Order order,
            @NonNull final OrderStateDTO orderState) {
        switch (orderState) {
            case DELIVERY_FROM_SELLER:
                return getFromSellerDeliveryCompany(order);
            case DELIVERY_TO_BUYER:
                return getToBuyerDeliveryCompany(order);
            default:
                return "";
        }
    }

    @NonNull
    private static String getFromSellerDeliveryCompany(@NonNull final Order order) {
        final Waybill waybill = order.getLastWaybillFromSeller(true);
        if (waybill != null) {
            return getDeliveryCompanyName(waybill);
        }
        final OurselvesDelivery ourselvesDelivery = order.getOurselvesDeliveryToExpertise();
        if (ourselvesDelivery != null) {
            return SKLAD_OSKELLY;
        }
        throw new IllegalStateException("Cannot find from seller delivery company for order: " + order.getId());
    }

    @NonNull
    private static String getToBuyerDeliveryCompany(@NonNull final Order order) {
        final Waybill waybill = order.getLastWaybillToBuyer(true);
        if (waybill != null) {
            return getDeliveryCompanyName(waybill);
        }
        final OurselvesDelivery ourselvesDelivery = order.getOurselvesDeliveryToBuyer();
        if (ourselvesDelivery != null) {
            return SKLAD_OSKELLY;
        }
        throw new IllegalStateException("Cannot find to buyer delivery company for order: " + order.getId());
    }

    @NonNull
    private static String getDeliveryCompanyName(@NonNull final Waybill waybill) {
        final String name = waybill.getDeliveryCompany().getName();
        return "OSKELLY".equals(name) ? SKLAD_OSKELLY : name;
    }
}
