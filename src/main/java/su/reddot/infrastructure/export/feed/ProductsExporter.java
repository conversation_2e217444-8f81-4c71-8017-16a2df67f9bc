package su.reddot.infrastructure.export.feed;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ProductsExporter extends FeedExporter {

	@Getter
    @Value("${app.feedExport.products.defaultPathToExportFile}")
    private String defaultExportPath;

	@Getter
    @Value("${app.feedExport.products.filename}")
    private String exportFileName;

	@Override
	public String getExportName(){
		return "Products";
	}

	@Override
	public String getQueryFileName(){
		return "sql/products.sql";
	}

}
