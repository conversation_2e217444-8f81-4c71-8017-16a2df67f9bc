package su.reddot.infrastructure.export.feed;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class CatalogReportExporter extends XMLFeedExporter {

	@Getter
    @Value("${app.feedExport.catalog-report.defaultPathToExportFile}")
    private String defaultExportPath;

	@Getter
    @Value("${app.feedExport.catalog-report.filename}")
    private String exportFileName;

	@Override
	public String getExportName(){
		return "CatalogReport";
	}

	@Override
	public String getQueryFileName(){
		return "sql/catalog.sql";
	}

}
