package su.reddot.infrastructure.export.feed;

import com.google.common.base.Strings;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class FacebookFeedExporter extends XMLFeedExporter {

    @Value("${app.feedExport.facebook.defaultPathToExportFile}")
	@Getter
    private String defaultExportPath;

    @Value("${app.feedExport.facebook.filename}")
	@Getter
    private String exportFileName;



	/**
	 * Конфиг-файл с кастомными настройками. Пример:
	 fb_oskelly.xml p.id > 500 AND p.id < 600
	 fb_oskelly_top_50.xml p.id IN (20899,20897,20783,20657,20308,20065,20035,20027,19647,19527,20895,20754,20448,20436,20220,20120,20104,19190,19212,19201,20873,20874,20882,20736,20551,20422,20241,19867,19618,19184,20908,20876,20821,20561,20546,19843,19910,19387,19021,19025,20112,20014,19680,19685,19684,19671,18548,16709,16858,11830,20598,20434,20432,20160,19280,18212,18092,17727,16785,12170,20444,18278,17731,15220,14045,13594,11977,11103,11101,11062,20761,20757,20761,20760,20755,20661,19734,19677,19525,17683)
	 fb_oskelly_no_rrp.xml p.rrp_price IS NULL
	 */
	@Value("${app.feedExport.facebook.config}")
	private String configFilename;

	public static final String productFilterStartStr = "/" + "* product_filter *" + "/";
	public static final String productFilterEndStr = "/" + "* /product_filter *" + "/";

    @Override
    public String extractData() {
	    return extractData(getQuery());
    }

	@Override
	public String getExportName() {
		return "FacebookFeed";
	}

	@Override
	public String getQueryFileName() {
		return "sql/facebook_feed.sql";
	}

    @Override
    public void defaultDelivery() throws IOException {
        log.info("Генерация выгрузки в формате Facebook");

	    Map<String, String> filename2productFilter = getExportConfiguration();
	    String query = getQuery();

		for(Map.Entry<String, String> fileAndFilter : filename2productFilter.entrySet()){
			String exportData = extractData(query, fileAndFilter.getValue());
			save(fileAndFilter.getKey(), exportData);
		}

        log.info("Выгрузка в формате FaceBook сохранена");

    }

	private String extractData(String query, String productFilter) {
    	String queryWithFilter = setProductFilter(query, productFilter);
		return extractData(queryWithFilter);
	}

    private void save(String filename, String exportData) throws IOException{
    	File file = new File(defaultExportPath + filename);
    	file.getParentFile().mkdirs();
	    BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(defaultExportPath + filename), StandardCharsets.UTF_8));
	    writer.write(exportData);
	    writer.close();
    }

    private Map<String, String> getExportConfiguration(){
	    Map<String, String> filename2productFilter = new HashMap<>();
	    filename2productFilter.put(exportFileName, "TRUE");
	    if(!Strings.isNullOrEmpty(configFilename)){
	    	try {
			    File configFile = new File(configFilename);
			    if(!configFile.exists()) configFile = new File(getClass().getClassLoader().getResource(configFilename).getFile());
			    if(configFile.exists() && configFile.isFile() && configFile.canRead()) {
				    BufferedReader reader = new BufferedReader(new FileReader(configFile));
				    String line;
				    while ((line = reader.readLine()) != null) {
					    if (line.isEmpty() || !line.contains(" ")) continue;
					    int firstSpaceIndex = line.indexOf(" ");
					    String exportFilename = line.substring(0, firstSpaceIndex).trim();
					    String exportProductFilter = line.substring(firstSpaceIndex + 1).trim();
					    if(Strings.isNullOrEmpty(exportFilename) || Strings.isNullOrEmpty(exportProductFilter)) continue;
					    filename2productFilter.put(exportFilename, exportProductFilter);
				    }
				    reader.close();
			    }
		    }
		    catch(FileNotFoundException e){
	    		log.warn("Конфигурационный файл для экспорта facebook feed не найден: " + configFilename);
		    }
		    catch(IOException e){
			    log.warn("Ошибка чтения конфигурационного файла: " + configFilename);
		    }
	    }
	    return filename2productFilter;
    }

    public String setProductFilter(@NonNull String query, @NonNull String productFilter){
		int productFilterStartIndex = query.indexOf(productFilterStartStr) + productFilterStartStr.length();
	    int productFilterEndIndex = query.indexOf(productFilterEndStr);
	    //Что-то пошло не так
	    if(productFilterStartIndex > productFilterEndIndex) return query;
	    return query.substring(0, productFilterStartIndex) + " " + productFilter + " " + query.substring(productFilterEndIndex);
    }
}
