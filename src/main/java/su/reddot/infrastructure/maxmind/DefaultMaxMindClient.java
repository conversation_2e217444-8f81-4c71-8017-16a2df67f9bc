package su.reddot.infrastructure.maxmind;

import com.maxmind.db.Reader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import su.reddot.infrastructure.maxmind.dto.MaxMindCountryDto;
import su.reddot.infrastructure.maxmind.dto.MaxMindDbResultDto;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class DefaultMaxMindClient implements MaxMindClient {

    private final Reader maxMindReader;

    @Override
    public String getCountryIsoCodeByIp(String ip) {
        if (ip == null) return null;
        try {
            return Optional.ofNullable(maxMindReader.get(InetAddress.getByName(ip), MaxMindDbResultDto.class))
                    .map(MaxMindDbResultDto::getCountry)
                    .map(MaxMindCountryDto::getIsoCode)
                    .orElse(null);
        } catch (UnknownHostException e) {
            log.error("Invalid ip address: " + ip);
            return null;
        } catch (IOException e) {
            log.error("Error on getting country by ip: " + ip, e);
        }
        return null;
    }
}
