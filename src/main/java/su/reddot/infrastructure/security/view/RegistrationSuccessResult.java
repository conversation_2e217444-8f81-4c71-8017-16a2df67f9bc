package su.reddot.infrastructure.security.view;

import org.springframework.http.ResponseEntity;
import su.reddot.domain.model.user.User;

import javax.annotation.Nonnull;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public class RegistrationSuccessResult implements SocialRegistrationResult {

	private final User user;

	private final NetworkType type;

	public RegistrationSuccessResult(@Nonnull User user, @Nonnull NetworkType type) {
		this.user = user;
		this.type = type;
	}

	@Override
	public Optional<User> getUser() {
		return Optional.of(user);
	}

	@Override
	public Map<String, String> getAuthCredentials(){
		Map<String, String> result = new HashMap<>(2);
		switch (type) {
			case FACEBOOK:
				result.put("facebook_id", user.getRestFacebookId());
				result.put("fb_uuid", user.getUserFbUuid());
				break;
			case VK:
				result.put("vk_id", user.getRestVkId());
				result.put("vk_uuid", user.getUserVkUuid());
				break;
			case APPLE:
				result.put("apple_id", user.getRestAppleId());
				result.put("apple_uuid", user.getUserAppleUuid());
				break;
		}
		return result;
	}

	@Override
	public ResponseEntity<?> toResponseEntity() {
		return ResponseEntity.ok(getAuthCredentials());
	}
}
