package su.reddot.infrastructure.security.view;

import lombok.Getter;
import org.springframework.http.ResponseEntity;

import java.util.Map;

public class SocialLoginNetworkInteractionError implements SocialLoginResult, SocialDetailedError {

	@Getter
	private final Map<String, Object> details;

	public SocialLoginNetworkInteractionError(Map<String, Object> details) {
		this.details = details;
	}

	@Override
	public ResponseEntity<?> toResponseEntity() {
		return ResponseEntity.badRequest().body(details);
	}

	@Override
	public Map<String, String> getAuthCredentials() {
		return null;
	}
}
