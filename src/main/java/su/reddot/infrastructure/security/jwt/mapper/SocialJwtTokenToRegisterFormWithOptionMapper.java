package su.reddot.infrastructure.security.jwt.mapper;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.stereotype.Component;
import su.reddot.infrastructure.security.SocialAuthProvider;
import su.reddot.infrastructure.security.jwt.dto.SocialUserInfoDTO;
import su.reddot.infrastructure.security.jwt.parser.SocialJwtTokenParser;
import su.reddot.infrastructure.security.view.PreFilledRegisterFormWithOptionsResponse;
import su.reddot.infrastructure.security.view.RegisterFormRequest;
import su.reddot.infrastructure.security.view.RegistrationOptionsResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class SocialJwtTokenToRegisterFormWithOptionMapper {

    private final Map<SocialAuthProvider, SocialJwtTokenParser> socialAuthProviderToJwtTokenParser;
    private final Map<SocialAuthProvider, SocialUserInfoToRegisterFormMapper> socialAuthProviderToSocialUserInfoToRegisterFormMapper;

    public SocialJwtTokenToRegisterFormWithOptionMapper(List<SocialJwtTokenParser> socialJwtTokenParsers, List<SocialUserInfoToRegisterFormMapper> socialUserInfoToRegisterFormMappers) {
        this.socialAuthProviderToJwtTokenParser = normalizeSocialJwtTokenParsers(socialJwtTokenParsers);
        this.socialAuthProviderToSocialUserInfoToRegisterFormMapper = normalizeSocialUserInfoToRegisterFormMappers(socialUserInfoToRegisterFormMappers);
    }

    private Map<SocialAuthProvider, SocialJwtTokenParser> normalizeSocialJwtTokenParsers(List<SocialJwtTokenParser> socialJwtTokenParsers) {
        Map<SocialAuthProvider, SocialJwtTokenParser> result = new HashMap<>();
        for (SocialAuthProvider socialAuthProvider : SocialAuthProvider.values()) {
            List<SocialJwtTokenParser> targetParsers = socialJwtTokenParsers.stream()
                    .filter(it -> Objects.equals(socialAuthProvider, it.targetProvider()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(targetParsers)) {
                throw new BeanInitializationException(
                        "Could not find SocialJwtTokenParser which has SocialAuthProvider:" +
                                socialAuthProvider.name() + " in targetProvider");
            }

            if (targetParsers.size() > 1) {
                throw new BeanInitializationException(
                        "Found more then 1 SocialJwtTokenParser which have SocialAuthProvider:" +
                                socialAuthProvider.name() + " in targetProvider");
            }

            result.put(socialAuthProvider, targetParsers.get(0));
        }
        return result;
    }

    private Map<SocialAuthProvider, SocialUserInfoToRegisterFormMapper> normalizeSocialUserInfoToRegisterFormMappers(List<SocialUserInfoToRegisterFormMapper> socialUserInfoToRegisterFormMappers) {
        Map<SocialAuthProvider, SocialUserInfoToRegisterFormMapper> result = new HashMap<>();
        for (SocialAuthProvider socialAuthProvider : SocialAuthProvider.values()) {
            List<SocialUserInfoToRegisterFormMapper> targetMappers = socialUserInfoToRegisterFormMappers.stream()
                    .filter(it -> CollectionUtils.isNotEmpty(it.targetSocialAuthProviders()))
                    .filter(it -> it.targetSocialAuthProviders().contains(socialAuthProvider))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(targetMappers)) {
                throw new BeanInitializationException(
                        "Could not find SocialUserInfoToRegisterFormMapper which has SocialAuthProvider:" +
                                socialAuthProvider.name() + " in targetSocialAuthProviders");
            }

            if (targetMappers.size() > 1) {
                throw new BeanInitializationException(
                        "Found more then 1 SocialUserInfoToRegisterFormMappers which have SocialAuthProvider:" +
                                socialAuthProvider.name() + " in targetSocialAuthProviders");
            }

            result.put(socialAuthProvider, targetMappers.get(0));
        }
        return result;
    }

    public PreFilledRegisterFormWithOptionsResponse mapSocialJwtTokenToRegisterFormWithOption(SocialAuthProvider socialAuthProvider, String socialJwtToken) {
        SocialUserInfoDTO socialUserInfo = socialAuthProviderToJwtTokenParser.get(socialAuthProvider).getSocialUserInfo(socialJwtToken);
        SocialUserInfoToRegisterFormMapper userInfoMapper = socialAuthProviderToSocialUserInfoToRegisterFormMapper
                .get(socialAuthProvider);
        RegisterFormRequest registerFormRequest = userInfoMapper.mapSocialUserInfoToRegisterForm(socialUserInfo);
        RegistrationOptionsResponse registrationOptionsResponse = userInfoMapper.mapSocialUserInfoToRegisterOptions(socialUserInfo);
        return new PreFilledRegisterFormWithOptionsResponse()
                .setRegisterForm(registerFormRequest)
                .setRegistrationOptions(registrationOptionsResponse);
    }
}
