package su.reddot.infrastructure.security.jwt.mapper;

import com.google.common.collect.ImmutableSet;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import su.reddot.infrastructure.security.SocialAuthProvider;
import su.reddot.infrastructure.security.jwt.parser.GoogleJwtTokenParser;

import java.util.Set;

@Component
@RequiredArgsConstructor
public class DefaultUserInfoToRegisterFormMapper implements SocialUserInfoToRegisterFormMapper {

    private static final Set<SocialAuthProvider> TARGET_SOCIAL_AUTH_PROVIDERS = ImmutableSet.of(
            SocialAuthProvider.APPLE,
            SocialAuthProvider.GOOGLE
    );

    private final GoogleJwtTokenParser yandexJwtTokenParser;

    @Override
    public Set<SocialAuthProvider> targetSocialAuthProviders() {
        return TARGET_SOCIAL_AUTH_PROVIDERS;
    }
}
