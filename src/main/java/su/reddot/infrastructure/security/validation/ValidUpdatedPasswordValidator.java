package su.reddot.infrastructure.security.validation;

import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.SecurityService;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import static su.reddot.infrastructure.security.SecurityService.UpdatePasswordRequest;

@RequiredArgsConstructor
public class ValidUpdatedPasswordValidator implements ConstraintValidator<ValidUpdatedPassword, UpdatePasswordRequest> {

	private final SecurityService securityService;
	private final UserService userService;
	private final BCryptPasswordEncoder passwordEncoder;

	public void initialize(ValidNewPassword constraint) {
	}

	public boolean isValid(UpdatePasswordRequest updatePasswordRequest,
	                       ConstraintValidatorContext context) {

		String password = updatePasswordRequest.getNewPassword();
		String passwordOnceMore = updatePasswordRequest.getNewPasswordOnceMore();
		String oldPassword = updatePasswordRequest.getOldPassword();

		if (Strings.isNullOrEmpty(password) || Strings.isNullOrEmpty(passwordOnceMore) || Strings.isNullOrEmpty(oldPassword)) {
			context.buildConstraintViolationWithTemplate("Не заданы обязательные поля").addConstraintViolation();
			return false;
		}

		if (!password.equals(passwordOnceMore)) {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Пароли не совпадают").addConstraintViolation();
			return false;
		}

		/* Использовать валидатор, назначенный на поле токена. */
		if (updatePasswordRequest.getOldPassword() == null) {
			return true;
		}

		User currentUser = securityService.getCurrentAuthorizedUser();
		if (currentUser == null) {
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Пользователь не авторизован").addConstraintViolation();
			return false;
		}
		if (!passwordEncoder.matches(oldPassword, currentUser.getHashedPassword())) { //Старый пароль неверный
			context.disableDefaultConstraintViolation();
			context.buildConstraintViolationWithTemplate("Неверный текущий пароль").addConstraintViolation();
			return false;
		}

		return true;
	}
}
