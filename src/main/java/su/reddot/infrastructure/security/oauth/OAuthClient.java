package su.reddot.infrastructure.security.oauth;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Map;

@RequiredArgsConstructor
public class OAuthClient {

    @Autowired
    protected TypeReference<Map<String, Object>> genericJsonTypeReference;
    @Autowired
    protected ObjectMapper mapper;

    @Qualifier("oauthHttpClient")
    @Autowired
    protected CloseableHttpClient closeableHttpClient;
}
