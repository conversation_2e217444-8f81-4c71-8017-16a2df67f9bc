package su.reddot.infrastructure.security.provider.auth;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserNotFoundException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserAuthorityService;
import su.reddot.infrastructure.security.SocialAuthProvider;
import su.reddot.infrastructure.security.jwt.dto.SocialUserCredentialsDTO;
import su.reddot.infrastructure.security.jwt.token.service.SocialAuthJwtTokenService;
import su.reddot.infrastructure.security.token.SocialReAuthorizationToken;

import static su.reddot.infrastructure.security.SocialAuthProvider.APPLE;

@Slf4j
@Component
@RequiredArgsConstructor
public class SocialReAuthProvider implements AuthenticationProvider {

    private final static String APPLE_PRIVATE_RELAY_EMAIL_DOMAIN = "@privaterelay.appleid.com";

    private final MessageSourceAccessor messageSourceAccessor;
    private final UserRepository userRepository;
    private final UserAuthorityService userAuthorityService;
    private final SocialAuthJwtTokenService socialAuthJwtTokenService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        SocialReAuthorizationToken socialReAuthorizationToken = (SocialReAuthorizationToken) authentication;
        String tokenUserEmail = socialReAuthorizationToken.getEmail();
        User user = userRepository.findByEmail(tokenUserEmail);

        // Здесь мы даём второй шанс, т. к. после трансфера приложения, такие имейлы поменялись так же, как и rest apple id
        if (user == null && tokenUserEmail != null && tokenUserEmail.endsWith(APPLE_PRIVATE_RELAY_EMAIL_DOMAIN)) {
            user = userRepository.findByOldApplePrivateRelayEmail(tokenUserEmail);
        }

        if (user == null) {
            log.error("SocialReAuthProvider Error: user with email {} not found", tokenUserEmail);
            throw new UserNotFoundException(messageSourceAccessor.getMessage("su.reddot.infrastructure.security.provider.auth.SocialReAuthProvider.UserNotFound", tokenUserEmail));
        }

        if (!isJwtValid(socialReAuthorizationToken, user)) {
            log.error("SocialReAuthProvider Error: invalid SocialReAuthorizationToken {}", socialReAuthorizationToken);
            throw new BadCredentialsException(messageSourceAccessor.getMessage("su.reddot.infrastructure.security.provider.auth.SocialReAuthProvider.BadJWTToken"));
        }

        return userAuthorityService.getUserIdIdentificationToken(user);
    }

    private boolean isJwtValid(SocialReAuthorizationToken socialReAuthorizationToken, User user) {
        SocialAuthProvider socialType = SocialAuthProvider.of(user);
        if (socialType == null) {
            log.error("SocialReAuthProvider Error:  user with id {} wasn't register by social way", user.getId());
            return false;
        }

        String socialAccountId = socialType.getSocialAccountId(user);
        SocialUserCredentialsDTO credentials = new SocialUserCredentialsDTO();
        credentials
                .setSocialType(socialType)
                .setEmail(socialReAuthorizationToken.getEmail())
                .setSocialAccountId(socialAccountId)
                .setUserId(user.getId())
                .setUid(user.getUid());

        // это условия здесь только потому, что я изначально не понимаю, зачем в качестве почты надо было брать
        // socialReAuthorizationToken.getEmail(), а не user.getEmail(), а починить проблему нужно только с apple
        // из-за их механизма миграции пользователей.
        // поэтому в будущем возможно надо отрефачить и просто протестить дополнительно яндекс и гугл авторизации
        if (APPLE.equals(socialType)) {
            credentials.setEmail(user.getEmail());
        }

        boolean isValid = socialAuthJwtTokenService.isJwtValid(credentials, socialReAuthorizationToken.getSocialReAuthJwtToken());
        if (isValid) {
            return true;
        }

        // т. к. у нас есть старые апол айди которые мы не хотим разлогинивать, дополнительно проверяем тут их
        String oldSocialAccountId = socialType.getOldSocialAccountId(user);
        if (oldSocialAccountId != null) {
            credentials.setSocialAccountId(oldSocialAccountId);

            if (APPLE.equals(socialType) &&
                    socialReAuthorizationToken.getEmail() != null &&
                    socialReAuthorizationToken.getEmail().endsWith(APPLE_PRIVATE_RELAY_EMAIL_DOMAIN) &&
                    user.getOldApplePrivateRelayEmail() != null) {
                credentials.setEmail(user.getOldApplePrivateRelayEmail());
            }

            return socialAuthJwtTokenService.isJwtValid(credentials, socialReAuthorizationToken.getSocialReAuthJwtToken());
        }

        return false;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return SocialReAuthorizationToken.class.isAssignableFrom(authentication);
    }
}
