package su.reddot.infrastructure.security.provider.register.v2;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.verification.VerificationService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.security.SocialAuthProvider;
import su.reddot.infrastructure.security.jwt.dto.SocialUserInfoDTO;
import su.reddot.infrastructure.security.jwt.parser.AppleJwtTokenParser;
import su.reddot.infrastructure.security.provider.register.OAuthRegistrationException;
import su.reddot.infrastructure.security.token.AppleAuthToken;
import su.reddot.infrastructure.security.view.AppleRegistrationRequest;
import su.reddot.infrastructure.security.view.RegisterFormRequest;

import java.util.Optional;
import java.util.UUID;

@Component
@Slf4j
public class AppleRegistrationProviderV2 extends AbstractRegistrationProvider {

    private final AppleJwtTokenParser appleJwtTokenParser;

    public AppleRegistrationProviderV2(UserService userService,
                                       UserRepository userRepository,
                                       MessageSourceAccessor messageSourceAccessor,
                                       SecurityService securityService,
                                       VerificationService verificationService,
                                       AppleJwtTokenParser appleJwtTokenParser) {
        super(userService, userRepository, messageSourceAccessor, securityService, verificationService);
        this.appleJwtTokenParser = appleJwtTokenParser;
    }

    @Override
    public boolean isApplicable(Object authRequest) {
        return authRequest instanceof AppleRegistrationRequest;
    }

    @Override
    public User registerAndAuth(RegisterFormRequest registerFormRequest, Object authRequest) {
        AppleRegistrationRequest appleRegistrationRequest = (AppleRegistrationRequest) authRequest;

        checkThatVerifyPhoneNumberJwtTokenValidIfNeed(
                SocialAuthProvider.APPLE,
                appleRegistrationRequest.getAppleIdToken(),
                registerFormRequest.getPhone(),
                appleRegistrationRequest.getPhoneNumberVerifyJwtToken(),
                log)
        ;
        checkThatUserWithVerifiedPhoneNumberNotExist(registerFormRequest, log);

        String appleIdToken = appleRegistrationRequest.getAppleIdToken();
        SocialUserInfoDTO appleUserInfo = appleJwtTokenParser.getSocialUserInfo(appleIdToken);

        checkThatEmailFromTokenEqualFormEmail(appleUserInfo.getEmail(), registerFormRequest.getEmail(), log);

        String appleAccountId = appleUserInfo.getAccountId();
        String transferAppleAccountId = appleUserInfo.getTransferAccountId();

        Optional<User> userOptional;
        if (transferAppleAccountId != null && !transferAppleAccountId.isEmpty()) {
            userOptional = userRepository.getUserByRestAppleIdAndTransferAppleId(appleAccountId, transferAppleAccountId);
        } else {
            userOptional = userRepository.getUserByRestAppleId(appleAccountId);
        }

        if (userOptional.isPresent()) {
            log.error("AppleRegistrationProviderV2 Error: User with appleAccountId already exist {}", appleAccountId);
            throw new OAuthRegistrationException(messageSourceAccessor.getMessage(
                    "su.reddot.infrastructure.security.provider.register.v2.AppleRegistrationProviderV2.userWithAppleRestIdAlreadyExist"));
        }

        User user = registerInUserService(registerFormRequest, true);
        user.setRestAppleId(appleAccountId);
        String userAppleUuid = UUID.randomUUID().toString();
        user.setUserAppleUuid(userAppleUuid);
        user = userRepository.saveAndFlush(user);
        securityService.authenticateByToken(new AppleAuthToken(appleIdToken));
        return user;
    }
}
