package su.reddot.infrastructure.security.provider.auth;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.TooManyUsersFoundException;
import su.reddot.domain.exception.UserNotFoundByPhoneException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.verification.VerifyJwtTokenRequest;
import su.reddot.domain.service.user.UserAuthorityService;
import su.reddot.domain.service.verification.VerificationService;
import su.reddot.infrastructure.security.token.PhoneNumberAuthorizationToken;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class PhoneNumberAuthProvider implements AuthenticationProvider {

    private final MessageSourceAccessor messageSourceAccessor;
    private final UserRepository userRepository;
    private final UserAuthorityService userAuthorityService;
    private final VerificationService verificationService;

    @Override
    @Timed("PhoneNumberAuthenticationProvider.authenticate")
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        PhoneNumberAuthorizationToken authorizationToken = (PhoneNumberAuthorizationToken) authentication;

        String tokenPhoneNumber = authorizationToken.getPhoneNumber();
        VerifyJwtTokenRequest request = new VerifyJwtTokenRequest()
                .phoneNumber(tokenPhoneNumber)
                .guestToken(authorizationToken.getGuestToken())
                .operation(VerifyJwtTokenRequest.OperationEnum.AUTH_OR_REGISTER)
                .jwtToken(authorizationToken.getJwtToken());
        boolean jwtVerifyResult = verificationService.verifyJwtToken(request);

        if (!jwtVerifyResult) {
            log.error("PhoneNumberAuthProvider Error: Invalid JWT token: {}", request);
            throw new BadCredentialsException(messageSourceAccessor.getMessage("infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.BadJWTToken", new Object[]{authorizationToken.getJwtToken()}));
        }

        List<User> users = userRepository.findAllByPhoneAndPhoneVerifiedTimeIsNotNull(tokenPhoneNumber);

        if (CollectionUtils.isEmpty(users)) {
            log.error("PhoneNumberAuthProvider Error: user with phoneNumber {} not found", tokenPhoneNumber);
            throw new UserNotFoundByPhoneException(messageSourceAccessor.getMessage("infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.UserNotFound", new Object[]{tokenPhoneNumber}));
        }

        if (users.size() > 1) {
            log.error("PhoneNumberAuthProvider Error: more then one user found with phoneNumber {}", tokenPhoneNumber);
            throw new TooManyUsersFoundException(messageSourceAccessor.getMessage("infrastructure.security.provider.auth.PhoneNumberAuthenticationProvider.UserNotFound", new Object[]{tokenPhoneNumber}));
        }

        User user = users.get(0);

        return userAuthorityService.getUserIdIdentificationToken(user);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return PhoneNumberAuthorizationToken.class.isAssignableFrom(authentication);
    }
}
