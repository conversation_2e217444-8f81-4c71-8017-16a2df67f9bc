package su.reddot.infrastructure.security.provider.auth;

import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.OAuthUserNotFoundException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserAuthorityService;
import su.reddot.infrastructure.security.oauth.AppleClient;
import su.reddot.infrastructure.security.token.AppleKidAndAuthorizationCodeToken;

import java.util.Map;
import java.util.Optional;

@Component @AllArgsConstructor
public class AppleAccessTokenAuthProvider implements AuthenticationProvider {

	private final AppleClient appleClient;
	private final UserRepository userRepository;
	private final UserAuthorityService userAuthorityService;
	private final MessageSourceAccessor messageSourceAccessor;

	@Override
	public Authentication authenticate(@NonNull Authentication authentication) throws AuthenticationException {
		Map<String, Object> response = appleClient.performUserRequest(authentication.getPrincipal().toString(), authentication.getCredentials().toString(), true);
		String restAppleId = response.get("appleUserId").toString();

		String transferRestAppleId = null;
		if (response.containsKey("transferAppleUserId") && response.get("transferAppleUserId") != null) {
			transferRestAppleId = response.get("transferAppleUserId").toString();
		}

		User user;
		if (transferRestAppleId != null && !transferRestAppleId.isEmpty()) {
			user = userRepository.getUserByRestAppleIdAndTransferAppleId(restAppleId, transferRestAppleId).orElseThrow(() -> new OAuthUserNotFoundException(messageSourceAccessor.getMessage("exception.oauth.user-not-found-apple"), response));
		} else {
			user = userRepository.getUserByRestAppleId(restAppleId).orElseThrow(() -> new OAuthUserNotFoundException(messageSourceAccessor.getMessage("exception.oauth.user-not-found-apple"), response));
		}
		return userAuthorityService.getUserIdIdentificationToken(user);
	}

	@Override
	public boolean supports(Class<?> aClass) {
		return AppleKidAndAuthorizationCodeToken.class.isAssignableFrom(aClass);
	}
}
