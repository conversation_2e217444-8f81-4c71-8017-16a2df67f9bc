package su.reddot.infrastructure.security.provider.auth;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.user.UserAuthorityService;
import su.reddot.infrastructure.security.authentication.encoding.MessageDigestPasswordEncoder;
import su.reddot.infrastructure.security.token.EmailAuthenticationToken;

/**
 * <AUTHOR> on 18.04.17.
 */
@Component @RequiredArgsConstructor @Slf4j
public class EmailAuthenticationProvider implements AuthenticationProvider {

	private final MessageDigestPasswordEncoder passwordEncoder;
	private final UserRepository userRepository;
	private final UserAuthorityService userAuthorityService;
	private final DeviceService deviceService;

	private final MessageSourceAccessor messageSourceAccessor;

	@Override
	public Authentication authenticate(@NonNull Authentication authentication) throws AuthenticationException {
		EmailAuthenticationToken token = (EmailAuthenticationToken) authentication;

		String email = (String) token.getPrincipal();
		User user = userRepository.findByEmail(email);

		if (user == null) {
			throw new BadCredentialsException(messageSourceAccessor.getMessage("infrastructure.security.provider.auth.EmailAuthenticationProvider.BadCredential"));
		}

		String requestPassword = (String) token.getCredentials();
		String encodedPassword = passwordEncoder.encodePassword(requestPassword, user.getEmail().toLowerCase());

		if (!encodedPassword.equals(user.getApiHashedPassword())
				&& !(user.getApiHashedPassword() + ":" + email).equals(requestPassword)) { //Добавляем проверку пароля без доп. кодировки для тестирования
			throw new BadCredentialsException(messageSourceAccessor.getMessage("infrastructure.security.provider.auth.EmailAuthenticationProvider.BadCredential"));
		} else {
			try {
				deviceService.registerCurrentDevice();
			} catch (Exception e){
				log.error("Couldn't register current device", e);
			}
			return userAuthorityService.getUserIdIdentificationToken(user);
		}
	}

	@Override
	public boolean supports(Class<?> aClass) {
		return EmailAuthenticationToken.class.isAssignableFrom(aClass);
	}
}
