package su.reddot.infrastructure.security.provider.auth;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.OAuthUserNotFoundException;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.user.UserAuthorityService;
import su.reddot.infrastructure.security.token.VkRestIdToken;

@Component @RequiredArgsConstructor
public class VkRestIdTokenAuthProvider implements AuthenticationProvider {
	private final UserRepository userRepository;
	private final UserAuthorityService userAuthorityService;
	private final MessageSourceAccessor messageSourceAccessor;

	@Override
	public Authentication authenticate(@NonNull Authentication authentication) throws AuthenticationException {
		String restVkId = authentication.getPrincipal().toString();
		String uuid = authentication.getCredentials().toString();
		User user = userRepository.getUserByRestVkIdAndUuid(restVkId, uuid).orElseThrow(() -> new OAuthUserNotFoundException(messageSourceAccessor.getMessage("exception.oauth.user-not-found-vk")));
		return userAuthorityService.getUserIdIdentificationToken(user);
	}

	@Override
	public boolean supports(Class<?> aClass) {
		return VkRestIdToken.class.isAssignableFrom(aClass);
	}
}
