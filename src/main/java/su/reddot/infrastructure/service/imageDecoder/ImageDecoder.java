package su.reddot.infrastructure.service.imageDecoder;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import su.reddot.domain.exception.ImageException;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class ImageDecoder {
    private static final Pattern format = Pattern.compile("^data:image/(.+);base64,([a-zA-Z0-9/=+]+)$");
    private static final Map<String, String> typeToExtension = new HashMap<>();
    public static final String BASE_64_WITH_DATA_SCHEMA_TEMPLATE = "data:image/%s;base64,%s";

    static {
        typeToExtension.put("jpeg", "jpeg");
        typeToExtension.put("png", "png");
        typeToExtension.put("svg+xml", "svg");
        typeToExtension.put("webp", "webp");
        typeToExtension.put("bmp", "bmp");
        typeToExtension.put("x-icon", "ico");
        typeToExtension.put("tiff", "tiff");
        typeToExtension.put("gif", "gif");
        typeToExtension.put("avif", "avif");
        typeToExtension.put("apng", "apng");
    }

    public DecodedImage decodeBase64Encoded(String base64)
    {
        return decodeBase64Encoded(base64, true);
    }

    public DecodedImage decodeBase64Encoded(String base64, boolean fakeExtension)
    {
        Matcher matcher = format.matcher(base64);
        if (!matcher.matches()) {
            throw new ImageException("Bad base64 string given");
        }

        String mimeType = matcher.group(1);
        String extension = typeToExtension.get(mimeType);

        if (extension == null) {
            throw new ImageException("Unsupported image type given");
        }

        String base64Data = matcher.group(2);
        byte[] bytes = Base64.getDecoder().decode(base64Data);
        return new DecodedImage(extension, bytes, fakeExtension);
    }

    public DecodedImage decodeBase64EncodedWithDefaultType(String base64, String defaultImageType) {
        if (format.matcher(base64).matches()) {
            return decodeBase64Encoded(base64);
        }
        return decodeBase64Encoded(String.format(BASE_64_WITH_DATA_SCHEMA_TEMPLATE, defaultImageType, base64));
    }
}
