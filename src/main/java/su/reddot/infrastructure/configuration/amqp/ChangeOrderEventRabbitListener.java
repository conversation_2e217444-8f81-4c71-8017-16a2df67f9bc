package su.reddot.infrastructure.configuration.amqp;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.order.ChangeOrderEvent;

@ConditionalOnExpression("${spring.rabbitmq.enabled} and ${app.queue.processor.change.order.enabled}")
@RequiredArgsConstructor
@Slf4j
@Component
public class ChangeOrderEventRabbitListener {
    private final CommonAMQPSender sender;
    @RabbitListener(queues = "#{fanoutChangeOrderEventsQueue.name}")
    public void processFanoutChangeOrderEventMessage(@NonNull ChangeOrderEvent event){
        sender.maybeSendEventToLocalInstance(event);
    }
}
