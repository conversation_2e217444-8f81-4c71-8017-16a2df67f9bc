package su.reddot.infrastructure.configuration.amqp;

import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.salerequest.NewSaleRequestEvent;

import static su.reddot.infrastructure.configuration.AMQPConfiguration.NEW_SALE_REQUEST_FANOUT_EVENT_QUEUE_NAME;

@Component
@ConditionalOnExpression("${spring.rabbitmq.enabled} and ${app.queue.processor.new.sale-request.enabled}")
@RequiredArgsConstructor
public class NewSaleRequestEventRabbitListener {

    private final CommonAMQPSender sender;

    @RabbitListener(queues = NEW_SALE_REQUEST_FANOUT_EVENT_QUEUE_NAME)
    public void processNewSaleRequestEvent(NewSaleRequestEvent event) {
        sender.maybeSendEventToLocalInstance(event);
    }
}
