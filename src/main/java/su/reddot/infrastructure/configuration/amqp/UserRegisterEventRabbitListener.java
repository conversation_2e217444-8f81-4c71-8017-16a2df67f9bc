package su.reddot.infrastructure.configuration.amqp;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;
import su.reddot.domain.event.UserRegisterEvent;

@ConditionalOnExpression("${spring.rabbitmq.enabled} and ${app.queue.processor.new.user-register.enabled}")
@RequiredArgsConstructor
@Slf4j
@Component
public class UserRegisterEventRabbitListener {
    private final CommonAMQPSender sender;
    @RabbitListener(queues = "#{fanoutUserRegisterEventsQueue.name}")
    public void processFanoutUserRegisterEventMessage(@NonNull UserRegisterEvent event){
        sender.maybeSendEventToLocalInstance(event);
    }
}
