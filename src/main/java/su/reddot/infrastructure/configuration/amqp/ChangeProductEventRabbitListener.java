package su.reddot.infrastructure.configuration.amqp;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.product.ChangeProductEvent;

@ConditionalOnExpression("${spring.rabbitmq.enabled} and ${app.queue.processor.change.product.enabled}")
@RequiredArgsConstructor
@Slf4j
@Component
public class ChangeProductEventRabbitListener {
    private final CommonAMQPSender sender;
    @RabbitListener(queues = "#{fanoutChangeProductEventsQueue.name}")
    public void processFanoutChangeProductEventMessage(@NonNull ChangeProductEvent event){
        sender.sendEventToLocalInstance(event);
    }
}
