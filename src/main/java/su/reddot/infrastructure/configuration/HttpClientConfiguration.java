package su.reddot.infrastructure.configuration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import su.reddot.infrastructure.cashregister.impl.starrys.StarrysConfiguration;
import su.reddot.infrastructure.httpclient.HttpClientSettings;
import su.reddot.infrastructure.httpclient.OauthHttpClientSettings;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.security.*;
import java.security.cert.CertificateException;

@Configuration
@Slf4j
@RequiredArgsConstructor
public class HttpClientConfiguration {

	private final HttpClientSettings httpClientSettings;
	private final OauthHttpClientSettings oauthHttpClientSettings;
	private final StarrysConfiguration starrysConfiguration;
	private final ResourceLoader resourceLoader;

	@Primary
	@Bean
	public CloseableHttpClient closeableHttpClient() {
		return HttpClients.custom().setDefaultRequestConfig(timeoutRequestConfig()).build();
	}

	@Bean
	public CloseableHttpClient oauthHttpClient() {
		return HttpClients.custom().setDefaultRequestConfig(oauthTimeoutRequestConfig()).build();
	}

	@Bean
	public RestTemplate restTemplate(RestTemplateCustomizer traceIdRestTemplateCustomizer) {
		RestTemplate restTemplate = new RestTemplate();
		restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory(closeableHttpClient()));
		traceIdRestTemplateCustomizer.customize(restTemplate);
		return restTemplate;
	}

	@Bean
	@Qualifier("starrys-rest-template")
	public RestTemplate starrysRestTemplate(RestTemplateBuilder restTemplateBuilder,
											StarrysConfiguration conf)
			throws KeyStoreException, IOException, CertificateException,
			NoSuchAlgorithmException, UnrecoverableKeyException, KeyManagementException {

		Resource clientCertificate = resourceLoader.getResource(starrysConfiguration.getClientCertificatePath());

		KeyStore keyStore = KeyStore.getInstance("PKCS12");
		keyStore.load(clientCertificate.getInputStream(), conf.getClientCertificatePassword().toCharArray());

		SSLContext sslContext = new SSLContextBuilder()
				.loadTrustMaterial(null, new TrustSelfSignedStrategy())
				.loadKeyMaterial(keyStore, conf.getClientCertificatePassword().toCharArray()).build();

		SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(
				sslContext);

		HttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(timeoutRequestConfig())
				.setSSLSocketFactory(socketFactory).build();
		ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

		return restTemplateBuilder.requestFactory(() -> requestFactory).build();
	}

	@Bean
	public RequestConfig timeoutRequestConfig() {
		return RequestConfig.custom()
				.setConnectTimeout(httpClientSettings.getConnectTimeout())
				.setConnectionRequestTimeout(httpClientSettings.getConnectionRequestTimeout())
				.setSocketTimeout(httpClientSettings.getSocketTimeout())
				.build();
	}

	public RequestConfig oauthTimeoutRequestConfig() {
		return RequestConfig.custom()
				.setConnectTimeout(oauthHttpClientSettings.getConnectTimeout())
				.setConnectionRequestTimeout(oauthHttpClientSettings.getConnectionRequestTimeout())
				.setSocketTimeout(oauthHttpClientSettings.getSocketTimeout())
				.build();
	}

}
