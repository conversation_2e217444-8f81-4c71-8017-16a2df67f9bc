package su.reddot.infrastructure.configuration.jackson;

import com.fasterxml.jackson.databind.ser.PropertyWriter;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import lombok.NoArgsConstructor;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.configuration.keycloak.ContextProvider;

import java.util.Arrays;
import java.util.List;

@NoArgsConstructor
public class RolesJsonFilter extends SimpleBeanPropertyFilter {

    private List<AuthorityName> contextColes;

    private RolesJsonFilter(List<AuthorityName> contextColes) {
        this.contextColes = contextColes;
    }

    public static RolesJsonFilter testInstance(List<AuthorityName> contextColes) {
        return new RolesJsonFilter(contextColes);
    }

    @Override
    public void serializeAsField(Object pojo,
                                 com.fasterxml.jackson.core.JsonGenerator jgen,
                                 com.fasterxml.jackson.databind.SerializerProvider provider,
                                 PropertyWriter writer) throws Exception {

        //проверяем, аннотировано ли поле Visibility
        Visibility visibility = writer.findAnnotation(Visibility.class);

        //если нет, вызываем дефолтный сериалайзер
        if (visibility == null) {
            defaultSerialize(pojo, jgen, provider, writer);
            return;
        }

        //если кто-то забыл указать список ролей, рендерим поле так,
        //будто аннотации нет вообще, т.к. в этом случае в ней нет смысла
        if (0 == visibility.roles().length) {
            defaultSerialize(pojo, jgen, provider, writer);
            return;
        }

        //роли текущего авторизованного пользователя
        List<AuthorityName> contextRoles = getContextRoles();

        //проверка skip
        List<AuthorityName> skipRoles = Arrays.asList(visibility.skipFor().roles());

        //если у пользователя есть роли, указанные в skip, пропускаем процессинг этого поля (показываем его)
        if (!skipRoles.isEmpty() &&
                fieldRolesFitsCondition(
                        visibility.skipFor().inclusion(),
                        skipRoles,
                        contextRoles)) {

            defaultSerialize(pojo, jgen, provider, writer);
            return;
        }

        //роли из аннотации Visibility
        List<AuthorityName> fieldRoles = Arrays.asList(visibility.roles());

        /*
            1. Набор ролей поля проверяется по ANY или ALL.
            2. Если роли прошли проверку, в зависимости от "действия", скрываем или показываем поле.

           --------------------------------------
           | Fits condition     | + | + | - | - |
           --------------------------------------
           | show (+), hide (-) | + | - | + | - |
           --------------------------------------
           | Render             | + | - | - | + |
           --------------------------------------

            В общем, получается xor "наоборот". Да, это не так явно (т.е. можно использовать много if),
            но с xor получается очень мало кода.
         */
        boolean fitCondition = fieldRolesFitsCondition(visibility.inclusion(), fieldRoles, contextRoles);
        boolean isHide = RenderAction.HIDE == visibility.action();

        //xor
        if (fitCondition ^ isHide) {
            defaultSerialize(pojo, jgen, provider, writer);
        }

        //else ничего не делаем, т.е. поле не сериализуется
    }

    private void defaultSerialize(Object pojo,
                                  com.fasterxml.jackson.core.JsonGenerator jgen,
                                  com.fasterxml.jackson.databind.SerializerProvider provider,
                                  PropertyWriter writer) throws Exception {
        super.serializeAsField(pojo, jgen, provider, writer);
    }

    private boolean fieldRolesFitsCondition(RolesInclusion inclusion, List<AuthorityName> fieldRoles,
                                            List<AuthorityName> contextRoles) {
        switch (inclusion) {
            case ANY:
                return containsAtLeast(fieldRoles, contextRoles);
            case ALL:
                return containsAll(fieldRoles, contextRoles);
        }

        return false;
    }

    private boolean containsAtLeast(List<AuthorityName> valuesList, List<AuthorityName> containerList) {
        return valuesList.stream().anyMatch(containerList::contains);
    }

    private boolean containsAll(List<AuthorityName> valuesList, List<AuthorityName> containerList) {
        return containerList.containsAll(valuesList);
    }

    private List<AuthorityName> getContextRoles() {
        if (contextColes != null) {
            return contextColes;
        }

        RolesContainer rolesContainer = ContextProvider.getBean(RolesContainer.class);

        return rolesContainer.getCurrentRoles();
    }

    @Override
    protected boolean include(com.fasterxml.jackson.databind.ser.BeanPropertyWriter writer) {
        return true;
    }

    @Override
    protected boolean include(PropertyWriter writer) {
        return true;
    }
}
