package su.reddot.infrastructure.configuration.kafka;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties("app.kafka")
public class KafkaTopicProperties {

	private KafkaTopicProperty confirmationsTopic;
	private KafkaTopicProperty historyTopic;
	private KafkaTopicProperty import2orderprocessingTopic;
	private KafkaTopicProperty bitrixCrossBorderTopic;

	@Getter
	@Setter
	public static class KafkaTopicProperty {
		private String name;
		private KafkaTopicListenerProperty listener;
		private Integer partitionsCount;
		private Integer replicationFactor;
	}

	@Getter
	@Setter
	public static class KafkaTopicListenerProperty {
		private boolean enabled;
	}
}
