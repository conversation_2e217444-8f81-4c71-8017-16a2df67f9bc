package su.reddot.infrastructure.configuration;

import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.retry.RejectAndDontRequeueRecoverer;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnExpression("${spring.rabbitmq.enabled} and ${app.queue.processor.bitrix.sale-request-deal.event.enabled}")
@RequiredArgsConstructor
public class BitrixSaleRequestDealEventRabbitListenerConfig {

    @Bean
    public SimpleRabbitListenerContainerFactory bitrixSaleRequestDealEventRabbitListenerContainerFactory(
            ConnectionFactory connectionFactory, MessageConverter jsonMessageConverter, RabbitProperties rabbitProperties) {

        // Конфигурируем всеми значениями из общих конфигов, за исключением настроек retry

        RabbitProperties.ListenerRetry retry = rabbitProperties.getListener().getSimple().getRetry();

        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setDefaultRequeueRejected(rabbitProperties.getListener().getSimple().getDefaultRequeueRejected());
        factory.setAdviceChain(
                RetryInterceptorBuilder.stateless()
                    .maxAttempts(retry.getMaxAttempts())
                    .backOffOptions(retry.getInitialInterval().toMillis() * 5, retry.getMultiplier(), retry.getInitialInterval().toMillis() * 5)
                    .recoverer(new RejectAndDontRequeueRecoverer())
                    .build());
        factory.setMessageConverter(jsonMessageConverter);
        return factory;
    }
}