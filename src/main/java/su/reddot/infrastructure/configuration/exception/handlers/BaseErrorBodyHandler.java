package su.reddot.infrastructure.configuration.exception.handlers;

import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.util.StringUtils;
import su.reddot.domain.exception.OskellyException;
import su.reddot.infrastructure.configuration.exception.ResponseBody.ResponseErrorBody;

import java.util.Optional;

public class BaseErrorBodyHandler extends AbstractErrorBodyHandler {

    public BaseErrorBodyHandler(MessageSourceAccessor messageSourceAccessor, String prefix) {
        super(messageSourceAccessor, prefix);
    }

    @Override
    public boolean isApplicable(ResponseErrorBody responseErrorBody) {
        return StringUtils.hasText(responseErrorBody.getCode());
    }

    @Override
    public Optional<OskellyException> handle(ResponseErrorBody responseErrorBody) {
        try {
            return Optional.of(new OskellyException(getMessageWithPrefix(responseErrorBody.getCode())));
        } catch (NoSuchMessageException e) {
            logErrorGettingMessageFromResource(responseErrorBody);
            return Optional.empty();
        }
    }

    @Override
    public int getPriority() {
        return Integer.MAX_VALUE;
    }
}