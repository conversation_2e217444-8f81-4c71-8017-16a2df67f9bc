package su.reddot.infrastructure.configuration.exception.handlers;

import org.springframework.context.NoSuchMessageException;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.exception.ValidationException;
import su.reddot.infrastructure.configuration.exception.ResponseBody.ResponseErrorBody;

import java.util.Optional;

public class ValidationErrorBodyHandler extends AbstractErrorBodyHandler {

    public ValidationErrorBodyHandler(MessageSourceAccessor messageSourceAccessor, String prefix) {
        super(messageSourceAccessor, prefix);
    }

    @Override
    public boolean isApplicable(ResponseErrorBody responseErrorBody) {
        return responseErrorBody.getCode().startsWith("bad-request")
                || responseErrorBody.getCode().startsWith("validation-error")
                || responseErrorBody.getCode().startsWith("state-flow-broken");
    }

    @Override
    public Optional<OskellyException> handle(ResponseErrorBody responseErrorBody) {
        try {
            return Optional.of(new ValidationException(getMessageWithPrefix(responseErrorBody.getCode())));
        } catch (NoSuchMessageException e) {
            logErrorGettingMessageFromResource(responseErrorBody);
            return Optional.of(new ValidationException(responseErrorBody.getCode()));
        }
    }
}