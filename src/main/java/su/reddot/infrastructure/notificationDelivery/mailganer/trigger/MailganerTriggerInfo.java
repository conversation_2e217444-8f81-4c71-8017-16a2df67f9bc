package su.reddot.infrastructure.notificationDelivery.mailganer.trigger;

import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import su.reddot.domain.model.notification.Notification;

@Component
public interface MailganerTriggerInfo {

    default String getTriggerName(Notification notification) {
        return this.getClass().getSimpleName();
    }
    Class<? extends Notification> getNotificationClass();
    MultiValueMap<String, Object> getAdditionalParams(Notification notification) throws JSONException;
}
