package su.reddot.infrastructure.notificationDelivery.mailganer.trigger.sale;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.infrastructure.notificationDelivery.mailganer.MailganerOrderProductsList;
import su.reddot.infrastructure.mailganer.MailganerUtils;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.MailganerTriggerInfo;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.SaleDeliveredToExpertiseNotification;

@Component
@RequiredArgsConstructor
public class SaleDeliveredToExpertiseMailganerTrigger implements MailganerTriggerInfo {

    private final MailganerUtils mailganerUtils;
    private final MessageSourceAccessor messageSourceAccessor;

    @Value("${mailganer.api_version}")
    private int apiVersion;

    @Override
    public String getTriggerName(Notification notification) {
        return SaleDeliveredToExpertiseMailganerTrigger.class.getSimpleName();
    }

    @Override
    public Class<? extends Notification> getNotificationClass() {
        return SaleDeliveredToExpertiseNotification.class;
    }

    @Override
    public MultiValueMap<String, Object> getAdditionalParams(Notification notification) throws JSONException {
        SaleDeliveredToExpertiseNotification saleDeliveredToExpertiseNotification = (SaleDeliveredToExpertiseNotification) notification;
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("firstName", mailganerUtils.getNickname(saleDeliveredToExpertiseNotification));

        params.add("orderNumber", mailganerUtils.getOrderNumber(saleDeliveredToExpertiseNotification));
        params.add("orderApprovedDate", mailganerUtils.getOrderApprovedDate(saleDeliveredToExpertiseNotification));
        params.add("orderApprovedTime", mailganerUtils.getOrderApprovedTime(saleDeliveredToExpertiseNotification));
        params.add("orderExaminationDate", mailganerUtils.getOrderExaminationDate(saleDeliveredToExpertiseNotification));
        params.add("orderExaminationTime", mailganerUtils.getOrderExaminationTime(saleDeliveredToExpertiseNotification));

        MailganerOrderProductsList mailganerOrderProductsList = new MailganerOrderProductsList(apiVersion);
        for(OrderPosition orderPosition : saleDeliveredToExpertiseNotification.getOrder().getConfirmedOrderPositions()){
            MailganerOrderProductsList.MailganerOrderProduct mailganerOrderProduct = new MailganerOrderProductsList.MailganerOrderProduct();
            mailganerOrderProduct.setProductname(mailganerUtils.getProductNameOrderPosition(orderPosition));
            mailganerOrderProduct.setSize(mailganerUtils.getSizeOrderPosition(orderPosition));
            mailganerOrderProduct.setProductStatus(messageSourceAccessor.getMessage(orderPosition.getState().getDescription()));
            mailganerOrderProduct.setAmount(mailganerUtils.getAmountOrderPosition(orderPosition));
            mailganerOrderProduct.setCost(mailganerUtils.getCostOrderPositon(orderPosition));
            mailganerOrderProduct.setProductUrl(mailganerUtils.getProductUrlOrderPosition(orderPosition));
            mailganerOrderProduct.setImageUrl(mailganerUtils.getImageUrlOrderPosition(orderPosition));
            mailganerOrderProduct.setProfit(mailganerUtils.getProfitOrderPosition(orderPosition));
            mailganerOrderProductsList.add(mailganerOrderProduct);
        }

        params.add("goods", mailganerOrderProductsList.getJson());

        params.add("orderCost", mailganerUtils.getOrderCostSale(saleDeliveredToExpertiseNotification));
        params.add("profitTotal", mailganerUtils.getProfitTotal(saleDeliveredToExpertiseNotification));
        return params;
    }
}
