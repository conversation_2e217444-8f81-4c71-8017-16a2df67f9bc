package su.reddot.infrastructure.notificationDelivery.mailganer.trigger.cart;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import su.reddot.infrastructure.notificationDelivery.mailganer.MailganerOrderProductsList;
import su.reddot.infrastructure.mailganer.MailganerUtils;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.MailganerTriggerInfo;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.LostCartNotification;
import su.reddot.domain.model.order.OrderPosition;

import java.math.BigDecimal;

@Component
@RequiredArgsConstructor
public class LostCartMailganerTrigger implements MailganerTriggerInfo {
    @Override
    public String getTriggerName(Notification notification) {
        return LostCartMailganerTrigger.class.getSimpleName();
    }

    @Autowired
    private MailganerUtils mailganerUtils;
    private final MessageSourceAccessor messageSourceAccessor;

    @Value("${mailganer.api_version}")
    private int apiVersion;

    @Override
    public Class<? extends Notification> getNotificationClass() {
        return LostCartNotification.class;
    }

    @Override
    public MultiValueMap<String, Object> getAdditionalParams(Notification notification) throws JSONException {
        LostCartNotification lostCartNotification = (LostCartNotification) notification;
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("firstName", mailganerUtils.getNickname(notification));

        //Для корзины с разными продавцами эти параметры установить невозможно, т.к. продавец еще не определен
        if(lostCartNotification.getOrder().getSellerUser() != null) {
            params.add("sellerName", mailganerUtils.getSellerName(lostCartNotification));
            params.add("sellerType", mailganerUtils.getSellerType(lostCartNotification));
            params.add("sellerPic", mailganerUtils.getSellerPic(lostCartNotification));
        }

        BigDecimal orderCost = new BigDecimal(0);
        MailganerOrderProductsList mailganerOrderProductsList = new MailganerOrderProductsList(apiVersion);
        for(OrderPosition orderPosition : lostCartNotification.getOrder().getAvailableOrderPositions()){
            MailganerOrderProductsList.MailganerOrderProduct mailganerOrderProduct = new MailganerOrderProductsList.MailganerOrderProduct();
            mailganerOrderProduct.setProductname(mailganerUtils.getProductNameOrderPosition(orderPosition));
            mailganerOrderProduct.setSize(mailganerUtils.getSizeOrderPosition(orderPosition));
            mailganerOrderProduct.setProductStatus(messageSourceAccessor.getMessage(orderPosition.getState().getDescription()));
            mailganerOrderProduct.setAmount(mailganerUtils.getAmountOrderPosition(orderPosition));
            mailganerOrderProduct.setCost(mailganerUtils.getCostOrderPositon(orderPosition));
            mailganerOrderProduct.setProductUrl(mailganerUtils.getProductUrlOrderPosition(orderPosition));
            mailganerOrderProduct.setImageUrl(mailganerUtils.getImageUrlOrderPosition(orderPosition));
            mailganerOrderProductsList.add(mailganerOrderProduct);

            orderCost = orderCost.add(orderPosition.getAmount());
        }

        params.add("orderCost1", mailganerUtils.priceRounding(orderCost));

        params.add("goods1", mailganerOrderProductsList.getJson());

        params.add("deliveryCost", mailganerUtils.getDeliveryPrice(lostCartNotification));
        return params;
    }
}
