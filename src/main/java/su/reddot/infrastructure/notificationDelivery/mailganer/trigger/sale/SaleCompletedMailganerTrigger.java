package su.reddot.infrastructure.notificationDelivery.mailganer.trigger.sale;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.SaleCompletedNotification;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.infrastructure.mailganer.MailganerUtils;
import su.reddot.infrastructure.notificationDelivery.mailganer.MailganerOrderProductsList;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.MailganerTriggerInfo;

@Slf4j
@Component
@RequiredArgsConstructor
public class SaleCompletedMailganerTrigger implements MailganerTriggerInfo {

    private final MailganerUtils mailganerUtils;
    private final MessageSourceAccessor messageSourceAccessor;

    @Value("${mailganer.api_version}")
    private int apiVersion;

    @Value("${promocode.sorry1000.enabled}")
    private boolean promocodeSorry1000Enabled;

    @Override
    public String getTriggerName(Notification notification) {
        return SaleCompletedMailganerTrigger.class.getSimpleName();
    }

    @Override
    public Class<? extends Notification> getNotificationClass() {
        return SaleCompletedNotification.class;
    }

    @Override
    public MultiValueMap<String, Object> getAdditionalParams(Notification notification) throws JSONException {
        SaleCompletedNotification saleCompletedNotification = (SaleCompletedNotification) notification;
        if (saleCompletedNotification.getOrder() == null || saleCompletedNotification.getOrder().getLastExpertise() == null) {
            throw new RuntimeException("Can't send SaleCompletedNotification because the order " +
                    "hasn't yet passed the examination. Order = " + saleCompletedNotification.getOrder());
        }
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("firstName", mailganerUtils.getNickname(saleCompletedNotification));

        params.add("isPro", mailganerUtils.isPro(saleCompletedNotification));

        if (mailganerUtils.isPro(saleCompletedNotification)) {
            params.add("header", messageSourceAccessor.getMessage("infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.header.isPro"));
            params.add("preheader", messageSourceAccessor.getMessage("infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.preheader.isPro"));
            params.add("info", messageSourceAccessor.getMessage("infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.info.isPro"));
        } else {
            params.add("header", messageSourceAccessor.getMessage("infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.header"));
            params.add("preheader", messageSourceAccessor.getMessage("infrastructure.notificationDelivery.mailganer.trigger.sale.SaleCompletedMailganerTrigger.preheader"));
        }

        addPromocodeSorry1000(params);

        params.add("orderNumber", mailganerUtils.getOrderNumber(saleCompletedNotification));

        params.add("orderApprovedDate", mailganerUtils.getOrderApprovedDate(saleCompletedNotification));
        params.add("orderApprovedTime", mailganerUtils.getOrderApprovedTime(saleCompletedNotification));
        params.add("orderExaminationDate", mailganerUtils.getOrderExaminationDate(saleCompletedNotification));
        params.add("orderExaminationTime", mailganerUtils.getOrderExaminationTime(saleCompletedNotification));
        params.add("examinationPassedDate", mailganerUtils.getDateString(saleCompletedNotification.getOrder().getLastExpertise().getCreateTime().toEpochSecond()));
        params.add("examinationPassedTime", mailganerUtils.getTimeString(saleCompletedNotification.getOrder().getLastExpertise().getCreateTime().toEpochSecond()));
        params.add("orderDeliveredDate", mailganerUtils.getDateString(saleCompletedNotification.getOrder().getDeliveryStateTime().toEpochSecond()));
        params.add("orderDeliveredTime", mailganerUtils.getTimeString(saleCompletedNotification.getOrder().getDeliveryStateTime().toEpochSecond()));

        MailganerOrderProductsList mailganerOrderProductsList = new MailganerOrderProductsList(apiVersion);
        for (OrderPosition orderPosition : saleCompletedNotification.getOrder().getEffectiveOrderPositions()) {
            MailganerOrderProductsList.MailganerOrderProduct mailganerOrderProduct = new MailganerOrderProductsList.MailganerOrderProduct();
            mailganerOrderProduct.setProductname(mailganerUtils.getProductNameOrderPosition(orderPosition));
            mailganerOrderProduct.setSize(mailganerUtils.getSizeOrderPosition(orderPosition));
            mailganerOrderProduct.setProductStatus(messageSourceAccessor.getMessage(orderPosition.getState().getDescription()));
            mailganerOrderProduct.setAmount(mailganerUtils.getAmountOrderPosition(orderPosition));
            mailganerOrderProduct.setCost(mailganerUtils.getCostOrderPositon(orderPosition));
            mailganerOrderProduct.setProductUrl(mailganerUtils.getProductUrlOrderPosition(orderPosition));
            mailganerOrderProduct.setImageUrl(mailganerUtils.getImageUrlOrderPosition(orderPosition));
            mailganerOrderProduct.setProfit(mailganerUtils.getProfitOrderPosition(orderPosition));
            mailganerOrderProductsList.add(mailganerOrderProduct);
        }

        params.add("goods", mailganerOrderProductsList.getJson());

        params.add("sellerName", mailganerUtils.getSellerName(saleCompletedNotification));
        params.add("sellerType", mailganerUtils.getSellerType(saleCompletedNotification));
        params.add("sellerPic", mailganerUtils.getSellerPic(saleCompletedNotification));
        params.add("orderCost", mailganerUtils.priceRounding(saleCompletedNotification.getOrder().getEffectiveAmount().subtract(saleCompletedNotification.getOrder().getDeliveryCost())) + "");
        params.add("fullCost", mailganerUtils.priceRounding(saleCompletedNotification.getOrder().getFinalAmount()) + "");
        params.add("profitTotal", mailganerUtils.getProfitTotal(saleCompletedNotification));

        return params;
    }

    private void addPromocodeSorry1000(MultiValueMap<String, Object> params) {
        if (!promocodeSorry1000Enabled) return;
        params.add("promocode", "SORRY1000");
    }
}
