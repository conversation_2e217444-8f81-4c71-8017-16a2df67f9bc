package su.reddot.infrastructure.notificationDelivery.mailganer.trigger.sale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import su.reddot.infrastructure.mailganer.MailganerUtils;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.MailganerTriggerInfo;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.order.SalePickupDeclinedNotification;

@Component
public class SalePickupDeclinedMailganerTrigger implements MailganerTriggerInfo {

    @Autowired
    private MailganerUtils mailganerUtils;

    @Override
    public String getTriggerName(Notification notification) {
        return SalePickupDeclinedMailganerTrigger.class.getSimpleName();
    }

    @Override
    public Class<? extends Notification> getNotificationClass() {
        return SalePickupDeclinedNotification.class;
    }

    @Override
    public MultiValueMap<String, Object> getAdditionalParams(Notification notification) {
        SalePickupDeclinedNotification salePickupDeclinedNotification = (SalePickupDeclinedNotification) notification;
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("firstName", mailganerUtils.getNickname(salePickupDeclinedNotification));
        return params;
    }
}
