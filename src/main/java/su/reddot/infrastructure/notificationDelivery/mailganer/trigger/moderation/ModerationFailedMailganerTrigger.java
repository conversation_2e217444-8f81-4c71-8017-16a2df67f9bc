package su.reddot.infrastructure.notificationDelivery.mailganer.trigger.moderation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import su.reddot.infrastructure.mailganer.MailganerUtils;
import su.reddot.infrastructure.notificationDelivery.mailganer.trigger.MailganerTriggerInfo;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.product.publication.ModerationFailedNotification;

@Component
public class ModerationFailedMailganerTrigger implements MailganerTriggerInfo {

    @Autowired
    private MailganerUtils mailganerUtils;

    @Override
    public String getTriggerName(Notification notification) {
        return ModerationFailedMailganerTrigger.class.getSimpleName();
    }

    @Override
    public Class<? extends Notification> getNotificationClass() {
        return ModerationFailedNotification.class;
    }

    @Override
    public MultiValueMap<String, Object> getAdditionalParams(Notification notification) {
        ModerationFailedNotification moderationFailedNotification = (ModerationFailedNotification) notification;
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("title", mailganerUtils.getTitle(moderationFailedNotification));
        params.add("firstName", mailganerUtils.getNickname(moderationFailedNotification));
        params.add("subtitle", mailganerUtils.getSubtitle(moderationFailedNotification));
        params.add("productname", mailganerUtils.getProductName(moderationFailedNotification));
        params.add("moderComment", mailganerUtils.getModerComment(moderationFailedNotification));
        return params;
    }
}
