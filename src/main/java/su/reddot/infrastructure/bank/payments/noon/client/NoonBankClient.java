package su.reddot.infrastructure.bank.payments.noon.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import su.reddot.infrastructure.bank.payments.noon.client.common.BaseApiResponse;
import su.reddot.infrastructure.bank.payments.noon.client.common.TransactionStatus;
import su.reddot.infrastructure.bank.payments.noon.client.common.TransactionType;
import su.reddot.infrastructure.bank.payments.noon.client.request.OrderActionRequest;
import su.reddot.infrastructure.bank.payments.noon.client.response.OrderActionResponse;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Builder
public class NoonBankClient {

    private final String apiUrl;
    private final String environment;
    private final String businessId;
    private final String applicationId;
    private final String applicationKey;

    public static final String ROUTE_PAYMENT_V1_ORDER_ACTION = "/order";
    public static final String ROUTE_PAYMENT_V1_ORDER_BY_IDS = "/order";
    public static final String ROUTE_PAYMENT_V1_ORDER_BY_REF = "/order/getbyreference";

    private final RestTemplate restTemplate = new RestTemplate();

    private final ObjectMapper mapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
            .registerModule(new JavaTimeModule())
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    private String getAuthHeader() {
        String dataToEncode = String.format("%s.%s:%s", businessId, applicationId, applicationKey);
        return Base64.getEncoder().encodeToString(dataToEncode.getBytes(StandardCharsets.UTF_8));
    }

    @SneakyThrows
    protected <T extends BaseApiResponse> T callServer(String requestUrl, HttpMethod httpMethod, Object requestData, Class<T> responseType) {
        String authHeader = String.format("Key_%s %s", environment, getAuthHeader());
        //
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", authHeader);
        //
        String jsonRequest = Objects.isNull(requestData) ? null : mapper.writeValueAsString(requestData);
        //
        HttpEntity<?> requestEntity = new HttpEntity<>(jsonRequest, headers);
        //
        try {
            log.info("Noon payments call init: {} with body {}", requestUrl, jsonRequest);
            //
            ResponseEntity<String> response = restTemplate.exchange(requestUrl,
                    httpMethod,
                    requestEntity,
                    String.class);
            //
            log.info("Noon payments call done: {} with response {}", requestUrl, response.getBody());
            //
            T result = mapper.readValue(response.getBody(), responseType);
            //
            result.rawResponse = response.getBody();
            //
            return result;
        } catch (Exception ex) {
            String bodyData = (ex instanceof HttpStatusCodeException) ? ((HttpStatusCodeException) ex).getResponseBodyAsString() : null;
            log.error("Noon payments call fail: {}, body data: {}", ex.getMessage(), bodyData, ex);
            throw ex;
        }
    }

    public OrderActionResponse orderAction(@NonNull OrderActionRequest orderActionRequest) {
        OrderActionResponse result = callServer(apiUrl + ROUTE_PAYMENT_V1_ORDER_ACTION,
                HttpMethod.POST,
                orderActionRequest,
                OrderActionResponse.class);
        //
        Set<TransactionStatus> validStatusSet = Collections.emptySet();
        switch (orderActionRequest.getApiOperation()) {
            case INITIATE:
                // Order.Status -> INITIATED
                break;
            case REVERSE:
                if (result.getResult().getTransaction().getType() != TransactionType.VOID_AUTHORIZATION) {
                    throw new NoonBankClientException("Unexpected transaction type %s on %s operation",
                            result.getResult().getTransaction().getType(),
                            orderActionRequest.getApiOperation());
                }
                validStatusSet = Sets.newHashSet(TransactionStatus.SUCCESS, TransactionStatus.FAILURE, TransactionStatus.REJECTED);
                break;
            case CAPTURE:
                if (result.getResult().getTransaction().getType() != TransactionType.CAPTURE) {
                    throw new NoonBankClientException("Unexpected transaction type %s on %s operation",
                            result.getResult().getTransaction().getType(),
                            orderActionRequest.getApiOperation());
                }
                validStatusSet = Sets.newHashSet(TransactionStatus.SUCCESS, TransactionStatus.FAILURE, TransactionStatus.REJECTED);
                break;
        }
        if (!validStatusSet.isEmpty() && !validStatusSet.contains(result.getResult().getTransaction().getStatus())) {
            throw new NoonBankClientException("Unexpected transaction status %s on %s operation",
                    result.getResult().getTransaction().getStatus(),
                    orderActionRequest.getApiOperation());
        }
        //
        return result;
    }

    public OrderActionResponse getOrderById(@NonNull Long orderId) {
        return callServer(apiUrl + ROUTE_PAYMENT_V1_ORDER_BY_IDS + "/" + orderId,
                HttpMethod.GET,
                null,
                OrderActionResponse.class);
    }

    public OrderActionResponse getOrderByReference(@NonNull String orderReference) {
        return callServer(apiUrl + ROUTE_PAYMENT_V1_ORDER_BY_REF + "/" + orderReference,
                HttpMethod.GET,
                null,
                OrderActionResponse.class);
    }

}
