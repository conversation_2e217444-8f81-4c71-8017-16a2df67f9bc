package su.reddot.infrastructure.bank.payments.noon.client.response;

import lombok.Getter;
import su.reddot.infrastructure.bank.payments.noon.client.common.BaseApiResponse;

@Getter
public class OrderActionResponse extends BaseApiResponse {
    private Integer resultCode;
    private String message;
    private Integer resultClass;
    private String classDescription;
    private String actionHint;
    private String requestReference;
    private Result result;
}
