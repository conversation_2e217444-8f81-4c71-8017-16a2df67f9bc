package su.reddot.infrastructure.bank.payments.noon.client.common;

import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang3.StringUtils;

public enum OrderStatus {
    INITIATED,
    AUTHORIZED,
    CANCELLED,
    CAPTURED,
    FAILED,
    PARTIALLY_CAPTURED,
    <PERSON>RTIALLY_REFUNDED,
    REFUNDED,
    PAYMENT_INFO_ADDED,
    _3DS_ENROLL_INITIATED,
    _3DS_ENROLL_CHECKED,
    _3DS_RESULT_VERIFIED,
    MARKED_FOR_REVIEW,
    AUTHENTICATED,
    PARTIALLY_REVERSED,
    TOKENIZED,
    EXPIRED,
    REVERSED,
    REJECTED,
    PENDING;

    @JsonValue
    public String getLowercaseValue() { // They all are lowercase, JsonNaming doesn`t work with enum
        return StringUtils.removeStart(this.toString(), "_");
    }

}
