package su.reddot.infrastructure.bank.payments.noon;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.WordUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.address.Currency;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.banktransaction.order.OrderBankOperation;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.banktransaction.BankOperationService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.BankOperationDTO;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.order.OrderPaymentFailParameters;
import su.reddot.domain.service.order.OrderPaymentOkayParameters;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.task.TaskPublisher;
import su.reddot.infrastructure.bank.BaseBankService;
import su.reddot.infrastructure.bank.Payable;
import su.reddot.infrastructure.bank.commons.AddressContactInfo;
import su.reddot.infrastructure.bank.commons.BankCommons;
import su.reddot.infrastructure.bank.commons.PaymentCaptureRequest;
import su.reddot.infrastructure.bank.commons.PaymentRefundsRequest;
import su.reddot.infrastructure.bank.commons.PaymentReverseRequest;
import su.reddot.infrastructure.bank.payments.noon.client.NoonBankClient;
import su.reddot.infrastructure.bank.payments.noon.client.NoonBankServiceException;
import su.reddot.infrastructure.bank.payments.noon.client.common.TransactionStatus;
import su.reddot.infrastructure.bank.payments.noon.client.common.TransactionType;
import su.reddot.infrastructure.bank.payments.noon.client.request.Address;
import su.reddot.infrastructure.bank.payments.noon.client.request.AddressContact;
import su.reddot.infrastructure.bank.payments.noon.client.request.ApiOperation;
import su.reddot.infrastructure.bank.payments.noon.client.request.Configuration;
import su.reddot.infrastructure.bank.payments.noon.client.request.Contact;
import su.reddot.infrastructure.bank.payments.noon.client.request.OrderActionRequest;
import su.reddot.infrastructure.bank.payments.noon.client.request.OrderInfo;
import su.reddot.infrastructure.bank.payments.noon.client.response.OrderActionResponse;
import su.reddot.infrastructure.bank.payments.noon.client.response.Transaction;
import su.reddot.infrastructure.bank.payoutpart.ManualCsvPayoutService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.j8missing;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
@ConditionalOnProperty("payments.noon-payments.enabled")
public class NoonBankService extends BaseBankService {

    public static final String NOON_BANK_NAME = "NoonPayments";
    public static final String NOON_SCHEMA = "noonpayments-1.0";

    public static final String NOON_METHOD_APPLE_PAY = "applepay";
    public static final String NOON_METHOD_CARD = "card";

    private static final int MAX_ADDRESS_LINE_LENGTH = 60;

    private final Clock clock;

    private final OrderRepository orderRepository;
    private final OrderService orderService;
    private final BankOperationService<BankOperation> bankOperationService;
    private final CurrencyService currencyService;
    private final OrderPaymentService orderPaymentService;

    private final TaskPublisher taskPublisher;
    private final CallInTransaction callInTransaction;

    private final NoonBankServiceConfiguration noonBankServiceConfiguration;

    private final ManualCsvPayoutService manualCsvPayoutService;

    private NoonBankClient noonBankClient;

    @Synchronized
    NoonBankClient getNoonBankClient() {
        if (Objects.nonNull(noonBankClient)) {
            return noonBankClient;
        }
        //
        noonBankClient = NoonBankClient.builder()
                .apiUrl(noonBankServiceConfiguration.getApiUrl())
                .environment(noonBankServiceConfiguration.getEnvironment())
                .businessId(noonBankServiceConfiguration.getBusinessId())
                .applicationId(noonBankServiceConfiguration.getApplicationId())
                .applicationKey(noonBankServiceConfiguration.getApplicationKey())
                .build();
        //
        return noonBankClient;
    }

    @Override
    public boolean isSyncOperationsMode() {
        return false;
    }

    @Override
    public List<String> getSupportedPaymentVersions() {
        return Collections.singletonList(NOON_SCHEMA);
    }

    @Override
    public List<Long> checkExpiringOrders() {
        return Collections.emptyList();
    }

    @Override
    @Synchronized
    @Transactional
    public BankPaymentDTO transferMoneyToSeller(@NonNull AgentReport agentReport) {
        return manualCsvPayoutService.taskTransferMoneyToSeller(agentReport.getId(), NOON_BANK_NAME);
    }

    @Override
    @Transactional
    public boolean checkOrderIsFinishedForSeller(Long orderId) {
        Order order = orderService.getOrder(orderId);
        return order.isFinishedForSeller();
    }

    @Override
    public List<BankOperationDTO> checkBankOperationsInProgress(Long maxQueueSize) {
        List<TransactionState> states = Arrays.asList(TransactionState.INPROGRESS, TransactionState.PREPARED);
        List<BankOperationDTO> bankOperationDTOs = new ArrayList<>();
        List<BankOperation> bankOperations = bankOperationService.getBankOperationsByStates(NOON_BANK_NAME, states);
        //
        log.info("Очередь обработки банковских операций (статусы: {}): {}", states, bankOperations.size());
        //
        for (BankOperation operation : bankOperations) {
            if (operation.getState() == TransactionState.INPROGRESS) {
                try {
                    BankOperationDTO operationDTO = checkBankOperationState(operation);
                    if (operationDTO.getState() != TransactionState.INPROGRESS) {
                        bankOperationDTOs.add(operationDTO);
                    }
                } catch (Exception ex) {
                    log.error("Unable to process bank operation with uuid {}: {}", operation.getUuid(), ex.getMessage(), ex);
                }
            } else {
                log.warn("Bank operation {}: state {} unsupported", operation.getUuid(), operation.getState());
            }
        }
        return bankOperationDTOs;
    }

    @Override
    @SneakyThrows
    public BankOperationDTO checkAndProcessBankOperation(String bankOperationId) {
        BankOperation operation = bankOperationService.getBankOperationByUUID(bankOperationId);
        if (operation.getState() != TransactionState.INPROGRESS) {
            return createBankOperationDTO(operation, operation.getState());
        }
        BankOperationDTO operationDTO = checkBankOperationState(operation);
        if (operationDTO.getState() != TransactionState.INPROGRESS) {
            taskPublisher.notifyMasterOnBankOperationStatusChange(Collections.singletonList(operationDTO));
        }
        return operationDTO;
    }

    @SneakyThrows
    private BankOperationDTO checkAuth(OrderActionResponse orderInfo, BankOperationDTO operationDTO, BigDecimal amount) {
        if (!Objects.equals(operationDTO.getUuid(), orderInfo.getResult().getOrder().getReference())) {
            throw new NoonBankServiceException("Order info mistmatch: orderId != reference (%s != %s)",
                    operationDTO.getUuid(), orderInfo.getResult().getOrder().getReference());
        }
        switch (orderInfo.getResult().getOrder().getStatus()) {
            case CANCELLED:
            case REJECTED:
            case FAILED:
            case EXPIRED:
                operationDTO.setState(TransactionState.CANCELED);
                break;
            case AUTHORIZED:
                if (amount.compareTo(orderInfo.getResult().getOrder().getTotalAuthorizedAmount()) != 0) {
                    throw new NoonBankServiceException("Operation %s: authorization amount differs, operationAmount != totalAuthorizedAmount (%s != %s)",
                            operationDTO.getUuid(), amount, orderInfo.getResult().getOrder().getTotalAuthorizedAmount());
                }
                operationDTO.setState(TransactionState.DONE);
                j8missing.streamOfNullable(orderInfo.getResult().getTransactions())
                        .filter(tr -> tr.getType() == TransactionType.AUTHORIZATION && tr.getStatus() == TransactionStatus.SUCCESS)
                        .findFirst()
                        .ifPresent(tr -> {
                            operationDTO.setRrn(tr.getRrn());
                            operationDTO.setBankOperationId(tr.getId());
                            //operationDTO.setPaymentSystemTime(LocalDateTime.from(tr.creationTime));
                        });
                break;
        }
        return operationDTO;
    }

    @SneakyThrows
    private BankOperationDTO checkCapture(OrderActionResponse orderInfo, BankOperationDTO operationDTO, BigDecimal amount) {
        switch (orderInfo.getResult().getOrder().getStatus()) {
            case CAPTURED:
                if (amount.compareTo(orderInfo.getResult().getOrder().getTotalCapturedAmount()) != 0) {
                    throw new NoonBankServiceException("Operation %s: capture amount differs, operationAmount != totalCapturedAmount (%s != %s)",
                            operationDTO.getUuid(), amount, orderInfo.getResult().getOrder().getTotalCapturedAmount());
                }
                operationDTO.setState(TransactionState.DONE);
                break;
        }
        return operationDTO;
    }

    @SneakyThrows
    private BankOperationDTO checkReverse(OrderActionResponse orderInfo, BankOperationDTO operationDTO, BigDecimal amount) {
        switch (orderInfo.getResult().getOrder().getStatus()) {
            case REVERSED:
                if (amount.compareTo(orderInfo.getResult().getOrder().getTotalReversedAmount()) != 0) {
                    throw new NoonBankServiceException("Operation %s: reverse amount differs, operationAmount != totalReversedAmount (%s != %s)",
                            operationDTO.getUuid(), amount, orderInfo.getResult().getOrder().getTotalReversedAmount());
                }
                operationDTO.setState(TransactionState.DONE);
                break;
        }
        return operationDTO;
    }

    @SneakyThrows
    private BankOperationDTO checkRefunds(OrderActionResponse orderInfo, BankOperationDTO operationDTO, BigDecimal amount) {
        switch (orderInfo.getResult().getOrder().getStatus()) {
            case REFUNDED:
            case PARTIALLY_REFUNDED:
                if (amount.compareTo(orderInfo.getResult().getOrder().getTotalRefundedAmount()) != 0) {
                    throw new NoonBankServiceException("Operation %s: refunds amount differs, operationAmount != totalRefundedAmount (%s != %s)",
                            operationDTO.getUuid(), amount, orderInfo.getResult().getOrder().getTotalCapturedAmount());
                }
                operationDTO.setState(TransactionState.DONE);
                break;
        }
        return operationDTO;
    }

    private BankOperation findOriginalOperation(BankOperation operation) {
        if (operation.getOperationType() == OperationType.HOLD) {
            return operation;
        }
        return bankOperationService.getBankOperationsByObject(operation.getObjectId()).stream()
                .filter(bo -> bo.getOperationType() == OperationType.HOLD && bo.getState() == TransactionState.DONE)
                .findFirst()
                .orElseThrow(() -> new NoonBankServiceException("Unable to find original operation with object id %s", operation.getObjectId()));
    }

    private BankOperationDTO checkBankOperationState(BankOperation operation) {
        BankOperation originalOperation = findOriginalOperation(operation);
        OrderActionResponse orderInfo = getNoonBankClient().getOrderByReference(originalOperation.getUuid());
        //
        BankOperationDTO operationDTO = createBankOperationDTO(operation, TransactionState.INPROGRESS);
        operationDTO.setRawResponse(orderInfo.rawResponse);
        operationDTO.setPaymentSystemCode(orderInfo.getResultCode().toString());
        operationDTO.setStateUserText(orderInfo.getMessage());
        switch (operation.getOperationType()) {
            case HOLD:
                return checkAuth(orderInfo, operationDTO, BigDecimal.valueOf(operation.getApiSentAmount(), 2));
            case HOLD_COMPLETE:
                return checkCapture(orderInfo, operationDTO, BigDecimal.valueOf(operation.getApiSentAmount(), 2));
            case HOLD_REVERSE:
                return checkReverse(orderInfo, operationDTO, BigDecimal.valueOf(operation.getApiSentAmount(), 2));
            case REFUND:
                return checkRefunds(orderInfo, operationDTO, BigDecimal.valueOf(operation.getApiSentAmount(), 2));
            default: // TODO: Better throw an exception, but will need to cover uplevel handler in try / catch
                log.warn("Operation {}, unsupported operation type: {}", operation.getUuid(), operation.getOperationType());
        }
        return operationDTO;
    }

    @Override
    synchronized public List<BankOperationDTO> handleBankOperations(List<BankOperationDTO> bankOperationDTOs) {
        List<BankOperationDTO> processedOperations = new ArrayList<>();
        for (BankOperationDTO bankOperationDTO : bankOperationDTOs) {
            try {
                callInTransaction.runInNewTransaction(() -> handleOneBankOperationDTO(bankOperationDTO));
                processedOperations.add(bankOperationDTO);
            } catch (Exception e) {
                log.error("Ошибка при сохранении информации о банковской операции {} (objectID: {}): {}", bankOperationDTO.getUuid(), bankOperationDTO.getObjectId(), e.getMessage(), e);
            }
        }
        return processedOperations;
    }

    private String handleOneBankOperationDTO(BankOperationDTO bankOperationDTO) {
        BankOperation bankOperation = bankOperationService.getBankOperation(bankOperationDTO.getId());
        //
        bankOperation.setState(bankOperationDTO.getState());
        bankOperation.setFee(bankOperationDTO.getFee());
        bankOperation.setStateUserText(bankOperationDTO.getStateUserText());
        bankOperation.setRrn(bankOperationDTO.getRrn());
        bankOperation.setRawResponse(bankOperationDTO.getRawResponse());
        bankOperation.setPaymentSystemCode(bankOperationDTO.getPaymentSystemCode());
        bankOperation.setPaymentSystemTime(bankOperationDTO.getPaymentSystemTimeLdt());
        //
        handleOneBankOperation(bankOperationDTO, bankOperation);
        bankOperationService.changeOperation(bankOperation);
        //
        return bankOperation.getUuid();
    }

    private void handleOneBankOperation(BankOperationDTO bankOperationDTO, BankOperation bankOperation) {
        switch (bankOperationDTO.getOperationType()) {
            case HOLD:
                handleBankOperationTypeHold(bankOperation);
                break;
            case HOLD_COMPLETE:
                handleBankOperationTypeHoldComplete(bankOperationDTO, bankOperation);
                break;
            case REFUND:
                handleBankOperationTypeRefund(bankOperationDTO, bankOperation);
                break;
            case SELLER_PAYOUT:
                // do nothing, case is handled via notifications on BankPayments
                break;
            case OSKELLY_PAYOUT:
                //handleBankOperationTypeOskellyPayout(bankOperationDTO, bankOperation);
                break;
            default: // HOLD_REVERSE / PARTIAL_REFUND / CARD_BIND / CARD_UNBIND
                log.info("Skipping handling for {}", bankOperationDTO);
        }
    }

    private void handleBankOperationTypeHoldComplete(BankOperationDTO bankOperationDTO, BankOperation bankOperation) {
        Order order = orderService.getOrder(bankOperation.getObjectId());
        orderService.handleHoldCompleteTransaction(order, bankOperation.getState() == TransactionState.DONE);
    }

    private void handleBankOperationTypeRefund(BankOperationDTO bankOperationDTO, BankOperation bankOperation) {
        Order order = orderService.getOrder(bankOperation.getObjectId());
        if (TransactionState.DONE == bankOperation.getState()) {
            orderPaymentService.changeOrderPaymentState(order, OrderPaymentState.REFUND_DONE);
        } else if (TransactionState.CANCELED == bankOperation.getState()) {
            orderPaymentService.changeOrderPaymentState(order, OrderPaymentState.REFUND_FAIL);
            log.warn("NoonBankService: refund fail in order {}", bankOperation.getObjectId());
        }
    }

    private void handleBankOperationTypeHold(BankOperation bankOperation) {
        // No order (deleted) is okay on HOLD_ERROR, in other cases it`s strange
        //TODO для регистрации операции HOLD в ошибочном кейсе проставить handleErrorTransaction(order, authorizedOperation.getId());
        Order order = orderService.getOrderOrFail(bankOperation.getObjectId());
        if (bankOperation.getState() == TransactionState.DONE) {
            OrderPaymentOkayParameters orderPaymentOkayParameters = OrderPaymentOkayParameters.builder()
                    .bankService(this)
                    .transactionId(bankOperation.getBankTransactionId())
                    .acquirerOrderId(bankOperation.getUuid())
                    .build();
            orderService.handleHoldTransaction(order, orderPaymentOkayParameters);
        } else if (bankOperation.getState() != TransactionState.INPROGRESS) {
            OrderPaymentFailParameters orderPaymentFailParameters = OrderPaymentFailParameters.builder()
                    .bankService(this)
                    .transactionId(bankOperation.getBankTransactionId())
                    .build();
            orderService.handleErrorTransaction(order, orderPaymentFailParameters);
        }
    }

    @Transactional
    @Override
    public String bindCard(User currentUser, String nullableReturnUrl) {
        return  "";
    }

    @Transactional
    @Override
    public Long handleBindCard(String bankOperationUUID) {
        return 0L;
    }

    @Transactional
    @Override
    public void unbindCard(Long counterpartyId) {
        //
    }

    @Override
    public OrderService.InitOrderResult getPaymentRequest(Payable p, String nullableReturnUrl, ZonedDateTime requestDateTime, boolean fromMobile) {
        validateOperationCurrency(p.getCurrencyCode());
        String cookedReturnUrl = Objects.isNull(nullableReturnUrl)
                ? noonBankServiceConfiguration.getReturnUrl()
                : String.format("%s%s", noonBankServiceConfiguration.getReturnUrl(), nullableReturnUrl);
        Optional<BankOperation> holdOperationInProgress = getHoldBankOperationInProgressOrDone(p, bankOperationService);
        try { // There must be no "null" extraInfo for HOLD operation (cause we saving it in getTcbHoldFormUrlAndSaveBankOperation), but if we`ll find one - let it fail
            String formUrl = holdOperationInProgress.isPresent()
                    ? holdOperationInProgress.get().getExtraInfo()
                    : getNoonHoldFormUrlAndSaveBankOperation(p, cookedReturnUrl);
            return new NoonPaymentsInitOrderResult(Long.valueOf(p.getOrderId()), formUrl);
        } catch (Exception e) {
            log.error("Order {} (amount: {}): payment fail with error {}", p.getOrderId(), p.getPaymentAmount(), e.getMessage(), e);
            throw new NoonBankServiceException("Order %s: unable to generate payment request", p.getOrderId());
        }
    }

    private String getNoonOrderCategory(Payable payable) {
        if (Objects.equals(payable.getPaymentMethods(), NOON_METHOD_APPLE_PAY))
            return "applepay";
        if (Objects.equals(payable.getPaymentMethods(), NOON_METHOD_CARD))
            return "3ds";
        return "pay";
    }

    @SneakyThrows
    private String getNoonHoldFormUrlAndSaveBankOperation(Payable p, String cookedReturnUrl) {
        String operationUUID = UUID.randomUUID().toString();
        Currency payCurrency = currencyService.getCurrencyByCode(p.getCurrencyCode());
        //
        String callbackUrl = String.format("%s/api/v2/acquirers/%s/verify-payment?redirectTo=%s&operationId=%s",
                noonBankServiceConfiguration.getReturnUrl(),
                getSupportedPaymentVersions().get(0),
                URLEncoder.encode(cookedReturnUrl, StandardCharsets.UTF_8.name()),
                operationUUID
        );
        //
        long amountInCurrency = BankCommons.toAmountInCents(p.getPaymentAmount());
        LocalDateTime linkTtl = LocalDateTime.now(clock.withZone(ZoneId.of("UTC"))).plus(noonBankServiceConfiguration.getPaymentsTtl());
        //
        AddressContact billingAddress = getNoonAddressContact(p.getBillingDetails());
        //
        OrderActionRequest orderActionRequest = OrderActionRequest.builder()
                .apiOperation(ApiOperation.INITIATE)
                .billing(billingAddress)
                .order(OrderInfo.builder()
                        .amount(BigDecimal.valueOf(amountInCurrency, 2))
                        .currency(p.getCurrencyCode())
                        .name(p.getOrderId())
                        .description(String.format("Payment for order %s", p.getOrderId()))
                        .reference(operationUUID)
                        .category(getNoonOrderCategory(p))
                        .channel("web")
                        .build())
                .configuration(Configuration.builder()
                        .locale("en")
                        .paymentAction("AUTHORIZE")
                        .returnUrl(callbackUrl)
                        .initiationValidity(linkTtl)
                        .styleProfile(noonBankServiceConfiguration.getStyleProfile())
                        .generateShortLink(noonBankServiceConfiguration.getUseShortLink())
                        .build())
                .build();
        //
        OrderActionResponse orderActionResponse = getNoonBankClient().orderAction(orderActionRequest);
        //
        OrderBankOperation orderBankOperation = new OrderBankOperation();
        orderBankOperation.setOrder(orderRepository.findById(new Long(p.getOrderId())).orElse(null));
        orderBankOperation.setUuid(operationUUID);
        orderBankOperation.setOperationType(OperationType.HOLD);
        orderBankOperation.setBank(NOON_BANK_NAME);
        orderBankOperation.setPaymentMethod(p.getPaymentMethods());
        orderBankOperation.setCurrency(payCurrency);
        orderBankOperation.setApiSentAmount(amountInCurrency);
        orderBankOperation.setState(TransactionState.INPROGRESS);
        orderBankOperation.setExtraInfo(orderActionResponse.getResult().getCheckoutData().postUrl);
        bankOperationService.createOperation(orderBankOperation);
        //
        return orderBankOperation.getExtraInfo();
    }

    public static List<String> getAddressSplitBySpaceAtLeastTwoLines(String fullAddressTxt) {
        String addressSplit = WordUtils.wrap(fullAddressTxt, MAX_ADDRESS_LINE_LENGTH, System.lineSeparator(), true, " ");
        List<String> addressLines = new ArrayList<>();
        if (Objects.nonNull(addressSplit)) {
            addressLines.addAll(Arrays.asList(addressSplit.split(System.lineSeparator())));
        }
        addressLines.addAll(Arrays.asList(null, null)); // to have at least 2 lines
        return addressLines;
    }

    private AddressContact getNoonAddressContact(AddressContactInfo billingDetails) {
        List<String> addressLines = getAddressSplitBySpaceAtLeastTwoLines(billingDetails.getAddress().getStreet());
        return AddressContact.builder()
                .address(Address.builder()
                        .country(billingDetails.getAddress().getCountry())
                        .postalCode(billingDetails.getAddress().getPostalCode())
                        .city(billingDetails.getAddress().getCity())
                        .street(addressLines.get(0))
                        .street2(addressLines.get(1))
                        .build())
                .contact(Contact.builder()
                        .firstName(billingDetails.getContact().getFirstName())
                        .lastName(billingDetails.getContact().getLastName())
                        .email(billingDetails.getContact().getEmail())
                        .mobilePhone(billingDetails.getContact().getPhone())
                        .build())
                .build();
    }

    @Override
    public void handleTransactionInfo(String paymentsSchema, String str) {
        //
    }

    private String executeNoonCapture(String operationUUID, Long noonOrderId, BigDecimal captureAmount, String currencyCode) {
        OrderActionRequest orderActionRequest = OrderActionRequest.builder()
                .apiOperation(ApiOperation.CAPTURE)
                .order(OrderInfo.builder()
                        .id(noonOrderId)
                        .build())
                .transaction(Transaction.builder()
                        .transactionReference(operationUUID)
                        .amount(captureAmount)
                        .currency(currencyCode)
                        .finalCapture(true)
                        .build())
                .build();
        //
        OrderActionResponse orderActionResponse = getNoonBankClient().orderAction(orderActionRequest);
        //
        return orderActionResponse.getResult().getTransaction().getId();
    }

    private String executeNoonReverse(String operationUUID, Long noonOrderId) {
        OrderActionRequest orderActionRequest = OrderActionRequest.builder()
                .apiOperation(ApiOperation.REVERSE)
                .order(OrderInfo.builder()
                        .id(noonOrderId)
                        .build())
                .transaction(Transaction.builder()
                        .transactionReference(operationUUID)
                        .build())
                .build();
        //
        OrderActionResponse orderActionResponse = getNoonBankClient().orderAction(orderActionRequest);
        //
        return orderActionResponse.getResult().getTransaction().getId();
    }

    private String executeNoonRefunds(String operationUUID, Long noonOrderId, String capturesId, BigDecimal refundsAmount, String currencyCode) {
        OrderActionRequest orderActionRequest = OrderActionRequest.builder()
                .apiOperation(ApiOperation.REFUND)
                .order(OrderInfo.builder()
                        .id(noonOrderId)
                        .build())
                .transaction(Transaction.builder()
                        .transactionReference(operationUUID)
                        .targetTransactionId(capturesId)
                        .amount(refundsAmount)
                        .currency(currencyCode)
                        .build())
                .build();
        //
        OrderActionResponse orderActionResponse = getNoonBankClient().orderAction(orderActionRequest);
        //
        return orderActionResponse.getResult().getTransaction().getId();
    }

    private void validateNoonOrderCurrency(Long orderId, OrderActionResponse orderInfo, String currencyCode) {
        String noonCurrency = orderInfo.getResult().getOrder().getCurrency();
        if (!Objects.equals(currencyCode, noonCurrency)) {
            throw new NoonBankServiceException("Order %d: currencyCode differs (%s != %s)", orderId, noonCurrency, currencyCode);
        }
    }

    private BankOperation findHoldSuccessOperation(Order order, String currencyCode, OperationType nextOperationType) {
        BankOperation holdOperation = order.findOperation(bo -> bo.getOperationType() == OperationType.HOLD && bo.getState() == TransactionState.DONE);
        if (Objects.isNull(holdOperation)) {
            throw new NoonBankServiceException("Order %d: unable to find HOLD operation with DONE state", order.getId());
        }
        if (!Objects.equals(holdOperation.getCurrency().getIsoCode(), currencyCode)) {
            throw new NoonBankServiceException("Order %d: authorize - %s currencies differs (%s != %s)",
                    order.getId(), nextOperationType.name(), holdOperation.getCurrency().getIsoCode(), currencyCode);
        }
        return holdOperation;
    }

    private BankOperation findCaptSuccessOperation(Order order, String currencyCode, OperationType nextOperationType) {
        BankOperation captOperation = order.findOperation(bo -> bo.getOperationType() == OperationType.HOLD_COMPLETE && bo.getState() == TransactionState.DONE);
        if (Objects.isNull(captOperation)) {
            throw new NoonBankServiceException("Order %d: unable to find HOLD_COMPLETE operation with DONE state", order.getId());
        }
        if (!Objects.equals(captOperation.getCurrency().getIsoCode(), currencyCode)) {
            throw new NoonBankServiceException("Order %d: capture - %s currencies differs (%s != %s)",
                    order.getId(), nextOperationType.name(), captOperation.getCurrency().getIsoCode(), currencyCode);
        }
        return captOperation;
    }

    @Override
    @SneakyThrows
    public boolean completeHold(PaymentCaptureRequest paymentCaptureRequest) {
        validateOperationCurrency(paymentCaptureRequest.getCurrencyCode());
        Currency payCurrency = currencyService.getCurrencyByCode(paymentCaptureRequest.getCurrencyCode());
        //
        Order order = orderService.getOrderOrFail(paymentCaptureRequest.getOrderId());
        long captureAmount = BankCommons.toAmountInCents(paymentCaptureRequest.getCaptureAmount());
        // 1. Find successfull HOLD operation
        BankOperation holdOperation = findHoldSuccessOperation(order, paymentCaptureRequest.getCurrencyCode(), OperationType.HOLD_COMPLETE);
        // 2. Get Order info
        OrderActionResponse orderInfo = getNoonBankClient().getOrderByReference(holdOperation.getUuid());
        validateNoonOrderCurrency(order.getId(), orderInfo, paymentCaptureRequest.getCurrencyCode());
        //
        String transactionId;
        String operationUUID = UUID.randomUUID().toString();
        BigDecimal requestSum2Capture = BigDecimal.valueOf(captureAmount, 2);
        BigDecimal existingCaptureSum = orderInfo.getResult().getOrder().getTotalCapturedAmount();
        if (existingCaptureSum.compareTo(BigDecimal.ZERO) == 0) { // No successful capture operation exists: call it
            transactionId = executeNoonCapture(operationUUID,
                    orderInfo.getResult().getOrder().getId(),
                    requestSum2Capture,
                    paymentCaptureRequest.getCurrencyCode());
        } else {
            if (existingCaptureSum.compareTo(requestSum2Capture) != 0) {
                throw new NoonBankServiceException("Order %d: existing capture amount differs (%s != %s)",
                        order.getId(), existingCaptureSum, requestSum2Capture);
            }
            Transaction captureTransaction = orderInfo.getResult().getTransactions().stream()
                    .filter(ti -> ti.getType() == TransactionType.CAPTURE && ti.getStatus() == TransactionStatus.SUCCESS)
                    .findFirst()
                    .orElseThrow(() -> new NoonBankServiceException("Order %d: unable to find '%s' transaction (totalCapturedAmount = %s)",
                            order.getId(), TransactionType.CAPTURE, existingCaptureSum));
            operationUUID = captureTransaction.getTransactionReference();
            transactionId = captureTransaction.getId();
        }
        //
        OrderBankOperation orderBankOperation = new OrderBankOperation();
        orderBankOperation.setUuid(operationUUID);
        orderBankOperation.setOrder(order);
        orderBankOperation.setState(TransactionState.INPROGRESS);
        orderBankOperation.setBank(NOON_BANK_NAME);
        orderBankOperation.setOperationType(OperationType.HOLD_COMPLETE);
        orderBankOperation.setBankTransactionId(transactionId);
        orderBankOperation.setCurrency(payCurrency);
        orderBankOperation.setApiSentAmount(captureAmount);
        orderBankOperation.setComment(paymentCaptureRequest.getComment());
        bankOperationService.createOperation(orderBankOperation);
        //
        return true;
    }

    @Transactional
    @Override
    @SneakyThrows
    public void reverse(PaymentReverseRequest paymentReverseRequest) {
        validateOperationCurrency(paymentReverseRequest.getCurrencyCode());
        Currency payCurrency = currencyService.getCurrencyByCode(paymentReverseRequest.getCurrencyCode());
        //
        Order order = orderService.getOrderOrFail(paymentReverseRequest.getOrderId());
        long reverseAmount = BankCommons.toAmountInCents(paymentReverseRequest.getReverseAmount());
        // 1. Find successful HOLD operation
        BankOperation holdOperation = findHoldSuccessOperation(order, paymentReverseRequest.getCurrencyCode(), OperationType.HOLD_REVERSE);
        // 2. Get OrderInfo
        OrderActionResponse orderInfo = getNoonBankClient().getOrderByReference(holdOperation.getUuid());
        validateNoonOrderCurrency(order.getId(), orderInfo, paymentReverseRequest.getCurrencyCode());
        if (orderInfo.getResult().getOrder().getAmount().compareTo(BigDecimal.valueOf(reverseAmount, 2))  != 0) {
            throw new NoonBankServiceException("Order %d: void amount differs (%d != %d)", paymentReverseRequest.getOrderId(), orderInfo.getResult().getOrder().getAmount(), reverseAmount);
        }
        // 3. Void payment and store bank operation
        String transactionId;
        String operationUUID = UUID.randomUUID().toString();
        BigDecimal requestSum2Reverse = BigDecimal.valueOf(reverseAmount, 2);
        BigDecimal existingReverseSum = orderInfo.getResult().getOrder().getTotalReversedAmount();
        if (existingReverseSum.compareTo(BigDecimal.ZERO) == 0) { // No successful reverse operation exists: call it
            transactionId = executeNoonReverse(operationUUID, orderInfo.getResult().getOrder().getId());
        } else {
            if (existingReverseSum.compareTo(requestSum2Reverse) != 0) {
                throw new NoonBankServiceException("Order %d: existing reverse amount differs (%s != %s)",
                        order.getId(), existingReverseSum, requestSum2Reverse);
            }
            Transaction reverseTransaction = orderInfo.getResult().getTransactions().stream()
                    .filter(ti -> ti.getType() == TransactionType.VOID_AUTHORIZATION && ti.getStatus() == TransactionStatus.SUCCESS)
                    .findFirst()
                    .orElseThrow(() -> new NoonBankServiceException("Order %d: unable to find '%s' transaction (totalReversedAmount = %s)",
                            order.getId(), TransactionType.VOID_AUTHORIZATION, existingReverseSum));
            transactionId = reverseTransaction.getId();
        }
        //
        OrderBankOperation orderBankOperation = new OrderBankOperation();
        orderBankOperation.setUuid(operationUUID);
        orderBankOperation.setOrder(order);
        orderBankOperation.setState(TransactionState.INPROGRESS);
        orderBankOperation.setBank(NOON_BANK_NAME);
        orderBankOperation.setOperationType(OperationType.HOLD_REVERSE);
        orderBankOperation.setBankTransactionId(transactionId);
        orderBankOperation.setCurrency(payCurrency);
        orderBankOperation.setApiSentAmount(reverseAmount);
        orderBankOperation.setComment(paymentReverseRequest.getComment());
        bankOperationService.createOperation(orderBankOperation);
    }

    private String getCaptureTransactionId(Order order, OrderActionResponse orderInfo, String transactionReference) {
        return orderInfo.getResult().getTransactions().stream()
                .filter(it -> it.getType() == TransactionType.CAPTURE)
                .filter(it -> Objects.equals(it.getTransactionReference(), transactionReference))
                .findFirst()
                .map(Transaction::getId)
                .orElseThrow(() ->
                        new NoonBankServiceException("Order %d: unable to find Noon capture transaction in payOrder %s with reference",
                                order.getId(), orderInfo.getResult().getOrder().getId(), transactionReference));
    }

    @Transactional
    @Override
    @SneakyThrows
    public void refund(PaymentRefundsRequest paymentRefundsRequest) {
        validateOperationCurrency(paymentRefundsRequest.getCurrencyCode());
        Currency payCurrency = currencyService.getCurrencyByCode(paymentRefundsRequest.getCurrencyCode());
        //
        Order order = orderService.getOrderOrFail(paymentRefundsRequest.getOrderId());
        long refundsAmount = BankCommons.toAmountInCents(paymentRefundsRequest.getRefundsAmount());
        // 1. Find successful HOLD operation
        BankOperation holdOperation = findHoldSuccessOperation(order, paymentRefundsRequest.getCurrencyCode(), OperationType.REFUND);
        BankOperation captOperation = findCaptSuccessOperation(order, paymentRefundsRequest.getCurrencyCode(), OperationType.REFUND);
        // 2. Get OrderInfo
        OrderActionResponse orderInfo = getNoonBankClient().getOrderByReference(holdOperation.getUuid());
        validateNoonOrderCurrency(order.getId(), orderInfo, paymentRefundsRequest.getCurrencyCode());
        // 3. Call refund and store bank operation
        String transactionId;
        String operationUUID = UUID.randomUUID().toString();
        BigDecimal requestSum2Refunds = BigDecimal.valueOf(refundsAmount, 2);
        BigDecimal existingRefundsSum = orderInfo.getResult().getOrder().getTotalRefundedAmount();
        String captureTransactionId = getCaptureTransactionId(order, orderInfo, captOperation.getUuid());
        if (existingRefundsSum.compareTo(BigDecimal.ZERO) == 0) { // No successful refunds operation exists: call it
            transactionId = executeNoonRefunds(operationUUID, orderInfo.getResult().getOrder().getId(), captureTransactionId, requestSum2Refunds, paymentRefundsRequest.getCurrencyCode());
        } else {
            if (existingRefundsSum.compareTo(requestSum2Refunds) != 0) {
                throw new NoonBankServiceException("Order %d: existing refunds amount differs (%s != %s)",
                        order.getId(), existingRefundsSum, requestSum2Refunds);
            }
            Transaction refundsTransaction = orderInfo.getResult().getTransactions().stream()
                    .filter(ti -> ti.getType() == TransactionType.REFUND && ti.getStatus() == TransactionStatus.SUCCESS)
                    .findFirst()
                    .orElseThrow(() -> new NoonBankServiceException("Order %d: unable to find '%s' transaction (totalRefundedAmount = %s)",
                            order.getId(), TransactionType.REFUND, existingRefundsSum));
            transactionId = refundsTransaction.getId();
        }
        //
        OrderBankOperation orderBankOperation = new OrderBankOperation();
        orderBankOperation.setUuid(operationUUID);
        orderBankOperation.setOrder(order);
        orderBankOperation.setState(TransactionState.INPROGRESS);
        orderBankOperation.setBank(NOON_BANK_NAME);
        orderBankOperation.setOperationType(OperationType.REFUND);
        orderBankOperation.setBankTransactionId(transactionId);
        orderBankOperation.setCurrency(payCurrency);
        orderBankOperation.setApiSentAmount(refundsAmount);
        orderBankOperation.setComment(paymentRefundsRequest.getComment());
        bankOperationService.createOperation(orderBankOperation);
    }

    public static class NoonPaymentsInitOrderResult extends OrderService.InitOrderResult {
        public NoonPaymentsInitOrderResult(Long orderId, String bank_url) {
            super(orderId, bank_url, NOON_SCHEMA);
        }
    }

    @Override
    public Set<String> getAllowCurrencies() {
        return noonBankServiceConfiguration.getCurrencies();
    }

    @Override
    @Synchronized
    @Transactional
    public List<BankPaymentDTO> checkTransferMoneyToSeller() {
        return manualCsvPayoutService.completeTransferMoneyToSeller(NOON_SCHEMA);
    }

    @Override
    @Synchronized
    @Async
    public void saveCheckTransferMoneyToPaymentAccountsAsync(List<BankPaymentDTO> bankPaymentDTOs) {
        callInTransaction.runInAnyTransaction(() -> manualCsvPayoutService.masterSaveTransferMoneyToSellers(bankPaymentDTOs, NOON_BANK_NAME, getSupportedPaymentVersions()));
    }

    @Override
    @Synchronized
    @Async
    public void saveTransferMoneyToSellersAsync(List<BankPaymentDTO> bankPaymentDTOs) {
        callInTransaction.runInAnyTransaction(() -> manualCsvPayoutService.masterSaveTransferMoneyToSellers(bankPaymentDTOs, NOON_BANK_NAME, getSupportedPaymentVersions()));
    }

    @Override
    public boolean isPayoutInBaseCurrencyOnly() {
        return false;
    }

    @Override
    public boolean isPrepaymentSupported() {
        return true;
    }

}
