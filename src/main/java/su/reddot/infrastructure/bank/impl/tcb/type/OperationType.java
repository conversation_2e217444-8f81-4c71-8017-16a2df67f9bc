package su.reddot.infrastructure.bank.impl.tcb.type;

/**
 * Тип операции
 */
public enum OperationType {
    /**
     *   Списание с зарегистрированной карты
     */
    FROMREGISTEREDCARD,

    /**
     *   Списание с незарегистрированной карты
     */
    FROMUNREGISTEREDCARD,

    /**
     * Зачисление на зарегистрированную карту
     */
    TOREGISTEREDCARD,

    /**
     * Зачисление на незарегистрированную карту
     */
    TOUNREGISTEREDCARD,


    /**
     * при безакцептном списании с карты
     */
    DIRECTFROMREGISTEREDCARD,

    /**
     * при зачислении на карту или при возврате на карту
     */
    CARD,

    HOLDUNREGISTEREDCARD,

    HOLDREGISTEREDCARD,

    TOACCOUNT,

    /**
     *  Возврат ранее списанных средств
     */
    REFUND,

    /**
     *  Отмена операции списания
     */
    REVERSE;
}
