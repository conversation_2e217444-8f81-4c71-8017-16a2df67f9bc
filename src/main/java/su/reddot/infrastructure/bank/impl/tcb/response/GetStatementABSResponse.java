package su.reddot.infrastructure.bank.impl.tcb.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * Операция зачисления средств по реквизитам счёта любого Банка в Российской Федерации
 */
@Getter
@Setter
@RequiredArgsConstructor
@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
public class GetStatementABSResponse {
	private List<Transaction> transactions;

	@JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy.class)
	@Getter
	@Setter
	public static class Transaction {

		private String account;

		private String accountCredit;

		private String accountDebet;

		private String comment;

		@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
		private LocalDateTime execDate;

		private BigDecimal feeAmount;

		@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
		private LocalDateTime operDate;

		private BigDecimal totalAmount;

		private Long transactID;

		private Long branchID;

		@JsonProperty("ID")
		private Long id;

	}
}
