package su.reddot.infrastructure.bank.impl.tcb.type;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigInteger;
import java.time.ZonedDateTime;

/**
 * Сущность предоставляет информацию об ошибки операции
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
public class ErrorInfo {
    /**
     * Код ошибки (0 - успех, 1 - ошибка)
     * Все остальные статусы не являются финальными и служат для получения
     * дополнительной информации
     */
    private Long errorCode;

    /** Описание ошибки */
    private String errorMessage;
}
