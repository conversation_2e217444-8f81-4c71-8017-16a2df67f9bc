package su.reddot.infrastructure.bank.impl.tcb.request;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonInclude;

@Getter
@Setter
@Builder
@AllArgsConstructor(access = AccessLevel.PACKAGE)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegisterHoldRequest {

	/**
	 * Уникальный идентификатор операции (UUID)
	 */
	private String extId;

	/**
	 * ID карты для предзаполнения на форме (необязательно)
	 */
	private Long cardRefId;

	/**
	 * Сумма в копейках
	 */
	private Long amount;

	/**
	 * Timeout, 00:05:00
	 */
	private String TTL;

	/**
	 * Комментарий
	 */
	private String description;

	private String returnURL;

	private boolean showReturnButton;

}