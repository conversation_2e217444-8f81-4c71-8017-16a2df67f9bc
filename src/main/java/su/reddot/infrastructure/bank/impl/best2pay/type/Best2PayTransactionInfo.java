package su.reddot.infrastructure.bank.impl.best2pay.type;

import best2pay.xsd.Operation;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Best2PayTransactionInfo extends Operation {
    private Integer sector;
    private Integer operation;

    public String getSequenceToSign() {
        StringBuilder sequence = new StringBuilder();
        if (sectorId != null || sector != null)
            sequence.append(sectorId == null ? sector.toString() : sectorId.toString());
        if (id != 0)
            sequence.append(id);
        if (operation != null)
            sequence.append(operation.toString());
        return sequence.toString();
    }
}