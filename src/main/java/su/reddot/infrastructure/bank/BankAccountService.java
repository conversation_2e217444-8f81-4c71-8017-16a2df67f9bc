package su.reddot.infrastructure.bank;

import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.BankBalanceDTO;
import su.reddot.domain.service.dto.BankMoneyTransferDTO;
import su.reddot.domain.service.dto.BankOperationDTO;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.infrastructure.bank.commons.PaymentCancelsRequest;
import su.reddot.infrastructure.bank.commons.PaymentCaptureRequest;
import su.reddot.infrastructure.bank.commons.PaymentRefundsRequest;
import su.reddot.infrastructure.bank.commons.PaymentReverseRequest;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * Сущность внешнего сервиса, предоставляющего услуги
 * проведения операций с банковским счетом Oskelly
 */
public interface BankAccountService {

	/**
     * Проверяет баланс счета в банке или был изменен согласно последней проверки
     * @return если был изменен согласно последней проверки, то возвращает данные в BankBalanceDTO, если не был, то null
     */
    default BankBalanceDTO checkBankBalanceWithLastBalance(){
		throw new UnsupportedOperationException("Метод checkBankBalanceWithLastBalance не реализован для сервиса "+getSupportedPaymentVersions());
	}

	default void saveBankBalanceHistory(BankBalanceDTO bankBalanceDTO) {
		throw new UnsupportedOperationException("Метод checkBankBalanceWithLastBalance не реализован для сервиса "+getSupportedPaymentVersions());
}

	default void saveBankBalanceHistoryAsync(BankBalanceDTO bankBalanceDTO){
		throw new UnsupportedOperationException("Метод checkBankBalanceWithLastBalance не реализован для сервиса "+getSupportedPaymentVersions());
	}

    /**
     * Проверка заказов за которые были переведены средства на банковский расчетный счет за определенный период
     */
    default BankMoneyTransferDTO checkTransferMoneyByAcquirer() {
		throw new UnsupportedOperationException("Метод checkTransferMoneyByAcquirer не реализован для сервиса "+getSupportedPaymentVersions());
	}

	default void saveBankMoneyTransferred(BankMoneyTransferDTO bankMoneyTransferDTO){
		throw new UnsupportedOperationException("Метод saveBankMoneyTransferred не реализован для сервиса "+getSupportedPaymentVersions());
	}

	default void saveBankMoneyTransferredAsync(BankMoneyTransferDTO bankMoneyTransferDTO){
		throw new UnsupportedOperationException("Метод saveBankMoneyTransferredAsync не реализован для сервиса "+getSupportedPaymentVersions());
	}

	default List<String> getUnsupportedPaymentVersionsInList(List<BankPaymentDTO> bankPaymentDTOList) {
		List<String> supportedVersions = getSupportedPaymentVersions();
		BankPaymentDTO unsupportedBankPayment = bankPaymentDTOList.stream().filter(bp -> !supportedVersions.contains(bp.getPaymentVersion())).findAny().orElse(null);
		if (Objects.isNull(unsupportedBankPayment))
			return Collections.emptyList();
		return Collections.singletonList(unsupportedBankPayment.getPaymentVersion());
	}

	default Boolean isPayoutToBoundCardAllowed() {
		return false;
	}

	default Boolean isPaymentFromBoundCardAllowed(String paymentScheme, String paymentMethod) { // TODO: Move to payments config (db)
		return false;
	}

	default boolean isPrepaymentSupported() { return false; }

	BankPaymentDTO transferMoneyToSeller(@NonNull AgentReport agentReport);

	default List<BankPaymentDTO> checkTransferMoneyToSeller() {
		return Collections.emptyList();
	}

	default void saveCheckTransferMoneyToPaymentAccount(List<BankPaymentDTO> bankPaymentDTOs) {}

	default void saveCheckTransferMoneyToPaymentAccountsAsync(List<BankPaymentDTO> bankPaymentDTOs) {}

	default List<BankOperationDTO> checkBankOperationsInProgress(Long maxQueueSize) {
		// TODO: Get rid of maxQueueSize
		// Put partial validation inside checkBankOperationsInProgress
		// So checkBankOperationsInProgress will call parts of fixd size (20 for example) and will callback master
		// Or just forget about it and move everything to PaymentsSever (much better)
		return Collections.emptyList();
	}

	default BankOperationDTO checkAndProcessBankOperation(String bankOperationId) {
		// TCB Only for the moment
		// others just return null
		return null;
	}

	default List<BankOperationDTO> handleBankOperations(List<BankOperationDTO> bankOperationDTOs) {
		return Collections.emptyList(); // TCB Only for the moment
	}


	default String bindCard(User currentUser, String nullableReturnUrl){
		throw new UnsupportedOperationException("Метод bindCard не реализован для сервиса "+getSupportedPaymentVersions());
	}


	default Long handleBindCard(String bankOperationUUID){
		throw new UnsupportedOperationException("Метод handleBindCard не реализован для сервиса "+getSupportedPaymentVersions());
	}

	default void unbindCard(String cardRefId){
		throw new UnsupportedOperationException("Метод unbindCard не реализован для сервиса "+getSupportedPaymentVersions());
	}

	default void unbindCard(Long counterpartyId){
		throw new UnsupportedOperationException("Метод unbindCard не реализован для сервиса "+getSupportedPaymentVersions());
	}

    default void saveTransferMoneyToSellers(List<BankPaymentDTO> bankPaymentDTOs) {}

	default List<Long> checkExpiringOrders() {
		return Collections.emptyList();
	}

	boolean checkOrderIsFinishedForSeller(Long orderId);

	default void saveTransferMoneyToSellersAsync(List<BankPaymentDTO> bankPaymentDTOs) {}

	/**
	 * Содержимое запроса на оплату
	 * @param p оплачиваемый заказ
	 * @param urlToReturnFromPaymentPage путь (без домена), по которому покупатель может перейти со страницы платежного шлюза обратно в магазин
	 * @param requestDateTime время запроса
	 * @return запрос на оплату
	 */
	OrderService.InitOrderResult getPaymentRequest(Payable p, String urlToReturnFromPaymentPage, ZonedDateTime requestDateTime, boolean fromMobile);

	/**
	 * Обработать входящую транзакцию
	 * @param transactionInfo данные транзакции
	 */
	@Deprecated
	void handleTransactionInfo(String paymentsSchema, String transactionInfo); // TODO move to paymentsService DEVALAN-1732

	/**
	 * Завершить расчет платежа после операции удержания средств на счету клиента (hold)
	 *
	 * @param transactionId   идентификатор оригинальной hold транзакции
	 * @param acquirerOrderId идентификатор внутреннего заказа в экваринге, в зависимости от реализации банка может отсутствовать
	 * @param orderId         идентификатор заказа, который был указан в оригинальной hold транзакции
	 * @param holdAmount      итоговая сумма, которая будет списана со счета клиента
	 */
	boolean completeHold(PaymentCaptureRequest paymentCaptureRequest);

	/**
	 * Отменить оригинальную транзакцию платежа.
	 * @param transactionId идентификатор оригинальной транзакции платежа
	 * @param acquirerOrderId идентификатор внутреннего заказа в экваринге, в зависимости от реализации банка может отсутсвовать
	 * @param orderId идентификатор заказа, который был указан в оригинальном платеже
	 * @param refundAmount сумму, которую нужно вернуть клиенту
	 * @param requestDateTime дата и время запроса
	 */
	void reverse(PaymentReverseRequest paymentReverseRequest);

	default void refund(PaymentRefundsRequest paymentRefundsRequest) {
		throw new UnsupportedOperationException("Метод refund не реализован для сервиса " + getSupportedPaymentVersions());
	}

	default void cancelPayments(PaymentCancelsRequest paymentCancelsRequest) {
		throw new UnsupportedOperationException("Метод refundAmount не реализован для сервиса " + getSupportedPaymentVersions());
	}

	default void refundAmount(PaymentRefundsRequest paymentRefundsRequest) {
		throw new UnsupportedOperationException("Метод refundAmount не реализован для сервиса " + getSupportedPaymentVersions());
	}

	List<String> getSupportedPaymentVersions();

	Set<String> getAllowCurrencies();

	default String validateOperations(LocalDate date) { return null; }

	boolean isSyncOperationsMode();

	default boolean isPayoutInBaseCurrencyOnly() {
		return true;
	}

	default List<BankOperationDTO> getBankOperationsViewList(long orderId) {
		return Collections.emptyList();
	}

	default List<Long> findOrdersByOperationsId(String operationsId) {
		return Collections.emptyList();
	}

	default String getOperationDetails(String scheme, String operationsId) {
		throw new UnsupportedOperationException(String.format("getOperationDetails: null handler (%s)", StringUtils.join(getSupportedPaymentVersions(), ",")));
	}

}