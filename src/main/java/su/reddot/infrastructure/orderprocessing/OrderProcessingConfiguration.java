package su.reddot.infrastructure.orderprocessing;

import java.time.Duration;

import lombok.RequiredArgsConstructor;
import su.reddot.oskelly.orderprocessing.internal.web.ApiClient;
import su.reddot.oskelly.orderprocessing.internal.web.client.IntegrationOrderApi;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;

import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(OrderProcessingProperties.class)
public class OrderProcessingConfiguration {

	private final OrderProcessingProperties properties;

	@Bean
	@Primary
	public IntegrationOrderApi integrationOrderProcessingClient() {
		ApiClient client = new ApiClient();
		client.setBasePath(properties.getBaseUrl());
		return new IntegrationOrderApi(client);
	}

	@Bean
	@Primary
	public OrderMobileApi orderMobileApi(RestTemplateBuilder restTemplateBuilder) {
		ApiClient client = new ApiClient(buildRestTemplate(restTemplateBuilder));
		client.setBasePath(properties.getBaseUrl());
		return new OrderMobileApi(client);
	}

	private RestTemplate buildRestTemplate(RestTemplateBuilder restTemplateBuilder) {
		return restTemplateBuilder
				.setConnectTimeout(Duration.ofMillis(properties.getConnectTimeout()))
				.setReadTimeout(Duration.ofMillis(properties.getReadTimeout()))
				.build();
	}
}
