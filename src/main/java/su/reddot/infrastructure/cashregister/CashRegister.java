package su.reddot.infrastructure.cashregister;

import su.reddot.infrastructure.cashregister.impl.starrys.type.BuyerCheckRequest;

import java.time.ZonedDateTime;

public interface CashRegister {

    String requestCheck(Checkable c, boolean isCorrection, ZonedDateTime dateTimeDocumentCorrection);

    String requestReceipt(BuyerCheckRequest buyerCheckRequest);

    String getQrUrl(String qrCode);

    boolean allowCorrectionCheckAutomation();

    String getCorrectionFromDate();

    String getAutomationBuyerCheckFromDate();
}
