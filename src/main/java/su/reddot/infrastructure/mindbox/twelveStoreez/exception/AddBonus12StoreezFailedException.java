package su.reddot.infrastructure.mindbox.twelveStoreez.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class AddBonus12StoreezFailedException extends RuntimeException{
    private String externalId;

    public AddBonus12StoreezFailedException(String message, String externalId) {
        super(message);
        this.externalId = externalId;
    }
}
