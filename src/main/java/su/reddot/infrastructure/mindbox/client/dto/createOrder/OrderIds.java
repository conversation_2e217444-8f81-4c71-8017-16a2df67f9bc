package su.reddot.infrastructure.mindbox.client.dto.createOrder;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderIds {
    private Long orderIdOrderNumberSeller;
    private Long orderIdOrderNumberBuyer;

    public static OrderIds seller(Long orderId) {
        OrderIds orderIds = new OrderIds();
        orderIds.setOrderIdOrderNumberSeller(orderId);
        return orderIds;
    }

    public static OrderIds buyer(Long orderId) {
        OrderIds orderIds = new OrderIds();
        orderIds.setOrderIdOrderNumberBuyer(orderId);
        return orderIds;
    }
}
