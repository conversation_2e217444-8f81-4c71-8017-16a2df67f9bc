package su.reddot.infrastructure.mindbox.client.dto.createOrder;

import lombok.Data;
import lombok.experimental.Accessors;
import su.reddot.domain.model.order.OrderPosition;

import java.util.List;

@Data
@Accessors(chain = true)
public class Line {
    private Double basePricePerItem;
    private Integer quantity;
    private String quantityType;
    private Integer lineNumber;
    private List<Discount> discounts;
    private Product product;
    private OrderPosition.Status status;

    public Line setProductId(Long productId) {
        this.product = new Product().setIds(new Product.Id().setOskellyProducts(productId));
        return this;
    }

    @Data
    @Accessors(chain = true)
    public static class Product {

        private Id ids;

        @Data
        @Accessors(chain = true)
        public static class Id {
            private Long oskellyProducts;
        }
    }
}
