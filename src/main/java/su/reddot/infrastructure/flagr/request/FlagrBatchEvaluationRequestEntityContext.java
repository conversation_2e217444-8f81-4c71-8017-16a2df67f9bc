package su.reddot.infrastructure.flagr.request;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class FlagrBatchEvaluationRequestEntityContext {
    Long userId;
    String platform;
    String version;
    String guestToken;
}
