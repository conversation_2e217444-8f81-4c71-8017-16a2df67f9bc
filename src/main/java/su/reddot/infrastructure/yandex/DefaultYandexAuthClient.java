package su.reddot.infrastructure.yandex;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import su.reddot.domain.exception.OskellyException;

@Component
@RequiredArgsConstructor
@Slf4j
public class DefaultYandexAuthClient implements YandexAuthClient {

    private final static String GET_USER_JWT_URL = "https://login.yandex.ru/info?format=jwt";
    private final static String REVOKE_ACCESS_TOKEN_URL = "https://oauth.yandex.com/revoke_token";

    private final RestTemplate restTemplate;

    @Value("${app.yandex-auth.client-id}")
    private String clientId;

    @Value("${app.yandex-auth.client-secret}")
    private String clientSecret;

    @Override
    public String getUserJwtByAccessToken(String yandexAccessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        headers.set(HttpHeaders.AUTHORIZATION, "OAuth " + yandexAccessToken);

        log.info("YandexAuthClient: Try to get Yandex user JWT");
        ResponseEntity<String> responseEntity = restTemplate.exchange(GET_USER_JWT_URL, HttpMethod.GET, new HttpEntity<>(headers), String.class);

        String yandexUserJWT = responseEntity.getBody();
        if (!responseEntity.getStatusCode().is2xxSuccessful() || StringUtils.isEmpty(yandexUserJWT)) {
            throw new OskellyException("YandexAuthClient: Unexpected result on getting Yandex user JWT from Yandex for url: %s \nresponse: %s", GET_USER_JWT_URL, responseEntity);
        }
        log.info("YandexAuthClient: Successful getting Yandex user JWT: {}", yandexUserJWT);
        return yandexUserJWT;
    }

    @Override
    public String revokeAccsessToken(String yandexAccessToken) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        String body = "access_token=" + yandexAccessToken + "&" +
                "client_id=" + clientId + "&" +
                "client_secret=" + clientSecret;

        HttpEntity<String> httpEntity = new HttpEntity<>(body, httpHeaders);

        log.info("YandexAuthClient: Try to revoke Yandex access token");
        ResponseEntity<RevokeTokenRequest> responseEntity = restTemplate
                .exchange(REVOKE_ACCESS_TOKEN_URL, HttpMethod.POST, new HttpEntity<>(body, httpHeaders), RevokeTokenRequest.class);

        if (!responseEntity.getStatusCode().is2xxSuccessful()
                || responseEntity.getBody() == null
                || !"ok".equalsIgnoreCase(responseEntity.getBody().getStatus())) {
            throw new OskellyException("YandexAuthClient: Unexpected result on revoking Yandex access token for url: %s \nresponse: %s", REVOKE_ACCESS_TOKEN_URL, responseEntity);
        }

        log.info("YandexAuthClient: Successful revoking Yandex access token");
        return responseEntity.getBody().getStatus();
    }

    @Data
    private static class RevokeTokenRequest {
        private String status;
    }
}
