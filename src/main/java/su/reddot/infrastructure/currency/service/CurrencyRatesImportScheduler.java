package su.reddot.infrastructure.currency.service;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CurrencyRatesImportScheduler {

    private final CurrencyRatesImportService currencyRatesImportService;

    @Scheduled(cron = "${app.integration.currencyrate.updatecron}")
    @Timed(value = "CurrentCurrencyUpdateScheduler.updateCurrencyRate", description = "Updating currency rate scheduler", histogram = true)
    public void updateCurrencyRate() {
        currencyRatesImportService.importRates4Currencies();
    }

}
