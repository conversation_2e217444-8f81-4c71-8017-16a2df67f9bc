package su.reddot.domain.dao.logistic;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import su.reddot.domain.model.logistic.OrderWaybills;
import su.reddot.domain.model.logistic.Waybill;
import su.reddot.domain.model.order.Order;

import java.util.List;

public interface OrderWaybillsRepository extends JpaRepository<OrderWaybills, Long>, QuerydslPredicateExecutor<OrderWaybills> {

    List<OrderWaybills> findAllByOrder(Order order);

    List<OrderWaybills> findAllByWaybill(Waybill waybill);
}
