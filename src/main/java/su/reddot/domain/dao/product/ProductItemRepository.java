package su.reddot.domain.dao.product;

import lombok.AllArgsConstructor;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface ProductItemRepository extends JpaRepository<ProductItem, Long>, QuerydslPredicateExecutor<ProductItem> {

	List<ProductItem> findAllByProduct(Product product);

	List<ProductItem> findAllByProductId(long productId);

	@Query(value = "SELECT pi.product_id, pi.id, pi.size_id, pi.count FROM product_item pi WHERE pi.product_id IN (?1) AND pi.delete_time IS NULL AND pi.count > 0 AND pi.is_hidden = FALSE ORDER BY pi.product_id, pi.id, pi.size_id" , nativeQuery = true)
	List<Object[]> getMinAvailableProductItemRowsForProducts(Collection<Long> productIds);

	@Query(value = "SELECT pi.product_id, pi.id, pi.size_id, pi.count FROM product_item pi WHERE pi.product_id IN (?1) AND pi.size_id IN (?2) AND pi.delete_time IS NULL AND pi.count > 0 AND pi.is_hidden = FALSE ORDER BY pi.product_id, pi.id, pi.size_id" , nativeQuery = true)
	List<Object[]> getMinAvailableProductItemRowsForProducts(Collection<Long> productIds, Collection<Long> interestingSizeIds);

	default Map<Long, List<MinAvailableProductItemRow>> getMinAvailableProductItemsForProducts(Collection<Long> productIds, Collection<Long> interestingSizeIds){
		if(productIds == null || productIds.isEmpty()) return Collections.emptyMap();
		List<Object[]> rows = interestingSizeIds == null || interestingSizeIds.isEmpty() ? getMinAvailableProductItemRowsForProducts(productIds)
				: getMinAvailableProductItemRowsForProducts(productIds, interestingSizeIds);
		Map<Long, List<MinAvailableProductItemRow>> result = new HashMap<>();
		for(Object[] row : rows){
			Long productId = ((BigInteger) row[0]).longValue();
			Long productItemId = ((BigInteger) row[1]).longValue();
			Long sizeId = ((BigInteger) row[2]).longValue();
			Integer count = (Integer) row[3];
			if(result.get(productId) == null) result.put(productId, new ArrayList<>());
			result.get(productId).add(new MinAvailableProductItemRow(productItemId, sizeId, count));
		}
		return result;
	}

	@AllArgsConstructor
	class MinAvailableProductItemRow{
		public Long productItemId;
		public Long sizeId;
		public Integer count;
	}

	@Query(value = "select distinct pi.id, pis.id stockId, pis.location_id stockLocationId, pis.count stockCount " +
			"from product_item pi " +
			"join product p on p.id = pi.product_id " +
			"left join product_item_stock pis on pis.item_id = pi.id " +
			"where p.product_state = 'PUBLISHED' " +
			"and pi.delete_time is null " +
			"and pi.is_hidden=false " +
			"and pi.count > 0 ",
			nativeQuery = true)
	List<UpdatingProductItemStocks.ProductItemProjection> getProductItemWithStocksForUpdating();

	@Query(value = "select distinct pi.id, pis.id stockId, pis.location_id stockLocationId, pis.count stockCount " +
			"from product_item pi " +
			"join product p on p.id = pi.product_id " +
			"join product_item_stock pis on pis.item_id = pi.id " +
			"where pi.id in (:productItemIds) ",
			nativeQuery = true)
	List<UpdatingProductItemStocks.ProductItemProjection> getProductItemWithStocksForUpdating(Set<Long> productItemIds);

	final class UpdatingProductItemStocks {

		private UpdatingProductItemStocks() {
		}

		public interface ProductItemProjection {
			long getId();
			Long getStockId();
			Long getStockLocationId();
			Integer getStockCount();
		}
	}

	@Query(value = "select pi.id, pi.product_id productId, pi.size_id sizeId, pi.is_hidden hidden, pi.count from product_item pi "
			+ "where pi.product_id IN (?1) and pi.delete_time is null" , nativeQuery = true)
	List<SendingProductToExternalCatalog.ProductItemProjection> getProductItemsForSendingToExternalCatalog(Collection<Long> productIds);

	@Query(value = "SELECT pi.id FROM ProductItem pi " +
			"WHERE pi.product.productState = 'PUBLISHED' AND pi.deleteTime IS NULL AND pi.isHidden = FALSE")
	List<Long> findAllIdForFeedExport();

	final class SendingProductToExternalCatalog {

		private SendingProductToExternalCatalog() {
		}

		public interface ProductItemProjection {
			long getId();
			long getProductId();
			long getSizeId();
			boolean isHidden();
			long getCount();
		}
	}
}
