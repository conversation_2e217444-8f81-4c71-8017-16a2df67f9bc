package su.reddot.domain.dao.product;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import su.reddot.domain.model.product.ProductTag;
import su.reddot.domain.model.product.ProductTagCategory;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface ProductTagRepository extends JpaRepository<ProductTag, Long> {

    @Query(value = "from ProductTag t "
            + " where :category is null or t.category = :category")
    List<ProductTag> getProductTags(ProductTagCategory category);

    @Query(value = "select b.product_id, t.id, t.category, t.code, t.short_name, t.name, t.description, t.priority, b.label "
            + " from product_tag_binding b join product_tag t on t.id = b.tag_id"
            + " where b.product_id in (?1)", nativeQuery = true)
    List<Object[]> getProductTagBindings(Collection<Long> productIds);

    Optional<ProductTag> getProductTagByCode(String code);
}