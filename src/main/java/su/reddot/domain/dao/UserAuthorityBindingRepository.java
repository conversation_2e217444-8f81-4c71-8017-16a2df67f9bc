package su.reddot.domain.dao;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import su.reddot.domain.model.Authority;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserAuthorityBinding;

import java.util.Collection;
import java.util.List;

public interface UserAuthorityBindingRepository extends JpaRepository<UserAuthorityBinding, Long>, QuerydslPredicateExecutor<UserAuthorityBinding> {

    @Query("select u.authority from UserAuthorityBinding u where u.user = :user")
    List<Authority> findByUser(@Param("user") User user);

    @Query("select u.authority from UserAuthorityBinding u where u.user.id = :userId")
    List<Authority> findByUserId(@Param("userId") long userId);

    @Query("select u.user from UserAuthorityBinding u where u.authority = :authority")
    List<User> findByAuthority(@Param("authority") Authority authority);

    @Query("select count(u.id) > 0 from UserAuthorityBinding u where u.authority.name = :name and u.user.id <> :excludedUserId")
    boolean existsOtherUserWithRole(AuthorityName name, long excludedUserId);

    UserAuthorityBinding getByUserIdAndAuthorityId(Long userId, Long authorityId);

    @Query(value = "SELECT EXISTS (SELECT 1 FROM user_authority_binding WHERE user_id = :userId)", nativeQuery = true)
    boolean existsByUserId(Long userId);

    List<UserAuthorityBinding> findAllByUserIdIn(Collection<Long> userIds);
}
