package su.reddot.domain.dao.adminalert;

import java.util.List;
import javax.persistence.EntityManager;

import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.transform.Transformers;
import org.hibernate.type.LongType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;

import su.reddot.domain.dao.product.custom.CustomQuerydslRepositorySupport;
import su.reddot.domain.model.adminalert.AdminAlert;
import su.reddot.domain.model.adminalert.QAdminAlert;
import su.reddot.domain.model.logistic.DestinationType;
import su.reddot.domain.service.dto.alert.OrderAlertGroupDTO;
import su.reddot.domain.service.dto.orderprocessing.AdminAlertDTOGroupByDType;

@Repository
public class AdminAlertRepositoryImpl extends CustomQuerydslRepositorySupport {
	public AdminAlertRepositoryImpl(@Autowired @Qualifier("mainEntityManager") EntityManager entityManager) {
		super(AdminAlert.class, entityManager);
	}

	public List<AdminAlertDTOGroupByDType> getAlertsGroupByDType(List<String> dtypes) {
		QAdminAlert qAlert = QAdminAlert.adminAlert;
		BooleanBuilder query = new BooleanBuilder();
		query.and(qAlert.closeTime.isNull());
		if (CollectionUtils.isNotEmpty(dtypes)) {
			query.and(qAlert.dtype.in(dtypes));
		}
		return new JPAQuery<List<AdminAlertDTOGroupByDType>>(getEntityManager())
				.select(Projections.constructor(AdminAlertDTOGroupByDType.class, qAlert.dtype, qAlert.id.count()))
				.from(qAlert)
				.where(query)
				.groupBy(qAlert.dtype)
				.fetch();
	}

	public List<OrderAlertGroupDTO> getAlertCountGroupByOrderId(List<String> dtypes) {
		return getEntityManager().createNativeQuery(
						"select aa.order_id as obj, count(aa.id) as count " +
								"from admin_alert aa " +
								"where aa.close_time is null and aa.order_id is not null and aa.dtype in :dtypes " +
								"group by aa.order_id")
				.setParameter("dtypes", dtypes)
				.unwrap(org.hibernate.query.NativeQuery.class)
				.addScalar("obj", LongType.INSTANCE)
				.addScalar("count", LongType.INSTANCE)
				.setResultTransformer(Transformers.aliasToBean(OrderAlertGroupDTO.class))
				.getResultList();
	}

	public List<OrderAlertGroupDTO> getDeliveryAlerts(List<String> dtypes, DestinationType from, DestinationType to) {
		return getEntityManager().createNativeQuery(
						"select aa.order_id as obj, count(aa.id) as count " +
								"from admin_alert aa " +
								"join \"order\" o on aa.order_id = o.id " +
								"join waybill w on o.id = w.order_id " +
								"where aa.close_time is null and aa.order_id is not null and aa.dtype in :dtypes " +
								"and w.pickup_destination_type=:fromParam and w.delivery_destination_type=:toParam " +
								"group by aa.order_id")
				.setParameter("dtypes", dtypes)
				.setParameter("fromParam", from.name())
				.setParameter("toParam", to.name())
				.unwrap(org.hibernate.query.NativeQuery.class)
				.addScalar("obj", LongType.INSTANCE)
				.addScalar("count", LongType.INSTANCE)
				.setResultTransformer(Transformers.aliasToBean(OrderAlertGroupDTO.class))
				.getResultList();
	}
}
