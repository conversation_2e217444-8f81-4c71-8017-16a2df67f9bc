package su.reddot.domain.dao;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.size.Size;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

public interface SizeRepository extends JpaRepository<Size, Long>, QuerydslPredicateExecutor {

    /**
     * Получить размеры той категории, у которой эти размеры есть
     * и которая находится ниже остальных в иерархии.
     *
     * @return список размеров для категории, которая стоит ниже остальных
     *         переданных категорий.
     */
    @Query("select s from Size s left join s.category c" +
            " where c in ?1 and c.leftOrder = " +
            " (select max(c.leftOrder) from Size s left join s.category c where c in ?1)" +
            " order by s.ordering, s.id")
    List<Size> findSizesSortedByOrdering(List<Category> categories);

    @Query("select s from Size s order by s.ordering, s.id")
    List<Size> findSizesSortedByOrdering();

    //Получит ьактуальные ID размеров
    @Query(value = "SELECT DISTINCT pi.size_id " +
            "FROM product_item pi " +
            "	INNER JOIN product p ON pi.product_id = p.id " +
            "WHERE " +
            "	p.brand_id = :brandId " +
            "	AND p.category_id IN (:leafCategoryIds) AND p.product_state = 'PUBLISHED' " +
            "	AND pi.delete_time IS NULL AND pi.is_hidden = FALSE AND pi.count > 0", nativeQuery = true)
    List<Long> getActualSizeIds(@Nonnull @Param("brandId") Long brandId, @Nonnull @Param("leafCategoryIds") Collection<Long> leafCategoryIds);

    //В отличие от предыдещего метода здесь отсутствует проверка бренда
    @Query(value = "SELECT DISTINCT pi.size_id " +
            "FROM product_item pi " +
            "	INNER JOIN product p ON pi.product_id = p.id " +
            "WHERE " +
            "	p.category_id IN (:leafCategoryIds) AND p.product_state = 'PUBLISHED' " +
            "	AND pi.delete_time IS NULL AND pi.is_hidden = FALSE AND pi.count > 0", nativeQuery = true)
    List<Long> getActualSizeIds(@Nonnull @Param("leafCategoryIds") Collection<Long> leafCategoryIds);
}
