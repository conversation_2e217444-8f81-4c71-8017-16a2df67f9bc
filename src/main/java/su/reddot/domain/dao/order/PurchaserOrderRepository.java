package su.reddot.domain.dao.order;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import su.reddot.domain.dao.order.dto.PurchaserOrderAmountByBrandView;
import su.reddot.domain.dao.order.dto.UserOrderTotalAmountView;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;

import java.util.List;
import java.util.Optional;

@Repository
public interface PurchaserOrderRepository extends JpaRepository<Order, Long> {

    @Query("SELECT o FROM Order o WHERE o.buyer.id =:buyerId OR o.effectiveBuyer.id = :buyerId")
    List<Order> findAllByBuyerId(Long buyerId);

    @Query("SELECT u.id AS userId, " +
            "COALESCE(SUM(COALESCE(op.effectiveAmount, op.amount)), 0) AS orderTotalAmount, " +
            "u.avatarPath AS avatarPath, " +
            "CASE WHEN u.phoneVerifiedTime IS NOT NULL THEN TRUE ELSE FALSE END AS phoneVerified " +
            "FROM User u " +
            "LEFT JOIN Order o ON u.id = COALESCE(o.effectiveBuyer.id, o.buyer.id) AND " +
            "(o.confirmedTime IS NOT NULL OR o.soldTime IS NOT NULL) AND " +
            "o.state NOT IN :exceptedStates " +
            "LEFT JOIN OrderPosition op ON op.order.id = o.id " +
            "WHERE u.id IN :buyerIds " +
            "GROUP BY u.id, u.avatarPath")
    List<UserOrderTotalAmountView> getAllOrdersAmountForBuyer(List<Long> buyerIds, List<OrderState> exceptedStates);

    @Query(value = "SELECT SUM(COALESCE(op.effective_amount, op.amount)) as brandAmount, " +
            "b.id as brandId, " +
            "b.name as brandName " +
            "FROM \"order\" o  " +
            "JOIN order_position op ON o.id = op.order_id " +
            "JOIN product_item pi ON op.product_item_id = pi.id " +
            "JOIN product p ON pi.product_id = p.id " +
            "JOIN brand b ON p.brand_id = b.id " +
            "WHERE COALESCE(o.effective_buyer_id, o.buyer_id) = :buyerId AND " +
            "(o.confirmed_time IS NOT NULL OR o.sold_time IS NOT NULL) AND " +
            "o.state NOT IN (:exceptedStates) " +
            "GROUP BY b.id, b.name " +
            "ORDER BY brandName",
            nativeQuery = true)
    List<PurchaserOrderAmountByBrandView> getAllOrdersAmountByBrandForBuyer(Long buyerId, List<String> exceptedStates);

    @Query("SELECT v.order FROM OrderExtraPropValue v " +
            "WHERE v.orderExtraProp.id = :propId AND v.propValue = :propValue")
    Optional<Order> findByExtraPropValue(@Param("propId") Long propId, @Param("propValue") String propValue);

}
