package su.reddot.domain.dao.delivery;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import su.reddot.domain.model.delivery.DeliveryZone;

import java.util.UUID;

public interface DeliveryZoneRepository extends JpaRepository<DeliveryZone, Long> {

    DeliveryZone findDeliveryZoneByRegionFiasId(UUID regionFiasId);

    @Query("select max(dz.averageDeliveryPeriodDays) from DeliveryZone dz")
    int getMaxDeliveryPeriod();
}