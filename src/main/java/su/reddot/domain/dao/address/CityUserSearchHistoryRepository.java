package su.reddot.domain.dao.address;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import su.reddot.domain.model.address.CityUserSearchHistory;

import java.util.List;
import java.util.Optional;

public interface CityUserSearchHistoryRepository extends JpaRepository<CityUserSearchHistory, Long> {
    List<CityUserSearchHistory> findAllByUserIdAndCityCountryId(long userId, long countryId);
    Optional<CityUserSearchHistory> findCityUserSearchHistoriesByUserIdAndCityId(long userId, long cityId);
    @Modifying
    void deleteAllByUserId(long userId);
}
