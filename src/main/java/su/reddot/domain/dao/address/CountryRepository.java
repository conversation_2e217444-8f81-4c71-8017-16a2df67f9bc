package su.reddot.domain.dao.address;

import org.springframework.data.jpa.repository.JpaRepository;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.address.CountryContextNameEnum;
import su.reddot.infrastructure.util.ProductionEnvironment;

import java.util.List;
import java.util.Optional;

public interface CountryRepository extends JpaRepository<Country, Long> {
    List<Country> findCountriesByContexts_nameIn(List<CountryContextNameEnum> contexts);

    Country findByIsoCodeAlpha2AndContexts_nameIn(String isoCode, List<CountryContextNameEnum> contexts);

    Country findByIsoCodeAlpha2(String isoCode);

    Optional<Country> findCountryByEnvironmentAndEnvironmentDefaultIsTrue(ProductionEnvironment environment);
}
