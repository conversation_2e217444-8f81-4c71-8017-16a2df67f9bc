package su.reddot.domain.dao.bankaccount;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.banktransaction.TransactionState;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface BankPaymentRepository extends JpaRepository<BankPayment, Long>, QuerydslPredicateExecutor<BankPayment> {


	Optional<BankPayment> findByUuid(UUID uuid);

	/**
	 * Найти все транзакции по переводу денег продавцу по списку состояний и схемы платежей
	 * @param states список состояний
	 * @param paymentVersion схема платежей
	 * @return список объектов BankPayment
	 */
	@Query("select bp from BankPayment bp join bp.agentReport r join r.order o" +
			" where bp.state in (:states) and o.paymentVersion = :paymentVersion" +
			" order by bp.createTime desc")
	List<BankPayment> findAllByStateInOrderByCreateTimeDesc(@Param("states") List<TransactionState> states, @Param("paymentVersion") String paymentVersion);
}
