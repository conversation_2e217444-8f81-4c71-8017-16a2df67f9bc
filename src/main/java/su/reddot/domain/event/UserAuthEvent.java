package su.reddot.domain.event;

/*
 * Created by <PERSON>
 * Событие об авторизации пользователя в системе
 */

import lombok.*;
import lombok.experimental.Accessors;
import su.reddot.domain.event.OskellyEvent;
import su.reddot.domain.service.device.DeviceService;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
public class UserAuthEvent extends OskellyEvent {

    private Long userId;
    private LocalDateTime time;
    private DeviceService.DeviceInfo deviceInfo;

    @Override
    public String getUniqueEntityId(){
        return this.getClass().getSimpleName() + "_" + userId;
    }
}