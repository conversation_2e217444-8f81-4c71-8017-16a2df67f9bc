package su.reddot.domain.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SellerConciergePayoutEvent extends OskellyEvent {
    private Long orderId;

    @Override
    public String getUniqueEntityId() {
        return this.getClass().getSimpleName() + "_" + orderId;
    }
}

