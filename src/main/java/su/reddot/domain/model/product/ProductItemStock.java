package su.reddot.domain.model.product;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

@Entity
@Getter
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ProductItemStock {

    public ProductItemStock(ProductItem item, ProductItemLocation location, int count,
            ZonedDateTime dateFrom) {
        this.item = item;
        this.location = location;
        this.count = count;
        this.dateFrom = dateFrom;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "item_id")
    @Audited
    private ProductItem item;

    @NotNull
    @ManyToOne(optional = false)
    @JoinColumn(name = "location_id")
    @Audited(targetAuditMode = NOT_AUDITED)
    private ProductItemLocation location;

    @Setter
    @NotNull
    @Min(0)
    @Audited
    private int count = 0;

    @NotNull
    @Audited
    private ZonedDateTime dateFrom = ZonedDateTime.now();
}