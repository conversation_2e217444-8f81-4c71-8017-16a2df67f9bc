package su.reddot.domain.model.product.publication;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.notification.product.publication.PublicationNotification;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class CompletePublicationNotificationRangeDay extends PublicationNotification {

    protected transient String title;
    protected transient String baseMessage;

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.CompletePublicationNotificationRangeDay.title");
        baseMessage = messageSourceAccessor.getMessage("entity.notification.CompletePublicationNotificationRangeDay.baseMessage");
    }

    @Override
    public String getTargetObjectUrl() {
        return String.format("/product_edit/%s", getProduct().getId());
    }
}
