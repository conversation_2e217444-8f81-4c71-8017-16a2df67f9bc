package su.reddot.domain.model.localization;

import lombok.Getter;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.service.adminpanel.primary.AdminPrimaryDataService;

@Getter
public class AdminBannerTypeLocalized extends AbstractLocalizedEnum<AdminPrimaryDataService.BannerType>{

    private final String title;

    public AdminBannerTypeLocalized(AdminPrimaryDataService.BannerType source, MessageSourceAccessor messageSourceAccessor) {
        super(source, messageSourceAccessor);
        this.title = messageSourceAccessor.getMessage(source.getTitle());
    }

    public boolean isActual() {
        return source.isActual();
    }
}
