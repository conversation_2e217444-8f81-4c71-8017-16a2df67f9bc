package su.reddot.domain.model.notification.osocial;

import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;
import java.util.Locale;
import java.util.Optional;

import static java.lang.String.format;

@Entity
@Accessors(chain = true)
public class OSocialUserMentionedAtPostNotification extends OSocialNotification {
    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.OSocialUserMentionedAtPostNotification.title");
        baseMessage = messageSourceAccessor.getMessage(
                "entity.notification.OSocialUserMentionedAtPostNotification.baseMessage",
                new Object[]{getInitiatorNickname(), Locale.getDefault().getCountry().equals("RU") ? getInitiatorSexPastEnding() : ""});
    }

    @Override
    public Optional<String> getTargetObjectImage() {
        return getPostFirstMediaFilePath();
    }

    @Override
    public String getTargetObjectType() {
        return "OSocialPost";
    }

    @Override
    public Long getTargetObjectId() {
        return getPostId()
                .orElseThrow(() -> new IllegalStateException("Пост не указан"));
    }

    @Override
    public String getTargetObjectUrl() {
        return format("osocial/posts/%s", getTargetObjectId());
    }
}
