package su.reddot.domain.model.notification.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;
import javax.persistence.Transient;

/**
 * Уведомление о незавершенной оплате заказа (отправляется через 30 минут)
 * <AUTHOR> on 20.01.20.
 */
@Entity
@Getter
@Setter
@Accessors(chain = true)
public class LostPaymentNotification extends OrderNotification {

	protected transient String title;
	protected transient String baseMessage;

	@Override
	public String getTinyIcon() {
		return "/imgs/notification/<EMAIL>";
	}

	@Override
	public String getMainIcon() {
		return "/imgs/notification/<EMAIL>";
	}

	@Override
	public String getTargetObjectUrl() {
		return "/cart";
	}

	@Override
	public void localize(MessageSourceAccessor messageSourceAccessor) {
		title = messageSourceAccessor.getMessage("entity.notification.LostPaymentNotification.title");
		baseMessage = messageSourceAccessor.getMessage("entity.notification.LostPaymentNotification.baseMessage");
	}
}
