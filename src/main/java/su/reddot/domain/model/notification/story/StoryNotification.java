package su.reddot.domain.model.notification.story;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import su.reddot.domain.model.notification.Notification;

import javax.persistence.Entity;
import java.util.Map;
import java.util.Optional;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public abstract class StoryNotification extends Notification {

    public static final String SLIDE_ID_PARAM = "slideId";

    public static final String IMAGE_PARAM = "image";

    @Override
    public String getTargetObjectType() {
        return "Slide";
    }

    @Override
    public Long getTargetObjectId() {
        return null;
    }

    @Override
    public String getTargetObjectUrl() {
        return String.format("/stories/slide/%s", extractSlideId());
    }

    @Override
    public Optional<String> getTargetObjectImage() {
        return extractImage();
    }

    protected String extractSlideId() {
        try {
            JsonNode jsonNode = new ObjectMapper().readTree(getParams());
            JsonNode slideIdNode = jsonNode.get(SLIDE_ID_PARAM);
            if (slideIdNode != null && slideIdNode.isTextual()) {
                return slideIdNode.textValue();
            }
        } catch (Exception ex) {
            return "";
        }
        return "";
    }

    protected Optional<String> extractImage() {
        try {
            JsonNode jsonNode = new ObjectMapper().readTree(getParams());
            JsonNode slideIdNode = jsonNode.get(IMAGE_PARAM);
            if (slideIdNode != null && slideIdNode.isTextual()) {
                return Optional.ofNullable(slideIdNode.textValue());
            }
        } catch (Exception ex) {
            return Optional.empty();
        }
        return Optional.empty();
    }
}
