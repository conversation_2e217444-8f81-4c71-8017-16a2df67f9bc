package su.reddot.domain.model.notification.streamsale;

/*
 * Created by <PERSON>
 */

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.streamsale.Stream;
import su.reddot.domain.model.user.userban.UserBan;

import javax.persistence.*;
import java.util.Optional;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class StreamNotification extends Notification {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "stream_id")
    private Stream stream;

    @Override
    public String getTargetObjectType() {
        return Stream.class.getSimpleName();
    }

    @Override
    public Optional<String> getTargetObjectImage() {
        return Optional.empty();
    }

    @Override
    public Long getTargetObjectId() {
        return stream != null ? stream.getId() : null;
    }

    @Override
    public String getTargetObjectUrl() { //TODO уточнить куда ведет
        return stream != null ? String.format("/streamsale/stream?streamId=%d", stream.getId()) : null;
    }

    @Override
    public String getTitle() {
        return stream.getTitle();
    }

    @Override
    public String getBaseMessage() {
        return getTxt();
    }

    @Override
    public Notification setTxt(String txt) {
        return super.setTxt(txt);
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {

    }
}
