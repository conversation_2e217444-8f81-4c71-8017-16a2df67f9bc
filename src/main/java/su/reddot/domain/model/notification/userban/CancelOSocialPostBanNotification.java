package su.reddot.domain.model.notification.userban;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class CancelOSocialPostBanNotification extends BanNotification {

    protected transient String title;
    protected transient String baseMessage;

    @Override
    public String getTinyIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.CancelOSocialPostBanNotification.title");
        baseMessage = messageSourceAccessor.getMessage("entity.notification.CancelOSocialPostBanNotification.baseMessage");
    }
}