package su.reddot.domain.model.notification.productRequest;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class NewProductRequestCommentForUserHowLikedItNotification extends ProductRequestCommentNotification {

    private transient String title;

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.NewProductRequestCommentForUserHowLikedItNotification.title");
        baseMessage = messageSourceAccessor.getMessage("entity.notification.NewProductRequestCommentForUserHowLikedItNotification.baseMessage", new Object[]{productRequestDTO.getCategory().getDisplayName()});
    }

    @Override
    public String getBaseMessage() {
        return baseMessage;
    }
}
