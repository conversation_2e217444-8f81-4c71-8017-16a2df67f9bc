package su.reddot.domain.model.notification.streamsale;

/*
 * Created by <PERSON>
 */

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.notification.Notification;

import javax.persistence.Entity;
import java.util.Optional;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class CanCreateStreamNotification extends Notification {

    protected transient String title;
    protected transient String baseMessage;

    @Override
    public String getTinyIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public Optional<String> getTargetObjectImage() {
        return Optional.empty();
    }

    @Override
    public String getTargetObjectType() {
        return CanCreateStreamNotification.class.getSimpleName();
    }

    @Override
    public Long getTargetObjectId() {
        return user.getId();
    }

    @Override
    public String getTargetObjectUrl() {
        return String.format("/profile/%s", user.getId());
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.CanCreateStreamNotification.title");
        baseMessage = messageSourceAccessor.getMessage("entity.notification.CanCreateStreamNotification.baseMessage");
    }
}
