package su.reddot.domain.model.notification.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.user.User;
import su.reddot.infrastructure.util.Utils;

import javax.persistence.Entity;
import javax.persistence.Transient;
import java.util.Optional;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class OrderExpertiseFailedNotification extends OrderNotification {

    static private String URL_CONTEXT = "BuyerOrderDetails";

    protected transient String title;
    protected transient String baseMessage;

    @Override
    public Optional<User> getInitiator() {
        return Optional.empty();
    }

    @Override
    public String getMainIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public String getTinyIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public String getTargetObjectUrl() {
        return super.getTargetObjectUrl(URL_CONTEXT);
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage(
                "entity.notification.OrderExpertiseFailedNotification.title",
                new Object[]{getOrder().getId().toString()});
        baseMessage = messageSourceAccessor.getMessage("entity.notification.OrderExpertiseFailedNotification.baseMessage");
    }
}