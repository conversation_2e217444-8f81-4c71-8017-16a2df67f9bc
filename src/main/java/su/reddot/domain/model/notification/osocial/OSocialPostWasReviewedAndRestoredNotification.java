package su.reddot.domain.model.notification.osocial;

import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;
import java.util.Optional;

@Entity
@Accessors(chain = true)
public class OSocialPostWasReviewedAndRestoredNotification extends OSocialNotification {

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.OSocialPostWasReviewedAndRestoredNotification.title");
        baseMessage = messageSourceAccessor.getMessage("entity.notification.OSocialPostWasReviewedAndRestoredNotification.baseMessage");
    }

    @Override
    public Optional<String> getTargetObjectImage() {
        return getPostFirstMediaFilePath();
    }

    @Override
    public String getTargetObjectType() {
        return null;
    }

    @Override
    public Long getTargetObjectId() {
        return null;
    }

    @Override
    public String getTargetObjectUrl() {
        return "/notifications";
    }
}
