package su.reddot.domain.model.notification.productRequest;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class NoResponseBuyerProductRequestNotification extends ProductRequestNotification {
    protected transient String title;
    protected transient String baseMessage;

    @Override
    public String getTargetObjectUrl() {
        return String.format("/catalog/productRequestSimilar/%s", productRequestDTO.getId());
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage("entity.notification.NoResponseProductRequestNotification.title", new Object[]{ getCounter() });
        baseMessage = messageSourceAccessor.getMessage("entity.notification.NoResponseProductRequestNotification.baseMessage", new Object[]{productRequestDTO.getCategory().getDisplayName()});
    }
}
