package su.reddot.domain.model.notification.profile;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;

@Entity
@Getter @Setter @Accessors(chain = true)
public class SupportMessageSentNotification extends ProfileNotification {

	protected transient String title;
	protected transient String baseMessage;

	@Override
	public void localize(MessageSourceAccessor messageSourceAccessor) {
		title = messageSourceAccessor.getMessage("entity.notification.SupportMessageNotification.title");
		baseMessage = messageSourceAccessor.getMessage("entity.notification.SupportMessageNotification.baseMessage");
	}

	@Override
	public String getTargetObjectUrl() {
		return "/account/support-chat";
	}

}
