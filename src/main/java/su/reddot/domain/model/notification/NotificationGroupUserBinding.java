package su.reddot.domain.model.notification;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.model.user.User;

import javax.persistence.*;

/**
 * <AUTHOR> on 03.01.20.
 */
@Entity
@Inheritance
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
@ToString
public class NotificationGroupUserBinding {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@ManyToOne
	private NotificationGroup notificationGroup;

	@ManyToOne
	@ToString.Exclude
	private User user;

	public NotificationGroupUserBinding(NotificationGroup notificationGroup, User user) {
		this.notificationGroup = notificationGroup;
		this.user = user;
	}

	public Long getNotificationGroupId() {
		return notificationGroup.getId();
	}
}