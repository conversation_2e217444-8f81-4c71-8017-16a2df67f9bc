package su.reddot.domain.model.notification.product.publication;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;

@Entity
@Getter @Setter @Accessors(chain = true)
public class ModerationPassedNotification extends PublicationNotification {
	protected transient String title;
	protected transient String baseMessage;

	@Override
	public String getTinyIcon() {
		return "/imgs/notification/<EMAIL>";
	}

	@Override
	public String getTargetObjectUrl() {
		return String.format("/products/%s", getTargetObjectId());
	}

	@Override
	public void localize(MessageSourceAccessor messageSourceAccessor) {
		title = messageSourceAccessor.getMessage("entity.notification.ModerationPassedNotification.title");
		baseMessage = messageSourceAccessor.getMessage("entity.notification.ModerationPassedNotification.baseMessage");
	}
}
