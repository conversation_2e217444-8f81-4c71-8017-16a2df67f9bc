package su.reddot.domain.model.notification.order;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.user.User;

import javax.persistence.Entity;
import java.util.Optional;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class SaleExpertisePassedWithDefectNotification extends OrderNotification {

    static private String URL_CONTEXT = "SellerOrderDetails";

    protected transient String title;
    protected transient String baseMessage;

    @Override
    public Optional<User> getInitiator() {
        return Optional.empty();
    }

    @Override
    public String getMainIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public String getTinyIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public String getTargetObjectUrl() {
        return super.getTargetObjectUrl(URL_CONTEXT);
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        title = messageSourceAccessor.getMessage(
                "entity.notification.SaleExpertisePassedWithDefectNotification.title",
                new Object[]{getOrder().getId().toString()});

        if (isBoutiqueOrder()) {
            baseMessage = messageSourceAccessor.getMessage(
                    // TODO: add to strings
                    "entity.notification.SaleExpertisePassedWithDefectNotification.isBoutiqueOrder.baseMessage",
                    new Object[]{getOrder().calcDefectsDiscountAmount().intValue()});
        } else {
            boolean isSingular = getOrder().getPassedPositionsCount() == 1;

            baseMessage = isSingular
                    ? messageSourceAccessor.getMessage("entity.notification.SaleExpertisePassedWithDefectNotification.baseMessage.singular")
                    : messageSourceAccessor.getMessage("entity.notification.SaleExpertisePassedWithDefectNotification.baseMessage.plural");
        }
    }
}
