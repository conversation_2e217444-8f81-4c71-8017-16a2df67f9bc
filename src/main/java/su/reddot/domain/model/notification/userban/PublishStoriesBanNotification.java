package su.reddot.domain.model.notification.userban;

/*
 * Created by <PERSON> on 2/26/2021
 */

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;
import javax.persistence.Transient;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class PublishStoriesBanNotification extends BanNotification {

    protected transient String baseMessage;

    @Override
    public String getTinyIcon() {
        return "/imgs/notification/<EMAIL>";
    }

    @Override
    public String getTitle() {
        return super.getTitle();
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        baseMessage = messageSourceAccessor.getMessage(
                "entity.notification.PublishStoriesBanNotification.baseMessage",
                new Object[]{getUserBan().getDescription()});
    }
}