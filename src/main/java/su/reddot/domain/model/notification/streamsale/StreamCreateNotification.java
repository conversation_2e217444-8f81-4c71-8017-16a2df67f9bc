package su.reddot.domain.model.notification.streamsale;

/*
 * Created by <PERSON>
 */

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.service.dto.notification.MessageFormat;
import su.reddot.infrastructure.util.Utils;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class StreamCreateNotification extends StreamNotification {

    protected transient MessageFormat messageFormat;
    protected transient String baseMessage;

    @Override
    public String getTinyIcon() {
        return getStream().getUser().getAvatarPath();
    }

    @Override
    public String getTitle() {
        return getStream().getUser().getNickname();
    }

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        baseMessage = messageSourceAccessor.getMessage("entity.notification.StreamCreateNotification.baseMessage");
        messageFormat = new MessageFormat(
                messageSourceAccessor.getMessage(
                        "entity.notification.StreamCreateNotification.messageFormat",
                        new Object[]{getStream().getTitle()}
                ),
                Utils.formatToUTC(getStream().getStartingDate())
        );
    }
}
