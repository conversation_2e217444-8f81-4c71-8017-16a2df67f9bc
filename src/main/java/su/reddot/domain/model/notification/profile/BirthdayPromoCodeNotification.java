package su.reddot.domain.model.notification.profile;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.presentation.LocalizationUtils;

import javax.persistence.Entity;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Entity
@Getter @Setter @Accessors(chain = true)
public class BirthdayPromoCodeNotification extends ProfileNotification {

	public static final String PARAM_OSKELLY_PROMOCODE_DISCOUNT = "oskellyPromoCodeDiscount";
	public static final String PARAM_OSKELLY_PROMOCODE_NAME = "oskellyPromoCodeName";
	public static final String PARAM_OSKELLY_PROMOCODE_BEGIN_PRICE = "oskellyPromoCodeBeginPrice";
	public static final String PARAM_CONCIERGE_PROMOCODE_DISCOUNT = "conciergePromoCodeDiscount";
	public static final String PARAM_CONCIERGE_PROMOCODE_NAME = "conciergePromoCodeName";
	public static final String PARAM_CONCIERGE_PROMOCODE_BEGIN_PRICE = "conciergePromoCodeBeginPrice";
	public static final String PARAM_DURATION = "duration";

	protected transient String title;
	protected transient String baseMessage;

	@Override
	public Optional<String> getTargetObjectImage() {
		return Optional.of("/imgs/notification/ic_birthday_promo_code.jpg");
	}

	@Override
	public String getTargetObjectUrl() {
		return "/";
	}

	@Override
	public String getMainIcon() {
		return "/imgs/notification/<EMAIL>";
	}

	@Override
	public void localize(MessageSourceAccessor messageSourceAccessor) {
		List<String> daysNames = Arrays.stream(messageSourceAccessor.getMessage("entity.notification.BirthdayPromoCodeNotification.days").split(":")).collect(Collectors.toList());
		String durationStr = getDuration().orElse("0");
		int daysIndex = LocalizationUtils.getLocalizationForm(Integer.parseInt(durationStr));
		title = messageSourceAccessor.getMessage("entity.notification.BirthdayPromoCodeNotification.title");
		baseMessage = messageSourceAccessor.getMessage(
				"entity.notification.BirthdayPromoCodeNotification.baseMessage",
				new Object[] {
						getOskellyPromoCodeDiscount().orElse(""),
						getOskellyPromoCodeName().orElse(""),
						getOskellyPromoCodeBeginPrice().orElse(""),
						getConciergePromoCodeDiscount().orElse(""),
						getConciergePromoCodeName().orElse(""),
						getConciergePromoCodeBeginPrice().orElse(""),
						durationStr,
						daysNames.get(daysIndex)
				}
		);
	}

	public Optional<String> getOskellyPromoCodeDiscount() {
		return getParamJsonNode(PARAM_OSKELLY_PROMOCODE_DISCOUNT)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}

	public Optional<String> getOskellyPromoCodeName() {
		return getParamJsonNode(PARAM_OSKELLY_PROMOCODE_NAME)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}

	public Optional<String> getOskellyPromoCodeBeginPrice() {
		return getParamJsonNode(PARAM_OSKELLY_PROMOCODE_BEGIN_PRICE)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}

	public Optional<String> getConciergePromoCodeDiscount() {
		return getParamJsonNode(PARAM_CONCIERGE_PROMOCODE_DISCOUNT)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}

	public Optional<String> getConciergePromoCodeName() {
		return getParamJsonNode(PARAM_CONCIERGE_PROMOCODE_NAME)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}

	public Optional<String> getConciergePromoCodeBeginPrice() {
		return getParamJsonNode(PARAM_CONCIERGE_PROMOCODE_BEGIN_PRICE)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}

	public Optional<String> getDuration() {
		return getParamJsonNode(PARAM_DURATION)
				.filter(JsonNode::isTextual)
				.map(JsonNode::textValue);
	}
}
