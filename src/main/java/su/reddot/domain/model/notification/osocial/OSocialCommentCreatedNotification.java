package su.reddot.domain.model.notification.osocial;

import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;
import java.util.Locale;
import java.util.Optional;

import static java.lang.String.format;
import static org.springframework.util.StringUtils.hasText;

@Entity
@Accessors(chain = true)
@Deprecated
public class OSocialCommentCreatedNotification extends OSocialNotification {

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {

        title = messageSourceAccessor.getMessage("entity.notification.OSocialCommentCreatedNotification.title");

        Optional<String> commentTextOpt = getCommentText();
        if (commentTextOpt.isPresent() && hasText(commentTextOpt.get())) {
            baseMessage = messageSourceAccessor.getMessage(
                    "entity.notification.OSocialCommentCreatedNotification.withText.baseMessage",
                    new Object[]{getInitiatorNickname(), commentTextOpt.get()});
        } else {
            baseMessage = messageSourceAccessor.getMessage(
                    "entity.notification.OSocialCommentCreatedNotification.withoutText.baseMessage",
                    new Object[]{getInitiatorNickname(), Locale.getDefault().getCountry().equals("RU") ? getInitiatorSexPastEnding() : ""});
        }
    }

    @Override
    public Optional<String> getTargetObjectImage() {
        return getPostFirstMediaFilePath();
    }

    @Override
    public String getTargetObjectType() {
        return "OSocialPostComment";
    }

    @Override
    public Long getTargetObjectId() {
        return getCommentId()
                .orElseThrow(() -> new IllegalStateException("Комментарий не указан"));
    }

    @Override
    public String getTargetObjectUrl() {
        return format(
                "osocial/posts/%s/comments",
                getPostId().orElseThrow(() -> new IllegalStateException("Пост не указан")));
    }
}
