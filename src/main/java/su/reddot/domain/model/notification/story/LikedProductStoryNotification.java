package su.reddot.domain.model.notification.story;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.support.MessageSourceAccessor;

import javax.persistence.Entity;

@Entity
@Getter
@Setter
@Accessors(chain = true)
public class LikedProductStoryNotification extends ProductStoryNotification {

    protected transient String baseMessage;

    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        baseMessage = messageSourceAccessor.getMessage("entity.notification.LikedProductStoryNotification.baseMessage");
    }

}
