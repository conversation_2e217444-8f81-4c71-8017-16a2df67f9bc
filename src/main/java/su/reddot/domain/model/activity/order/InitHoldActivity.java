package su.reddot.domain.model.activity.order;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.model.order.Order;

import javax.persistence.Entity;
import javax.persistence.Inheritance;

/**
 * <AUTHOR> on 07.05.20.
 */
@Entity
@Inheritance
@Getter
@Setter
@Accessors(chain = true)
@NoArgsConstructor
public class InitHoldActivity extends OrderActivity {

	public InitHoldActivity(Long orderId){
		super(orderId);
	}
}