package su.reddot.domain.model.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

@RequiredArgsConstructor
@Getter
public enum AuthorityName {

	ADMIN("Администратор сайта. Полные права на все действия."),

	PRODUCT_MODERATION("Модерация товаров"),

	USER_MODERATION("Управление пользователями"),

	USERFILE_MODERATION("Управление файлами пользователей"),

	AUTHORITY_MODERATION("Управление правами"),

	CONTENT_CREATE("Публикация информации"),
	CONTENT_DELETE("Удаление опубликованной информации"),

	ORDER_MODERATION("Обслуживание заказов"),

	CAN_VIEW_ALL_PRODUCTS("Просмотр всех товаров, кроме черновиков"),

	OFFER_MODERATION("Модерация торгов"),

	RETOUCHING_MODERATION("Ретуширование"),

	COMMENT_MODERATION("Модерация комментариев"),

	PROMOCODES_MODERATION("Модерация промокодов"), // доступно создание и редактирование своих промокодов
	PROMOCODES_ADMINISTRATION("Администрация промокодов"), // доступно создание и редактирование всех промокодов

	CURRENCY_RATE_MODERATION("Модерация курса валюты"),

	CONTENT_MODERATION("Модерация контента"),

	MASTER_USER("Взаимодействие системных пользователей с главным сервером"),

	STORY_MODERATION("Управление и модерация сторисов"),

	ORDER_RETURNS("Операции возвратов в заказах"),

	ORDER_PAYOUTS("Операции выплаты в заказах"),
	ORDER_CONCIERGE_PAYOUTS("Операции выплаты в заказах консьержа для продавца"),

	STREAM_MODERATION("Управление и модерация эфиров"),

	EXPERTISE("Отображать в отчете экспертизы"),

	ORDER_PREPAYMENTS("Предоплата (списать до экспертизы)"),

	ORDER_RETURN_COMPLETED("Возврат завершенного заказа"),

	ORDER_RESOLVE_DISPUTE("Разрешать спор"),

	ORDER_EXPERTISE_FINISH_ANY_DEFECTS_NEGOTIATION("Завершение всех согласований дефектов"),

	BOUTIQUE_SALES("Продажи Бутика"),

	ORDER_MARKING_CODES("Коды маркировки в заказах"),

	USER_BALANCE_CHANGE("Изменение баланса"),

	PAYOUT_BY_CASH("Выплата наличными"),

	CUSTOM_COMMISSION("Произвольная комиссия"),

	LOGISTIC("Логистика"),

    SALE_REQUEST_MODERATION("Модерация заявок на продажу товаров"),

	PRODUCTS_BULK_EDIT("Товары: массовая модерация"),
	LOGISTIC_SEND_DELIVERY_COMPANY_MAIL_REQUEST("Логистика: Отправить запрос в КС"),
	ORDER_REFUND_AMOUNT("Заказы: произвольный возврат"),
	ORDER_MANUAL_TRANSFER("Заказы: ручное перемещение"),
	SPLIT_ORDER("Разделить заказ"),
	MARK_ORDER_AS_PREPARATION_FOR_PUBLICATION_REQUIRED("Заказы: Требуется фотосъемка"),
	ORDER_VIEW_ALL_ORDER_SOURCES("Заказы: просмотр всех местонахождений"),
	ORDER_BOUTIQUE_0ST_ACTION("Заказы: управление заказами 'Перемещение' + 'Склад'"),
	ORDER_BOUTIQUE_1ST_ACTION("Заказы: управление заказами 'Бутик 1'"),
	ORDER_BOUTIQUE_2ND_ACTION("Заказы: управление заказами 'Бутик 2'"),
	ORDER_VIEW_LIST_BY_LOCATION("Заказы: список заказов в OSKELLY"),
	USER_DELETE("Пользователи: удаление, восстановление"),
	ORDER_MANUAL_CHANGE_DELIVERY_STATE("Заказы: ручное управление логистикой"),
	ORDER_MANUAL_CANCEL("Заказы: отмена до доставки в офис"),
	FAST_AUTO_SHIPMENT_TO_BOUTIQUE("Заказы, бутик: на отгрузку в бутик"),
	ORDER_BOUTIQUE_PACKING_STEP("Заказы, бутик: этап упаковка"),
	TEST_AUTHORITY_00("Тестовое право: разработка"),
	ORDER_SELLER_CONCIERGE_MOVE_TO_BOUTIQUE("Заказы, консьерж для продавца: перемещение в бутик"),
	USER_CHANGE_MAIL("Пользователи: изменить e-mail"),
	COMMISSION_MODERATION("Пользователи: управление комиссионными сетками"),
	GAZPROM_BONUS("Внешние пользователи от программы Газпром Бонус"),
	OSOCIAL_ADMIN("Администратор OSocial"),
	OSOCIAL_MODERATOR("Модератор OSocial"),
    BONUS_MODERATION("Управление бонусным счетом"),
	BONUS_INTEGRATION("Интеграция для работы с бонусным счетом в оффлайн заказах"),
    EXPORT2BITRIX_CROSS_BORDER("Экспорт заказа в Битрикс24 CrossBorder"),
    ADMIN_SHOW_PERSONAL_DATA("Администрирование: просмотр персональных данных"),
    ADMIN_EDIT_PERSONAL_DATA("Администрирование: редактирование персональных данных"),
    CONCIERGE_SALES_ADMIN("Администратор Консьерж Сейлз"),
    SALES("Сейлз"),
    CONCIERGE_SOURCERS_ADMIN("Администратор Консьерж Сорсеры"),
    SOURCER("Сорсер"),
	MERCAUX_ADMIN("Супер администратор Mercaux"),
	STOLESHNIKOV_ADMIN("Администратор Столешников"),
	STOLESHNIKOV_BOUTIQUE_SALESMAN("Продавец бутика Столешников"),
	KUZNETSKY_BRIDGE_ADMIN("Администратор Кузнецкий Мост"),
	KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN("Продавец бутика Кузнецкий Мост"),
	ACCESS_TO_MERKO("Доступ к приложению для сотрудников"),
    SHOW_EMAIL("Пользователи: Просмотр e-mail"),
	USER_SHOW_PHONE_NUMBER("Пользователи: Просмотр номера телефона"),
	USER_CHANGE_PHONE_NUMBER("Пользователи: Редактирование номера телефона"),
	USER_SHOW_SEX("Пользователи: Просмотр пола"),
	USER_CHANGE_SEX("Пользователи: Редактирование пола"),
	USER_SHOW_BIRTHDAY_DATE("Пользователи: Просмотр даты рождения"),
	USER_CHANGE_BIRTHDAY_DATE("Пользователи: Редактирование даты рождения"),
	CREATE_PAYOUT_REQUEST("Администрирование: Создать заявку на резерв денежных средств для заказа КБ"),
	REFUSE_PAYOUT_REQUEST("Администрирование: Отменить заявку на резерв денежных средств для заказа КБ"),
	USER_SHOW_COUNTERPARTY("Пользователи: Просмотр реквизитов"),
	USER_CHANGE_COUNTERPARTY("Пользователи: Редактирование реквизитов"),
	USER_SHOW_ADDRESS("Пользователи: Просмотр адреса"),
	USER_CHANGE_ADDRESS("Пользователи: Редактирование адреса");


	private final String description;

	public static String[] getAllAuthorityNamesAsArray(){
		return Arrays.stream(AuthorityName.values()).map(Enum::name).toArray(String[]::new);
	}

	public static AuthorityName valueOfNullable(String name) {
		if (name == null) {
			return null;
		}

		return Arrays.stream(values()).filter(
				authorityName -> name.equalsIgnoreCase(authorityName.name()))
				.findFirst()
				.orElse(null);
	}

	/**
	 * Список ролей сотрудников
	 */
	public static List<AuthorityName> getEmployeeRoles() {
		return Arrays.asList(
			AuthorityName.STOLESHNIKOV_BOUTIQUE_SALESMAN,
			AuthorityName.STOLESHNIKOV_ADMIN,
			AuthorityName.KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN,
			AuthorityName.KUZNETSKY_BRIDGE_ADMIN,
			AuthorityName.CONCIERGE_SALES_ADMIN,
			AuthorityName.SALES,
			AuthorityName.CONCIERGE_SOURCERS_ADMIN,
			AuthorityName.SOURCER,
			AuthorityName.MERCAUX_ADMIN
		);
	}
}
