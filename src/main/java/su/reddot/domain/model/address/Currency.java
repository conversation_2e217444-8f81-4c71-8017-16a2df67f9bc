package su.reddot.domain.model.address;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
@Getter
@Setter
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
public class Currency {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private String name;

	private String sign;

	private String isoCode;

	private Integer isoNumber;

	private Boolean isBase;

	private Boolean isActive;

	private Boolean selectedByDefault;
}
