package su.reddot.domain.model.report;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

import lombok.Getter;
import lombok.Setter;
import su.reddot.domain.model.order.Order;

@Getter
@Setter
@Entity
public class LogisticsActItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private LogisticsAct logisticsAct;

    @ManyToOne
    private Order order;
}
