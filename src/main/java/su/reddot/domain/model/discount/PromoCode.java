package su.reddot.domain.model.discount;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Formula;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.promocode.model.PromoCodeAppliedNumberType;
import su.reddot.domain.service.promocode.model.PromoCodeResetRange;
import su.reddot.domain.service.promocode.sort.impl.DefaultSort;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Inheritance;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

@Entity
@Audited
@Inheritance
@Getter
@Setter
@Accessors(chain = true)
public abstract class PromoCode {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * Тип промокода. Значение {@link Class#getSimpleName()} наследников
	 */
	@Column(insertable = false, updatable = false)
	private String dtype;

	public String getDtype() {
		return dtype != null ? dtype : getClass().getSimpleName();
	}

	/**
	 * Уникальный код
	 */
	@Nonnull
	private String code;

	@Nullable
	private String description;

	@Nonnull
	private ZonedDateTime createdAt;

	@Nullable
	private ZonedDateTime startsAt;

	/**
	 * Срок действия промо-кода
	 */
	@Nullable
	private ZonedDateTime expiresAt;

	private boolean deleted = false;

	private Long applyRuleId;

	@Nullable
	private BigDecimal beginPrice;

	/**
	 * Промокод с фиксированным числом использований
	 */
	@Nullable
	private Integer numberOfApplies;
	/**
	 * Промокод с фиксированным числом использований для одного покупателя
	 */
    @Column(name = "number_of_applies_type")
	@Nullable
    @Enumerated(EnumType.STRING)
	private PromoCodeAppliedNumberType numberOfAppliesType;

	@Column(name = "reset_range")
	@Nullable
	@Enumerated(EnumType.STRING)
	private PromoCodeResetRange resetRange;

	/**
	 * Поле-метка того, что промокод активен. Необходимо для использования в сортировке по умолчанию в Spring JPA
	 * используется в {@link DefaultSort}
	 */
	@NotAudited
	@Formula("COALESCE(starts_at <= now(), 'TRUE') AND COALESCE(now() < expires_at, 'TRUE')")
	private boolean isActive;

	@ManyToOne
	@Nullable
	@Audited(targetAuditMode = NOT_AUDITED)
	private PromoCodeType type;

	private Boolean barter;

	@ManyToOne
	@JoinColumn(name = "created_by")
	@Nullable
	@Audited(targetAuditMode = NOT_AUDITED)
	private User createdBy;

	/**
	 * Проверить применим ли промокод в указанное время
	 *
	 * @return true если промокод еще применим
	 */
	public boolean isApplicable(@Nonnull ZonedDateTime dateTime) {
		if (this.deleted) {
			// если промокод деактивирован, то он не применим
			return false;
		}

		boolean isAfterStart = true;
		if (this.startsAt != null) {
			isAfterStart = this.startsAt.compareTo(dateTime) <= 0;
		}

		boolean isBeforeEnd = true;
		if (this.expiresAt != null) {
			isBeforeEnd = dateTime.compareTo(this.expiresAt) < 0;
		}

		return isAfterStart && isBeforeEnd;
	}

	/**
	 * Проверить применим ли промокод в текущий момент
	 *
	 * @return true если промокод еще применим
	 */
	public boolean isApplicable() {
		return this.isApplicable(ZonedDateTime.now());
	}

	/**
	 * @return true если промокод уже закончился, или даже не начался
	 */
	public final boolean isExpired() {
		return !this.isApplicable(ZonedDateTime.now());
	}

	/**
	 * Цена заказа с учетом скидки.
	 */
	public abstract BigDecimal getPriceWithDiscount(BigDecimal originalPrice);

	/**
	 * Размер суммы, которую экономит владелец заказа, оплачивая его с помощью скидки.
	 * То есть разница между начальной ценой и ценой со скидкой.
	 */
	public BigDecimal getSavingsValue(BigDecimal originalPrice) {
		return originalPrice.subtract(getPriceWithDiscount(originalPrice));
	}

	/**
	 * Получить значение скидки в денежнем эквиваленте по позиции
	 */
	public abstract BigDecimal getDiscountAmountByPosition(BigDecimal orderOriginalPrice, BigDecimal positionOriginalPrice);

	/**
	 * Значение скидки в читаемом виде.
	 */
	public abstract String getFormattedValue();

    /** Значение скидки в чистом виде. */
    public abstract BigDecimal getValue();

	/**
	 * Применима ли скидка для данной цены.
	 */
	public boolean supports(BigDecimal price) {
		if (this.beginPrice == null) {
			return true;
		}
		return this.beginPrice.compareTo(price) <= 0;
	}
}
