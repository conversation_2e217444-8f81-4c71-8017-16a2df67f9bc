package su.reddot.domain.model.expertise;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import su.reddot.domain.model.logistic.DestinationType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Entity
@Getter
@Setter
@Audited
@Accessors(chain = true)
public class Expertise {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@ManyToOne
	private Order order;

	@ManyToOne
	private OrderPosition orderPosition;

	private ZonedDateTime createTime;

	//Если isApproved == false, значит экспертиза не пройдена
	//Экспертиза считается пойденной в случаях:
	// 1. isApproved == true
	// 2. defectDiscount != null
	// 3. cleaningPrice != null
	private Boolean isApproved;

	/* Скидка за дефекты в процентах по старому */
	private BigDecimal defectDiscount;

	/**
	 * Скидка за дефекты в рублях по новому
	 *
	 * Важно: Нельзя задавать одновременно defectDiscount и defectDiscount. Только чтото одно.
	 */
	private BigDecimal defectDiscountPrice;

	private String defectComment;

	private BigDecimal cleaningPrice;

	/**
	 * Заключение экспертизы. Например:
	 * "Установлено полное несоответствие аналогичной модели бренда SAINT LAURENT".
	 */
	private String conclusion;

	private String rejectionReason;

	/**
	 * Откуда был доставлен товар на экспертизу.
	 */
	@Enumerated(EnumType.STRING)
	private DestinationType pickupFrom;

	public void refresh() {
		isApproved = null;
		defectDiscount = null;
		defectDiscountPrice = null;
		defectComment = null;
		cleaningPrice = null;
		conclusion = null;
		rejectionReason = null;
	}

	public boolean hasCleaning() {
		return cleaningPrice != null;
	}

	public boolean hasDefect() {
		return defectDiscount != null || defectDiscountPrice != null;
	}

	public boolean isFinishedNegative(){
		return isFinished() && !isExpertisePassed();
	}

	public boolean isFinishedPositive(){
		return isFinished() && isExpertisePassed();
	}

	public boolean isExpertisePassed() {
		return (isApproved != null && isApproved) || hasCleaning() || hasDefect();
	}

	public boolean isFinished(){
		return OrderState.isMoneyInProgressOnOurSide(order.getState()) || order.getState() == OrderState.COMPLETED || order.getState() == OrderState.REFUND;
	}
}