package su.reddot.domain.model.order;

import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import su.reddot.infrastructure.logistic.DeliveryState;

import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * Используется для инициализации определенных экранов в админке заказов
 */
@RequiredArgsConstructor
@Getter
public enum OrderStatus {
	UNDEFINED(OrderStatusGroup.ALL, "entity.enum.OrderStatus.UNDEFINED.title"),
	UNCOMPLETED(OrderStatusGroup.UNCOMPLETED, "entity.enum.OrderStatus.UNCOMPLETED.title"),

	/* Оплата */
	ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS(OrderStatusGroup.PAYMENT, "entity.enum.OrderStatus.ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS.title"),
	ORDER_CONFIRMING(OrderStatusGroup.PAYMENT, "entity.enum.OrderStatus.ORDER_CONFIRMING.title"),
	ORDER_REFUND(OrderStatusGroup.PAYMENT, "entity.enum.OrderStatus.ORDER_REFUND.title"),
	ORDER_CONFIRMED(OrderStatusGroup.PAYMENT, "entity.enum.OrderStatus.ORDER_CONFIRMED.title"),
	CONCIERGE_ITEMS_WAITING_CONFIRMATION(OrderStatusGroup.PAYMENT, "entity.enum.OrderStatus.CONCIERGE_ITEMS_WAITING_CONFIRMATION.title"),

	/* Логисты забирают у продавца */
	SELLER_IN_MOSCOW(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.SELLER_IN_MOSCOW.title"),
	EXPECTING_COURIER_TO_SELLER(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.EXPECTING_COURIER_TO_SELLER.title"),
	OURSELVES_PICKING_UP_FROM_SELLER(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.OURSELVES_PICKING_UP_FROM_SELLER.title"),
	OURSELVES_FROM_SELLER_TO_OFFICE(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.OURSELVES_FROM_SELLER_TO_OFFICE.title"),
	LOGIST_ON_WAY_TO_SELLER(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.LOGIST_ON_WAY_TO_SELLER.title"),
	FROM_SELLER_TO_OFFICE(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.FROM_SELLER_TO_OFFICE.title"),
	HAS_CONCIERGE_ITEMS(OrderStatusGroup.FROM_SELLER, "entity.enum.OrderStatus.HAS_CONCIERGE_ITEMS.title"),

	/* Экспертиза */
	EXPERTISE_START(OrderStatusGroup.EXPERTISE, "entity.enum.OrderStatus.EXPERTISE_START.title"),
	EXPERTISE_COMPLETED(OrderStatusGroup.EXPERTISE, "entity.enum.OrderStatus.EXPERTISE_COMPLETED.title"),
	CHOOSING_DELIVERY_METHOD_O2B(OrderStatusGroup.EXPERTISE, "entity.enum.OrderStatus.CHOOSING_DELIVERY_METHOD_O2B.title"),
	HOLD_COMPLETE_REJECTED(OrderStatusGroup.EXPERTISE, "entity.enum.OrderStatus.HOLD_COMPLETE_REJECTED.title"),

	/* Доставка покупателю */
	EXPECTING_COURIER_TO_BUYER(OrderStatusGroup.TO_BUYER, "entity.enum.OrderStatus.EXPECTING_COURIER_TO_BUYER.title"),
	LOGIST_ON_WAY_TO_BUYER(OrderStatusGroup.TO_BUYER, "entity.enum.OrderStatus.LOGIST_ON_WAY_TO_BUYER.title"),
	BUYER_IN_MOSCOW(OrderStatusGroup.TO_BUYER, "entity.enum.OrderStatus.BUYER_IN_MOSCOW.title"),
	OURSELVES_DELIVERY_TO_BUYER(OrderStatusGroup.TO_BUYER, "entity.enum.OrderStatus.OURSELVES_DELIVERY_TO_BUYER.title"),
	OURSELVES_FROM_OFFICE_TO_BUYER(OrderStatusGroup.TO_BUYER, "entity.enum.OrderStatus.OURSELVES_FROM_OFFICE_TO_BUYER.title"),

	/* Выплата продавцам */
	ORDER_DELIVERED(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.ORDER_DELIVERED.title"),
	HAS_DISPUTE(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.HAS_DISPUTE.title"),
	ORDER_IN_BOUTIQUE(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.ORDER_IN_BOUTIQUE.title"),
	ORDER_SOLD_IN_BOUTIQUE(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.ORDER_SOLD_IN_BOUTIQUE.title"),
	EXPECTING_CONFIRM_AGENT_REPORT(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.EXPECTING_CONFIRM_AGENT_REPORT.title"),
	WAIT_PAYMENT_MONEY_TO_SELLER(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.WAIT_PAYMENT_MONEY_TO_SELLER.title"),
	ORDER_COMPLETED(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.ORDER_COMPLETED.title"),
	ORDER_COMPLETED_RETURN(OrderStatusGroup.PAYMENT_TO_SELLER, "entity.enum.OrderStatus.ORDER_COMPLETED_RETURN.title"),

	/* Возврат товара */
	RETURN_CREATED(OrderStatusGroup.RETURN, "entity.enum.OrderStatus.RETURN_CREATED.title"),
	RETURN_ON_WAY_TO_OFFICE(OrderStatusGroup.RETURN, "entity.enum.OrderStatus.RETURN_ON_WAY_TO_OFFICE.title"),
	RETURN_EXPERTISE(OrderStatusGroup.RETURN, "entity.enum.OrderStatus.RETURN_EXPERTISE.title"),
	RETURN_COMPLETED(OrderStatusGroup.RETURN, "entity.enum.OrderStatus.RETURN_COMPLETED.title"),

	BOUTIQUE_ORDER_ON_WAY_TO_OFFICE(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_WAY_TO_OFFICE.title"),
	BOUTIQUE_ORDER_ON_EXPERTISE(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_EXPERTISE.title"),
	BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_ON_WAY_TO_BOUTIQUE.title"),
	BOUTIQUE_ORDER_IN_BOUTIQUE(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_IN_BOUTIQUE.title"),
	BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_SOLD_IN_BOUTIQUE.title"),
	BOUTIQUE_ORDER_ONLINE_CONFIRM(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_ONLINE_CONFIRM.title"),
	BOUTIQUE_ORDER_ONLINE_PICKUP(OrderStatusGroup.BOUTIQUE_ORDERS, "entity.enum.OrderStatus.BOUTIQUE_ORDER_ONLINE_PICKUP.title");

	private final OrderStatusGroup group;

	private final String title;

	@NonNull
	public static OrderStatus getStatus(@NotNull OrderState orderState,
										@NotNull DeliveryState deliveryState,
										boolean isConfirmed,
										Boolean isConfirmedAgentReport,
										boolean isSettedSellerAddressEndpoint,
										boolean isAllExcludedFromAgentReport,
										boolean hasDispute,
										boolean isBoutiqueOrder,
										boolean isSoldInBoutique) {

		if (deliveryState != null) {
			/* Логисты забирают у продавца */
			if (orderState == OrderState.HOLD && deliveryState == DeliveryState.PICKUP_IN_MOSCOW)
				return SELLER_IN_MOSCOW;

			if (orderState == OrderState.HOLD && deliveryState == DeliveryState.JUST_CREATED)
				return EXPECTING_COURIER_TO_SELLER;

			if (orderState == OrderState.HOLD && deliveryState == DeliveryState.OURSELVES_PICKING_UP_FROM_SELLER)
				return OURSELVES_PICKING_UP_FROM_SELLER;

			if (orderState == OrderState.HOLD && deliveryState == DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE)
				return OURSELVES_FROM_SELLER_TO_OFFICE;

			if (orderState == OrderState.HOLD && deliveryState == DeliveryState.PICKING_UP_FROM_SELLER)
				return LOGIST_ON_WAY_TO_SELLER;

			if (orderState == OrderState.HOLD && deliveryState == DeliveryState.FROM_SELLER_TO_OFFICE)
				return FROM_SELLER_TO_OFFICE;


			/* Экспертиза */
			if ((orderState == OrderState.HOLD
					|| orderState == OrderState.HOLD_COMPLETED
					|| orderState == OrderState.MONEY_TRANSFERRED) // HOLD_COMPLETED необходим в случае если произошла ошибка отправки логистами и нужно перевыбрать отправку
					&& deliveryState == DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE)
				return EXPERTISE_START;

			if ((orderState == OrderState.HOLD_COMPLETE_REJECTED)
					&& deliveryState == DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE)
				return HOLD_COMPLETE_REJECTED;


			/* Доставка покупателю */
			if (OrderState.isMoneyInProgressOnOurSide(orderState) && deliveryState == DeliveryState.DELIVERY_IN_MOSCOW)
				return BUYER_IN_MOSCOW;

			if (OrderState.isMoneyInProgressOnOurSide(orderState) && deliveryState == DeliveryState.JUST_CREATED_TO_BUYER)
				return EXPECTING_COURIER_TO_BUYER;

			if (OrderState.isMoneyInProgressOnOurSide(orderState) && Arrays.asList(DeliveryState.FROM_OFFICE_TO_BUYER, DeliveryState.DELIVERY_TODAY_TO_BUYER).contains(deliveryState))
				return LOGIST_ON_WAY_TO_BUYER;

			if (OrderState.isMoneyInProgressOnOurSide(orderState) && deliveryState == DeliveryState.OURSELVES_DELIVERY_TO_BUYER)
				return OURSELVES_DELIVERY_TO_BUYER;

			if (OrderState.isMoneyInProgressOnOurSide(orderState) && Arrays.asList(DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, DeliveryState.OURSELVES_DELIVERY_TODAY_TO_BUYER).contains(deliveryState))
				return OURSELVES_FROM_OFFICE_TO_BUYER;

			if (OrderState.isMoneyTransferedToBankAccount(orderState) && deliveryState == DeliveryState.DELIVERED_TO_BUYER
					&& (isConfirmedAgentReport != null  && !isConfirmedAgentReport))
				return EXPECTING_CONFIRM_AGENT_REPORT;

			if ((OrderState.isMoneyPaymentWaitToSeller(orderState) || orderState == OrderState.MONEY_TRANSFERRED)
					&& deliveryState == DeliveryState.DELIVERED_TO_BUYER
					&& isConfirmedAgentReport != null  && isConfirmedAgentReport)
				return WAIT_PAYMENT_MONEY_TO_SELLER;

			if (OrderState.isMoneyInProgressOnOurSide(orderState) && deliveryState == DeliveryState.DELIVERED_TO_BUYER
					&& isConfirmedAgentReport == null) {
				if (hasDispute) {
					return HAS_DISPUTE;
				}
				if (isBoutiqueOrder) {
					return isSoldInBoutique ? ORDER_SOLD_IN_BOUTIQUE : ORDER_IN_BOUTIQUE;
				}
				return ORDER_DELIVERED;
			}
		}

		/* Оплата */
		if (orderState == OrderState.REFUND) return ORDER_REFUND;
		if (orderState == OrderState.HOLD && !isSettedSellerAddressEndpoint) return ORDER_CANT_CONFIRM_NO_SELLER_ADDRESS;
		if (orderState == OrderState.HOLD && deliveryState == null && isConfirmed) return ORDER_CONFIRMED;
		if (orderState == OrderState.HOLD && deliveryState == null) return ORDER_CONFIRMING;

		/* Возврат */
		if (orderState == OrderState.RETURN && deliveryState == DeliveryState.FROM_BUYER_TO_OFFICE) return RETURN_CREATED;
		if (orderState == OrderState.RETURN && DeliveryState.isReturnOnWayToOffice(deliveryState)) return RETURN_ON_WAY_TO_OFFICE;
		if (orderState == OrderState.RETURN && deliveryState == DeliveryState.DELIVERED_FROM_BUYER_TO_OFFICE) return RETURN_EXPERTISE;
		if (orderState == OrderState.RETURN && deliveryState == DeliveryState.DELIVERED_TO_SELLER) return RETURN_COMPLETED;
		if (orderState == OrderState.RETURN) return RETURN_COMPLETED;


		if (orderState == OrderState.COMPLETED && isAllExcludedFromAgentReport) return ORDER_COMPLETED_RETURN;
		if (orderState == OrderState.COMPLETED) return ORDER_COMPLETED;
		if (orderState == OrderState.SELLER_PAID) return ORDER_COMPLETED;
		if (OrderState.isUncompletedOrder(orderState)) return UNCOMPLETED;

		return UNDEFINED;
	}

	public static Boolean isPickingUpFromSeller(OrderStatus orderStatus) {
		return Arrays.asList(SELLER_IN_MOSCOW,
				EXPECTING_COURIER_TO_SELLER,
				LOGIST_ON_WAY_TO_SELLER,
				OURSELVES_PICKING_UP_FROM_SELLER).contains(orderStatus);
	}

	public static Boolean isFromSellerToOffice(OrderStatus orderStatus) {
		return Arrays.asList(OURSELVES_FROM_SELLER_TO_OFFICE,
				FROM_SELLER_TO_OFFICE).contains(orderStatus);
	}

	public static Boolean isPickingUpFromOffice(OrderStatus orderStatus) {
		return Arrays.asList(BUYER_IN_MOSCOW,
				EXPECTING_COURIER_TO_BUYER,
				OURSELVES_DELIVERY_TO_BUYER).contains(orderStatus);
	}

	public static Boolean isFromOfficeToBuyer(OrderStatus orderStatus) {
		return Arrays.asList(LOGIST_ON_WAY_TO_BUYER, OURSELVES_FROM_OFFICE_TO_BUYER).contains(orderStatus);
	}

	public static Boolean isConfirmationStage(OrderStatus orderStatus) {
		return Arrays.asList(ORDER_CONFIRMING, ORDER_CONFIRMED, SELLER_IN_MOSCOW).contains(orderStatus);
	}
}