package su.reddot.domain.model.order;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum OrderPaymentFailCode {

    OK(null),
    CALL_FAIL(null),
    JUST_FAIL(null),
    PAYMENT_REQUEST_EXPIRED("entity.enum.OrderPaymentFailCode.PAYMENT_REQUEST_EXPIRED"),
    PAYMENT_REQUEST_REJECTS("entity.enum.OrderPaymentFailCode.PAYMENT_REQUEST_REJECTS");

    private final String failText;

}