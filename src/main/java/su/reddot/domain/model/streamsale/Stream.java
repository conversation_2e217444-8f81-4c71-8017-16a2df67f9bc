package su.reddot.domain.model.streamsale;

/*
 * Created by <PERSON>
 */

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.streamsale.enums.StreamCover;
import su.reddot.domain.model.streamsale.enums.StreamStatus;
import su.reddot.domain.model.user.User;

import javax.persistence.*;
import java.time.ZonedDateTime;
import java.util.Set;

@Entity
@Getter
@Setter
@Accessors(chain = true)
@RequiredArgsConstructor
@EqualsAndHashCode
@Table(name = "\"stream\"")
public class Stream {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "create_date")
    @Audited
    private ZonedDateTime createDate;

    @Column(name = "starting_date")
    @Audited
    private ZonedDateTime startingDate;

    @Column(name = "deletion_date")
    @Audited
    private ZonedDateTime deletionDate;

    @Column(name = "stream_status")
    @Enumerated(EnumType.STRING)
    @Audited
    private StreamStatus streamStatus;

    @Column(name = "stream_cover")
    @Enumerated(EnumType.STRING)
    @Audited
    private StreamCover streamCover;

    @Column(name = "title")
    @Audited
    private String title;

    @Column(name = "description")
    @Audited
    private String description;

    @Column(name = "broadcast_id")
    @Audited
    private String broadCastId;

    @Column(name = "resource_uri")
    @Audited
    private String resourceURI;

    @Column(name = "is_ready_send_notification")
    private Boolean isReadySendNotification;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinTable(
            name = "stream_product_binding",
            joinColumns = @JoinColumn(name = "stream_id"),
            inverseJoinColumns = @JoinColumn(name = "product_id"))
    private Set<Product> products;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
}
