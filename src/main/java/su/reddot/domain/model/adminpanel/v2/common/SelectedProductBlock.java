package su.reddot.domain.model.adminpanel.v2.common;

import lombok.*;
import lombok.experimental.Accessors;
import su.reddot.presentation.adminpanel.v2.dto.common.BaseContentBlockDto;
import su.reddot.presentation.adminpanel.v2.dto.common.SelectedProductBlockDto;
import su.reddot.presentation.adminpanel.v2.dto.segment.property.BaseSegmentPropertiesDto;

@ToString(callSuper = true)
@NoArgsConstructor
public class SelectedProductBlock extends GenericTitledDisabledContentBlock<SelectedProductBlock.SelectedProduct, BaseSegmentPropertiesDto> {

    public SelectedProductBlock(SelectedProductBlockDto dto) {
        super(dto, SelectedProduct::new);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @EqualsAndHashCode
    @Accessors(chain = true)
    public static class SelectedProduct {
        private String productId;
        private String description;

        public SelectedProduct(SelectedProductBlockDto.SelectedProductDto dto) {
            if (dto == null) {
                return;
            }
            this.productId = dto.getProductId();
            this.description = dto.getDescription();
        }
    }

    @Override
    public BaseContentBlockDto mapToDto() {
        return new SelectedProductBlockDto(this);
    }
}
