package su.reddot.domain.model.adminpanel.v2;

import lombok.AllArgsConstructor;
import lombok.Getter;
import su.reddot.domain.service.catalog.CategoryService;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

@AllArgsConstructor
@Getter
public enum PrimaryPageTypeCategory {
    MALE(CategoryService.MALE_ROOT_CATEGORY_ID,
            "muzhskoe"),
    FEMALE(CategoryService.FEMALE_ROOT_CATEGORY_ID,
            "zhenskoe"),
    CHILD(CategoryService.CHILDREN_ROOT_CATEGORY_ID,
            "detskoe"),
    LIFESTYLE(CategoryService.LIFESTYLE_ROOT_CATEGORY_ID,
            "lifestyle");

    private final long id;
    private final String url;

    public static Optional<PrimaryPageTypeCategory> of(@Nullable Long baseCategoryId) {
        return Arrays.stream(PrimaryPageTypeCategory.values())
                .filter(it -> Objects.equals(it.getId(), baseCategoryId))
                .findFirst();
    }
}
