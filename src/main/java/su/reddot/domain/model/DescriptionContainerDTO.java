package su.reddot.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import su.reddot.domain.model.category.DescriptionGender;
import su.reddot.domain.model.category.DescriptionNumberType;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DescriptionContainerDTO {
    private String singularMaleValue;
    private String singularFemaleValue;
    private String singularNeutralValue;
    private String pluralValue;

    public String getDescriptionValue(DescriptionGender descriptionGender, DescriptionNumberType numberType) {

        if (null == descriptionGender || null == numberType) {
            return null;
        }

        if (DescriptionNumberType.PLURAL == numberType ||
                DescriptionGender.NOT_DEFINED_PLURAL == descriptionGender) {
            return pluralValue;
        }

        switch (descriptionGender) {
            case MALE:
                return singularMaleValue;
            case FEMALE:
                return singularFemaleValue;
        }

        return singularNeutralValue;
    }
}
