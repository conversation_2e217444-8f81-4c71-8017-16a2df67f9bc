package su.reddot.domain.model.returninfo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.Audited;
import su.reddot.domain.dao.ZonedDateTimeConverter;

import javax.persistence.*;
import java.time.ZonedDateTime;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

@Entity
@Setter @Getter
@Accessors(chain = true)
@Slf4j
public class ReturnReasonPositionBinding {
    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	//Дата создания
	@Convert(converter = ZonedDateTimeConverter.class)
	@Audited
	private ZonedDateTime createTime;

	//Возвращаемая позиция
	@ManyToOne
	@Audited
	private ReturnPosition returnPosition;

	//Причина возврата
	@ManyToOne
	@Audited (targetAuditMode = NOT_AUDITED)
	private ReturnReason returnReason;
}
