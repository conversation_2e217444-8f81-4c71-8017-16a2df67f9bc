package su.reddot.domain.model.returninfo;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
@Setter @Getter
@Slf4j
public class ReturnStateLevel {
    @Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	//Заголовок
	private String title;

	//Позиция сортировки (меньше - выше)
	private int pos;

}
