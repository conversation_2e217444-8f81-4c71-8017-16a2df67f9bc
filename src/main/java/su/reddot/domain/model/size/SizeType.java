package su.reddot.domain.model.size;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
public enum SizeType {
	RU("entity.enum.SizeType.RU.abbreviation", "entity.enum.SizeType.RU.description"),
	EU("entity.enum.SizeType.EU.abbreviation", "entity.enum.SizeType.EU.description"),
	US("entity.enum.SizeType.US.abbreviation", "entity.enum.SizeType.US.description"),
	INT("entity.enum.SizeType.INT.abbreviation", "entity.enum.SizeType.INT.description"),
	UK("entity.enum.SizeType.UK.abbreviation", "entity.enum.SizeType.UK.description"),
	FR("entity.enum.SizeType.FR.abbreviation", "entity.enum.SizeType.FR.description"),
	IT("entity.enum.SizeType.IT.abbreviation", "entity.enum.SizeType.IT.description"),
	DE("entity.enum.SizeType.DE.abbreviation", "entity.enum.SizeType.DE.description"),
	AU("entity.enum.SizeType.AU.abbreviation", "entity.enum.SizeType.AU.description"),
	JPN("entity.enum.SizeType.JPN.abbreviation", "entity.enum.SizeType.JPN.description"),

	INCHES("entity.enum.SizeType.INCHES.abbreviation", "entity.enum.SizeType.INCHES.description"),
	CENTIMETERS("entity.enum.SizeType.CENTIMETERS.abbreviation", "entity.enum.SizeType.CENTIMETERS.description"),

	COLLAR_CENTIMETERS("entity.enum.SizeType.COLLAR_CENTIMETERS.abbreviation", "entity.enum.SizeType.COLLAR_CENTIMETERS.description"),
	COLLAR_INCHES("entity.enum.SizeType.COLLAR_INCHES.abbreviation", "entity.enum.SizeType.COLLAR_INCHES.description"),

	RING_RUSSIAN("entity.enum.SizeType.RING_RUSSIAN.abbreviation", "entity.enum.SizeType.RING_RUSSIAN.description"),
	RING_EUROPEAN("entity.enum.SizeType.RING_EUROPEAN.abbreviation", "entity.enum.SizeType.RING_EUROPEAN.description"),

	JEANS("entity.enum.SizeType.JEANS.abbreviation", "entity.enum.SizeType.JEANS.description"),

	HEIGHT("entity.enum.SizeType.HEIGHT.abbreviation", "entity.enum.SizeType.HEIGHT.description"),
	AGE("entity.enum.SizeType.AGE.abbreviation", "entity.enum.SizeType.AGE.description"),
	NO_SIZE("entity.enum.SizeType.ONE_SIZE.abbreviation", "entity.enum.SizeType.ONE_SIZE.description"),

	BUST("entity.enum.SizeType.BUST.abbreviation", "entity.enum.SizeType.BUST.description");

	@Getter
	private String abbreviation;

	@Getter
	private String description;

	public static SizeType[] valuesRussiaLast() {
		SizeType[] values = SizeType.values();
		SizeType memory = values[0];
		for (int i = 0; i < values.length; i++) {
			if(i != values.length - 1) {
				values[i] = values[i + 1];
			} else {
				values[i] = memory;
			}
		}
		return values;
	}
}
