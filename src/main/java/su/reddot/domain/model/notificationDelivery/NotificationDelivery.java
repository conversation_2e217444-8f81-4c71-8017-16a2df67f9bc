package su.reddot.domain.model.notificationDelivery;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import su.reddot.domain.dao.ZonedDateTimeConverter;
import su.reddot.domain.service.dto.NotificationDeliveryResult;

import javax.persistence.*;
import java.time.ZonedDateTime;

import static org.hibernate.envers.RelationTargetAuditMode.NOT_AUDITED;

@Data
@Entity
@Accessors(chain = true)
public class NotificationDelivery {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	private String channel;

	@Convert(converter = ZonedDateTimeConverter.class)
	@Audited
	private ZonedDateTime createTime;

	@Audited(targetAuditMode = NOT_AUDITED)
	private Long notificationId;

	@Audited
	private Integer countSuccessfulSent = 0;

	@Convert(converter = ZonedDateTimeConverter.class)
	@Audited
	private ZonedDateTime successfulSentTime;

	@Audited
	private Integer countErrorSent = 0;

	@Convert(converter = ZonedDateTimeConverter.class)
	@Audited
	private ZonedDateTime errorSentTime;

	@Audited
	private String metadata;

	private String skipReason;
	private String errorReason;

	public void markAsSent(NotificationDeliveryResult notificationDeliveryResult){
		countSuccessfulSent ++;
		successfulSentTime = notificationDeliveryResult.getTime();
		metadata = notificationDeliveryResult.getMessage();
	}

	public void markAsSkipped(NotificationDeliveryResult notificationDeliveryResult) {
		metadata = notificationDeliveryResult.getMetadata();
		skipReason = notificationDeliveryResult.getMessage();
	}

	public void markAsFailed(NotificationDeliveryResult notificationDeliveryResult){
		countErrorSent ++;
		errorSentTime = notificationDeliveryResult.getTime();
		errorReason = notificationDeliveryResult.getMessage();
		metadata = notificationDeliveryResult.getMetadata();
	}
}
