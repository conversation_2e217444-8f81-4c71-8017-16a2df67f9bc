package su.reddot.domain.model.bargain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.data.querydsl.QSort;
import su.reddot.domain.model.product.QProduct;

@AllArgsConstructor @Getter
public enum BargainSort {
    CREATE_TIME("entity.BargainSort.enum.date-create", new QSort(QBargain.bargain.createTime.asc())),
    CREATE_TIME_DESC("entity.BargainSort.enum.date-create-desc", new QSort(QBargain.bargain.createTime.desc())),
    CHANGE_TIME("entity.BargainSort.enum.date-change", new QSort(QBargain.bargain.changeTime.asc())),
    CHANGE_TIME_DESC("entity.BargainSort.enum.date-change-desc", new QSort(QBargain.bargain.changeTime.desc())),
    LAST_PRICE("entity.BargainSort.enum.last-price", new QSort(QBargain.bargain.lastPrice.asc())),
    LAST_PRICE_DESC("entity.BargainSort.enum.last-price-desc", new QSort(QBargain.bargain.lastPrice.desc()));

    private final String title;
    private final QSort qSort;
}
