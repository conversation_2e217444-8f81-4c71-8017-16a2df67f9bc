package su.reddot.domain.model.primary;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import su.reddot.domain.model.localization.AdminBannerTypeLocalized;
import su.reddot.domain.service.adminpanel.primary.AdminPrimaryDataService;
import su.reddot.domain.service.primary.PrimaryJSONView;

import java.beans.ConstructorProperties;
import java.io.Serializable;
import java.util.List;

public interface PrimaryContent extends Serializable {
    String getId();
    @JsonIgnore
    PrimaryType getPrimaryType();

    enum PrimaryType {
        BANNER ("Баннеры"),
        WORLD_TRENDS ("Мировые тренды"),
        BLOG ("Блог"),
        MEDIA ("Медиа"),
        OSKELLY_CHOICE ("Выбор <PERSON>skelly"),
        BANNER_DIRECT ("Баннера каталог"),
        SET("Подборки"),
        OUR_CHOICE("Выбор OSKELLY - Page"),
        SALE("SALE"),
        LIKED("Любимые бренды"),
        LAST_SEEN("Недавно просмотренные"),
        WISHLIST("Вишлист"),
        IS_AT_OFFICE("На складе ОСКЕЛИ");

        private String title;

        PrimaryType(String title) {
            this.title = title;
        }

        public String getTitle() {
            return title;
        }

    }

    interface Banner extends PrimaryContent {
        AdminPrimaryDataService.BannerType getBannerType();
        void setBannerType(AdminPrimaryDataService.BannerType bannerType);

        AdminBannerTypeLocalized getBannerTypeLocalized();
        void setBannerTypeLocalized(AdminBannerTypeLocalized bannerTypeLocalized);

        String getTitle();
        void setTitle(String title);

        String getSubtitle();
        void setSubtitle(String subtitle);

        String getImageWeb();
        void setImageWeb(String img);

        String getImageMobile();
        void setImageMobile(String img);

        AdminPrimaryDataService.PrimaryCategoryNewResale getBase();
        void setBase(AdminPrimaryDataService.PrimaryCategoryNewResale base);

        List<AdminPrimaryDataService.PrimaryProduct> getProducts();
        void setProducts(List<AdminPrimaryDataService.PrimaryProduct> products);

        Long getProductsTotal();
        void setProductsTotal(Long productsTotal);

        String getNewBannerDeepLink();
        void setNewBannerDeepLink(String newBannerDeepLink);

        default String bannerClassType() {
            return this.getClass().getSimpleName() + " - " + this.getClass().getTypeName();
        }
    }

    @Data
    class BannerCatalogFilters implements Banner {
        PrimaryType primaryType = PrimaryType.BANNER;
        String id = String.valueOf(System.currentTimeMillis());
        AdminPrimaryDataService.BannerType bannerType;

        @JsonProperty(access = JsonProperty.Access.READ_ONLY)
        AdminBannerTypeLocalized bannerTypeLocalized;
        @JsonView(PrimaryJSONView.SHOW_ONLY_REST.class)
        Integer typeId = 1;
        String title;
        String subtitle;
        String imageWeb;
        String imageMobile;
        AdminPrimaryDataService.PrimaryCategoryNewResale base;
        AdminPrimaryDataService.BannerFilter filter;
        List<AdminPrimaryDataService.PrimaryProduct> products;
        Long productsTotal;
        String newBannerDeepLink;

        @ConstructorProperties({"primaryType", "id", "bannerType", "title", "subtitle", "imageWeb", "imageMobile", "base", "filter", "products", "productsTotal" })
        public BannerCatalogFilters(PrimaryType primaryType, String id, AdminPrimaryDataService.BannerType bannerType, String title, String subtitle, String imageWeb, String imageMobile, AdminPrimaryDataService.PrimaryCategoryNewResale base, AdminPrimaryDataService.BannerFilter filter, List<AdminPrimaryDataService.PrimaryProduct> products, Long productsTotal) {
            this.primaryType = primaryType;
            this.id = id;
            this.bannerType = bannerType;
            this.title = title;
            this.subtitle = subtitle;
            this.imageWeb = imageWeb;
            this.imageMobile = imageMobile;
            this.base = base;
            if (filter != null) {
                this.filter = filter;
            } else {
                this.filter = new AdminPrimaryDataService.BannerFilter();
            }
            this.products = products;
            this.productsTotal = productsTotal;
        }

        public BannerCatalogFilters(AdminPrimaryDataService.BannerType bannerType) {
            this.bannerType = bannerType;
        }

    }

    @Data
    class BannerCatalogLinks implements Banner {
        PrimaryType primaryType = PrimaryType.BANNER;
        String id = String.valueOf(System.currentTimeMillis());
        AdminPrimaryDataService.BannerType bannerType;

        @JsonProperty(access = JsonProperty.Access.READ_ONLY)
        AdminBannerTypeLocalized bannerTypeLocalized;
        @JsonView(PrimaryJSONView.SHOW_ONLY_REST.class)
        Integer typeId = 2;
        String title;
        String subtitle;
        String imageWeb;
        String imageMobile;
        AdminPrimaryDataService.PrimaryCategoryNewResale base;
        List<String> productLinks;
        List<Long> productsIds;
        List<AdminPrimaryDataService.PrimaryProduct> products;
        Long productsTotal;
        String newBannerDeepLink;

        public BannerCatalogLinks() {
        }

        @ConstructorProperties({"primaryType", "id", "bannerType", "title", "subtitle", "imageWeb", "imageMobile", "base", "productLinks", "productsIds", "products", "productsTotal" })
        public BannerCatalogLinks(PrimaryType primaryType, String id, AdminPrimaryDataService.BannerType bannerType, String title, String subtitle, String imageWeb, String imageMobile, AdminPrimaryDataService.PrimaryCategoryNewResale base, List<String> productLinks, List<Long> productsIds, List<AdminPrimaryDataService.PrimaryProduct> products, Long productsTotal) {
            this.primaryType = primaryType;
            this.id = id;
            this.bannerType = bannerType;
            this.title = title;
            this.subtitle = subtitle;
            this.imageWeb = imageWeb;
            this.imageMobile = imageMobile;
            this.base = base;
            this.productLinks = productLinks;
            this.productsIds = productsIds;
            this.products = products;
            this.productsTotal = productsTotal;
        }
    }

    @Data
    class BannerUsers implements Banner {
        PrimaryType primaryType = PrimaryType.BANNER;
        String id = String.valueOf(System.currentTimeMillis());
        AdminPrimaryDataService.BannerType bannerType;

        @JsonProperty(access = JsonProperty.Access.READ_ONLY)
        AdminBannerTypeLocalized bannerTypeLocalized;
        @JsonView(PrimaryJSONView.SHOW_ONLY_REST.class)
        Integer typeId = 3;
        String title;
        String subtitle;
        String imageWeb;
        String imageMobile;
        AdminPrimaryDataService.PrimaryCategoryNewResale base;
        AdminPrimaryDataService.PrimaryUser user;
        List<AdminPrimaryDataService.PrimaryProduct> products;
        Long productsTotal;
        String newBannerDeepLink;

        public BannerUsers() { }

        @ConstructorProperties({"primaryType", "id", "bannerType", "title", "subtitle", "imageWeb", "imageMobile", "base", "user", "products", "productsTotal" })
        public BannerUsers(PrimaryType primaryType, String id, AdminPrimaryDataService.BannerType bannerType, String title, String subtitle, String imageWeb, String imageMobile, AdminPrimaryDataService.PrimaryCategoryNewResale base, AdminPrimaryDataService.PrimaryUser user, List<AdminPrimaryDataService.PrimaryProduct> products, Long productsTotal) {
            this.primaryType = primaryType;
            this.bannerType = bannerType;
            this.id = id;
            this.title = title;
            this.subtitle = subtitle;
            this.imageWeb = imageWeb;
            this.imageMobile = imageMobile;
            this.base = base;
            this.user = user;
            this.products = products;
            this.productsTotal = productsTotal;
        }

    }

    @Data @AllArgsConstructor @NoArgsConstructor
    class WorldTrends implements PrimaryContent {
        private String id;
        private String image;
        private AdminPrimaryDataService.PrimaryCategoryNewResale base;           // Категория
        private AdminPrimaryDataService.WorldTrendsFilter filter;
        private Long productsTotal;

        public void setProductsTotal(Long productsTotal) {
            this.productsTotal = productsTotal;
        }

        public PrimaryType getPrimaryType(){
            return PrimaryType.WORLD_TRENDS;
        }
    }

    @Data @AllArgsConstructor @NoArgsConstructor
    class Blog implements PrimaryContent {
        private String id;
        private AdminPrimaryDataService.PrimaryCategoryNewResale base;
        private String image;
        private String title;
        private String link;

        public PrimaryType getPrimaryType(){
            return PrimaryType.BLOG;
        }
    }

    @Data @AllArgsConstructor @NoArgsConstructor
    class Media implements PrimaryContent {
        private String id;
        private String title;
        private String image;
        private String link;
        private String publishDate;

        public PrimaryType getPrimaryType(){
            return PrimaryType.MEDIA;
        }
    }

    @Data @AllArgsConstructor @NoArgsConstructor
    class OskellyChoice implements PrimaryContent {
        private String id;
        private String link;

        public PrimaryType getPrimaryType(){
            return PrimaryType.OSKELLY_CHOICE;
        }
    }

    @Data @AllArgsConstructor @NoArgsConstructor
    class BannerDirect implements PrimaryContent {
        private String id;
        private AdminPrimaryDataService.BannerDirectType bannerType;
        private AdminPrimaryDataService.PrimaryCategoryNewResale base;
        private String image;
        private AdminPrimaryDataService.PrimaryBrand brand;
        private AdminPrimaryDataService.PrimaryCategory category;

        public PrimaryType getPrimaryType(){
            return PrimaryType.BANNER_DIRECT;
        }
    }

    @Data @AllArgsConstructor @NoArgsConstructor
    class PrimarySets implements PrimaryContent {
        private String id;
        private String title;
        private List<String> _links;
        private List<AdminPrimaryDataService.PrimaryProduct> products;

        public void setHashId(String hashId) {
            this.id = hashId;
        }

        @JsonProperty("productsTotal")
        public Integer getProductsTotal() {
            return products.size();
        }

        public PrimaryType getPrimaryType(){
            return PrimaryType.SET;
        }
    }

}
