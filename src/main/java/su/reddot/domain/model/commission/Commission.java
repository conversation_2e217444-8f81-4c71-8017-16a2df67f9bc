package su.reddot.domain.model.commission;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import su.reddot.domain.model.commissiongridhistory.CommissionData;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.SalesChannel;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Сущность комиссии. Представляет собой записи интервалов комиссионных сеток.
 */
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@Entity
@Table(name = "commission")
public class Commission implements Serializable {

    /**
     * ID.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @EqualsAndHashCode.Include
    private Long id;

    @ManyToOne
    private CommissionGrid commissionGrid;

    /**
     * Максимальная публичная (сумма, которую платит покупатель) цена (НЕ включительно).
     */
    @Column(nullable = false)
    private BigDecimal publicPrice;

    /**
     * Комиссия (от 0 до 1).
     */
    @Column(nullable = false)
    private BigDecimal value;

    /**
     * Комиссия для товаров, каналом продаж которых указан бутик и сайт (от 0 до 1).
     * {@link Product#getSalesChannel()}
     */
    @Column(nullable = false)
    private BigDecimal boutiqueValue;

    @Transient
    private transient CommissionData state;

    public Commission(Long id, CommissionGrid commissionGrid, BigDecimal publicPrice, BigDecimal value, BigDecimal boutiqueValue) {
        this.id = id;
        this.commissionGrid = commissionGrid;
        this.publicPrice = publicPrice;
        this.value = value;
        this.boutiqueValue = boutiqueValue;
    }

    /**
     * Получение максимальной приватной (сумма, которую получает продавец) цены (НЕ включительно).
     */
    public BigDecimal getPrivatePrice() {
        return publicPrice.multiply(BigDecimal.ONE.subtract(value));
    }

    /**
     * Входит ли публичная цена (сумма, которую платит покупатель) в комиссионный интервал.
     */
    public boolean isPublicPriceInInterval(BigDecimal publicPrice) {
        return publicPrice.compareTo(this.publicPrice) < 0;
    }

    public BigDecimal getValue(SalesChannel salesChannel) {
        if (salesChannel != null && salesChannel.isBoutiqueCommissionApplied()) {
            return value.add(boutiqueValue);
        }

        return value;
    }
}
