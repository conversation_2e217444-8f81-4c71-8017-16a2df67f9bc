package su.reddot.domain.model.adminalert;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Inheritance;
import javax.persistence.Transient;

import org.hibernate.envers.Audited;
import org.springframework.context.support.MessageSourceAccessor;

import su.reddot.domain.dao.ZonedDateTimeConverter;

/**
 * <AUTHOR> on 28.11.18.
 */
@Entity
@Inheritance
@Getter
@Setter
@Audited
@Accessors(chain = true)
public abstract class AdminAlert {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	protected Long id;

	@Convert(converter = ZonedDateTimeConverter.class)
	protected ZonedDateTime createTime;

	@Convert(converter = ZonedDateTimeConverter.class)
	protected ZonedDateTime closeTime;

	//Нужно для перевода на разные языки сообщения для админа
	@Transient
	protected String message;
	@Transient
	protected Long slaDays;

	public boolean isClosed() {
		return closeTime != null;
	}

	/**
	 * Возвращает ID объекта с которым связан Алерт (продукт, оффер, заказ и т.д.)
	 * @return
	 */
	public abstract Long getObjectId();

	@Column(insertable = false, updatable = false)
	private String dtype;

	/**
	 * Возвращает более подробное описание алерта для администратора.
	 * Такое описание может выводиться в тултипе.
	 * @return
	 */
	public String getDescription() {return null;}

	public abstract void localize(MessageSourceAccessor messageSourceAccessor);

	public long calculateSLADays() {
		return 0L;
	}
}