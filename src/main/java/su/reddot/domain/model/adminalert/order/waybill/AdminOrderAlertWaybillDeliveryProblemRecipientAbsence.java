package su.reddot.domain.model.adminalert.order.waybill;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;

import org.hibernate.envers.Audited;
import org.springframework.context.support.MessageSourceAccessor;

@Entity
@Getter
@Setter
@Audited
@Accessors(chain = true)
public class AdminOrderAlertWaybillDeliveryProblemRecipientAbsence extends AdminOrderAlertWaybillDeliveryProblem {
    @Override
    public void localize(MessageSourceAccessor messageSourceAccessor) {
        message = messageSourceAccessor.getMessage("entity.admin-alert.AdminOrderAlertWaybillDeliveryProblemRecipientAbsence");
    }
}