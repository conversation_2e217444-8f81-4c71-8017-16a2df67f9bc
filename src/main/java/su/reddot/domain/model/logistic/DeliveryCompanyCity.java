package su.reddot.domain.model.logistic;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.model.address.City;

import org.hibernate.envers.Audited;

@Getter
@Setter
@Accessors(chain = true)
@Entity
public class DeliveryCompanyCity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private DeliveryCompany deliveryCompany;

    @ManyToOne
    private City city;
}
