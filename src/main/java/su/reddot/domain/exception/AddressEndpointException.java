package su.reddot.domain.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class AddressEndpointException extends OskellyException {
	private String field;
	public AddressEndpointException(String message, String field){
		super(message);
		this.field = field;
	}
	public AddressEndpointException(String message){
		super(message);
	}
	public Object getData(){
		return field;
	}
}
