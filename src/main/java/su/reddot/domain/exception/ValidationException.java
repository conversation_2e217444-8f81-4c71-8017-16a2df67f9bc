package su.reddot.domain.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.List;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ValidationException extends OskellyException {
	public ValidationException() {
	}

	public ValidationException(String message) {
		super(message);
	}

	public ValidationException(List<String> messages) {
		super(String.join(";", messages));
	}
}
