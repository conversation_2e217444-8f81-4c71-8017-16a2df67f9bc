package su.reddot.domain.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

@Getter
public class HttpStatusCodeException extends ResponseStatusException {

    public HttpStatusCodeException(HttpStatus status) {
        super(status);
    }

    public HttpStatusCodeException(HttpStatus status, String reason) {
        super(status, reason);
    }
}