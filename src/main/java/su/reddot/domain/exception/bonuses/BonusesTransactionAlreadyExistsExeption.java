package su.reddot.domain.exception.bonuses;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.bonuses.OrderBonusesTransaction;

@Getter
@RequiredArgsConstructor
public class BonusesTransactionAlreadyExistsExeption extends OskellyException {
    private final OrderBonusesTransaction orderBonusesTransaction;
}
