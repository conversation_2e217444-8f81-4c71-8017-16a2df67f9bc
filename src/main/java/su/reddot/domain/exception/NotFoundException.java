package su.reddot.domain.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/** Используется в случае, если запрашиваемая сущность не найдена */
@ResponseStatus(HttpStatus.NOT_FOUND)
public class NotFoundException extends OskellyException {
    public NotFoundException() {
    }

    public NotFoundException(String message) {
        super(message);
    }

    public NotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public NotFoundException(Throwable cause) {
        super(cause);
    }
}
