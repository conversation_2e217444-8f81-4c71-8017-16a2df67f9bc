package su.reddot.domain.service.size;

import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.service.dto.SizeValueDTO;
import su.reddot.domain.service.dto.size.SizeTypeDTO;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SizeService {
	void updateCache();
	Size getSizeCached(Long sizeId);
	Size fromId(long sizeId);
	SizeValueDTO getSizeValueDTO(ProductItem productItem, SizeType interestingSizeType);
	SizeValueDTO getSizeValueDTO(Size size, SizeType categorySizeType, SizeType interestingSizeType, SizeType productSizeType);
	SizeValueDTO getSizeValueDTO(Long sizeId, SizeType categorySizeType, SizeType interestingSizeType, SizeType productSizeType, Integer count, Map<Long, Integer> additionalSizeValues);
	SizeValueDTO getSizeValueDTO(Size size, SizeType categorySizeType, SizeType interestingSizeType, SizeType productSizeType, Integer count, Map<Long, Integer> additionalSizeValues);
	SizeValueDTO getSizeValueDTO(Size size, SizeType categorySizeType, SizeType interestingSizeType, SizeType productSizeType, Integer count,
			String productCustomSizeType, String productCustomSizeValue, Map<Long, Integer> additionalSizeValues);
	List<SizeValueDTO> getSizeValueDTOList(List<ProductItem> productItems, Collection<Long> interestingSizes, SizeType interestingSizeType);
	List<SizeValueDTO> getSizeValueDTOList(List<Long> sizeIds, SizeType sizeType);
	List<Size> findAll(List<Long> sizesIds);
	List<Size> findAll();
	List<Size> findSizesSortedByOrdering(List<Category> categories);
	//Выдает актуальные размеры, сгруппированные в список SizeTypeDTO по заданным параметрам
	List<SizeTypeDTO> getActualSizes(Long brandId, @Nonnull Long categoryId);
	//Кэшируемая версия
	List<SizeTypeDTO> getActualSizesCached(Long brandId, @Nonnull Long categoryId);
	//Группирует размеры, упаковывая в список SizeTypeDTO
	List<SizeTypeDTO> cookSizeTypes(List<Size> sizes);
}
