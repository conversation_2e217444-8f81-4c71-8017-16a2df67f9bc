package su.reddot.domain.service.externalcatalog;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import su.reddot.domain.service.ComponentRegistry;

import java.util.ArrayList;
import java.util.List;
import su.reddot.domain.service.externalcatalog.entitylisteners.ExternalStorageSynchronizationObjectListener.ObjectCausingExternalStorageSynchronization;

@Slf4j
public class ProductsToExternalCatalogSendingSynchronization implements TransactionSynchronization {

    private final List<ChangedObject> changedObjects = new ArrayList<>();

    public void addChangedObject(ObjectCausingExternalStorageSynchronization object) {
        changedObjects.add(new ChangedObject(object, false));
    }

    public void addPhysicallyDeletedObject(ObjectCausingExternalStorageSynchronization object) {
        changedObjects.add(new ChangedObject(object, true));    }

    @Override
    public void afterCommit() {
        if (!changedObjects.isEmpty()) {
            List<AbstractProductsToExternalStorageEventuallySender> senders = ComponentRegistry.getProductsToExternalStorageSenderList();

            if (senders != null && !senders.isEmpty()) {
                senders.forEach(c -> c.processChangedObjects(changedObjects));
            }
        }
    }

    public static ProductsToExternalCatalogSendingSynchronization register() {
        return (ProductsToExternalCatalogSendingSynchronization) TransactionSynchronizationManager.getSynchronizations()
                .stream()
                .filter(s -> s instanceof ProductsToExternalCatalogSendingSynchronization)
                .findAny()
                .orElseGet(() -> {
                    ProductsToExternalCatalogSendingSynchronization s = new ProductsToExternalCatalogSendingSynchronization();
                    TransactionSynchronizationManager.registerSynchronization(s);
                    return s;
                });
    }

    @Getter
    @AllArgsConstructor
    public static class ChangedObject {
        private ObjectCausingExternalStorageSynchronization object;
        private boolean physicallyDeleted;
    }
}