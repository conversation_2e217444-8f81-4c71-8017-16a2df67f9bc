package su.reddot.domain.service.externalcatalog;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.external.catalog.model.SortingData;
import su.reddot.domain.service.product.ProductService.SortAttribute;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.common.collect.ImmutableList.of;
import static su.reddot.domain.service.external.catalog.model.SortingData.OrderEnum.ASC;
import static su.reddot.domain.service.external.catalog.model.SortingData.OrderEnum.DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.CHANGE_TIME;
import static su.reddot.domain.service.product.ProductService.SortAttribute.CHANGE_TIME_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.DISCOUNT;
import static su.reddot.domain.service.product.ProductService.SortAttribute.DISCOUNT_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.ID;
import static su.reddot.domain.service.product.ProductService.SortAttribute.ID_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PERSONALIZED;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PRICE;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PRICE_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PRODUCT_STATE_TIME;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PRODUCT_STATE_TIME_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PROMOTION_TIME_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PUBLISH_TIME;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PUBLISH_TIME_DESC;
import static su.reddot.domain.service.product.ProductService.SortAttribute.SCORE_DESC;

@Component
@RequiredArgsConstructor
class ExternalCatalogSortingFactory {

    private final Map<SortAttribute, List<SortingData>> sortingDataMap = new HashMap<>();
    private final List<SortingData> defaultSortingData = of(
                new SortingData().code("publish_time").order(DESC),
                new SortingData().code("id").order(DESC));

    @PostConstruct
    public void init() {
        sortingDataMap.put(ID, of(
                new SortingData().code("id").order(ASC)));
        sortingDataMap.put(ID_DESC, of(
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PRICE, of(
                new SortingData().code("current_price").order(ASC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PRICE_DESC, of(
                new SortingData().code("current_price").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PUBLISH_TIME, of(
                new SortingData().code("publish_time").order(ASC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PUBLISH_TIME_DESC, of(
                new SortingData().code("publish_time").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PROMOTION_TIME_DESC, of(
                new SortingData().code("promotion_or_publish_time").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PERSONALIZED, of( // Задание базовой сортировки для персонализированной выдачи
                new SortingData().code("promotion_or_publish_time").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(PRODUCT_STATE_TIME, of(
                new SortingData().code("state_time").order(ASC),
                new SortingData().code("id").order(ASC)));
        sortingDataMap.put(PRODUCT_STATE_TIME_DESC, of(
                new SortingData().code("state_time").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(CHANGE_TIME, of(
                new SortingData().code("change_time").order(ASC),
                new SortingData().code("id").order(ASC)));
        sortingDataMap.put(CHANGE_TIME_DESC, of(
                new SortingData().code("change_time").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(DISCOUNT_DESC, of(
                new SortingData().code("discount_koef").order(ASC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(DISCOUNT, of(
                new SortingData().code("discount_koef").order(DESC).nullValue(SortingData.NullValueEnum.FIRST),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(IS_OUR_CHOICE_DESC_PUBLISH_TIME_DESC, of(
                new SortingData().code("our_choice").order(DESC),
                new SortingData().code("publish_time").order(DESC),
                new SortingData().code("id").order(DESC)));
        sortingDataMap.put(SCORE_DESC, of(
                new SortingData().code("_score").order(DESC),
                new SortingData().code("custom_score").order(DESC),
                new SortingData().code("promotion_or_publish_time").order(DESC),
                new SortingData().code("id").order(DESC)));
    }

    public List<SortingData> getSortingData(SortAttribute sortAttribute) {

        /* Если у сортируемых строк одно и то же значение атрибута сортировки, то они могут выводиться в произвольном порядке.
         * Их порядок будет различаться в разных запросах. Одна и та же строка может одновременно быть на разных страницах пагинации.
         * Поэтому если у строк совпадает значение атрибута сортировки, дополнительно сортировать их по идентификатору товара в порядке убывания
         * значения идентификатора. Тогда разные запросы будут возвращать строки в одном и том же порядке.*/

        return sortingDataMap.getOrDefault(sortAttribute, defaultSortingData);
    }
}