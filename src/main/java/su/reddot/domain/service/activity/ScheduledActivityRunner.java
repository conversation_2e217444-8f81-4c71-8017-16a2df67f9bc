package su.reddot.domain.service.activity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.service.appsflyer.AppsflyerService;
import su.reddot.domain.service.dto.AppsflyerResultDTO;
import su.reddot.domain.service.master.MasterServiceRequest;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@Setter @Getter
@RequiredArgsConstructor
@Slf4j
public class ScheduledActivityRunner {

	private final AppsflyerService appsflyerService;
	private final ApplicationEventPublisher publisher;

	public void sendActivitiesToAppsflyer(List<Activity> activities){
		UUID uuid = UUID.randomUUID();

		if(activities == null || activities.isEmpty()) return;

		List<Long> activityIds = activities.stream().map(Activity::getId).collect(Collectors.toList());

		log.info("Sending activities to Appsflyer (" + uuid + "): " + activityIds);

		List<AppsflyerResultDTO> activitiesResult = appsflyerService.sendToAppsflyer(activities);

		log.info("Successfully sent activities to Appsflyer (" + uuid + "): " + activitiesResult);

		//Mark activities as sent
		MasterServiceRequest request = new MasterServiceRequest()
				.setMediaType(MediaType.APPLICATION_JSON)
				.setRequestEntityObject(activitiesResult)
				.setUrl("/api/v2/master/activity/markAsSentToAppsflyer");

		publisher.publishEvent(request);
	}

	public void sendActivitiesToAppsflyer(int limit){
		List<Activity> activitiesToSend = appsflyerService.getActivitiesToSendToAppsflyer(limit);
		log.info("Activities to send to AppsFlyer: " + activitiesToSend.size());
		if(!activitiesToSend.isEmpty()) sendActivitiesToAppsflyer(activitiesToSend);
	}

}