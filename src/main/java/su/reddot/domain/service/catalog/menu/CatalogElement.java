package su.reddot.domain.service.catalog.menu;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import su.reddot.domain.service.catalog.menu.impl.ConciergeElement;
import su.reddot.domain.service.catalog.menu.impl.StreamElement;
import su.reddot.domain.service.catalog.menu.impl.banner.BannerElement;
import su.reddot.domain.service.catalog.menu.impl.banner.BannerGridElement;
import su.reddot.domain.service.catalog.menu.impl.banner.BannerMiniElement;
import su.reddot.domain.service.catalog.menu.impl.banner.BannerScrollableRowElement;
import su.reddot.domain.service.catalog.menu.impl.banner.BannerSquareGridElement;
import su.reddot.domain.service.catalog.menu.impl.list.ListElement;
import su.reddot.domain.service.catalog.menu.impl.text.PlainTextElement;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = BannerElement.class, name = BannerElement.TYPE),
        @JsonSubTypes.Type(value = BannerGridElement.class, name = BannerGridElement.TYPE),
        @JsonSubTypes.Type(value = BannerScrollableRowElement.class, name = BannerScrollableRowElement.TYPE),
        @JsonSubTypes.Type(value = BannerMiniElement.class, name = BannerMiniElement.TYPE),
        @JsonSubTypes.Type(value = BannerSquareGridElement.class, name = BannerSquareGridElement.TYPE),
        @JsonSubTypes.Type(value = ListElement.class, name = ListElement.TYPE),
        @JsonSubTypes.Type(value = StreamElement.class, name = StreamElement.TYPE),
        @JsonSubTypes.Type(value = ConciergeElement.class, name = ConciergeElement.TYPE),
        @JsonSubTypes.Type(value = PlainTextElement.class, name = PlainTextElement.TYPE)
})
public interface CatalogElement {

    String getType();

}
