package su.reddot.domain.service.order.track.expertise;

import lombok.Data;
import lombok.experimental.Accessors;
import su.reddot.oskelly.orderprocessing.internal.web.dto.OrderItemExpertiseReconciliationDetailsDefectMatchingStateDTO;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ItemState {
    private Long orderPositionId;
    private String productName;
    private ExpertiseStageType stageType;
    private ExpertiseStageProgress stageProgress;

    private AuthenticityFailedType authenticityFailedType;
    private String authenticityMessage;

    private BigDecimal reconciliationDiscountAmount;
    private int defectsCount;
    private int fieldChangesCount;
    private OrderItemExpertiseReconciliationDetailsDefectMatchingStateDTO changesReconciliationState;

    private Boolean isDestroyed;
}
