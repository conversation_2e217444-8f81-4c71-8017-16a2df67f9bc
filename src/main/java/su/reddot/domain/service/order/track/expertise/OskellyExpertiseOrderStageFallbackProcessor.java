package su.reddot.domain.service.order.track.expertise;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.expertise.Expertise;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.track.OrderPositionTrackingState;
import su.reddot.domain.model.order.track.OrderTrackingState;
import su.reddot.domain.service.currency.CurrencyConverter;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.order.track.OrderStageDTO;
import su.reddot.domain.service.dto.order.track.OrderStageDTO.ProgressState;
import su.reddot.domain.service.dto.order.track.OrderTrackDTO;
import su.reddot.domain.service.dto.order.track.PositionDetailsDTO;
import su.reddot.domain.service.estimation.EventDateEstimationService;
import su.reddot.domain.service.order.impl.OrderRequestContext;
import su.reddot.domain.service.order.track.AbstractOrderStageProcessor;
import su.reddot.domain.service.order.track.positiondetails.PositionDetailsTextBuilder;
import su.reddot.domain.service.product.ProductService;
import su.reddot.infrastructure.logistic.DeliveryState;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

import static su.reddot.domain.service.dto.order.track.OrderStageDTO.SuccessState;
import static su.reddot.domain.service.dto.order.track.OrderStageDTO.Type;

@Slf4j
public class OskellyExpertiseOrderStageFallbackProcessor extends AbstractOrderStageProcessor {
    public static final String MESSAGE_CODE_PREFIX = "service.OrderTrackDTO.OrderStageDTO.OskellyFallbackExpertise";
    public static final String POSITION_TEXT_PART = "positionText";

    private final CurrencyService currencyService;
    private final ProductService productService;

    public OskellyExpertiseOrderStageFallbackProcessor(
        MessageSourceAccessor messageSourceAccessor,
        Order order,
        OrderTrackDTO orderTrack,
        OrderRequestContext context,
        CurrencyService currencyService,
        ProductService productService,
        EventDateEstimationService eventDateEstimationService
    ) {
        super(
            messageSourceAccessor,
            eventDateEstimationService,
            MESSAGE_CODE_PREFIX,
            orderTrack,
            order,
            context
        );

        this.currencyService = currencyService;
        this.productService = productService;
    }

    @Override
    @NonNull
    public OrderStageDTO.Type getOrderStageType() {
        return Type.EXPERTISE;
    }

    @Override
    public OrderStageDTO createOrderStage() {
        return createStage((orderStage) -> {
            InternalTrackingStatus internalStatus = determineInternalStatus(order);
            setDescription(internalStatus, orderStage);
            if (internalStatus == InternalTrackingStatus.EXPERTISE_PASSED_PARTLY) {
                setStepPositionDetails(orderStage);
            }
            setStepResultAndState(internalStatus, orderStage);
            setUpdatedAt(orderStage, order);
            setExpertiseEstimatedDate(order, orderStage);
            return orderStage;
        });
    }

    private void setDescription(InternalTrackingStatus internalStatus, OrderStageDTO orderStage) {
        String description = null;
        switch (internalStatus) {
            case EXPERTISE_PASSED:
                description = getMessage(OrderTrackingState.EXPERTISE_PASSED, DESCRIPTION_PART);
                break;
            case EXPERTISE_FAILED:
                description = getMessage(OrderTrackingState.EXPERTISE_FAILED, DESCRIPTION_PART);
                break;
            case EXPERTISE_IN_PROGRESS:
                description = getMessage(OrderTrackingState.DELIVERED_TO_EXPERTISE, DESCRIPTION_PART);
                break;
        }
        if (description != null) {
            orderStage.setDescription(description);
        }
    }

    private void setStepResultAndState(InternalTrackingStatus internalStatus, OrderStageDTO orderStage) {
        if (internalStatus == InternalTrackingStatus.EXPERTISE_PASSED) {
            orderStage.setProgressState(ProgressState.COMPLETE);
            orderStage.setSuccessState(SuccessState.SUCCEEDED);
        } else if (internalStatus == InternalTrackingStatus.EXPERTISE_FAILED) {
            orderStage.setProgressState(ProgressState.COMPLETE);
            orderStage.setSuccessState(SuccessState.FAILED);
        } else if (internalStatus == InternalTrackingStatus.EXPERTISE_PASSED_PARTLY) {
            orderStage.setProgressState(ProgressState.COMPLETE);
            orderStage.setSuccessState(SuccessState.PARTIALLY_SUCCEEDED);
        } else if (internalStatus == InternalTrackingStatus.EXPERTISE_IN_PROGRESS) {
            orderStage.setProgressState(ProgressState.IN_PROGRESS);
        }
    }

    private void setStepPositionDetails(OrderStageDTO orderStage) {
        CurrencyConverter currencyConverter = context.getCurrencyConverter();
        String currencySign = currencyConverter.getCurrency() != null ? currencyConverter.getCurrency().getSign() : currencyService.getBaseCurrencyCached().getSign();

        List<PositionDetailsDTO> positionsDetails = new ArrayList<>();

        for (Expertise expertise : order.getExpertises()) {
            PositionDetailsDTO details = new PositionDetailsDTO();
            PositionDetailsTextBuilder detailsTextBuilder = new PositionDetailsTextBuilder();
            details.setPositionId(expertise.getOrderPosition().getId());

            String productName = new StringBuilder()
                .append(productService.getProductDisplayName(expertise.getOrderPosition().getProductItem().getProduct()))
                .append(" ")
                .append(expertise.getOrderPosition().getProductItem().getProduct().getBrand().getName())
                .toString();

            if (expertise.isFinishedNegative()) {
                String reasonText = expertise.getRejectionReason();
                detailsTextBuilder.setText(
                    getMessage(OrderPositionTrackingState.EXPERTISE_POSITION_NOT_PASSED, POSITION_TEXT_PART, productName)
                );
                detailsTextBuilder.setReason(reasonText);

            } else if (expertise.isFinishedPositive()) {
                if (!expertise.hasDefect() && !expertise.hasCleaning()) {
                    detailsTextBuilder.setText(
                        getMessage(OrderPositionTrackingState.EXPERTISE_POSITION_PASSED, POSITION_TEXT_PART, productName)
                    );
                }
                if (expertise.hasDefect()) {
                    detailsTextBuilder.setText(
                        getMessage(
                            OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_DEFFECTS, POSITION_TEXT_PART,
                            productName,
                            expertise.getDefectDiscount() == null ? StringUtils.EMPTY : expertise.getDefectDiscount().toString() + " " + currencySign
                        )
                    );
                }
                if (expertise.hasCleaning()) {
                    if (!context.isForBuyer()) {
                        detailsTextBuilder.setText(
                            getMessage(
                                OrderPositionTrackingState.EXPERTISE_POSITION_PASSED_WITH_CLEANING, POSITION_TEXT_PART,
                                productName,
                                expertise.getCleaningPrice() == null ? StringUtils.EMPTY : expertise.getCleaningPrice().toString() + " " + currencySign
                            )
                        );
                    }
                }

                details.setText(detailsTextBuilder.build());
            }

            orderStage.setPositions(positionsDetails);
        }
    }

    private InternalTrackingStatus determineInternalStatus(Order order) {
        boolean expertisePassed = order.getDeliveryState().getRank() >= DeliveryState.JUST_CREATED_TO_BUYER.getRank();
        boolean expertiseFailed = isAllPositionsExamined(order) &&
            order.getExpertises()
                .stream()
                .allMatch(Expertise::isFinishedNegative);

        boolean expertisePassedPartly = isAllPositionsExamined(order) && !expertisePassed && !expertiseFailed;

        if (expertisePassed) {
            return InternalTrackingStatus.EXPERTISE_PASSED;
        } else if (expertiseFailed) {
            return InternalTrackingStatus.EXPERTISE_FAILED;
        } else if (expertisePassedPartly) {
            return InternalTrackingStatus.EXPERTISE_PASSED_PARTLY;
        } else {
            return InternalTrackingStatus.EXPERTISE_IN_PROGRESS;
        }
    }

    private boolean isAllPositionsExamined(Order order) {
        return order.getOrderPositions() != null
            && order.getExpertises() != null
            && order.getOrderPositions().size() == order.getExpertises().size();
    }

    private void setUpdatedAt(OrderStageDTO orderStage, Order order) {
        ZonedDateTime updatedAt;
        Expertise lastExpertise = order.getLastExpertise();
        if (lastExpertise != null) {
            updatedAt = lastExpertise.getCreateTime();
        } else {
            updatedAt = order.getChangeTime();
        }

        orderStage.setUpdatedAt(toOffsetDateTime(updatedAt));
    }

    private enum InternalTrackingStatus {
        EXPERTISE_FAILED,
        EXPERTISE_PASSED,
        EXPERTISE_PASSED_PARTLY,
        EXPERTISE_IN_PROGRESS
    }
}

