package su.reddot.domain.service.subscriptionContact;

import lombok.Data;
import lombok.experimental.Accessors;
import su.reddot.domain.model.user.User;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * Отвечает за подписку пользователей на email-рассылку
 */
public interface SubscriptionContactService {

	/**
	 * Подписывает пользователя/гостя на получение новостей по email
	 * @param request
	 * @return
	 */
	Long subscribe(SubscriptionContactRequest request);

	/**
	 * Подписывает по заранее провалидированным данным пользователя или гостя
	 * @param email
	 * @param name
	 * @param user
	 * @param guestToken
	 * @param sex
	 * @return
	 */
	Long subscribeValidated(String email, String name, User user, String guestToken, User.Sex sex);

	@Data
	@Accessors(chain = true)
	class SubscriptionContactRequest {
		@NotBlank(message = "validators.SubscriptionContactService.EmailNotSpecified")
		@Email(message = "validators.UserService.IncorrectEmail")
		@Size(max = 100, message = "validators.SubscriptionContactService.TooLargeEmail")
		private String emailAddress;
		@Size(max = 100, message = "validators.SubscriptionContactService.TooLargeName")
		private String name;
		private User.Sex sex;
	}

}
