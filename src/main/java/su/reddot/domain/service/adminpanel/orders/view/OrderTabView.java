package su.reddot.domain.service.adminpanel.orders.view;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.model.order.OrderStatusGroup;
import su.reddot.domain.service.adminpanel.view.AdminObjectView;

@Getter
@Setter
@Accessors(chain = true)
public class OrderTabView extends AdminObjectView {
    private OrderStatusGroup tab;
    private long count;
    private String title;
    private String pagePath;
}
