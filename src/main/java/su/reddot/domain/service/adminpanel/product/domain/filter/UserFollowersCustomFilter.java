package su.reddot.domain.service.adminpanel.product.domain.filter;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.QFollowing;
import su.reddot.domain.model.user.QUser;
import su.reddot.domain.service.adminpanel.domain.PredicateRequestIF;
import su.reddot.domain.service.adminpanel.user.domain.AdminV2UsersRequest;

@Component
@RequiredArgsConstructor
public class UserFollowersCustomFilter implements CustomFilterIF {
    public static final String NAME = "userFollowersCustomFilter";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public BooleanBuilder booleanBuilder(PredicateRequestIF queryObject) {
        QFollowing following = QFollowing.following1;
        QUser qUser = QUser.user;

        AdminV2UsersRequest query = (AdminV2UsersRequest) queryObject;

        //Те, кто подписан на пользователя (т.е. его фоловеры)
        BooleanExpression filterExpression = qUser.id.in(
                JPAExpressions.selectDistinct(following.follower.id)
                        .from(following)
                        .where(following.following.id.eq(query.getFollowersOfUserId())));

        return new BooleanBuilder(filterExpression);
    }
}
