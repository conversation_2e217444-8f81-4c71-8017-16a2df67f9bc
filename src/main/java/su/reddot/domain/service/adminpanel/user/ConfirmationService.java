package su.reddot.domain.service.adminpanel.user;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import su.reddot.domain.dao.UserConfirmationDataRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserConfirmationData;
import su.reddot.domain.model.user.UserConfirmationData.ConfirmationStatus;
import su.reddot.domain.model.user.UserConfirmationData.UserDataType;
import su.reddot.domain.model.user.UserConfirmationData.UserDescriptionDetails;
import su.reddot.domain.service.adminpanel.user.confirmation.ConfirmationAction;
import su.reddot.domain.service.adminpanel.user.confirmation.ConfirmationDetails;
import su.reddot.domain.service.adminpanel.user.confirmation.ConfirmationMessageDTO;
import su.reddot.domain.service.adminpanel.user.confirmation.ConfirmationType;
import su.reddot.domain.service.adminpanel.user.confirmation.ConfirmationUserDetails;
import su.reddot.domain.service.adminpanel.user.confirmation.UserDescriptionConfirmationDetails;
import su.reddot.domain.service.dto.AccountDTO;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.configuration.kafka.KafkaConfirmationsConfigVariables;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationActionRequest;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationConfirmedRequest;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationEditedRequest;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationTransferredToUserModerationRequest;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class ConfirmationService {

    private final KafkaSenderService kafkaSenderService;
    private final KafkaConfirmationsConfigVariables kafkaConfigVariables;
    private final ObjectMapper objectMapper;
    private final StaticResourceBalancer staticResourceBalancer;
    private final UserConfirmationDataRepository userConfirmationDataRepository;
    private final List<ConfirmationActionProcessor<? extends ConfirmationDetails>> processors;

    @Value("${app.kafka.confirmationsTopic.enabled:false}")
    private boolean messagingEnabled;

    public void sendConfirmationSilent(ConfirmationMessageDTO messageDTO) {
        if (!messagingEnabled) {
            log.info("Confirmation messaging disabled!");
            return;
        }

        try {
            kafkaSenderService.sendMessageSilent(kafkaConfigVariables.getTopicName(),
                    messageDTO.getUuid().toString(),
                    objectMapper.writeValueAsString(messageDTO));

            log.debug("Confirmation message sent " + messageDTO);
        } catch (Throwable t) {
            log.error("Failed to send confirmation event:" + t.getMessage(), t);
        }
    }

    private void startUserDescriptionConfirmation(User user, String description) {

        UserConfirmationData data = userConfirmationDataRepository.save(
                new UserConfirmationData(
                        user.getId(),
                        UserDataType.DESCRIPTION,
                        new UserDescriptionDetails(description)));

        // TODO: выставляем ConfirmationMessageDTO.moderatorId == ИД пользователя, у которого меняется описание,
        //  (т.е. и по совместительству текущего пользователя). Это нужно в сервисе oskelly-admin при записи в историю,
        //  т.к. moderatorId там not nullable.
        sendConfirmationSilent(
                new ConfirmationMessageDTO(
                    new ConfirmationUserDetails(
                            user.getId(),
                            staticResourceBalancer.getImageFullPath(user.getAvatarPath()),
                            user.getNickname()),
                        user.getId(),
                    ConfirmationType.USER_DESCRIPTION,
                    new UserDescriptionConfirmationDetails(data.getId(), description),
                    ConfirmationAction.EDIT));
    }

    private boolean finishUserDescriptionConfirmationIfNeed(User user) {

        UserConfirmationData data = userConfirmationDataRepository.findFirstByUserIdAndDataTypeOrderByCreatedAtDesc(
                user.getId(),
                UserDataType.DESCRIPTION);
        if (data == null) {
            return false;
        }

        boolean someFinished = false;
        if (data.getConfirmationStatus() == ConfirmationStatus.IN_PROGRESS) {
            sendConfirmationSilent(
                    new ConfirmationMessageDTO(
                            new ConfirmationUserDetails(
                                    user.getId(),
                                    staticResourceBalancer.getImageFullPath(user.getAvatarPath()),
                                    user.getNickname()),
                            user.getId(),
                            ConfirmationType.USER_DESCRIPTION,
                            new UserDescriptionConfirmationDetails(data.getId(), ((UserDescriptionDetails) data.getDetails()).getDescription()),
                            ConfirmationAction.CLOSE));
            createConfirmedConfirmationData(user);
            someFinished = true;
        } else if (data.getConfirmationStatus() == ConfirmationStatus.DECLINED) {
            createConfirmedConfirmationData(user);
            someFinished = true;
        }
        return someFinished;
    }

    private void createConfirmedConfirmationData(User user) {
        userConfirmationDataRepository.save(
                new UserConfirmationData(
                        user.getId(),
                        UserDataType.DESCRIPTION,
                        new UserDescriptionDetails(user.getDescription() != null ? user.getDescription() : ""),
                        ConfirmationStatus.CONFIRMED));
    }

    @Transactional
    public <T extends ConfirmationDetails> void processConfirmed(ConfirmationConfirmedRequest<T> request) {
        getConfirmationProcessor(request).processConfirmed(request);
    }

    @Transactional
    public <T extends ConfirmationDetails> void transferredToUserModeration(
            ConfirmationTransferredToUserModerationRequest<T> request
    ) {
        getConfirmationProcessor(request).processTransferredToUserModeration(request);
    }

    @Transactional
    public <T extends ConfirmationDetails> void processEdited(ConfirmationEditedRequest<T> request) {
        getConfirmationProcessor(request).processConfirmationEditedRequest(request);
    }

    @SuppressWarnings("unchecked")
    private <T extends ConfirmationDetails> ConfirmationActionProcessor<T> getConfirmationProcessor(
            ConfirmationActionRequest<T> request
    ) {
        return (ConfirmationActionProcessor<T>) processors.stream()
                .filter(p -> p.isApplicable(request))
                .findAny()
                .orElseThrow(() -> new IllegalArgumentException("Processor not found for request " + request));
    }

    public void fillConfirmableData(UserDTO dto, User user, User currentUser) {

        dto.setDescription(user.getDescription());
        dto.setDescriptionConfirmationStatus(ConfirmationStatus.CONFIRMED);

        if (currentUser != null && user.getId().equals(currentUser.getId())) {

            // TODO: при появлении необходимости модерации также и других данных (аватара, например)
            //  можно подумать о единственном запросе к БД

            UserConfirmationData data = userConfirmationDataRepository.findFirstByUserIdAndDataTypeOrderByCreatedAtDesc(
                    user.getId(),
                    UserDataType.DESCRIPTION);
            if (data != null && data.getConfirmationStatus() != ConfirmationStatus.CONFIRMED) {
                dto.setDescription(((UserDescriptionDetails) data.getDetails()).getDescription());
                dto.setDescriptionConfirmationStatus(data.getConfirmationStatus());
                dto.setDescriptionDeclinedComment(data.getComment());
            }
        }
    }

    @Transactional
    public void updateConfirmableData(AccountDTO accountDTO, User user) {
        if (accountDTO.getDescription() != null) {

            String userDescription = user.getDescription() != null ? user.getDescription() : "";

            if (!accountDTO.getDescription().equals(userDescription)) {
                if (StringUtils.hasText(accountDTO.getDescription())) {
                    startUserDescriptionConfirmation(user, accountDTO.getDescription());
                } else {
                    // если новое описание - пустая строка, обновляем без модерации и на всякий случай подчищаем данные по модерации
                    user.setDescription("");
                    if (!finishUserDescriptionConfirmationIfNeed(user)) {
                        createConfirmedConfirmationData(user);
                    }
                }
            } else {
                // если новое описание совпадает с актуальным, на всякий случай подчищаем данные по модерации
                // (пользователь задал новое описание, а затем до реагирования на изменение админом передумал и вернул)
                finishUserDescriptionConfirmationIfNeed(user);
            }
        }
    }
}
