package su.reddot.domain.service.adminpanel.user.counter.sql;

import org.springframework.stereotype.Service;
import su.reddot.domain.dao.counter.ProductCommentsUpdaterUserCounterRepository;
import su.reddot.domain.dao.counter.UpdateUserCounterRepository;
import su.reddot.domain.service.adminpanel.user.UserCounterTypeService;

@Service
public class ProductCommentUserCounterUpdater extends AbstractSQLCounterUpdater {
    private static final String COUNTER_CODE = "PRODUCT_COMMENT";
    private final ProductCommentsUpdaterUserCounterRepository userCounterRepository;

    public ProductCommentUserCounterUpdater(UserCounterTypeService counterTypeService, ProductCommentsUpdaterUserCounterRepository userCounterRepository) {
        super(counterTypeService);
        this.userCounterRepository = userCounterRepository;
    }

    @Override
    protected UpdateUserCounterRepository getUpdateRepository() {
        return userCounterRepository;
    }

    @Override
    public String getCounterCode() {
        return COUNTER_CODE;
    }
}
