package su.reddot.domain.service.adminpanel.orders.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Nonnull;

import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.service.adminpanel.orders.view.AdminAddressView;

import org.springframework.stereotype.Component;

@Component
public class AdminAddressViewMapper {

    @Nonnull
    public List<AdminAddressView> mapAddressEndpoints(@Nonnull final List<AddressEndpoint> addressEndpoints) {
        return addressEndpoints.stream()
                .map(AdminAddressViewMapper::map)
                .collect(Collectors.toList());
    }

    @Nonnull
    private static AdminAddressView map(@Nonnull final AddressEndpoint addressEndpoint) {
        final Address address = addressEndpoint.getAddress();
        return new AdminAddressView(
                addressEndpoint.getId(),
                addressEndpoint.getFullName(),
                addressEndpoint.getPhone(),
                address.getAdminFullAddress(),
                address.isFullyValidated());
    }
}
