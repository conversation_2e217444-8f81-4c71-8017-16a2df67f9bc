package su.reddot.domain.service.adminpanel.product.domain.filter;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.user.QUser;
import su.reddot.domain.model.user.QUserAuthorityBinding;
import su.reddot.domain.service.adminpanel.domain.PredicateRequestIF;
import su.reddot.domain.service.adminpanel.user.domain.AdminV2UsersRequest;

@Component
public class AnyAuthorityCustomFilter implements CustomFilterIF {
    public static final String NAME = "anyAuthorityCustomFilter";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public BooleanBuilder booleanBuilder(PredicateRequestIF queryObject) {
        if (queryObject.getClass() != AdminV2UsersRequest.class) {
            return new BooleanBuilder();
        }
        AdminV2UsersRequest query = (AdminV2UsersRequest) queryObject;

        QUser user = QUser.user;
        QUserAuthorityBinding authorityBinding = QUserAuthorityBinding.userAuthorityBinding;

        JPQLQuery allBindingUserIds = JPAExpressions.select(authorityBinding.user.id)
                .from(authorityBinding)
                .distinct();

        BooleanExpression filterExpression = query.getAnyAuthority() ?
                user.id.in(allBindingUserIds) :
                user.id.notIn(allBindingUserIds);

        return new BooleanBuilder(filterExpression);
    }
}
