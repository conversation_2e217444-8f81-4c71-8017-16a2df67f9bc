package su.reddot.domain.service.adminpanel.product;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.commission.CommissionRepository;
import su.reddot.domain.model.commission.Commission;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.exception.AdministrationException;
import su.reddot.domain.service.boutique.BoutiqueService;
import su.reddot.domain.service.commission.CommissionService;
import su.reddot.domain.service.commission.Price;
import su.reddot.domain.service.dto.duty.DutyDTO;
import su.reddot.domain.service.duty.DutyService;
import su.reddot.domain.service.productpublication.Conversion;
import su.reddot.domain.service.productpublication.ProductPublicationService;

import java.math.BigDecimal;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class AdminProductPriceServiceImpl {

    private final DutyService dutyService;
    private final CommissionService commissionService;
    private final BoutiqueService boutiqueService;
    private final MessageSourceAccessor messageSourceAccessor;
    private final ProductPublicationService productPublicationService;
    private final CommissionRepository commissionRepository;

    @Transactional
    public void setPricesAndCommissions(Product product, BigDecimal currentPrice, BigDecimal sellerPrice,
                                        boolean isCustomCommission, SalesChannel salesChannel) {
        //todo этот метод используется в нескольких местах. Подойдет такая правка или сделать ее только для админки?
        if (currentPrice == null || BigDecimal.ZERO.compareTo(currentPrice) >= 0) {
            throw new AdministrationException(
                    messageSourceAccessor.getMessage("exception.AdministrationException.current-price-must-not-be-null"));
        }
        if (isCustomCommission && sellerPrice == null) {
            throw new AdministrationException(
                    messageSourceAccessor.getMessage("exception.AdministrationException.seller-price-must-not-be-null")
            );
        }

        if (isCustomCommission && sellerPrice.compareTo(currentPrice) > 0) {
            throw new AdministrationException(
                    messageSourceAccessor.getMessage("exception.AdministrationException.current-price-must-be-grater-than-seller"));
        }

        Long pickupCountryId = product.getSeller().getPickupCountry() != null ? product.getSeller().getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
        Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

        // calculate price without commission (correct seller price)
        List<DutyDTO> duties = null;
        if (isCustomCommission) {
            duties = dutyService.calculateDutiesByPricesWithAndWithoutDuties(
                    currentPrice,
                    sellerPrice,
                    product.getSeller(),
                    pickupCountryId,
                    deliveryCountryId);
        } else if (sellerPrice != null) {
            duties = dutyService.calculateDutiesByPriceWithCommission(
                    currentPrice,
                    sellerPrice,
                    product.getSeller(),
                    pickupCountryId,
                    deliveryCountryId);
        }

        if (duties != null && duties.size() > 0) {
            BigDecimal priceWithoutCommission = duties.stream()
                    .filter(DutyDTO::isOskellyCommission)
                    .map(DutyDTO::getAmount).reduce(currentPrice, BigDecimal::subtract);

            //здесь меняется входной параметр!
            sellerPrice = priceWithoutCommission;
        }

        if (BooleanUtils.isTrue(isCustomCommission)
                || salesChannel == null || !salesChannel.isBoutiqueCommissionApplied()) {

            Price price = commissionService.calculatePriceWithCommission(
                    currentPrice,
                    sellerPrice,
                    salesChannel,
                    isCustomCommission,
                    product.getSeller().getCommissionGrid());

            product.setCurrentPrice(price.getPriceWithCommission());

            product.setCurrentPriceWithoutCommission(
                    commissionService.calculatePriceWithoutCommission(
                            currentPrice,
                            sellerPrice,
                            salesChannel,
                            isCustomCommission,
                            product.getSeller().getCommissionGrid()
                    )
            );
            product.setCustomCommission(isCustomCommission);
            product.setCommission(
                    isCustomCommission
                            ? null
                            : price.getCommission());
        } else {
            // значит проставлен канал продаж бутиковский, но цены не кастомные (а значит не округленные)
            product.setCurrentPriceWithoutCommission(
                    commissionService.calculatePriceWithoutCommission(
                            currentPrice,
                            sellerPrice,
                            salesChannel,
                            isCustomCommission,
                            product.getSeller().getCommissionGrid()
                    )
            );

            Price rawCurrentPrice = commissionService.calculatePriceWithCommission(
                    currentPrice,
                    sellerPrice,
                    salesChannel,
                    isCustomCommission,
                    product.getSeller().getCommissionGrid()
            );

            product.setCurrentPrice(commissionService.roundPriceToHundred(rawCurrentPrice.getPriceWithCommission()));
            product.setCommission(null);
            product.setCustomCommission(true);
        }

        boutiqueService.propagatePriceChange(product);
    }

    @Transactional
    public void setPricesAndCommissions(ProductItem productItem, BigDecimal currentPrice, BigDecimal sellerPrice,
                                        boolean isCustomCommission, SalesChannel salesChannel) {
        // метод выполняется для варианта товара
        if (currentPrice == null || BigDecimal.ZERO.compareTo(currentPrice) >= 0) {
            throw new AdministrationException(
                    messageSourceAccessor.getMessage("exception.AdministrationException.current-price-must-not-be-null"));
        }
        if (isCustomCommission && sellerPrice == null) {
            throw new AdministrationException(
                    messageSourceAccessor.getMessage("exception.AdministrationException.seller-price-must-not-be-null")
            );
        }

        if (isCustomCommission && sellerPrice.compareTo(currentPrice) > 0) {
            throw new AdministrationException(
                    messageSourceAccessor.getMessage("exception.AdministrationException.current-price-must-be-grater-than-seller"));
        }

        User seller = productItem.getProduct().getSeller();
        Long pickupCountryId = seller.getPickupCountry() != null ? seller.getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
        Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

        // calculate price without commission (correct seller price)
        List<DutyDTO> duties = null;
        if (isCustomCommission) {
            duties = dutyService.calculateDutiesByPricesWithAndWithoutDuties(
                    currentPrice,
                    sellerPrice,
                    seller,
                    pickupCountryId,
                    deliveryCountryId);
        } else if (sellerPrice != null) {
            duties = dutyService.calculateDutiesByPriceWithCommission(
                    currentPrice,
                    sellerPrice,
                    seller,
                    pickupCountryId,
                    deliveryCountryId);
        }

        if (duties != null && duties.size() > 0) {
            BigDecimal priceWithoutCommission = duties.stream()
                    .filter(DutyDTO::isOskellyCommission)
                    .map(DutyDTO::getAmount).reduce(currentPrice, BigDecimal::subtract);

            //здесь меняется входной параметр!
            sellerPrice = priceWithoutCommission;
        }

        if (BooleanUtils.isTrue(isCustomCommission)
                || salesChannel == null || !salesChannel.isBoutiqueCommissionApplied()) {

            Price price = commissionService.calculatePriceWithCommission(
                    currentPrice,
                    sellerPrice,
                    salesChannel,
                    isCustomCommission,
                    seller.getCommissionGrid());

            productItem.setCurrentPrice(price.getPriceWithCommission());

            productItem.setCurrentPriceWithoutCommission(
                    commissionService.calculatePriceWithoutCommission(
                            currentPrice,
                            sellerPrice,
                            salesChannel,
                            isCustomCommission,
                            seller.getCommissionGrid()
                    )
            );
            productItem.setCustomCommission(isCustomCommission);
            productItem.setCommission(
                    isCustomCommission
                            ? null
                            : price.getCommission());
        } else {
            // значит проставлен канал продаж бутиковский, но цены не кастомные (а значит не округленные)
            productItem.setCurrentPriceWithoutCommission(
                    commissionService.calculatePriceWithoutCommission(
                            currentPrice,
                            sellerPrice,
                            salesChannel,
                            isCustomCommission,
                            seller.getCommissionGrid()
                    )
            );

            Price rawCurrentPrice = commissionService.calculatePriceWithCommission(
                    currentPrice,
                    sellerPrice,
                    salesChannel,
                    isCustomCommission,
                    seller.getCommissionGrid()
            );

            productItem.setCurrentPrice(commissionService.roundPriceToHundred(rawCurrentPrice.getPriceWithCommission()));
            productItem.setCommission(null);
            productItem.setCustomCommission(true);
        }
//        todo будет сделано тут https://jira.oskelly.ru/browse/CTOOLS-12
//        boutiqueService.propagatePriceChange(productItem);
    }

    @Transactional
    @Timed(value = "AdminProductPriceServiceImpl.refreshPriceForAutoCalculate")
    public void refreshPriceForAutoCalculate(Product product) {
        /*
        Price price = commissionService.calculatePriceWithCommission(
                product.getCurrentPriceWithoutCommission(),
                product.getSeller().getCommissionGrid(),
                product.getSalesChannel());

        product.setCurrentPrice(price.getPriceWithCommission());
        product.setCommission(price.getCommission());
         */

        Conversion conversion = productPublicationService.getConversionValueInternal(
                product.getCurrentPriceWithoutCommission(), null,
                product.getSalesChannel(),
                product.getSeller().getId(),
                null,
                product.getSeller().getCommissionGrid());

        product.setCurrentPrice(conversion.getPriceWithCommission());
        product.setCurrentPriceWithoutCommission(conversion.getPriceWithoutCommission());

        Commission commission = conversion.getCommissionId() != null ?
                commissionRepository.getOne(conversion.getCommissionId()) : null;
        product.setCommission(commission);

        //в реальности возможно только для товаров с ценой 0.0
        if (product.getCommission() == null) {
            log.error("refreshPriceForAutoCalculate product with null commission {}", product.getId());
        }

        boutiqueService.propagatePriceChange(product);
    }

    @Transactional
    @Timed(value = "AdminProductPriceServiceImpl.refreshProductItemPriceForAutoCalculate")
    public void refreshProductItemPriceForAutoCalculate(ProductItem productItem) {
        Product product = productItem.getProduct();
        Conversion conversion = productPublicationService.getConversionValueInternal(
                productItem.getCurrentPriceWithoutCommission(), null,
                product.getSalesChannel(),
                product.getSeller().getId(),
                null,
                product.getSeller().getCommissionGrid());

        productItem.setCurrentPrice(conversion.getPriceWithCommission());
        productItem.setCurrentPriceWithoutCommission(conversion.getPriceWithoutCommission());

        Commission commission = conversion.getCommissionId() != null ?
                commissionRepository.getOne(conversion.getCommissionId()) : null;
        productItem.setCommission(commission);

        //в реальности возможно только для товаров с ценой 0.0
        if (productItem.getCommission() == null) {
            log.error("refreshProductItemPriceForAutoCalculate product item with null commission {}", productItem.getId());
        }
//        todo будет сделано тут https://jira.oskelly.ru/browse/CTOOLS-12
//        boutiqueService.propagatePriceChange(productItem);
    }
}
