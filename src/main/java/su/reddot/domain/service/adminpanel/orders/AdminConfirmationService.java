package su.reddot.domain.service.adminpanel.orders;

import org.springframework.security.access.prepost.PreAuthorize;

import java.math.BigDecimal;

import su.reddot.domain.model.order.SaleRejectionReason;
import su.reddot.domain.service.dto.order.OrderPositionExcludeAgentReportParamsDTO;

public interface AdminConfirmationService {
	void approveOrderPosition(Long orderPositionId, boolean isOrderProcessingNotificationRequired);

	void declineOrderPosition(
		Long orderPositionId,
		boolean isOrderProcessingNotificationRequired,
		SaleRejectionReason saleRejectionReason
	);

	void cancelOrderPosition(Long orderPositionId, boolean isOrderProcessingNotificationRequired);

	void setExpertisePassed(Long orderPositionId);

	void setExpertiseIssues(long orderPositionId, BigDecimal defectsAmount, BigDecimal serviceAmount, String expertiseComment);

	void setExpertiseCleaning(Long orderPositionId, BigDecimal price);

	void setExpertiseDefect(Long orderPositionId, BigDecimal discount, String comment);

	void setExpertiseDefectPrice(Long orderPositionId, BigDecimal discountPrice, String comment);

	void setExpertiseReject(Long orderPositionId, String conclusion, String reason);

	@PreAuthorize("hasAnyAuthority('ORDER_RETURNS')")
	void excludeFromAgentReport(OrderPositionExcludeAgentReportParamsDTO params);

	void setReturnAccepted(Long orderPositionId);

	void setReturnDeclined(Long orderPositionId, String reason);

	void setReturnExpertiseApproved(Long orderPositionId);

	void setReturnExpertiseRejected(Long orderPositionId, String reason);

	/**
	 * Fill datamatrix fill for item
	 * @param orderPositionId Item ID
	 * @param datamatrix Data matrix for items
	 */
	void setItemsDatamatrix(Long orderPositionId, String datamatrix);

	void approveItemsMarkingCode(long orderPositionId, boolean approveValue);
}
