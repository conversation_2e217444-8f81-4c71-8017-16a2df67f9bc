package su.reddot.domain.service.adminpanel.orders;

import lombok.NonNull;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.AddressEndpointDTO;

import java.util.List;

public interface AdminAddressService {
    AddressEndpointDTO saveAddressEndpoint(@NonNull User user, @NonNull AddressEndpointDTO addressEndpointDto);

    AddressEndpointDTO saveAddressEndpoint(@NonNull Long userId, @NonNull AddressEndpointDTO addressEndpointDto);

    void deleteAddressEndpoint(@NonNull Long addressEndpointId);

    AddressEndpointDTO markAddressAsNonActual(@NonNull Long addressEndpointId);

    List<AddressEndpointDTO> findAllByUserId(@NonNull Long userId);
}
