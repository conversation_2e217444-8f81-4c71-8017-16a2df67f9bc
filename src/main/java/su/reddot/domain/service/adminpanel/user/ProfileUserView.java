package su.reddot.domain.service.adminpanel.user;

import su.reddot.domain.model.localization.CounterpartyTypeLocalized;
import su.reddot.domain.model.user.User;
import su.reddot.presentation.adminpanel.AdminOrdersController;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.List;

public interface ProfileUserView extends UserView {
	User getUser();

	String getAvatarPath();

	String getFullName();

	String getNickName();

	// "Доверенный" пользователь
	boolean isTrusted();

	int getLikedBrandsCount();

	int getSubscribersCount();

	long getUserSubscriptionsCount();

	int getLikedProductsCount();

	long getPriceSubscriptionsCount();

	int getProductsCount();

	int getOrdersCount();

	int getSalesCount();

	BigDecimal getUserBalance();

	int getVisibleCommentsCount();

	String getEmail();

	String getPhoneNumber();

	Boolean isPhoneVerified();

	User.Sex getSex();

	LocalDateTime getBirthDate();

	ZonedDateTime getRegistrationDate();

	long getId();

	List<PaymentRequisite> getRequisites();

	List<AdminOrdersController.AddressData> getAddresses();

	List<User.Role> getRoles();

	List<User.UserType> getUserTypes();

	List<CounterpartyTypeLocalized> getCounterpartyTypes();

	List<User.CompanyForm> getCompanyForms();

	Boolean getIsTrusted();

	String getRoleInfo();

	String getPosition();

	List<String> getTags();

	Boolean getIsDeleted();

	String getDeleteTime();
}
