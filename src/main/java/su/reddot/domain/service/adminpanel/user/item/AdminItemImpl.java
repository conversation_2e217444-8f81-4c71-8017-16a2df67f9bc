package su.reddot.domain.service.adminpanel.user.item;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import su.reddot.domain.service.adminpanel.user.item.itemparams.AdminItem;
import su.reddot.domain.service.adminpanel.user.item.itemparams.WithCity;

import javax.annotation.Resource;

@Resource
@Slf4j
@Data(staticConstructor = "of")
public class AdminItemImpl implements AdminItem, WithCity {
	private final Long 		id;				// BaseUserItem
	private final String 	fullName;		// BaseUserItem
	private final String	nickName;		// BaseUserItem
	private final String 	email;			// BaseUserItem
	private final String 	phoneNumber;	// BaseUserItem
	private final String    city;           // WithCity
	private final String	role;			// BaseUserItem
	private final boolean	trusted;		// BaseUserItem
	private final Boolean isBaned;

}
