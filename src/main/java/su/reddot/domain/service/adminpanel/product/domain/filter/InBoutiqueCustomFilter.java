package su.reddot.domain.service.adminpanel.product.domain.filter;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.model.product.QProductTagBinding;
import su.reddot.domain.service.adminpanel.domain.PredicateRequestIF;
import su.reddot.domain.service.product.ProductTagService;

@Component
@RequiredArgsConstructor
public class InBoutiqueCustomFilter implements CustomFilterIF {
    private static final String NAME = "inBoutiqueFilter";

    private final ProductTagService productTagService;

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public BooleanBuilder booleanBuilder(PredicateRequestIF queryObject) {
        QProduct qProduct = QProduct.product;
        QProductTagBinding productTagBinding = QProductTagBinding.productTagBinding;

        BooleanExpression filterExpression = JPAExpressions.selectOne()
                .from(qProduct)
                .leftJoin(productTagBinding).on(qProduct.id.eq(productTagBinding.product.id))
                .where(productTagBinding.tag.id.in(productTagService.getInBoutiqueProductTagIdsCached()))
                .exists();

        return new BooleanBuilder(filterExpression);
    }
}
