package su.reddot.domain.service.adminpanel.comment.filter.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.comment.CommentRepository;
import su.reddot.domain.model.Comment;
import su.reddot.domain.service.adminpanel.comment.filter.CommentFilter;

@Component
@RequiredArgsConstructor
public class BeegzProductCommentFilter implements CommentFilter {

    private static final String TYPE = "BEEGZ";

    private final CommentRepository commentRepository;

    @Override
    public Page<Comment> getPage(Pageable pageable) {
        return commentRepository.findByBeegzProduct(pageable);
    }

    @Override
    public String getType() {
        return TYPE;
    }
}
