package su.reddot.domain.service.adminpanel.user.counter.sql;

import org.springframework.stereotype.Service;
import su.reddot.domain.dao.counter.FollowingUpdaterUserCounterRepository;
import su.reddot.domain.dao.counter.UpdateUserCounterRepository;
import su.reddot.domain.service.adminpanel.user.UserCounterTypeService;

@Service
public class FollowingUserCounterUpdater extends AbstractSQLCounterUpdater {

    private static final String COUNTER_CODE = "USER_FOLLOWING";
    private final FollowingUpdaterUserCounterRepository userCounterRepository;

    public FollowingUserCounterUpdater(UserCounterTypeService counterTypeService, FollowingUpdaterUserCounterRepository userCounterRepository) {
        super(counterTypeService);
        this.userCounterRepository = userCounterRepository;
    }

    @Override
    protected UpdateUserCounterRepository getUpdateRepository() {
        return userCounterRepository;
    }

    @Override
    public String getCounterCode() {
        return COUNTER_CODE;
    }
}
