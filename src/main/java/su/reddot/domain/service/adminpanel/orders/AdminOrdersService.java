package su.reddot.domain.service.adminpanel.orders;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.security.access.prepost.PreAuthorize;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderStatusGroup;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.orders.view.AdminAddressView;
import su.reddot.domain.service.adminpanel.orders.view.AdminBankOperationsView;
import su.reddot.domain.service.adminpanel.orders.view.AdminCounterpartyView;
import su.reddot.domain.service.adminpanel.orders.view.AdminHistoryOrderView;
import su.reddot.domain.service.adminpanel.orders.view.AdminOrderCommonPageInfoView;
import su.reddot.domain.service.adminpanel.orders.view.AdminOrderPageInfoView;
import su.reddot.domain.service.adminpanel.orders.view.AdminOrderPayoutView;
import su.reddot.domain.service.adminpanel.orders.view.AdminOrderPositionView;
import su.reddot.domain.service.adminpanel.orders.view.AdminOrderView;
import su.reddot.domain.service.adminpanel.orders.view.OrderRefundReasonSubTabView;
import su.reddot.domain.service.adminpanel.orders.view.OrderSubTabView;
import su.reddot.domain.service.adminpanel.orders.view.OrderTabView;
import su.reddot.domain.service.adminpanel.view.AdminObjectAbstractView;
import su.reddot.domain.service.adminpanel.view.AdminPage;
import su.reddot.domain.service.adminpanel.view.TableFilterView;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.delivery.DeliveryParamRequestDTO;
import su.reddot.domain.service.dto.order.AgentReportParams;
import su.reddot.domain.service.dto.order.BoutiqueOrderStockParams;
import su.reddot.domain.service.dto.order.BoutiqueSoldParams;
import su.reddot.domain.service.dto.order.ChargeParams;
import su.reddot.domain.service.dto.order.ConciergePaymentToSellerParams;
import su.reddot.domain.service.dto.order.DocumentLinkDTO;
import su.reddot.domain.service.dto.order.OrderCounterPartyDTO;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderRefundAmountParams;
import su.reddot.domain.service.dto.order.OrderRefundParams;
import su.reddot.domain.service.dto.order.OrderReturnParams;
import su.reddot.domain.service.dto.order.ReturnToSellerDTO;
import su.reddot.domain.service.dto.order.TransferToSellerParams;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDTO;
import su.reddot.domain.service.dto.order.adminpanel.DisputeDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.presentation.adminpanel.AdminOrdersController;
import su.reddot.presentation.api.v2.order.OrderSplitRequest;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface AdminOrdersService {
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	AdminPage<AdminOrderView> getOrdersTablePage(OrderService.FilterSpecification filterSpecification, int page, String sort);

	OrderService.FilterSpecification createFilterSpecification(AdminOrdersController.OrderTablePageRequest request);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<AdminHistoryOrderView> getOrderHistory(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	AdminOrderView getOrderInfo(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	AdminBankOperationsView getOrderBankOperations(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	OrderDTO getOrderDetails(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<DocumentLinkDTO> getDocumentLinks(Long orderId, User user, String baseUrl);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<AdminOrderPositionView> getOrderPositionsView(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	long countRawOrdersCached(OrderService.FilterSpecification spec);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	long countRawOrders(OrderService.FilterSpecification spec);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<OrderTabView> getOrderStatusTabViews();

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<OrderSubTabView> getOrderStatusSubTabViews(OrderStatusGroup currentTab);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<OrderRefundReasonSubTabView> getOrderRefundReasonsSubTabViews();

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<OrderTabView> getOrderStateTabViews();

	void confirmOrder(Long orderId);

	void confirmOrderAndSendToPickup(DeliveryParamRequestDTO params);

	void confirmOrderAndSendToPickupOurselves(DeliveryParamRequestDTO params);

	@PreAuthorize("hasAnyAuthority('ORDER_PREPAYMENTS')")
	void convertHoldToPrepayment(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void handleSaleResolution(DeliveryParamRequestDTO source);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void chargeMoney(ChargeParams params);
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void retryOrderReceiptRequest(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	String viewReceiptsForOrder(Long orderId);

	@PreAuthorize("hasAnyAuthority('ORDER_MODERATION')")
	String viewBankOperationDetails(String scheme, String operationsId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void setDisputeFlag(DisputeDTO params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_RETURN_COMPLETED')")
	void switchDebtOnPayout(long orderId, boolean switchTo);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_RETURN_COMPLETED')")
	void returnCompletedOrSoldInBoutiqueOrder(OrderReturnParams params);

	@PreAuthorize("hasAnyAuthority('ORDER_MODERATION')")
	void refundTryAgainOnFail(long orderId);

	@PreAuthorize("hasAnyAuthority('SPLIT_ORDER')")
	List<Long> splitOrder(OrderSplitRequest request);

	@PreAuthorize("hasAnyAuthority('SPLIT_ORDER')")
	List<Order> splitOrderByOrderPositions(Long orderId, List<List<Long>> orderPositions);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void sendBoutiqueOrderToStockQueue(BoutiqueOrderStockParams params);

	void handleOrdersTask();

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_PAYOUTS')")
	void sendAgentReportToSeller(AgentReportParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','BOUTIQUE_SALES')")
	void sellOrderInBoutique(BoutiqueSoldParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_PAYOUTS')")
	@SneakyThrows
	void confirmAgentReport(AgentReportParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_CONCIERGE_PAYOUTS')")
	void payMoneyToConcierge(ConciergePaymentToSellerParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void transferMoneyToSeller(TransferToSellerParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void refund(OrderRefundParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void refundAmount(OrderRefundAmountParams params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void returnedToSeller(ReturnToSellerDTO params);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	Boolean addressIsValidatedFIAS(AddressEndpoint addressEndpoint);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<AdminAddressView> getUserAddresses(User user);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	List<AdminCounterpartyView> getUserCounterparties(User user);

	/**
	 * Get active seller counterparty for the order.
	 *
	 * @param orderId Order ID.
	 * @return Active seller counterparty if any, otherwise {@code null}.
	 */
	@Nullable
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	AdminCounterpartyView getActiveSellerCounterparty(long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	User getBuyer(Long orderId);

	/**
	 * Возвращает соответствия orderPositionId -> productDTO
	 * @param orderPositions
	 * @param orderPositionIds передается для ключа кэша
	 * @return
	 */
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	Map<Long, ProductDTO> getProductDTOsCached(List<OrderPosition> orderPositions, List<Long> orderPositionIds);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	void changeAddressEndpoint(Long orderId, Long pickupAddressEndpointId, Long deliveryAddressEndpointId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_PAYOUTS')")
	void changeSellerCounterparty(OrderCounterPartyDTO params);

	@PreAuthorize("hasAnyAuthority('ADMIN', 'PAYOUT_BY_CASH')")
	@SneakyThrows
	void payoutWithCashWithdraw(Order order);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	Long saveSellerAddressEndpoint(User user, AdminOrdersController.AddressData addressInfo);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	Long saveBuyerAddressEndpoint(User user, AdminOrdersController.AddressData addressInfo);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	Long saveSellerCounterparty(User user, AdminOrdersController.CounterpartyData counterpartyInfo);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
    AdminOrderView.SellerType getSellerTypeByOrder(Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	AdminOrdersController.AddressData getUserAddressData(Long orderId, Long addressEndpointId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	AdminOrdersController.CounterpartyData getUserCounterpartyData(Long counterpartyId);

	Map<String,TableFilterView> getTableFilters(OrderService.FilterSpecification filterSpecification);

	AdminOrderPageInfoView getOrderPageInfo(OrderService.FilterSpecification spec);

	AdminOrderPageInfoView getOrderPageInfoCached(OrderService.FilterSpecification spec);

	AdminOrderCommonPageInfoView getOrderRefundPageInfo(OrderService.FilterSpecification spec);

	AdminOrderCommonPageInfoView getOrderRefundPageInfoCached(OrderService.FilterSpecification spec);

	AdminOrderCommonPageInfoView getOrderNotReturnToSellerPageInfo(OrderService.FilterSpecification spec);

	List<AdminOrderPayoutView> getOrderCompletedPayoutsView(Long orderId);
	List<AdminOrderPayoutView> getOrderCalculatePayoutsView(Long orderId);
	BigDecimal getOrderTotal(Order order);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION','EXPERTISE')")
	byte[] getCertifPDF(@NonNull Long orderPositionId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION','EXPERTISE')")
	byte[] getNonAuthenticityCertificatePDF(long orderPositionId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	byte[] getOskWaybillFromSeller(@NonNull Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	byte[] getOskWaybillToBuyer(@NonNull Long orderId);

	/**
	 * Получить акт приема-передачи курьерской службы для списка заказов в PDF-формате.
	 * Все заказы должны быть назначены одной и той же курьерской службе. Сейчас поддерживается
	 * акт для CSE и Dalli.
	 *
	 * @param orderIds Список ID заказов в базе.
	 * @return Массив байт PDF файла с отчетом.
	 */
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	@Nonnull
	byte[] getLogisticCompanyAct(@Nonnull List<Long> orderIds);

	/**
	 * Получить акт приема товаров от продавца в PDF-формате.
	 *
	 * @param orderPositionIds Список ID позиций заказа в базе. Все позиции должны принадлежать
	 *                         одному заказу.
	 * @return Массив байт PDF файла с отчетом.
	 */
	@Nonnull
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	byte[] getActOfAcceptance(@NonNull List<Long> orderPositionIds);

	/**
	 * Получить акт возврата товаров продавцу в PDF-формате.
	 *
	 * @param orderPositionIds Список ID позиций заказа в базе. Все позиции должны принадлежать
	 *                         одному заказу.
	 * @return Массив байт PDF файла с отчетом.
	 */
	@Nonnull
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	byte[] getActOfReturn(@NonNull List<Long> orderPositionIds);

	/**
	 * Определяет для заказа было ли подтверждение получения покупателем
	 * @param orderId
	 * @return
	 */
	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	Boolean isDeliveryToBuyerConfirmed(@NonNull Long orderId);

	@PreAuthorize("hasAnyAuthority('ADMIN','ORDER_MODERATION')")
	su.reddot.domain.service.dto.Page<AdminPanelOrderDTO> getAPIv2AdminPageOrders(AdminOrdersController.OrderTablePageRequest request);

	String getBoutiqueDeliveryBuyerWarning(Order order);
	String getBoutiqueDeliverySellerWarning(Order order);

	@RequiredArgsConstructor
	@Getter @Setter
	class IdFilterItemView extends AdminObjectAbstractView {
		private final IdFilterParam param;
		private final MessageSourceAccessor messageSourceAccessor;
		@Override
		public Object getId(){
			return param.name();
		}
		@Override
		public String getName(){
			return messageSourceAccessor.getMessage(param.getDescription());
		}
		@AllArgsConstructor
		@Getter
		public enum IdFilterParam {
			PROBLEMS_WITH_BANK("entity.enum.IdFilterParam.PROBLEMS_WITH_BANK.description"),
			PROBLEMS_WITH_ADDRESS("entity.enum.IdFilterParam.PROBLEMS_WITH_ADDRESS.description");
			private String description;
		}
	}
}
