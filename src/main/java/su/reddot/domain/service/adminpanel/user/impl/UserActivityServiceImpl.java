package su.reddot.domain.service.adminpanel.user.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import su.reddot.domain.service.adminpanel.user.UserActivityService;
import su.reddot.domain.service.adminpanel.user.activity.UserActivityProvider;
import su.reddot.domain.service.adminpanel.user.domain.UserActivityDTO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserActivityServiceImpl implements UserActivityService {

    private final List<UserActivityProvider> userActivityProviders;

    @Override
    public Map<String, UserActivityDTO> getUserActivity(Long userId) {
       return userActivityProviders.stream()
               .map(provider -> getUserActivity(userId, provider))
               .filter(Objects::nonNull)
               .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
    }

    private Pair<String, UserActivityDTO> getUserActivity(Long userId, UserActivityProvider provider) {
        try {
            return provider.getUserActivity(userId);
        } catch (Throwable t) {
            log.error(t.getMessage(), t);
            return null;
        }
    }
}
