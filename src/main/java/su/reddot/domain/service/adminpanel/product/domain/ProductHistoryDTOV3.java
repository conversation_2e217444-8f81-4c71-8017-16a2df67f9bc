package su.reddot.domain.service.adminpanel.product.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ProductHistoryDTOV3 extends AbstractProductDTOV3 {
    private Long brandId;
    private Long sellerId;
    private Long imageId;
    private Long retoucherId;
    private Long productModelId;
    private List<AttributeValueHistoryDTOV3> attributeValues = new ArrayList<>();
    private List<ProductItemDTOV3> productItems = new ArrayList<>();
    private CommissionDTOV3 commission;
    @EqualsAndHashCode.Exclude
    private String categoryPath;
    @EqualsAndHashCode.Exclude
    private String brandName;
    @EqualsAndHashCode.Exclude
    private String productConditionName;
    @EqualsAndHashCode.Exclude
    private String productStateName;
    @EqualsAndHashCode.Exclude
    private String salesChannelName;
    @EqualsAndHashCode.Exclude
    private String currencyName;
    @EqualsAndHashCode.Exclude
    private String sizeTypeName;
    @EqualsAndHashCode.Exclude
    private String productModelName;
}
