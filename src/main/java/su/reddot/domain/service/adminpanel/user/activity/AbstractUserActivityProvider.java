package su.reddot.domain.service.adminpanel.user.activity;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.service.adminpanel.user.domain.UserActivityDTO;

import java.math.BigDecimal;

public abstract class AbstractUserActivityProvider implements UserActivityProvider {

    private final MessageSourceAccessor messageSourceAccessor;

    private static final String LABEL_PREFIX = "enum.UserActivity.";

    public AbstractUserActivityProvider(MessageSourceAccessor messageSourceAccessor) {
        this.messageSourceAccessor = messageSourceAccessor;
    }

    protected String getLabel() {
        return messageSourceAccessor.getMessage(LABEL_PREFIX + getUserActivityType());
    }

    private UserActivityDTO getEmptyDTO() {
        return new UserActivityDTO(getLabel(), BigDecimal.ZERO, 0);
    }

    @Override
    public Pair<String, UserActivityDTO> getUserActivity(Long userId) {
        UserActivityDTO dto = getUserActivityDTO(userId);

        return Pair.of(getUserActivityType(),
                dto != null ? dto : getEmptyDTO());
    }

    protected abstract UserActivityDTO getUserActivityDTO(Long userId);

    protected abstract String getUserActivityType();
}
