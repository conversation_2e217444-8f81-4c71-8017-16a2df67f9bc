package su.reddot.domain.service.adminpanel.events;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import su.reddot.domain.service.ComponentRegistry;

@Slf4j
public abstract class AbstractEntityListener<EntityClass> {

    protected boolean isTransactionActive() {
        return TransactionSynchronizationManager.isActualTransactionActive();
    }

    protected boolean isTransactionReadOnly() {
        return TransactionSynchronizationManager.isCurrentTransactionReadOnly();
    }

    protected abstract String getEntityId(EntityClass entity);

    protected abstract boolean stateWasSaved(EntityClass entity);

    protected abstract boolean eventWasSent(EntityClass entity);

    protected abstract void saveStateToEntity(EntityClass entity);

    protected abstract void setEventWasSent(EntityClass entity);

    protected abstract boolean stateNotChanged(EntityClass entity);

    protected abstract Object makeEventObject(EntityClass entity);

    protected void onPostLoadCommon(EntityClass entity) {
        log.debug("onPostLoad " + getEntityId(entity));

        saveState(entity);
    }

    protected void onPostSaveCommon(EntityClass entity) {

        log.debug("onPostSave " + getEntityId(entity));

        publishEditEvent(entity);
    }

    private void publishEditEvent(EntityClass entity) {

        if (!isTransactionActive()
                || isTransactionReadOnly()) {
            log.debug("Transaction is not active " + getEntityId(entity));
            return;
        }

        if (!stateWasSaved(entity)) {
            log.warn("State was not saved " + getEntityId(entity));
            return;
        }

        if (eventWasSent(entity)) {
            log.warn("Event was sent " + getEntityId(entity));
            return;
        }

        if (stateNotChanged(entity)) {
            log.warn("State is not changed " + getEntityId(entity));
            return;
        }

        setEventWasSent(entity);

        ComponentRegistry.getEventPublisher().publishEvent(makeEventObject(entity));
    }

    private void saveState(EntityClass entity) {
        if (stateWasSaved(entity)
                || !isTransactionActive()
                || isTransactionReadOnly()) {
            return;
        }

        saveStateToEntity(entity);
    }
}
