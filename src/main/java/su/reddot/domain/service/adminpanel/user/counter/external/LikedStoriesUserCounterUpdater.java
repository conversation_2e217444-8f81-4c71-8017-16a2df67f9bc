package su.reddot.domain.service.adminpanel.user.counter.external;

import org.springframework.stereotype.Service;
import su.reddot.domain.service.adminpanel.user.UserCounterService;
import su.reddot.domain.service.adminpanel.user.UserCounterTypeService;
import su.reddot.domain.service.dto.primary.UserCounterDataDTO;
import su.reddot.infrastructure.story.StoryClient;

import java.util.List;

@Service
public class LikedStoriesUserCounterUpdater extends AbstractExternalUserCounterUpdater {
    private static final String COUNTER_CODE = "STORIES_WISHLIST";
    private final StoryClient storyClient;

    public LikedStoriesUserCounterUpdater(UserCounterTypeService counterTypeService,
                                          UserCounterService userCounterService, StoryClient storyClient) {
        super(counterTypeService, userCounterService);
        this.storyClient = storyClient;
    }

    @Override
    protected List<UserCounterDataDTO> getExternalCountsDTOList() {
        return storyClient.getLikedSlideCountGroupedByUser();
    }

    @Override
    public String getCounterCode() {
        return COUNTER_CODE;
    }

    @Override
    protected boolean isNeedToCleanZeroCounters() {
        return true;
    }
}
