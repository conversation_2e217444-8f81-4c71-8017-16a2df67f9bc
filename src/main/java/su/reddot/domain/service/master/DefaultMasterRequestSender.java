package su.reddot.domain.service.master;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

@Service
@Primary
@Getter
public class DefaultMasterRequestSender extends AbstractMasterR<PERSON><PERSON><PERSON><PERSON> implements MasterRequestSender {
    @Value("${app.master.url}")
    private String masterServiceUrl;

    @Value("${app.master.user-email}")
    private String masterServiceUserEmail;

    @Value("${app.master.user-password}")
    private String masterServiceUserPassword;

    @Value("${app.master.client-user-agent}")
    private String masterClientUserAgent;

    @Value("${app.master.attemptsCount:25}")
    private int attemptsCount;

    @Value("${app.master.attemptsDelayPeriodSeconds:30}")
    private int attemptsDelayPeriodSeconds;
}
