package su.reddot.domain.service.master;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@NoArgsConstructor
@Getter @Setter @Accessors(chain = true)
public class MasterServiceRequest {
	MediaType mediaType = MediaType.MULTIPART_FORM_DATA;
	HttpMethod method = HttpMethod.POST;
	String url = "";
	Map<String, String>httpParams;
	Object requestEntityObject;

	public MasterServiceRequest addHttpParam(String name, String value){
		if(httpParams == null) httpParams = new HashMap<>();
		httpParams.put(name, value);
		return this;
	}

	public MasterServiceRequest addHttpParam(String name, List<?> multivalue){
		String value = multivalue.stream().map(o -> ((Object) o).toString()).collect(Collectors.joining(","));
		return addHttpParam(name, value);
	}
}
