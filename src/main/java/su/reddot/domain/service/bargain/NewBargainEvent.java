package su.reddot.domain.service.bargain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import su.reddot.domain.event.OskellyEvent;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NewBargainEvent extends OskellyEvent {

    private Long bargainId;

    @Override
    public String getUniqueEntityId() {
        return String.valueOf(bargainId);
    }
}
