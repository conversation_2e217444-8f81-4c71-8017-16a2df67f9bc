package su.reddot.domain.service.estimation.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.estimation.EventDateEstimationRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.estimation.EstimatedEventType;
import su.reddot.domain.model.estimation.EventDateEstimation;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.service.dto.estimation.EstimatedEventTypeDTO;
import su.reddot.domain.service.dto.estimation.EventDateEstimationDTO;
import su.reddot.domain.service.dto.estimation.EventDateEstimationHistoryDTO;
import su.reddot.domain.service.estimation.DateRange;
import su.reddot.domain.service.estimation.EstimatedDateCalculator;
import su.reddot.domain.service.estimation.EventDateEstimationService;

import javax.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static su.reddot.domain.service.util.DateTimeUtils.toOffsetDateTime;

@RequiredArgsConstructor
@Slf4j
@Service
public class EventDateEstimationServiceImpl implements EventDateEstimationService {

    private final OrderRepository orderRepository;

    private final EventDateEstimationRepository eventDateEstimationRepository;

    private final EstimatedDateCalculator estimatedDateCalculator;

    private final MessageSourceAccessor messageSourceAccessor;

    @Override
    public void calculateAndSaveEventDateEstimation(
            @NonNull final Order order,
            @NonNull final EstimatedEventType eventType,
            @NonNull final String calculationContextCode) {
        final DateRange estimatedDateRange = estimatedDateCalculator.calculateEstimatedDate(order,
                eventType);
        if (estimatedDateRange == null) {
            return;
        }

        saveEventDateEstimation(order, eventType, calculationContextCode, estimatedDateRange);
    }

    @Override
    @Nullable
    public DateRange getEstimatedDate(
            @NonNull final Order order,
            @NonNull final EstimatedEventType eventType) {
        return eventDateEstimationRepository.findByIsLastCalculationIsTrueAndOrderAndEventType(order, eventType)
                .map(EventDateEstimationServiceImpl::toDateRange)
                .orElse(null);
    }

    @Override
    public boolean hasEstimationChanged(
            @NonNull final Order order,
            @NonNull final EstimatedEventType eventType) {
        return eventDateEstimationRepository.findAllByOrderAndEventType(order, eventType).stream()
                       .map(EventDateEstimationServiceImpl::toDateRange)
                       .collect(Collectors.toSet())
                       .size() > 1;
    }

    @Override
    @Transactional
    public void changeExpertiseEstimation(
            final long orderId,
            @NonNull final LocalDate completionDate,
            @NonNull final String comment) {
        final Order order = orderRepository.getOne(orderId);
        final DateRange estimatedDateRange = new DateRange(completionDate);
        saveEventDateEstimation(order, EstimatedEventType.EXPERTISE_COMPLETION, comment, estimatedDateRange);

        // После изменения сроков экспертизы надо пересчитать ориентировочную дату доставки Покупателю
        calculateAndSaveEventDateEstimation(order, EstimatedEventType.DELIVERY_TO_BUYER_COMPLETION,
                "EventDateEstimation.calculationContext.afterExpertiseEstimationChange");
    }

    private void saveEventDateEstimation(
            @NonNull final Order order,
            @NonNull final EstimatedEventType eventType,
            @NonNull final String calculationContextCode,
            @NonNull final DateRange estimatedDateRange) {
        final Optional<EventDateEstimation> existingEstimationOpt = eventDateEstimationRepository
                .findByIsLastCalculationIsTrueAndOrderAndEventType(order, eventType);
        if (existingEstimationOpt.isPresent()) {
            final EventDateEstimation existingEstimation = existingEstimationOpt.get();
            if (existingEstimation.getEstimatedDateFrom().equals(estimatedDateRange.getDateFrom())
                    && existingEstimation.getEstimatedDateTo().equals(estimatedDateRange.getDateTo())) {
                // Новая ориентировочная дата совпадает с ранее рассчитанной: ничего делать не нужно
                return;
            }
            existingEstimation.setLastCalculation(false);
            eventDateEstimationRepository.save(existingEstimation);
        }

        final String calculationContext = messageSourceAccessor.getMessage(calculationContextCode, calculationContextCode);
        final EventDateEstimation estimation = new EventDateEstimation();
        estimation.setOrder(order);
        estimation.setEventType(eventType);
        estimation.setEstimatedDateFrom(estimatedDateRange.getDateFrom());
        estimation.setEstimatedDateTo(estimatedDateRange.getDateTo());
        estimation.setCalculationTime(LocalDateTime.now(ZoneOffset.UTC));
        estimation.setCalculationContext(calculationContext);
        estimation.setLastCalculation(true);
        eventDateEstimationRepository.save(estimation);
    }

    @NonNull
    private static DateRange toDateRange(@NonNull final EventDateEstimation estimation) {
        return new DateRange(estimation.getEstimatedDateFrom(), estimation.getEstimatedDateTo());
    }

    @Override
    public List<EventDateEstimationDTO> getEstimations(long orderId) {
        List<EventDateEstimation> estimations = eventDateEstimationRepository.findByOrderIdAndIsLastCalculationIsTrue(orderId);
        return estimations
                .stream()
                .map(
                        it -> EventDateEstimationDTO.builder()
                                .estimatedDateTo(toOffsetDateTime(it.getEstimatedDateTo()))
                                .estimatedDateFrom(toOffsetDateTime(it.getEstimatedDateFrom()))
                                .eventType(EstimatedEventTypeDTO.valueOf(it.getEventType().name()))
                                .build()
                )
                .sorted(Comparator.comparing(it -> it.getEventType().getOrder()))
                .collect(Collectors.toList());
    }

    @Override
    public List<EventDateEstimationHistoryDTO> getEstimationsHistory(long orderId, @NonNull EstimatedEventTypeDTO eventType) {
        List<EventDateEstimation> historyItems = eventDateEstimationRepository.findByOrderIdAndEventType(orderId,
                EstimatedEventType.valueOf(eventType.name()));
        return historyItems
                .stream()
                .sorted(Comparator.comparing(EventDateEstimation::getCalculationTime))
                .map(
                        it -> EventDateEstimationHistoryDTO.builder()
                                .calculationTime(toOffsetDateTime(it.getCalculationTime()))
                                .estimatedDateFrom(toOffsetDateTime(it.getEstimatedDateFrom()))
                                .estimatedDateTo(toOffsetDateTime(it.getEstimatedDateTo()))
                                .calculationContext(it.getCalculationContext())
                                .build()
                )
                .collect(Collectors.toList());
    }
}
