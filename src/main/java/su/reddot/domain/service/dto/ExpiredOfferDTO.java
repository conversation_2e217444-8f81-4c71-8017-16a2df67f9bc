package su.reddot.domain.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Accessors;
import su.reddot.infrastructure.logistic.DeliveryState;

@Getter
@Setter
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExpiredOfferDTO {

	private Long offerId;
	private boolean isRejected;
	private boolean isConsumedByBuyer;

}
