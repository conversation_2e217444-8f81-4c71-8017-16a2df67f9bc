package su.reddot.domain.service.dto.osocial.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import su.reddot.domain.service.dto.osocial.CoreOSocialLinkDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialMediaDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialProductShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialTagShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserShortViewDto;
import su.reddot.domain.service.social.model.PostStatus;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialAdminPostDto {
    private long id;
    private ZonedDateTime createdAt;
    private ZonedDateTime publishedAt;
    private CoreOSocialUserDto author;
    private double rating;
    private double boostingRate;
    private ZonedDateTime boostingExpiresAt;
    private PostStatus status;
    private boolean deleted;
    private String deletionReason;
    private String rejectionReason;
    private boolean hidden;
    private String text;
    private List<CoreOSocialMediaDto> media;
    private List<CoreOSocialUserShortViewDto> mentionedUsers;
    private List<CoreOSocialProductShortViewDto> products;
    private List<CoreOSocialTagShortViewDto> tags;
    private CoreOSocialLinkDto link;
    private long viewsCount;
    private long likesCount;
    private long sharesCount;
    private long favoritesCount;
    private long commentsCount;
}