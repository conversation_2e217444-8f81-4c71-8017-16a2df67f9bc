package su.reddot.domain.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.model.product.ProductState;
import su.reddot.infrastructure.util.Utils;

import java.util.Objects;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ProductDTOIntegrationLite {

    private Long productId;

    private Long categoryId;

    private Long brandId;

    private Long conditionId;

    private ProductState productState;

    private String storeCode;

    @Override
    public int hashCode() {
        return Objects.hash(productId);
    }

    @Override
    public boolean equals(Object another) {
        if (another == this) {
            return true;
        }
        if (!(another instanceof ProductDTOIntegrationLite)) {
            return false;
        }
        return equals((ProductDTOIntegrationLite) another);
    }

    public boolean equals(ProductDTOIntegrationLite another) {
        if (another == null) {
            return false;
        }
        return Utils.equals(productId, another.productId);
    }
}
