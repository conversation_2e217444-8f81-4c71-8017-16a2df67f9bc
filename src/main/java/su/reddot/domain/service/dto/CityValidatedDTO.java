package su.reddot.domain.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.ZonedDateTime;

@Getter
@Setter
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CityValidatedDTO {

	private Long addressId;

	private String validatedCity;

	private ZonedDateTime lastCityValidationTime;

	/**
	 * Город прошел валидацию и однозначно определяется с учетом региона
	 */
	private Boolean isCityValidated = false;

	@Override
	public String toString(){
		return String.format("%d (%s) = %b", addressId, validatedCity, isCityValidated);
	}
}
