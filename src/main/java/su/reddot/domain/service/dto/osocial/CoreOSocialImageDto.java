package su.reddot.domain.service.dto.osocial;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import su.reddot.domain.service.social.model.MediaStatus;

import java.util.List;

import static su.reddot.domain.service.social.model.MediaType.IMAGE;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialImageDto extends CoreOSocialMediaDto {

    private String similarProductsGetToken;

    public CoreOSocialImageDto(
            long fileId,
            MediaStatus status,
            int orderNumber,
            List<CoreOSocialMediaVariantDto> variants,
            String similarProductsGetToken
    ) {
        super(fileId, status, IMAGE, orderNumber, variants);
        this.similarProductsGetToken = similarProductsGetToken;
    }
}