package su.reddot.domain.service.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import su.reddot.domain.exception.ValidationException;
import su.reddot.presentation.api.v2.Api2PageRequest2022;

/**
 * Параметры запрашиваемой страницы
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter @Setter
public class PageRequest {
	public static final int MIN_PAGE_SIZE = 1;
	public static final int MAX_PAGE_SIZE = Integer.MAX_VALUE;
	public static final int DEFAULT_PAGE_SIZE = 10;

	//Номер запрашиваемой страницы
	private int page = 1;

	//Размер запрашиваемой страницы
	private int pageSize = DEFAULT_PAGE_SIZE;

	public static PageRequest oneMaximumSizePage() {
		return new PageRequest(1, MAX_PAGE_SIZE);
	}

	public static PageRequest of(int page, int pageSize){
		return new PageRequest(page, pageSize);
	}

	public static PageRequest fromApi2PageRequest(Api2PageRequest2022 request) {
		return request.getRowsPerPage() == 0
				? PageRequest.oneMaximumSizePage() // Загрузить все записи
				: PageRequest.of(request.getPage(), request.getRowsPerPage());
	}
}
