package su.reddot.domain.service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AlertDTO {

	private final Long id;
	private final String dtype;
	private Long waybillId;

	@Override
	public String toString(){
		return dtype + ":" + id;
	}

}
