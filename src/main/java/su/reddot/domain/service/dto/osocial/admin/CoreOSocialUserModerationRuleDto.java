package su.reddot.domain.service.dto.osocial.admin;

import java.time.ZonedDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserShortViewDto;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialUserModerationRuleDto {

    private long id;
    private boolean active;
    private ZonedDateTime createdAt;
    private CoreOSocialUserShortViewDto createdBy;
    private String reason;
    private ZonedDateTime startedAt;
    private ZonedDateTime finishedAt;
    private ZonedDateTime deletedAt;
    private CoreOSocialUserShortViewDto deletedBy;
}
