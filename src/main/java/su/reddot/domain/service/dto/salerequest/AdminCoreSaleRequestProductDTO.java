package su.reddot.domain.service.dto.salerequest;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import su.reddot.domain.service.adminpanel.product.domain.ProductDTOV3;

@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AdminCoreSaleRequestProductDTO extends BaseCoreSaleRequestProductDTO {

    private ProductDTOV3 product;
}
