package su.reddot.domain.service.dto.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * Цепочка стадий заказа для представления пользователю (продавцу/покупателю/модератору).
 */

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter @Accessors(chain = true)
public class OrderStepChain {
	private List<OrderStepDTO> steps = new ArrayList<>();

	public void addStep(OrderStepDTO orderStepDTO){
		OrderStepDTO lastStep = getLastStep();
		if(lastStep != null) {
			if(orderStepDTO.getType() != OrderStepDTO.Type.DISABLED) {
				lastStep.setType(OrderStepDTO.Type.COMPLETE);
			}
			if(orderStepDTO.getType() == OrderStepDTO.Type.COMPLETE) {
				lastStep.setTemporaryMessage(null);
			}
		}
		steps.add(orderStepDTO);
	}

	@JsonIgnore
	public OrderStepDTO getLastStep(){
		if(steps.isEmpty()) return null;
		return steps.get(steps.size() - 1);
	}
}
