package su.reddot.domain.service.dto.osocial;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import su.reddot.domain.service.social.model.PostStatus;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialPostSearchViewDto {
    private long id;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private ZonedDateTime createdAt;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private ZonedDateTime publishedAt;
    private CoreOSocialUserDto author;
    private String text;
    private List<CoreOSocialMediaDto> media;
    private boolean mentionedUsersExists;
    private boolean likedByMe;
    private long likesCount;
    private boolean addedToFavoritesByMe;
    private CoreOSocialLinkDto link;
    private List<CoreOSocialProductShortViewDto> products;
    private List<CoreOSocialTagShortViewDto> tags;
    private PostStatus status;
    private long commentsCount;
    private Double rating;
    private CoreOSocialRatingExplanationDto ratingExplanation;
    private long sharesCount;
}