package su.reddot.domain.service.dto.osocial.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import su.reddot.domain.service.social.model.FeedObjectsCollectionType;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialCreateFeedObjectsCollectionRequestDto {

    private String title;
    private long linkedPostNumber;
    private FeedObjectsCollectionType type;

    // posts collection members
    private CoreOSocialSaveFeedPostsCollectionFiltersDataDto filters;
}