package su.reddot.domain.service.dto.osocial;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.community.CommunityBadge;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialUserDto extends CoreOSocialUserShortViewDto {

    private CommunityBadge communityBadge;
    @JsonProperty("isFollowed")
    private boolean followedByMe;
    private int productsCount;

    public CoreOSocialUserDto(
            long id,
            String nickname,
            String avatarPath,
            CommunityBadge communityBadge,
            boolean followedByMe,
            int productsCount,
            boolean pro,
            User.Sex sex,
            String email,
            Boolean isTrusted
    ) {
        super(id, nickname, avatarPath, pro, sex, email, isTrusted);
        this.communityBadge = communityBadge;
        this.followedByMe = followedByMe;
        this.productsCount = productsCount;
    }
}
