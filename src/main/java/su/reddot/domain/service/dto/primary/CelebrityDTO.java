package su.reddot.domain.service.dto.primary;

/*
 * Created by <PERSON> on 18.03.2022
 */

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CelebrityDTO {

    @ApiModelProperty(notes = "Первичный ключ", required = true)
    private Long id;
    @ApiModelProperty(notes = "Описание", required = true)
    private String description;
    @ApiModelProperty(notes = "Путь для перехода", required = true)
    private String link;
    @ApiModelProperty(notes = "Путь для подписки", required = true)
    private String subscribeLink;
    @ApiModelProperty(notes = "Путь перехода на соц сеть", required = true)
    private String socialNetworkURI;
    @ApiModelProperty(notes = "Количество продуктов", required = true)
    private Integer productCount;
    @ApiModelProperty(notes = "Количество подписчиков в instagram")
    private String instagramFollowerCount;
    @ApiModelProperty(notes = "Пользователь(Celebrity)", required = true)
    private PrimaryPageUserDTO userDTO;
}
