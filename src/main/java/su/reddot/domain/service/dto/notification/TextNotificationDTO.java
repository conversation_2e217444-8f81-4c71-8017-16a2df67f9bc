package su.reddot.domain.service.dto.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * Упрощенная модель текстового уведомления для передачи на сервер с целью создать новое текстовое уведомление в базе.
 * Текстовое уведомление не содержит внешних ключей на связанные объекты, а содержит только текст.
 */
@Getter @Setter @AllArgsConstructor @RequiredArgsConstructor
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TextNotificationDTO {
	//Полное имя класса
	private String className;
	//ID пользователя-получателя
	private Long userId;
	//Гостевой токен получателя
	private String guestToken;
	//Требуется действие
	private boolean needAction = false;
	//Текст
	private String txt;
}
