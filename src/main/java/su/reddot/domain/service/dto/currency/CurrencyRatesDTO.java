package su.reddot.domain.service.dto.currency;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(value = "AdminCurrencyRatesList", description = "Currency Rates List info for adminpanel")
public class CurrencyRatesDTO {

    private List<CurrencyRateDTO> rates;
}
