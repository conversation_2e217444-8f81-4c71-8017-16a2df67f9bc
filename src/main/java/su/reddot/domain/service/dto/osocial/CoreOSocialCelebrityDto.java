package su.reddot.domain.service.dto.osocial;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.community.CommunityBadge;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialCelebrityDto extends CoreOSocialUserDto {

    private String followersCountStr;

    public CoreOSocialCelebrityDto(
            long id,
            String nickname,
            String avatarPath,
            CommunityBadge communityBadge,
            boolean followedByMe,
            int productsCount,
            String followersCountStr,
            boolean pro,
            User.Sex sex,
            String email,
            Boolean isTrusted) {
        super(id, nickname, avatarPath, communityBadge, followedByMe, productsCount, pro, sex, email, isTrusted);
        this.followersCountStr = followersCountStr;
    }
}
