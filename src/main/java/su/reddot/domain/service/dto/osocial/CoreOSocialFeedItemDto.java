package su.reddot.domain.service.dto.osocial;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;
import su.reddot.domain.service.social.model.FeedItemType;

@Data
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialFeedItemDto {
    private FeedItemType type;
    private CoreOSocialFeedEmbeddable content;
}