package su.reddot.domain.service.dto;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.experimental.Accessors;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.infrastructure.util.Utils;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BankPaymentDTO {
	private String paymentVersion;

	private Long paymentId;

	private TransactionState paymentState;

	private Long orderId;

	private OrderState orderState;

	private String bank;

	private String bankTransactionId;

	private String rrn;

	private String paymentSystemCode;

	// конечная сумма в копейках отправленная по банк апи
	private Long amountSentViaApi;
	// комиссия банка по операции
	private Long fee;

	private String originalCurrencyCode;

	private Long originalCurrencyAmount;

	private String rawResponse;

	private UUID uuid;

	private ZonedDateTime paymentSystemTime;

	public BankPaymentDTO(String paymentVersion, Long paymentId, TransactionState paymentState, Long orderId, OrderState orderState, String bank, String bankTransactionId) {
		this.paymentVersion = paymentVersion;
		this.paymentId = paymentId;
		this.paymentState = paymentState;
		this.orderId = orderId;
		this.orderState = orderState;
		this.bank = bank;
		this.bankTransactionId = bankTransactionId;
	}

	@JsonIgnore
	public LocalDateTime getPaymentSystemTimeLdt() {
		return paymentSystemTime == null ? null : paymentSystemTime.toLocalDateTime();
	}

	public BankPaymentDTO setPaymentSystemTimeFormatted(ZonedDateTime paymentSystemTime) {
		this.paymentSystemTime = Utils.formatToUTC(paymentSystemTime);
		return this;
	}
}
