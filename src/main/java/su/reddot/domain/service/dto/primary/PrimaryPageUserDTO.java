package su.reddot.domain.service.dto.primary;

/*
 * Created by <PERSON> on 22.03.2022
 */

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.service.dto.community.CommunityBadge;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PrimaryPageUserDTO {

    @ApiModelProperty(notes = "Первичный ключ", required = true)
    private Long id;
    @ApiModelProperty(notes = "Имя пользователя", required = true)
    private String name;
    @ApiModelProperty(notes = "Ник пользователя", required = true)
    private String nickName;
    @ApiModelProperty(notes = "Путь к изображению пользователя", required = true)
    private String avatarPath;
    @ApiModelProperty(notes = "[DEPRECATED] Тип пользователя / Бутик/Частный продавец", required = true)
    private String type;
    @ApiModelProperty(notes = "Доверенный ли пользователь", required = true)
    private Boolean isTrusted = false;
    @ApiModelProperty(notes = "Подписан ли пользователь, на данного пользователя", required = true)
    private Boolean isSubscribed = false;
    @ApiModelProperty(notes = "Информация о принадлежности к сообществу")
    private CommunityBadge communityBadge;
    @ApiModelProperty(notes = "Вид продавца", required = true)
    private SellerType sellerType;
}
