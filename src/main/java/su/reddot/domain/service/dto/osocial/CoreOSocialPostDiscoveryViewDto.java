package su.reddot.domain.service.dto.osocial;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CoreOSocialPostDiscoveryViewDto implements CoreOSocialDiscoveryEmbeddable {
    private long id;
    private CoreOSocialUserShortViewDto author;
    private CoreOSocialMediaDto previewMedia;
    private boolean multipleMedia;
    private Double rating;
}