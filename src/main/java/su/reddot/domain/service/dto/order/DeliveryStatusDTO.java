package su.reddot.domain.service.dto.order;

import java.time.ZonedDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Getter
@Builder
public class DeliveryStatusDTO {

	/**
	 * Имя КС (например, "<PERSON><PERSON>").
	 */
	@NonNull
	private String deliveryCompany;

	/**
	 * ID накладной, назначаемый КС.
	 */
	private String waybillExternalId;

	/**
	 * ID заказа накладной, назначаемый КС.
	 */
	private String waybillOrderExternalId;

	/**
	 * Время изменения статуса доставки.
	 */
	@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
	@NonNull
	private ZonedDateTime statusTime;

	private String trackingState;

	private String trackingComment;

	private ZonedDateTime trackingTime;
}
