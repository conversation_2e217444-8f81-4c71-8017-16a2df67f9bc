package su.reddot.domain.service.dto.notification;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.infrastructure.util.Utils;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Getter @Setter @AllArgsConstructor @RequiredArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class NotificationDTO {
	private Long id;
	private String type; //Тип уведомления (имя класса, например HowItWorksNotification)

	//Иконка основная. Обычно используется для аватарки инициатора события или логотип Оскелли "О"
	private String mainIcon;
	//Мелкая иконка, показывающая смысл уведомления (комментарий, покупка)
	private String tinyIcon;

	private UserDTO initiator;
	private UserDTO targetUser;
	private String guestToken;

	/**
	 * Целевой объект (товар, комментарий, пользователь, оффер, заказ и т.п.)
	 */
	private Object targetObject;

	//Картинка с объектом, в отношении которого было выполнено действие. Для заказа это картинка товара.
	private String targetObjectImage;

	//Короткий текст поверх картинки объекта. Для заказа "+3" означет, что всего в заказе 4 позиции.
	private String targetObjectImageHint;

	//Тип объекта
	private String targetObjectType;
	private Long targetObjectId;
	private String targetObjectUrl;
	@JsonFormat(shape=JsonFormat.Shape.NUMBER)
	private ZonedDateTime createTime;

	@JsonFormat(shape=JsonFormat.Shape.NUMBER)
	private ZonedDateTime readTime;
	private boolean isRead = false;

	private boolean isDeleted = false;

	private Integer counter;

	private boolean needAction = false;
	@JsonFormat(shape=JsonFormat.Shape.NUMBER)
	private ZonedDateTime actionCompletedTime;
	private boolean isActionCompleted = false;

	//Первый заголовок (обычно имя пользователя)
	private String title;
	//Второй заголовок (обычно тема уведомления)
	private String subTitle;
	//Укороченное сообщение уведомления
	private String shortMessage;
	//Полное сообщение уведомление
	private String message;

	//Метаданные для заполнения форм
	private ModificationMetadata modificationMetadata;

	//Метаданные для нестандартных уведомлений
	private Object metadata;

	//Изображения, например для отображения в комментариях
	private List<String> images;

	private MessageFormat messageFormat;

	private String params;

	public Long getTargetUserId() {
		return Optional.ofNullable(getTargetUser())
				.map(UserDTO::getId)
				.orElse(null);
	}

	@Override
	public boolean equals(Object another){
		if (another == this) return true;
		if(!(another instanceof NotificationDTO)) return false;
		return equals((NotificationDTO) another);
	}
	public boolean equals(NotificationDTO another){
		if(another == null) return false;
		return Utils.equals(id, another.id);
	}
	@Override
	public int hashCode() {
		return Objects.hash(id);
	}

	@Override
	public String toString() {
		return "NotificationDTO{" +
				"id=" + id +
				", type='" + type + '\'' +
				", mainIcon='" + mainIcon + '\'' +
				", tinyIcon='" + tinyIcon + '\'' +
				", initiator=" + initiator +
				", targetUser=" + targetUser +
				", guestToken='" + guestToken + '\'' +
				", targetObject=" + targetObject +
				", targetObjectImage='" + targetObjectImage + '\'' +
				", targetObjectImageHint='" + targetObjectImageHint + '\'' +
				", targetObjectType='" + targetObjectType + '\'' +
				", targetObjectId=" + targetObjectId +
				", targetObjectUrl='" + targetObjectUrl + '\'' +
				", createTime=" + createTime +
				", readTime=" + readTime +
				", isRead=" + isRead +
				", needAction=" + needAction +
				", actionCompletedTime=" + actionCompletedTime +
				", isActionCompleted=" + isActionCompleted +
				", title='" + title + '\'' +
				", subTitle='" + subTitle + '\'' +
				", shortMessage='" + shortMessage + '\'' +
				", message='" + message + '\'' +
				", modificationMetadata=" + modificationMetadata +
				", metadata=" + metadata +
				", images=" + images +
				", messageFormat=" + messageFormat +
				'}';
	}
}
