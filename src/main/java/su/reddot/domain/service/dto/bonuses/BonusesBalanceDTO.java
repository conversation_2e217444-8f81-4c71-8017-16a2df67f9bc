package su.reddot.domain.service.dto.bonuses;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

import static java.math.BigDecimal.ZERO;

@Getter
@Setter
@ToString
@AllArgsConstructor
@RequiredArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BonusesBalanceDTO {
	private BonusesAmountDTO amount;
	private BigDecimal burningAmount;

	public static BonusesBalanceDTO createZeroBalance() {
		return new BonusesBalanceDTO(new BonusesAmountDTO(ZERO, ZERO, ZERO), ZERO);
	}
}
