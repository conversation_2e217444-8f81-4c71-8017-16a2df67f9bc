package su.reddot.domain.service.dto.primary.converter;

import com.google.common.collect.Streams;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageProductDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageProductRequestDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageUserDTO;
import su.reddot.domain.service.dto.productrequest.ProductRequestDTO;
import su.reddot.domain.service.util.ProductSizeUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class PrimaryPageProductDtoConverter {

    private String sellerTypeTitle;
    private String proSellerTypeTitle;

    MessageSourceAccessor messageSourceAccessor;

    public PrimaryPageProductDtoConverter(MessageSourceAccessor messageSourceAccessor) {
        this.messageSourceAccessor = messageSourceAccessor;

        sellerTypeTitle = this.messageSourceAccessor.getMessage("entity.enum.Role.SIMPLE_USER.role");
        proSellerTypeTitle = this.messageSourceAccessor.getMessage("entity.enum.Role.BOUTIQUE.role");
    }

    public List<PrimaryPageProductDTO> convertFrom(Iterable<ProductDTO> products) {
        if (products == null) {
            return Collections.emptyList();
        }

        return Streams.stream(products)
                .map(this::convertFrom)
                .collect(Collectors.toList());
    }

    public List<PrimaryPageProductDTO> convertFromDtoList(Iterable<ProductDTO> products) {
        if (products == null) {
            return Collections.emptyList();
        }

        return Streams.stream(products)
                .map(this::convertFrom)
                .collect(Collectors.toList());
    }

    public PrimaryPageProductDTO convertFrom(ProductDTO product) {
        if (product == null) {
            return null;
        }

        return new PrimaryPageProductDTO()
                .setId(product.getProductId())
                .setName(product.getName())
                .setImgPath(product.getPrimaryImageUrl())
                .setBrandName(product.getBrand().getName())
                .setCategoryName(product.getCategory().getDisplayName())
                .setPrice(product.getPrice().intValue())
                .setOldPrice(product.getHigherPrice() != null ? product.getHigherPrice().intValue() : null)
                .setDiscount(product.getDiscount())
                .setIsLiked(product.getIsLiked())
                .setLikeCount(product.getLikesCount())
                .setWithBadge(product.getConditionId() == 1)
                .setFormattedSize(ProductSizeUtils.reduceProductSizes(product.getSizes(), this.messageSourceAccessor))
                .setSizeType(product.getSizeType().sizeType())
                .setSizes(product.getSizes())
                .setConditionId(product.getConditionId())
                .setConditionName(product.getConditionName())
                .setSeller(new PrimaryPageUserDTO()
                        .setId(product.getSeller().getId())
                        .setName(product.getSeller().getName())
                        .setNickName(product.getSeller().getNickname())
                        .setAvatarPath(product.getSeller().getAvatarPath())
                        .setType(product.getSeller().getIsPro() ? proSellerTypeTitle : sellerTypeTitle)
                        .setIsTrusted(product.getSeller().getIsTrusted())
                        .setIsSubscribed(product.getSeller().getIsFollowed())
                        .setCommunityBadge(product.getSeller().getCommunityBadge())
                        .setSellerType(product.getSeller().getSellerType()))
                .setSplit(product.getSplit())
                .setTabbySplit(product.getTabbySplit())
                .setYandexPlus(product.getYandexPlus())
                .setExclusiveSelectionTime(product.getExclusiveSelectionTime())
                .setExclusiveSelectionTimeForLowStatuses(product.getExclusiveSelectionTimeForLowStatuses());

    }

    public PrimaryPageProductRequestDTO convertFrom(ProductRequestDTO productRequest) {
        if (productRequest == null) {
            return null;
        }

        return new PrimaryPageProductRequestDTO()
                .setId(productRequest.getId())
                .setProductRequest(productRequest);
    }
}
