package su.reddot.domain.service.brand;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.BrandCategoryDescriptionRepository;
import su.reddot.domain.dao.category.CategoryRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.BrandCategoryDescription;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.service.catalog.CatalogCategory;
import su.reddot.domain.service.dto.BrandDescriptionDto;
import su.reddot.domain.service.dto.CategoryDisplayNameDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BrandDescriptionServiceImpl implements BrandDescriptionService {

    private final BrandCategoryDescriptionRepository brandCategoryDescriptionRepository;
    private final CategoryRepository categoryRepository;
    private final BrandProperties brandProperties;


    @Override
    public List<BrandDescriptionDto> getDescriptionsForBrand(Brand brand) {
        return brandCategoryDescriptionRepository.findAllByBrandId(brand.getId())
                .stream()
                .map(this::getBrandDescriptionDto)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<String> getBrandDescriptionForCategory(Brand brand, CatalogCategory category) {
        return brandCategoryDescriptionRepository.findFirstByCategoryIdAndBrandId(category.getId(), brand.getId())
                .map(BrandCategoryDescription::getDescription);
    }

    @Override
    public List<BrandCategoryDescription> saveDescriptions(Brand brand, List<BrandDescriptionDto> brandDescriptions) {
        List<BrandCategoryDescription> brandCategoryDescriptions = new ArrayList<>(brandDescriptions.size());
        for (BrandDescriptionDto brandDescription : brandDescriptions) {
            Optional<BrandCategoryDescription> brandCategoryDescriptionStored = brandCategoryDescriptionRepository.findFirstByCategoryIdAndBrandId(brandDescription.getCategory().getId(), brand.getId());
            BrandCategoryDescription brandCategoryDescription = brandCategoryDescriptionStored.orElse(new BrandCategoryDescription());
            Category category = categoryRepository.getOne(brandDescription.getCategory().getId());
            brandCategoryDescription.setBrand(brand);
            brandCategoryDescription.setCategory(category);
            brandCategoryDescription.setDescription(brandDescription.getDescription());
            brandCategoryDescriptions.add(brandCategoryDescription);
        }
        return brandCategoryDescriptionRepository.saveAll(brandCategoryDescriptions);
    }

    @Override
    public List<CategoryDisplayNameDTO> getAvailableSpecificDescriptionCategories() {
        return categoryRepository.findAllById(brandProperties.getCategoriesWithSpecificDescriptionIds())
                .stream()
                .map(this::getAvailableCategoryDTO)
                .collect(Collectors.toList());
    }

    private BrandDescriptionDto getBrandDescriptionDto(BrandCategoryDescription brandCategoryDescription) {
        CategoryDisplayNameDTO category = new CategoryDisplayNameDTO(
                brandCategoryDescription.getCategory().getId(),
                brandCategoryDescription.getCategory().getDisplayName()
        );
        return new BrandDescriptionDto(category, brandCategoryDescription.getDescription());
    }

    private CategoryDisplayNameDTO getAvailableCategoryDTO(Category category) {
        return new CategoryDisplayNameDTO(category.getId(), category.getDisplayName());
    }
}