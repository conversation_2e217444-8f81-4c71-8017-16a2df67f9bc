package su.reddot.domain.service.ratelimiter;

import java.time.Duration;

/**
 * Сервис для контроля частоты действий, использует Redis для отслеживания.
 */
public interface RateLimiter {

    /**
     * Регистрирует действие, если лимит для ключа не превышен, иначе - бросает RateLimiterException
     *
     * @param key ключ действия.
     * @param limit максимальное количество допустимых действий в заданном временном окне.
     * @param window временное окно в секундах, в течение которого осуществляется подсчёт действий.
     * @throws RateLimiterException
     */
    void registerAction(String key, long limit, Duration window);
}