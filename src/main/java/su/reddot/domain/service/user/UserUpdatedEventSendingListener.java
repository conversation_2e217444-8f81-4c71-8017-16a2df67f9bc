package su.reddot.domain.service.user;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import ru.oskelly.common.messaging.messages.users.UserUpdatedEvent;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserUpdatedEventState;
import su.reddot.domain.service.ComponentRegistry;

import javax.persistence.PostLoad;
import javax.persistence.PostPersist;
import javax.persistence.PostUpdate;
import java.util.Objects;

/**
 * Слушатель, отправляющий событие об изменении пользователя в топик Kafka.
 * Отправка события осуществляется после анализа произошедших изменений.
 * Отправка события синхронизована с коммитом транзакции.
 */
@Slf4j
public class UserUpdatedEventSendingListener {

    @PostLoad
    private void onPostLoad(User user) {

        log.debug("UserUpdatedEventSendingListener.onPostLoad user " + user.getId());

        ComponentRegistry.getMicrometerService().record("UserUpdatedEventSendingListener.onPostLoad", () -> saveState(user));
    }

    private void saveState(User user) {

        // Если состояние объекта уже было сохранено, ничего не делаем. Это значит, что он уже загружен в контекст.
        if (user.getUpdatedEventState() != null) {
            return;
        }

        ComponentRegistry.getMicrometerService().increment("UserUpdatedEventSendingListener.saveState.setOnLoadState");

        user.setUpdatedEventState(
                new UserUpdatedEventState(
                        user.getId(),
                        user.getNickname()));
    }

    @PostPersist
    private void onPostCreate(User user) {
        log.debug("UserUpdatedEventSendingListener.onPostCreate user " + user.getId());
        publishEventIfNeed(user, true);
    }

    @PostUpdate
    private void onPostUpdate(User user) {
        log.debug("UserUpdatedEventSendingListener.onPostUpdate user " + user.getId());
        publishEventIfNeed(user, false);
    }

    private void publishEventIfNeed(User user, boolean newUser) {

        if (!ComponentRegistry.getKafkaConfigVariables().isEnabled()
                || user.isUserUpdatedEventSent()) {
            return;
        }

        //защита от повторной отправки
        user.setUserUpdatedEventSent(true);

        if (newUser || isUserChanged(user)) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    ComponentRegistry.getKafkaSenderService()
                            .sendObjectMessageSilent(
                                    ComponentRegistry.getEnvironment().getProperty("app.kafka.user-updated-events-topic.name"),
                                    "",
                                    new UserUpdatedEvent(user.getId(), user.getNickname()));
                    log.debug("User updated event for user " + user.getId() + " sent");
                }
            });
        }
    }

    private boolean isUserChanged(User user) {
        UserUpdatedEventState onLoadState = user.getUpdatedEventState();
        if (onLoadState == null) {
            return false;
        }
        return !Objects.equals(onLoadState.getNickname(), user.getNickname());
    }
}
