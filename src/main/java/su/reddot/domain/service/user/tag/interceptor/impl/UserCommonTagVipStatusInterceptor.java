package su.reddot.domain.service.user.tag.interceptor.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import su.reddot.domain.dao.uservipstatus.UserVipStatusRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.vipstatus.UserVipStatus;
import su.reddot.domain.model.user.vipstatus.VipStatusManagementMode;
import su.reddot.domain.service.adminpanel.tag.UserCommonTagService;
import su.reddot.domain.service.user.tag.interceptor.UserCommonTagInterceptor;
import su.reddot.domain.service.userattribute.UserAttributeValueService;

import java.time.ZonedDateTime;


@Component
@RequiredArgsConstructor
public class UserCommonTagVipStatusInterceptor implements UserCommonTagInterceptor {

    private final UserVipStatusRepository userVipStatusRepository;
    private final UserAttributeValueService userAttributeValueService;

    @Override
    public void afterAdding(User user) {
        changeVipStatusManagementMode(user);
    }

    @Override
    public void afterRemoval(User user) {
        changeVipStatusManagementMode(user);
    }

    @Override
    public String getTag() {
        return UserCommonTagService.VIP_STATUS_TAG_CODE;
    }

    @Override
    public boolean isUserCommonTag() {
        return true;
    }

    private void changeVipStatusManagementMode(@NonNull User user) {
        UserVipStatus userVipStatus = userVipStatusRepository.findByUserId(user.getId())
                .orElse(UserVipStatus.builder()
                        .userId(user.getId())
                        .build()
                );
        userVipStatus.setManagementMode(VipStatusManagementMode.ADMIN);
        userVipStatus.setManagementModeUpdateTime(ZonedDateTime.now());

        userVipStatusRepository.save(userVipStatus);

        userAttributeValueService.forceUseDeskUserSync(user.getId());
    }
}
