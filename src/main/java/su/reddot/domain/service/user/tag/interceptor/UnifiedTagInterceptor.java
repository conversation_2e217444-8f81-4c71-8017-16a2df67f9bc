package su.reddot.domain.service.user.tag.interceptor;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Обработчик добавления или удаления тега пользователя
 */
@Component
public class UnifiedTagInterceptor extends AbstractUnifiedTagInterceptor {
    public UnifiedTagInterceptor(List<TagInterceptor> interceptors) {
        super(interceptors.stream()
                .filter(interceptor -> !interceptor.isUserCommonTag())
                .collect(Collectors.toMap(TagInterceptor::getTag, interceptor -> interceptor)));
    }
}
