package su.reddot.domain.service.user.trustinfo;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.usertrustinfo.UserTrustInfoRepository;
import su.reddot.domain.model.user.trust.UserTrustInfo;
import su.reddot.domain.model.user.trust.UserTrustInfoManagementMode;
import su.reddot.domain.service.dto.usertrustinfo.UserTrustInfoDTO;
import su.reddot.domain.service.user.trustinfo.mapper.UserTrustInfoMapper;

import java.time.ZonedDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserTrustInfoService {

    private final UserTrustInfoRepository userTrustInfoRepository;
    private final UserTrustInfoMapper userTrustInfoMapper;

    @Transactional
    public void markAsAdminManaged(@NonNull Long userId) {
        UserTrustInfo userTrustInfo = userTrustInfoRepository.findByUserId(userId)
                .orElse(UserTrustInfo.builder()
                        .userId(userId)
                        .build()
                );
        userTrustInfo.setManagementMode(UserTrustInfoManagementMode.ADMIN);
        userTrustInfo.setManagementModeUpdateTime(ZonedDateTime.now());
        userTrustInfoRepository.save(userTrustInfo);
    }

    @Transactional(readOnly = true)
    public UserTrustInfoDTO getUserTrustInfoDto(@NonNull Long userId) {
        return userTrustInfoRepository.findByUserId(userId)
                .map(userTrustInfoMapper::toDto)
                .orElse(null);
    }
}
