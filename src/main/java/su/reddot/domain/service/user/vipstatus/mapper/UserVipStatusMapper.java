package su.reddot.domain.service.user.vipstatus.mapper;

import org.springframework.stereotype.Component;
import su.reddot.domain.model.user.vipstatus.UserVipStatus;
import su.reddot.domain.service.dto.uservipstatus.UserPurchaseVolumeDTO;
import su.reddot.domain.service.dto.uservipstatus.UserVipStatusDTO;
import su.reddot.domain.service.dto.uservipstatus.VipStatusManagementModeDTO;

import java.time.LocalDate;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class UserVipStatusMapper {

    public UserVipStatusDTO toDto(UserVipStatus entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        return UserVipStatusDTO.builder()
                .managementMode(VipStatusManagementModeDTO.valueOf(entity.getManagementMode().name()))
                .managementModeUpdateTime(entity.getManagementModeUpdateTime().toOffsetDateTime())
                .purchaseVolumes(entity.getPurchaseVolumes()
                        .stream()
                        .map(this::toDto)
                        .collect(Collectors.toList())
                )
                .build();
    }

    public UserPurchaseVolumeDTO toDto(UserVipStatus.UserPurchaseVolume entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        return UserPurchaseVolumeDTO.builder()
                .year(LocalDate.now()
                        .minusYears(entity.getYearOrder() - 1)
                        .getYear()
                )
                .amount(entity.getAmount())
                .build();
    }
}
