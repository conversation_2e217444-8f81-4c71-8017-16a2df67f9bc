package su.reddot.domain.service.user.userban.specification;

/*
 * Created by <PERSON> on 2/17/2021
 */

import org.springframework.data.jpa.domain.Specification;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.dto.userban.AdminPanelBanSearchFilerDTO;

import java.io.Serializable;

/**
 * Класс представляет собой спецификацию критерий для поиска моделей {@link UserBan}
 */

public class AdminPanelBanSearchSpecification implements Serializable {


    public Specification<UserBan> searchByFilter(AdminPanelBanSearchFilerDTO filer) {

        Specification<UserBan> spec = Specification.where(null);

        if (filer != null) {

            if (filer.getBanIds() != null && !filer.getBanIds().isEmpty()) {
                spec = spec.and((r, q, cb) -> cb.isTrue(r.get("id").in(filer.getBanIds())));
            }


            if (filer.getUserIds() != null && !filer.getUserIds().isEmpty()) {
                spec = spec.and((r, q, cb) -> cb.isTrue(r.get("userId").in(filer.getUserIds())));
            }

            if (filer.getBanTypes() != null && !filer.getBanTypes().isEmpty()) {
                spec = spec.and((r, q, cb) -> cb.isTrue(r.get("banType").in(filer.getBanTypes())));
            }

            if (filer.getIsBaned() != null) {
                spec = spec.and((r, q, cb) -> cb.equal(r.get("isBaned"), filer.getIsBaned()));
            }

            if (filer.getIsDeleted() != null) {
                spec = spec.and((r, q, cb) -> cb.equal(r.get("isDeleted"), filer.getIsDeleted()));
            }
        }

        return spec;
    }
}