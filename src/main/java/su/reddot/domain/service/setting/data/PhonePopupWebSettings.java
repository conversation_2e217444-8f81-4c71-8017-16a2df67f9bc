package su.reddot.domain.service.setting.data;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PhonePopupWebSettings {
    private Boolean addToFavorites;
    private Boolean makeOrder;
    private Boolean productSceneContactSeller;
    private Boolean productSceneSubscribe;
    private Boolean publishProduct;
    private Boolean blackStick;
}