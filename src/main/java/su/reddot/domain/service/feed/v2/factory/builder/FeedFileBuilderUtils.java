package su.reddot.domain.service.feed.v2.factory.builder;

import com.fasterxml.jackson.dataformat.csv.CsvGenerator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import lombok.SneakyThrows;

import java.io.BufferedWriter;
import java.io.File;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

public abstract class FeedFileBuilderUtils {

    @SneakyThrows
    public static BufferedWriter createBufferWriter(String localFileName) {
        File file = new File(localFileName);
        File parentFile = file.getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        return new BufferedWriter(new OutputStreamWriter(
                Files.newOutputStream(Paths.get(localFileName)),
                StandardCharsets.UTF_8));
    }

    @SneakyThrows
    public static CsvGenerator createCsvGenerator(CsvMapper csvMapper, BufferedWriter bufferedWriter, Class<?> pojoType) {
        CsvGenerator generator = csvMapper.getFactory().createGenerator(bufferedWriter);
        generator.setSchema(csvMapper.schemaFor(pojoType).withHeader());
        return generator;
    }
}
