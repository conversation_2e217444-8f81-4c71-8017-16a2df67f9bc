package su.reddot.domain.service.feed.v2.configurationProperties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConditionalOnExpression("${app.feed-db.enabled} and ${app.feed-db.export.enabled} and ${app.feed-db.export.product.v2.admitad.enabled}")
@Component
@ConfigurationProperties(prefix = "app.feed-db.export.product.v2.admitad.static-fields")
@Getter
@Setter
public class AdmitadXmlFeedStaticFieldsConfigurationProperties extends AbstractXmlFeedStaticFieldContainer {
}
