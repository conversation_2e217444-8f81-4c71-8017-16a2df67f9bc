package su.reddot.domain.service.feed.order;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.feed.dao.FeedOrderRepository;
import su.reddot.domain.feed.dao.FeedOrderYandexMetrikaCsvExportLogRepository;
import su.reddot.domain.feed.model.FeedOrderYandexMetrikaCsvExportLog;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;

@Component
@ConditionalOnProperty("app.feed-db.enabled")
@RequiredArgsConstructor
public class FeedOrderYandexMetrikaCsvExportLogService {

    private static final long LOCK_ROWS_COUNT_LIMIT = 100_000L;
    private static final List<FeedOrderYandexMetrikaCsvExportLog.Status> EXCLUDED_LOG_STATUSES =
            Collections.singletonList(FeedOrderYandexMetrikaCsvExportLog.Status.FAILED);

    private final FeedOrderYandexMetrikaCsvExportLogRepository exportLogRepository;
    private final FeedOrderRepository feedOrderRepository;

    public synchronized FeedOrderYandexMetrikaCsvExportLog initCsvExport(UUID jobRunUUID) {
        FeedOrderYandexMetrikaCsvExportLog yandexLogEntity = new FeedOrderYandexMetrikaCsvExportLog()
                .setJobRunUUID(jobRunUUID.toString())
                .setStartTime(LocalDateTime.now())
                .setStatus(FeedOrderYandexMetrikaCsvExportLog.Status.IN_PROGRESS);
        return exportLogRepository.save(yandexLogEntity);
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public boolean isCsvExportNeed(Function<LocalDateTime, LocalDateTime> expectedStartTimeGetter) {
        Optional<FeedOrderYandexMetrikaCsvExportLog> firstLog = exportLogRepository.findFirstByStatusNotInOrderByStartTimeDesc(EXCLUDED_LOG_STATUSES);
        return firstLog
                .map(FeedOrderYandexMetrikaCsvExportLog::getStartTime)
                .map(expectedStartTimeGetter)
                .map(expectedTime -> LocalDateTime.now().isAfter(expectedTime))
                .orElse(true);
    }

    public FeedOrderYandexMetrikaCsvExportLog makeCsvExportFail(FeedOrderYandexMetrikaCsvExportLog exportLog) {
        exportLog.setStatus(FeedOrderYandexMetrikaCsvExportLog.Status.FAILED);
        exportLog.setFinishTime(LocalDateTime.now());
        return exportLogRepository.save(exportLog);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED)
    public Integer lockNotExportedOrders(FeedOrderYandexMetrikaCsvExportLog exportLog) {
        return feedOrderRepository.setYandexLogIdForUnsentOrders(exportLog.getId(), LOCK_ROWS_COUNT_LIMIT);
    }

    public FeedOrderYandexMetrikaCsvExportLog makeCsvExportSent(FeedOrderYandexMetrikaCsvExportLog exportLog) {
        exportLog.setStatus(FeedOrderYandexMetrikaCsvExportLog.Status.SENT);
        exportLog.setFinishTime(LocalDateTime.now());
        return exportLogRepository.save(exportLog);
    }
}
