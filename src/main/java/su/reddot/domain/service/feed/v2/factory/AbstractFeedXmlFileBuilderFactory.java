package su.reddot.domain.service.feed.v2.factory;

import org.springframework.beans.factory.annotation.Value;
import su.reddot.domain.service.feed.v2.configurationProperties.AbstractXmlFeedStaticFieldContainer;
import su.reddot.presentation.DeeplinkUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

public abstract class AbstractFeedXmlFileBuilderFactory {

    private static final String HEADER = "<?xml version=\"1.0\"?>\n" +
            "<rss xmlns:g=\"http://base.google.com/ns/1.0\" version=\"2.0\">\n" +
            "\t<channel>";

    private static final String FOOTER = "\t</channel>\n" +
            "</rss>";

    private static final String TITLE_TAG = "<title>%s</title>";
    private static final String LINK_TAG = "<link>%s</link>";
    private static final String DESCRIPTION_TAG = "<description><![CDATA[%s]]></description>";

    @Value("${app.feed-db.export.product.v2.title}")
    private String title;
    @Value("${app.feed-db.export.product.v2.link}")
    private String link;
    @Value("${app.feed-db.export.product.v2.description}")
    private String description;

    protected final List<String> staticFields;
    protected final AbstractXmlFeedStaticFieldContainer staticFieldContainer;
    protected final DeeplinkUtils deeplinkUtils;

    protected AbstractFeedXmlFileBuilderFactory(AbstractXmlFeedStaticFieldContainer staticFieldContainer,
                                                DeeplinkUtils deeplinkUtils) {
        this.staticFields = staticFieldContainer != null ? staticFieldContainer.getAllStaticFields() : Collections.emptyList();
        this.staticFieldContainer = staticFieldContainer;
        this.deeplinkUtils = deeplinkUtils;
    }


    protected String getContentBeforeItems() {
        return HEADER + getTagsBeforeItems();
    }

    private String getTagsBeforeItems() {
        return String.join("", Arrays.asList(
                String.format(TITLE_TAG, title),
                String.format(LINK_TAG, link),
                String.format(DESCRIPTION_TAG, getDescriptionValue())
        ));
    }

    protected String getContentAfterItems() {
        return FOOTER;
    }

    protected Function<Long, String> getProductLinkIfMobileLinkEnabledGetter() {
        if (staticFieldContainer.isMobileLinkEnabled()) {
            return deeplinkUtils::getProductDeeplinkWithHost;
        }
        return null;
    }

    protected String getDescriptionValue() {
        return description;
    }

}
