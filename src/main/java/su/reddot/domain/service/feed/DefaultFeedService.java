package su.reddot.domain.service.feed;

import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.helpers.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.ExportException;
import su.reddot.domain.feed.dao.FeedOrderPositionRepository;
import su.reddot.domain.feed.dao.FeedProductItemRepository;
import su.reddot.domain.feed.dao.FeedProductRepository;
import su.reddot.domain.feed.dao.FeedUserRepository;
import su.reddot.domain.feed.model.FeedOrderPosition;
import su.reddot.domain.feed.model.FeedProduct;
import su.reddot.domain.feed.model.FeedProductItem;
import su.reddot.domain.feed.model.FeedUser;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.service.feed.orderPosition.mindbox.MindboxCSVOrderOrderPositionRecordExporter;
import su.reddot.domain.service.feed.orderPosition.mindbox.MindboxCSVSaleOrderPositionRecordExporter;
import su.reddot.domain.service.feed.product.exporter.FeedProductItemRecordExporter;
import su.reddot.domain.service.feed.product.exporter.v2.FeedV2ProductGoogleRecordExporter;
import su.reddot.domain.service.feed.product.exporter.v2.FeedV2ProductRecordExporter;
import su.reddot.domain.service.feed.product.exporter.v2.FeedV2ProductVKRecordExporter;
import su.reddot.domain.service.feed.product.exporter.v2.FeedV2ProductYandexRecordExporter;
import su.reddot.domain.service.feed.user.mindbox.MindboxCSVUserRecordExporter;
import su.reddot.infrastructure.util.Utils;

import javax.annotation.PostConstruct;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@ConditionalOnProperty("app.feed-db.enabled")
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultFeedService implements FeedService {

	private final FeedProductRepository feedProductRepository;
	private final FeedProductItemRepository feedProductItemRepository;
	private final ProductRepository productRepository;
	private final FeedUserRepository feedUserRepository;
	private final FeedOrderPositionRepository feedOrderPositionRepository;
	private final UserRepository userRepository;
	private final OrderPositionRepository orderPositionRepository;
	private final MindboxCSVUserRecordExporter mindboxCSVUserRecordExporter;
	private final MindboxCSVOrderOrderPositionRecordExporter mindboxCSVOrderOrderPositionRecordExporter;
	private final MindboxCSVSaleOrderPositionRecordExporter mindboxCSVSaleOrderPositionRecordExporter;
	private final FeedV2ProductRecordExporter feedV2ProductRecordExporter;
	private final FeedV2ProductYandexRecordExporter feedV2ProductYandexRecordExporter;
	private final FeedV2ProductVKRecordExporter feedV2ProductVKRecordExporter;
	private final FeedV2ProductGoogleRecordExporter feedV2ProductGoogleRecordExporter;
	private final FeedProductItemRecordExporter feedProductItemExporter;
	private final MessageSourceAccessor messageSourceAccessor;

	private final JdbcTemplate jdbcTemplate;

	@Autowired @Lazy
	private FeedService self;

	@Value("${internationalVersion}")
	private Boolean internationalVersion;

	private Map<Field, FeedRecordExporter> feedProductExportersMap;

	private Map<BiConsumer<FeedProductItem, String>, FeedMultiRecordsExporter> feedProductItemExportersMap;
	
	private Map<Field, FeedRecordExporter> feedUserExportersMap;

	//Заказы и продажи синхронизируются совместно в FeedOrder
	private Map<Field, FeedRecordExporter> feedOrderPositionExportersMap;

	@PostConstruct
	public void init(){
		feedProductExportersMap = new HashMap<Field, FeedRecordExporter>(){{
			try {
				put(FeedProduct.class.getField("mindboxYml"),
					new SQLFileQueryTemplateBasedFeedRecordExporter(
						jdbcTemplate,
						internationalVersion
							? "sql/feed/product/yml/mindbox/mindbox_yml_int.sql"
							: "sql/feed/product/yml/mindbox/mindbox_yml.sql"
					)
				);
				put(FeedProduct.class.getField("yandexProductFeedYml"),
					new SQLFileQueryTemplateBasedFeedRecordExporter(
						jdbcTemplate,
						internationalVersion
							? "sql/feed/product/yml/productfeed/yandex_product_feed_yml_int.sql"
							: "sql/feed/product/yml/productfeed/yandex_product_feed_yml.sql"
					)
				);
				put(FeedProduct.class.getField("feedV2Xml"), feedV2ProductRecordExporter);
				put(FeedProduct.class.getField("feedV2YandexXml"), feedV2ProductYandexRecordExporter);
				put(FeedProduct.class.getField("feedV2VKXml"), feedV2ProductVKRecordExporter);
				put(FeedProduct.class.getField("feedV2GoogleXml"), feedV2ProductGoogleRecordExporter);
			} catch (NoSuchFieldException e) {
				log.error("Невозможно инициировать экспортеры feedProductExportersMap", e);
			}
		}};
		feedProductItemExportersMap = ImmutableMap.of(FeedProductItem::setFeedXml, feedProductItemExporter);
		feedUserExportersMap = new HashMap<Field, FeedRecordExporter>(){{
			try {
				put(FeedUser.class.getField("mindboxCsv"), mindboxCSVUserRecordExporter);
			} catch (NoSuchFieldException e) {
				log.error("Невозможно инициировать экспортеры feedUserExportersMap", e);
			}
		}};
		feedOrderPositionExportersMap = new HashMap<Field, FeedRecordExporter>(){{
			try {
				//Заказы и продажи синхронизируются совместно в FeedOrderPosition (в разные поля)
				put(FeedOrderPosition.class.getField("mindboxOrdersOrderPositionCsv"), mindboxCSVOrderOrderPositionRecordExporter);
				put(FeedOrderPosition.class.getField("mindboxSalesOrderPositionCsv"), mindboxCSVSaleOrderPositionRecordExporter);
			} catch (NoSuchFieldException e) {
				log.error("Невозможно инициировать экспортеры feedOrderExportersMap", e);
			}
		}};
	}

	@Transactional
	@Override
	public boolean createOrUpdateFeedProduct(long productId){
		LocalDateTime productChangeTime = productRepository.getProductChangeTimeById(productId);
		if(productChangeTime == null) productChangeTime = Utils.nowAtUTC();
		FeedProduct record = feedProductRepository.findById(productId).orElse(null);
		if(record == null){
			log.debug("Creating new FeedProduct: " + productId);
			record = new FeedProduct(productId, Utils.nowAtUTC());
		}
		else{
			log.debug("Updating FeedProduct: " + productId);
		}
		final FeedProduct rec = record;
		feedProductExportersMap.forEach((field, exporter) -> {
			String recordContent = exporter.exportRecord(productId);
			if(Strings.isNullOrEmpty(recordContent)) return; //continue
			recordContent = recordContent.trim();
			try {
				field.set(rec, recordContent);
			} catch (IllegalAccessException e) {
				log.error("Невозможно установить значение поля " + field.getName(), e);
			}
		});
		record.setFeedChangeTime(Utils.nowAtUTC());
		record.setProductChangeTime(productChangeTime);
		feedProductRepository.save(record);
		return true;
	}

	@Override
	public int createOrUpdateFeedProducts(@NonNull List<Long> productIds){
		int result = 0;
		for(Long productId : productIds){
			if(createOrUpdateFeedProduct(productId)) result ++;
		}
		return result;
	}

	@Override
	public List<FeedProductItem> createOrUpdateFeedProductItem(long productId) {
		Map<Long, FeedProductItem> productItemToFeedProductItem = feedProductItemRepository.findAllByProductId(productId)
				.stream()
				.collect(Collectors.toMap(FeedProductItem::getProductItemId, Function.identity()));
		List<FeedProductItem> toSave = new ArrayList<>();

		feedProductItemExportersMap.forEach((valueSetter, exporter) -> {
			Map<Long, Object> feedDataStream = exporter.getFeedXmlPojoMap(productId);
			Map<Long, String> productItemToXml = exporter.exportRecords(feedDataStream);
			for (Map.Entry<Long, String> entry : productItemToXml.entrySet()) {
				Long productItemId = entry.getKey();
				String feedXml = entry.getValue();

				FeedProductItem record = productItemToFeedProductItem.getOrDefault(productItemId,
						new FeedProductItem(productItemId, productId, Utils.nowAtUTC()));
				valueSetter.accept(record, feedXml);
				toSave.add(record);
			}
		});

		if (toSave.isEmpty()) {
			return Collections.emptyList();
		}

		LocalDateTime productChangeTime = Optional
				.ofNullable(productRepository.getProductChangeTimeById(productId))
				.orElse(Utils.nowAtUTC());

		toSave.forEach(it -> it.setFeedChangeTime(Utils.nowAtUTC())
						.setProductChangeTime(productChangeTime));

		return feedProductItemRepository.saveAll(toSave);
	}

	@Override
	public int createOrUpdateFeedProductItems(@NonNull List<Long> productIds) {
		return productIds.stream()
				.map(self::createOrUpdateFeedProductItem)
				.map(List::size)
				.reduce(0, Integer::sum);
	}

	@Transactional
	@Override
	public boolean createOrUpdateFeedUser(long userId){
		LocalDateTime userChangeTime = userRepository.getUserChangeTimeById(userId);
		if(userChangeTime == null) userChangeTime = Utils.nowAtUTC();
		FeedUser record = feedUserRepository.findById(userId).orElse(null);
		if(record == null){
			log.debug("Creating new FeedUser: " + userId);
			record = new FeedUser(userId, Utils.nowAtUTC());
		}
		else{
			log.debug("Updating FeedUser: " + userId);
		}
		final FeedUser rec = record;
		feedUserExportersMap.forEach((field, exporter) -> {
			String recordContent = exporter.exportRecord(userId);
			if(Strings.isNullOrEmpty(recordContent)) return; //continue
			recordContent = recordContent.trim();
			try {
				field.set(rec, recordContent);
			} catch (IllegalAccessException e) {
				log.error("Невозможно установить значение поля " + field.getName(), e);
			}
		});
		record.setFeedChangeTime(Utils.nowAtUTC());
		record.setUserChangeTime(userChangeTime);
		feedUserRepository.save(record);
		return true;
	}

	@Override
	public int createOrUpdateFeedUsers(@NonNull List<Long> userIds){
		int result = 0;
		for(Long userId : userIds){
			if(self.createOrUpdateFeedUser(userId)) result ++;
		}
		return result;
	}

	@Transactional
	@Override
	public boolean createOrUpdateFeedOrderPosition(long orderPositionId){
		OrderPosition orderPosition = orderPositionRepository.findById(orderPositionId).orElse(null);
		LocalDateTime orderChangeTime = orderPosition != null ? Utils.zonedDateTimeToLocalDateTimeUTC(orderPosition.getOrder().getChangeTime()) : null;
		if(orderChangeTime == null) orderChangeTime = Utils.nowAtUTC();
		FeedOrderPosition record = feedOrderPositionRepository.findById(orderPositionId).orElse(null);
		if(record == null){
			log.debug("Creating new FeedOrderPosition: " + orderPositionId);
			record = new FeedOrderPosition(orderPositionId, Utils.nowAtUTC());
		}
		else{
			log.debug("Updating FeedOrderPosition: " + orderPositionId);
		}
		final FeedOrderPosition rec = record;
		for(Field field : feedOrderPositionExportersMap.keySet()){
			FeedRecordExporter exporter = feedOrderPositionExportersMap.get(field);
			log.debug("Exporting FeedOrderPosition field " + field + " with exporter " + exporter);
			String recordContent = exporter.exportRecord(orderPositionId);
			if(Strings.isNullOrEmpty(recordContent)) {
				log.debug("Empty content for FeedOrderPosition field: " + field);
				continue;
			}
			recordContent = recordContent.trim();
			try {
				field.set(rec, recordContent);
			} catch (IllegalAccessException e) {
				log.error("Невозможно установить значение поля " + field.getName(), e);
			}
		}
		record.setFeedChangeTime(Utils.nowAtUTC());
		record.setOrderPositionChangeTime(orderChangeTime);
		feedOrderPositionRepository.save(record);
		return true;
	}

	@Override
	public int createOrUpdateFeedOrderPositions(@NonNull List<Long> orderPositionIds){
		int result = 0;
		for(Long orderPositionId : orderPositionIds){
			if(self.createOrUpdateFeedOrderPosition(orderPositionId)) result ++;
		}
		return result;
	}

	@Override
	public String prepareFeedTemplate(@NonNull String templateQueryFilePath) throws IOException{
		String templateQuery = IOUtils.readStringFromStream(getClass().getClassLoader().getResource(templateQueryFilePath).openStream());
		return Utils.formatXML(jdbcTemplate.queryForObject(templateQuery, String.class));
	}

	@Override
	public int exportYmlFeed(@NonNull String outputFilename, @NonNull String ymlTemplate, @NonNull Iterator<String> offersIterator) throws IOException{
		//ymlTemplate должен содержать тег <offers/>, на место которого будет писаться содержимое фида
		String[] ymlTemplateParts = ymlTemplate.split("<offers/>");
		if(ymlTemplateParts.length != 2){
			throw new ExportException(messageSourceAccessor.getMessage("exception.export.not-found-marker"));
		}
		File directory = new File(outputFilename).getParentFile();
		//Создаем директорию, если требуется
		if(!directory.exists()) directory.mkdirs();
		BufferedWriter writer = new BufferedWriter(
				new OutputStreamWriter(
						new FileOutputStream(outputFilename),
						StandardCharsets.UTF_8));
		//Пишем начало фида
		writer.write(ymlTemplateParts[0]);
		writer.write("\r\n<offers>\r\n  ");

		//Добавляем cодержимое фида (offer'ы)
		String offer;
		int offersCount = 0;
		while (offersIterator.hasNext()){
			offer = offersIterator.next();
			//Пустые офферы не пишем
			if(Strings.isNullOrEmpty(offer)) continue;
			writer.write(offer);
			offersCount ++;
		}

		//Пишем завершение фида
		writer.write("\r\n</offers>\r\n");
		writer.write(ymlTemplateParts[1]);
		writer.close();
		return offersCount;
	}

	@Override
	public int exportCsvFeed(@NonNull String outputFilename, String header, @NonNull Iterator<String> linesIterator) throws IOException{
		File directory = new File(outputFilename).getParentFile();
		//Создаем директорию, если требуется
		if(!directory.exists()) directory.mkdirs();
		BufferedWriter writer = new BufferedWriter(
				new OutputStreamWriter(
						new FileOutputStream(outputFilename),
						StandardCharsets.UTF_8));
		//Пишем заголовок, если требуется
		if(!Strings.isNullOrEmpty(header)) writer.write(header + "\r\n");

		//Добавляем cодержимое выгрузки (строки'ы)
		String feedPart;
		int feedPartsCount = 0;
		while (linesIterator.hasNext()){
			feedPart = linesIterator.next();
			//Пустые строки не пишем
			if(Strings.isNullOrEmpty(feedPart)) continue;
			writer.write(feedPart);
			//Переносим строку
			writer.write("\r\n");
			feedPartsCount ++;
		}
		writer.close();
		return feedPartsCount;
	}

}
