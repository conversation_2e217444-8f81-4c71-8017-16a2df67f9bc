package su.reddot.domain.service.feed.user.mindbox.fieldExporter.user;

import lombok.NonNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.feed.user.mindbox.MindboxCSVUserRecordExporter;

/**
 * Выгружает что-нибудь одно из User, Counterparty или AddressEndpoint
 */
@ConditionalOnProperty("app.feed-db.enabled")
@Component
public abstract class AbstractUserOrCounterpartyOrAddressFieldExporter implements MindboxCSVUserRecordExporter.UserFieldExporter {

    @Override
    public String exportUserField(@NonNull User user) {
        String result = getFromUser(user);
        //Если не нашли в пользователе в пользователе, то ищем его в активном контрагенте
        if(!isValid(result)){
            Counterparty counterparty = user.getActiveCounterparty();
            if(counterparty != null) result = getFromCounterparty(counterparty);
        }
        //Если не нашли в активном контрагенте, то ищем его во всех контрагентах
        if(!isValid(result)){
            for( Counterparty counterparty : user.getCounterparties()){
                String cpResult = getFromCounterparty(counterparty);
                if(isValid(cpResult)){
                    result = cpResult;
                    break;
                }
            }
        }
        //Если до сих пор не нашли, то ищем в адресных точках
        if(!isValid(result)){
            for(AddressEndpoint addressEndpoint : user.getActualAddressEndpoints()){
                String aeResult = getFromAddressEndpoint(addressEndpoint);
                if(isValid(aeResult)){
                    result = aeResult;
                    break;
                }
            }
        }
        return format(result);
    }

    protected abstract String getFromUser(@NonNull User user);

    protected abstract String getFromCounterparty(@NonNull Counterparty counterparty);

    protected abstract String getFromAddressEndpoint(@NonNull AddressEndpoint addressEndpoint);

    protected boolean isValid(String result){
        return result != null && !result.trim().isEmpty();
    }

    protected String format(String result){
        if(!isValid(result)) return null;
        //TODO: возможно потребуется форматирование
        return result.trim();
    }
}
