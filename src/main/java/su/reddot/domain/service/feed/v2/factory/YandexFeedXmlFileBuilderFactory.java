package su.reddot.domain.service.feed.v2.factory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Component;
import su.reddot.domain.feed.model.FeedProduct;
import su.reddot.domain.service.feed.v2.configurationProperties.YandexXmlFeedStaticFieldsConfigurationProperties;
import su.reddot.domain.service.feed.v2.factory.builder.DefaultFeedXmlFileBuilder;
import su.reddot.domain.service.feed.v2.factory.builder.FeedXmlFileBuilder;
import su.reddot.presentation.DeeplinkUtils;

@Component
@Qualifier(FeedXmlFileBuilderFactory.PRODUCT_V2_QUALIFIER)
@ConditionalOnExpression("${app.feed-db.enabled} and ${app.feed-db.export.enabled} and ${app.feed-db.export.product.v2.yandex.enabled}")
public class YandexFeedXmlFileBuilderFactory extends AbstractFeedXmlFileBuilderFactory implements FeedXmlFileBuilderFactory {

    @Value("${app.feed-db.export.product.v2.yandex.filename}")
    private String localFileName;
    @Value("${app.feed-db.export.product.v2.yandex.publish-dirname}")
    private String publishDirName;
    @Value("${app.feed-db.export.product.v2.yandex.zip-result}")
    private boolean isZipResult = false;

    @Autowired
    public YandexFeedXmlFileBuilderFactory(YandexXmlFeedStaticFieldsConfigurationProperties properties,
                                           DeeplinkUtils deeplinkUtils) {
        super(properties, deeplinkUtils);
    }

    @Override
    public FeedXmlFileBuilder getBuilder() {
       return new DefaultFeedXmlFileBuilder(
               localFileName,
               publishDirName,
               isZipResult,
               staticFields,
               getContentBeforeItems(),
               getContentAfterItems(),
               getProductLinkIfMobileLinkEnabledGetter()
       ).setFeedProductXmlValueGetter(FeedProduct::getFeedV2YandexXml);
    }
}
