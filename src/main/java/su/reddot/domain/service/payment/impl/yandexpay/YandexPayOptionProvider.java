package su.reddot.domain.service.payment.impl.yandexpay;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.YandexPlusInfo;
import su.reddot.domain.service.experiments.ExperimentsService;
import su.reddot.domain.service.payment.PaymentContext;
import su.reddot.domain.service.payment.PaymentOption;
import su.reddot.domain.service.payment.PaymentOptionProvider;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;

import java.util.Optional;

import static su.reddot.domain.service.experiments.ExperimentsService.FlagrKey.YANDEX_SPLIT;

@Component
@RequiredArgsConstructor
public class YandexPayOptionProvider implements PaymentOptionProvider {

    public static final String TYPE = "YANDEX_PAY";

    private static final String ICON = "/images/payments/ya_pay_2_3x.jpeg";

    private final MessageSourceAccessor messageSourceAccessor;

    private final DeviceService deviceService;

    private final ExperimentsService experimentsService;

    private final StaticResourceBalancer staticResourceBalancer;

    private final YandexPayService yandexPayService;

    @Value("${app.paymentOptions.yandexPay.enabled}")
    private boolean yandexPayEnabled;

    @Value("${app.paymentOptions.yandexPay.ignoreExp}")
    private boolean ignoreExp;

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public int getOrder() {
        return 20;
    }

    @Override
    public Optional<PaymentOption> getPaymentOption(PaymentContext context) {

        if (context == null || context.getUser() == null || context.getAmount() == null
                || !context.isSupportYandex()) {
            return Optional.empty();
        }

        if (!yandexPayEnabled) {
            return Optional.empty();
        }

        YandexPlusInfo yandexPlusInfo = yandexPayService
                .prepareYandexPlusInfo(context.getAmount(),
                        context.isConciergeProductExists())
                .orElse(null);

        if (ignoreExp) {
            return Optional.of(create(yandexPlusInfo));
        }

        // TODO: experiment key YANDEX_SPLIT -> YANDEX_PAY ?
        return experimentsService
                .getExperimentForUser(
                        context.getUser().getId(),
                        deviceService.getCurrentDeviceExperimentsInfo(), YANDEX_SPLIT)
                .map(key -> create(yandexPlusInfo));
    }

    private YandexPayOption create(YandexPlusInfo yandexPlusInfo) {
        return new YandexPayOption(
                getType(),
                messageSourceAccessor.getMessage("YandexPayOptionProvider.title"),
                staticResourceBalancer.transform(ICON),
                yandexPlusInfo);
    }
}
