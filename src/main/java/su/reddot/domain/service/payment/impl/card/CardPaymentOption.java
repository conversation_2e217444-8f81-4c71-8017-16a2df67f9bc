package su.reddot.domain.service.payment.impl.card;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import su.reddot.domain.service.payment.PaymentOption;

import java.util.List;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CardPaymentOption extends PaymentOption {


    private final List<CardOption> cards;

    public CardPaymentOption(String type, String title, String icon, List<CardOption> cards) {
        super(type, title, icon);
        this.cards = cards;
    }

}
