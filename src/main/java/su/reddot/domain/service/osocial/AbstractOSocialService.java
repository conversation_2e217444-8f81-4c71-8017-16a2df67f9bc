package su.reddot.domain.service.osocial;

import com.google.common.base.Functions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.following.FollowingRepository;
import su.reddot.domain.dao.like.BrandLikeRepository;
import su.reddot.domain.dao.like.ProductLikeRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.tags.UserCommonTagBindingRepository;
import su.reddot.domain.dao.tags.UserCommonTagBindingRepository.UserCommonTagBindingProjections.UserCommonTagBindingExtraData;
import su.reddot.domain.exception.OpenCvException;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.product.Image;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.CountryService;
import su.reddot.domain.service.currency.CurrencyConverter;
import su.reddot.domain.service.currency.CurrencyConverterFactory;
import su.reddot.domain.service.dto.CountryDTO;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.community.CommunityBadge;
import su.reddot.domain.service.dto.osocial.CoreOSocialCommentDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialFeature;
import su.reddot.domain.service.dto.osocial.CoreOSocialFeedSectionDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialImageDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialLinkDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialMediaDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialMediaVariantDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialPostExtendedViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialPostFeedViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialPostSearchViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialProductShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialProductShortViewDto.CoreOSocialBrandShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialProductShortViewDto.CoreOSocialCategoryShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialRatingExplanationDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialTagExtendedViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialTagShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialVideoDto;
import su.reddot.domain.service.social.model.BrandApiDto;
import su.reddot.domain.service.social.model.CategoryApiDto;
import su.reddot.domain.service.social.model.CommentApiDto;
import su.reddot.domain.service.social.model.FeedItemApiDto;
import su.reddot.domain.service.social.model.FeedPostsCollectionApiDto;
import su.reddot.domain.service.social.model.FeedSectionApiDto;
import su.reddot.domain.service.social.model.ImageApiDto;
import su.reddot.domain.service.social.model.LinkApiDto;
import su.reddot.domain.service.social.model.MediaApiDto;
import su.reddot.domain.service.social.model.MediaVariantApiDto;
import su.reddot.domain.service.social.model.PageApiDtoTagExtendedViewApiDto;
import su.reddot.domain.service.social.model.PostExtendedViewApiDto;
import su.reddot.domain.service.social.model.PostFeedViewApiDto;
import su.reddot.domain.service.social.model.PostSearchViewApiDto;
import su.reddot.domain.service.social.model.ProductApiDto;
import su.reddot.domain.service.social.model.RatingExplanationApiDto;
import su.reddot.domain.service.social.model.TagExtendedViewApiDto;
import su.reddot.domain.service.social.model.TagShortViewApiDto;
import su.reddot.domain.service.social.model.UpdateUserRequestApiDto;
import su.reddot.domain.service.social.model.VideoApiDto;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.UserService.UserListInfo;
import su.reddot.domain.service.user.UserService.UserRequest;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.CompletableFutureWrapper.StageTimeoutException;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Sets.newHashSet;
import static java.util.Collections.emptySet;
import static su.reddot.domain.model.product.ProductState.PUBLISHED;
import static su.reddot.domain.model.product.ProductState.SOLD;
import static su.reddot.domain.model.tags.UserCommonTagGroup.USER_COMMON_TAG_GROUP_COMMUNITY_STATUS_CODE;
import static su.reddot.domain.model.tags.UserCommonTagGroup.USER_COMMON_TAG_GROUP_USER_STATUS_CODE;
import static su.reddot.domain.service.social.model.MediaStatus.PROCESSED;
import static su.reddot.domain.service.social.model.MediaVariantType.THUMBNAIL;
import static su.reddot.infrastructure.service.imageProcessing.ProcessingType.TINY;

public abstract class AbstractOSocialService {

    @Autowired
    protected MessageSourceAccessor messageSourceAccessor;
    @Autowired
    protected UserRepository userRepository;
    @Autowired
    protected ProductRepository productRepository;
    @Autowired
    protected SecurityService securityService;
    @Autowired
    protected ProductLikeRepository productLikeRepository;
    @Autowired
    protected BrandLikeRepository brandLikeRepository;
    @Autowired
    protected Executor osocialExecutor;
    @Autowired
    protected CallInTransaction callInTransaction;
    @Autowired
    protected UserService userService;
    @Autowired
    protected StaticResourceBalancer staticResourceBalancer;
    @Autowired
    protected CurrencyConverterFactory currencyConverterFactory;
    @Autowired
    protected CountryService countryService;
    @Autowired
    protected FollowingRepository followingRepository;
    @Autowired
    protected UserCommonTagBindingRepository userCommonTagBindingRepository;

    @Value("${following.defaultFollowerIdsOnRegister}")
    private String defaultFollowerIdsStr;

    @Value("${app.osocial.users.syncing.following-users.too-many-threshold}")
    private int tooManyFollowingUsersThreshold;

    protected Supplier<Optional<Long>> getUserIdOptSupplier() {
        return () -> Optional.ofNullable(securityService.getCurrentAuthorizedUserId());
    }

    protected Function<Void, Optional<Long>> getUserIdOptFunction() {
        return v -> getUserIdOptSupplier().get();
    }

    protected OSocialFeedItemsListInfo prepareFeedItemsListInfo(
            List<FeedItemApiDto> items,
            CurrencyConverter currencyConverter
    ) {

        if (items == null) {
            return new OSocialFeedItemsListInfo();
        }

        Set<Long> userFullViewIds = newHashSet();
        Set<Long> userShortViewIds = newHashSet();
        Set<Long> productShortViewIds = newHashSet();

        items
                .forEach(item -> {
                    switch (item.getType()) {
                        case POST: {
                            PostFeedViewApiDto post = (PostFeedViewApiDto) item.getContent();
                            userFullViewIds.add(post.getAuthorId());
                            userShortViewIds.addAll(post.getLastLikedUserIds());
                            userShortViewIds.addAll(TagUtils.findTaggedUserIds(post.getText()));
                            productShortViewIds.addAll(post.getProductIds());
                            break;
                        }
                        case POSTS_COLLECTION: {
                            FeedPostsCollectionApiDto collection = (FeedPostsCollectionApiDto) item.getContent();
                            collection.getItems()
                                    .forEach(post -> userShortViewIds.add(post.getAuthorId()));
                            break;
                        }
                        case USERS_COLLECTION: {
                            break;
                        }
                    }
                });

        return prepareFeedItemsListInfo(userFullViewIds, userShortViewIds, productShortViewIds, currencyConverter);
    }

    protected OSocialFeedItemsListInfo prepareExtendedPostsListInfo(
            List<PostExtendedViewApiDto> posts,
            CurrencyConverter currencyConverter) {

        if (posts == null) {
            return new OSocialFeedItemsListInfo();
        }

        Set<Long> userFullViewIds = newHashSet();
        Set<Long> userShortViewIds = newHashSet();
        Set<Long> productShortViewIds = newHashSet();

        posts.forEach(post -> {
            userFullViewIds.add(post.getAuthorId());
            userFullViewIds.addAll(post.getMentionedUserIds());
            userShortViewIds.addAll(post.getLastLikedUserIds());
            userShortViewIds.addAll(TagUtils.findTaggedUserIds(post.getText()));
            productShortViewIds.addAll(post.getProductIds());
        });

        return prepareFeedItemsListInfo(userFullViewIds, userShortViewIds, productShortViewIds, currencyConverter);
    }

    protected OSocialFeedItemsListInfo prepareFeedPostsListInfo(
            List<PostFeedViewApiDto> posts,
            CurrencyConverter currencyConverter
    ) {

        if (posts == null) {
            return new OSocialFeedItemsListInfo();
        }

        Set<Long> userFullViewIds = newHashSet();
        Set<Long> userShortViewIds = newHashSet();
        Set<Long> productShortViewIds = newHashSet();

        posts.forEach(post -> {
            userFullViewIds.add(post.getAuthorId());
            userShortViewIds.addAll(post.getLastLikedUserIds());
            userShortViewIds.addAll(TagUtils.findTaggedUserIds(post.getText()));
            productShortViewIds.addAll(post.getProductIds());
        });

        return prepareFeedItemsListInfo(userFullViewIds, userShortViewIds, productShortViewIds, currencyConverter);
    }

    protected OSocialFeedItemsListInfo prepareFeedItemsListInfo(
            Set<Long> userFullViewIds,
            Set<Long> userShortViewIds,
            Set<Long> productShortViewIds,
            CurrencyConverter currencyConverter
    ) {

        OSocialFeedItemsListInfo listInfo = new OSocialFeedItemsListInfo();

        if (!userFullViewIds.isEmpty() || !userShortViewIds.isEmpty()) {

            Set<Long> userIds = newHashSet(userFullViewIds);
            userIds.addAll(userShortViewIds);

            List<User> users = userRepository.findByIdJoiningEager(userIds);

            // заполняем полные структуры пользователей
            List<User> fullUsers = users.stream()
                    .filter(u -> userFullViewIds.contains(u.getId()))
                    .collect(Collectors.toList());

            UserListInfo userListInfo = userService.userListInfo(fullUsers,
                    new UserRequest()
                            .setWithCommunityBadge(true)
                            .setWithProductCount(true)
                            .setWithIsFollowed(true));

            List<CoreOSocialUserDto> fullUserViewDtoList = fullUsers.stream()
                    .map(u -> toCoreUser(u, userListInfo))
                    .collect(Collectors.toList());

            listInfo.addUsers(fullUserViewDtoList);

            // и укороченные структуры пользователей
            List<CoreOSocialUserShortViewDto> shortUserViewDtoList = users.stream()
                    .filter(u -> userShortViewIds.contains(u.getId()))
                    .map(this::toCoreUserShortView)
                    .collect(Collectors.toList());

            listInfo.addUserShortViewList(shortUserViewDtoList);
        }

        if (!productShortViewIds.isEmpty()) {
            User currentUser = securityService.getCurrentAuthorizedUser();

            List<Product> products = productRepository.findByIdJoiningEager(productShortViewIds);

            List<Long> likedProducts = productLikeRepository.findAllLikedIds(currentUser, productShortViewIds);

            List<CoreOSocialProductShortViewDto> productShortViewDtoList = products.stream()
                    .map(p -> toProductShortView(p, likedProducts, currencyConverter))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            listInfo.addProductShortViewList(productShortViewDtoList);
        }

        return listInfo;
    }

    protected CoreOSocialPostExtendedViewDto toCoreExtendedViewPost(
            PostExtendedViewApiDto post,
            OSocialFeedItemsListInfo feedItemsListInfo,
            Set<CoreOSocialFeature> features) {
        // TODO: здесь возможно нужен какой-то фильтр указанных в посте юзеров и продуктов,
        //  сейчас выдаются все юзеры, продукты, как они прилетели от сервиса
        String postText;
        if (FeatureUtils.hasFeature(features, CoreOSocialFeature.TAGGED_USERS)) {
            postText = TagUtils.addUsernamesToTaggedUsers(post.getText(), feedItemsListInfo.getUserShortViewMap());
        } else {
            postText = TagUtils.replaceTaggedUserIdsToUsernames(post.getText(), feedItemsListInfo.getUserShortViewMap());
        }
        return new CoreOSocialPostExtendedViewDto(
                post.getId(),
                post.getCreatedAt().toZonedDateTime(),
                post.getPublishedAt() != null ? post.getPublishedAt().toZonedDateTime() : null,
                feedItemsListInfo.getUser(post.getAuthorId()),
                postText,
                toCoreMediaList(post.getMedia()),
                Boolean.TRUE.equals(post.getMentionedUsersExists()),
                prepareUserList(post.getMentionedUserIds()),
                Boolean.TRUE.equals(post.getLikedByMe()),
                post.getLikesCount(),
                prepareUserShortViewList(post.getLastLikedUserIds(), feedItemsListInfo),
                Boolean.TRUE.equals(post.getAddedToFavoritesByMe()),
                prepareCoreLink(post.getLink()),
                prepareCoreProductShortViewList(post.getProductIds(), feedItemsListInfo),
                toCoreTagShortViewList(post.getTags()),
                post.getStatus(),
                post.getCommentsCount(),
                toCoreComments(post.getLastComments(), features),
                post.getRating(),
                toCoreRatingExplanation(post.getRatingExplanation()),
                post.getSharesCount());
    }

    protected CoreOSocialPostFeedViewDto toCoreFeedViewPost(PostFeedViewApiDto post,
            OSocialFeedItemsListInfo feedItemsListInfo,
            Set<CoreOSocialFeature> features
    ) {
        // TODO: здесь возможно нужен какой-то фильтр указанных в посте юзеров и продуктов,
        //  сейчас выдаются все юзеры, продукты, как они прилетели от сервиса
        String postText;
        if (FeatureUtils.hasFeature(features, CoreOSocialFeature.TAGGED_USERS)) {
            postText = TagUtils.addUsernamesToTaggedUsers(post.getText(), feedItemsListInfo.getUserShortViewMap());
        } else {
            postText = TagUtils.replaceTaggedUserIdsToUsernames(post.getText(), feedItemsListInfo.getUserShortViewMap());
        }
        return new CoreOSocialPostFeedViewDto(
                post.getId(),
                post.getCreatedAt().toZonedDateTime(),
                post.getPublishedAt() != null ? post.getPublishedAt().toZonedDateTime() : null,
                feedItemsListInfo.getUser(post.getAuthorId()),
                postText,
                toCoreMediaList(post.getMedia()),
                Boolean.TRUE.equals(post.getMentionedUsersExists()),
                Boolean.TRUE.equals(post.getLikedByMe()),
                post.getLikesCount(),
                prepareUserShortViewList(post.getLastLikedUserIds(), feedItemsListInfo),
                Boolean.TRUE.equals(post.getAddedToFavoritesByMe()),
                prepareCoreLink(post.getLink()),
                prepareCoreProductShortViewList(post.getProductIds(), feedItemsListInfo),
                toCoreTagShortViewList(post.getTags()),
                post.getStatus(),
                post.getCommentsCount(),
                toCoreComments(post.getLastComments(), features),
                post.getRating(),
                toCoreRatingExplanation(post.getRatingExplanation()),
                post.getSharesCount());
    }

    private CoreOSocialRatingExplanationDto toCoreRatingExplanation(RatingExplanationApiDto ratingExplanation) {
        if (ratingExplanation == null) {
            return null;
        }
        return new CoreOSocialRatingExplanationDto(
                ratingExplanation.getValue(),
                ratingExplanation.getDescription(),
                ratingExplanation.getDetails() != null
                        ? ratingExplanation.getDetails().stream()
                            .map(this::toCoreRatingExplanation)
                            .collect(Collectors.toList())
                        : Collections.emptyList());
    }

    protected List<CoreOSocialTagShortViewDto> toCoreTagShortViewList(List<TagShortViewApiDto> tags) {
        if (tags == null) {
            return Collections.emptyList();
        }
        return tags.stream()
                .map(this::toCoreTagShortView)
                .collect(Collectors.toList());
    }

    private CoreOSocialTagShortViewDto toCoreTagShortView(TagShortViewApiDto tag) {
        if (tag == null) {
            return null;
        }
        return new CoreOSocialTagShortViewDto(tag.getId(), tag.getName(), tag.getDeleted());
    }

    protected List<CoreOSocialFeedSectionDto> toCoreFeedSectionList(List<FeedSectionApiDto> sections) {
        if (sections == null) {
            return Collections.emptyList();
        }
        return sections.stream()
                .map(this::toCoreFeedSection)
                .collect(Collectors.toList());
    }

    protected CoreOSocialFeedSectionDto toCoreFeedSection(FeedSectionApiDto section) {
        if (section == null) {
            return null;
        }
        return new CoreOSocialFeedSectionDto(section.getCode(), section.getName());
    }

    protected List<CoreOSocialProductShortViewDto> prepareCoreProductShortViewList(
            List<Long> productIds,
            OSocialFeedItemsListInfo listInfo
    ) {
        if (productIds == null) {
            return Collections.emptyList();
        }
        return productIds.stream()
                .map(listInfo::getProductShortView)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private CoreOSocialProductShortViewDto toProductShortView(
            Product product,
            List<Long> likedProducts,
            CurrencyConverter currencyConverter
    ) {
        if (!shouldBePresent(product)) {
            return null;
        }
        return new CoreOSocialProductShortViewDto(
                product.getId(),
                toCoreCategoryShortView(product.getCategory()),
                toCoreBrandShortView(product.getBrand()),
                toCoreProductSocialImage(product),
                product.getDisplayName(),
                prepareStatusDisplayName(product),
                currencyConverter.convertFromBase(product.getCurrentPrice()),
                currencyConverter.getCurrency().getIsoCode(),
                likedProducts.contains(product.getId()),
                isAvailable(product),
                hasSimilar(product),
                product.getUrl());
    }

    private boolean hasSimilar(Product product) {
        return !isAvailable(product);
    }

    private String prepareStatusDisplayName(Product product) {
        return product.getProductState().getDescription();
    }

    protected boolean shouldBePresent(Product product) {
        return product.getProductState() == SOLD || product.getProductState() == PUBLISHED;
    }

    private boolean isAvailable(Product product) {
        return product.getProductState() == PUBLISHED;
    }

    private CoreOSocialBrandShortViewDto toCoreBrandShortView(Brand brand) {
        return new CoreOSocialBrandShortViewDto(brand.getId(), brand.getName());
    }

    private CoreOSocialCategoryShortViewDto toCoreCategoryShortView(Category category) {
        return new CoreOSocialCategoryShortViewDto(category.getId(), category.getDisplayName());
    }

    private CoreOSocialImageDto toCoreProductSocialImage(Product product) {
        return product.getPrimaryImage()
                .map(this::toCoreProductSocialImage)
                .orElse(null);
    }

    private CoreOSocialImageDto toCoreProductSocialImage(Image image) {

        List<CoreOSocialMediaVariantDto> variants = Collections.singletonList(
                new CoreOSocialMediaVariantDto()
                    .setId(0)
                    .setType(THUMBNAIL)
                    .setFilePath(image.getImagePath())
                    .setFileUrl(staticResourceBalancer.getImageFullPath(image.getImagePath(TINY))));

        return new CoreOSocialImageDto(
                image.getId(),
                PROCESSED,
                1,
                variants,
                null);
    }

    protected CoreOSocialLinkDto prepareCoreLink(LinkApiDto link) {
        if (link == null) {
            return null;
        }
        return new CoreOSocialLinkDto(link.getUrl(), link.getText(), link.getColor());
    }

    protected List<CoreOSocialUserShortViewDto> prepareUserShortViewList(List<Long> userIds) {
        if (userIds == null) {
            return Collections.emptyList();
        }
        return userRepository.findAllById(userIds).stream()
                .map(this::toCoreUserShortView)
                .collect(Collectors.toList());
    }

    protected List<CoreOSocialUserShortViewDto> prepareNotDeletedUserShortViewListWithOrder(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        Map<Long, CoreOSocialUserShortViewDto> userShortViewDtoMap = userRepository.findAllById(userIds).stream()
                .filter(user -> !user.isDeleted())
                .map(this::toCoreUserShortView)
                .collect(Collectors.toMap(CoreOSocialUserShortViewDto::getId, Function.identity()));

        return userIds.stream()
                .map(userShortViewDtoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    protected CoreOSocialUserShortViewDto toCoreUserShortView(User user) {
        return new CoreOSocialUserShortViewDto(
                user.getId(),
                user.getNickname(),
                staticResourceBalancer.getImageFullPath(user.getAvatarPath()),
                user.isPro(),
                user.getSex(),
                user.getEmail(),
                user.getIsTrusted());
    }

    protected List<CoreOSocialUserShortViewDto> prepareUserShortViewList(
            List<Long> userIds,
            OSocialFeedItemsListInfo listInfo
    ) {
        if (userIds == null) {
            return Collections.emptyList();
        }
        return userIds.stream()
                .map(listInfo::getUserShortView)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    protected List<CoreOSocialMediaDto> toCoreMediaList(List<MediaApiDto> media) {
        if (media == null) {
            return Collections.emptyList();
        }
        return media.stream()
                .map(this::toCoreMedia)
                .collect(Collectors.toList());
    }

    protected CoreOSocialMediaDto toCoreMedia(MediaApiDto media) {
        if (media == null) {
            return null;
        }
        switch (media.getType()) {
            case IMAGE: {
                return toCoreImage((ImageApiDto) media);
            }
            case VIDEO: {
                return toCoreVideo((VideoApiDto) media);
            }
            default: throw new IllegalStateException("Media type not supported: " + media.getType());
        }
    }

    private CoreOSocialImageDto toCoreImage(ImageApiDto image) {
        if (image == null) {
            return null;
        }
        return
                new CoreOSocialImageDto(
                        image.getId(),
                        image.getStatus(),
                        image.getOrderNumber(),
                        toCoreMediaVariants(image.getVariants()),
                        image.getOpenCvPostId() != null ? encodeSimilarProductsGetToken(image.getOpenCvPostId()) : null);
    }

    private CoreOSocialVideoDto toCoreVideo(VideoApiDto video) {
        if (video == null) {
            return null;
        }
        return
                new CoreOSocialVideoDto(
                        video.getId(),
                        video.getStatus(),
                        video.getOrderNumber(),
                        toCoreMediaVariants(video.getVariants()),
                        video.getDuration());
    }

    protected List<CoreOSocialImageDto> toCoreImageList(List<ImageApiDto> images) {
        if (images == null) {
            return Collections.emptyList();
        }
        return images.stream()
                .map(this::toCoreImage)
                .collect(Collectors.toList());
    }

    private List<CoreOSocialMediaVariantDto> toCoreMediaVariants(List<MediaVariantApiDto> variants) {
        if (variants == null) {
            return Collections.emptyList();
        }
        return variants.stream()
                .map(this::toCoreMediaVariant)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private CoreOSocialMediaVariantDto toCoreMediaVariant(MediaVariantApiDto variant) {
        if (variant == null) {
            return null;
        }
        return
                new CoreOSocialMediaVariantDto(
                        variant.getId(),
                        variant.getType(),
                        variant.getFilePath(),
                        variant.getFileType(),
                        variant.getFileUrl(),
                        variant.getWidth(),
                        variant.getHeight());
    }

    protected <T> T callWithExceptionConverting(Callable<T> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            throw convert(e);
        }
    }

    protected RuntimeException convert(Throwable e) {

        if (e instanceof TimeoutException) {
            return new OskellyException(messageSourceAccessor.getMessage("osocial.method.timeout"));
        } else if (e instanceof ExecutionException) {

            e = e.getCause();

            if (e instanceof OpenCvException) {
                return new OskellyException(messageSourceAccessor.getMessage("osocial.media.similar-products.exception.unexpected"));
            } else if (e instanceof StageTimeoutException) {
                return new OskellyException(messageSourceAccessor.getMessage("osocial.request.timeout"));
            } else if (e instanceof RuntimeException) {
                return (RuntimeException) e;
            } else {
                return new OskellyException(e);
            }
        } else {
            return new OskellyException(e);
        }
    }

    protected Page<CoreOSocialTagExtendedViewDto> toCoreTagExtendedViewPage(PageApiDtoTagExtendedViewApiDto page) {
        return page != null
                ? new Page<>(toCoreTagExtendedViewList(page.getItems()), page.getTotalPages(), page.getTotalAmount())
                : Page.emptyPage();
    }

    protected List<CoreOSocialTagExtendedViewDto> toCoreTagExtendedViewList(List<TagExtendedViewApiDto> tags) {
        if (tags == null) {
            return Collections.emptyList();
        }
        return tags.stream()
                .map(this::toCoreTagExtendedView)
                .collect(Collectors.toList());
    }

    protected CoreOSocialTagExtendedViewDto toCoreTagExtendedView(TagExtendedViewApiDto tag) {
        if (tag == null) {
            return null;
        }
        return new CoreOSocialTagExtendedViewDto(tag.getId(), tag.getName(), tag.getDeleted(), tag.getPostsCount(), tag.getIsBound());
    }

    protected List<CoreOSocialUserDto> prepareUserList(List<Long> userIds) {

        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }

        return callInTransaction.runInAnyReadOnlyTransaction(() -> {
            List<User> users = userRepository.findAllById(userIds);

            UserListInfo userListInfo = userService.userListInfo(users,
                    new UserRequest()
                            .setWithCommunityBadge(true)
                            .setWithProductCount(true)
                            .setWithIsFollowed(true));

            return users.stream()
                    .map(user -> toCoreUser(user, userListInfo))
                    .collect(Collectors.toList());
        });
    }

    protected CoreOSocialUserDto toCoreUser(User user, UserListInfo userListInfo) {

        CommunityBadge communityBadge = userListInfo.getCommunityBadges() != null
                ? userListInfo.getCommunityBadges()
                .get(user.getId()) : null;

        boolean followedByMe = userListInfo.getFollowingUserIds() != null
                && userListInfo.getFollowingUserIds().contains(user.getId());

        Integer productsCount = userListInfo.getUserProductCounts() != null
                ? userListInfo.getUserProductCounts().get(user.getId()) : null;

        return new CoreOSocialUserDto(
                user.getId(),
                user.getNickname(),
                staticResourceBalancer.getImageFullPath(user.getAvatarPath()),
                communityBadge,
                followedByMe,
                productsCount != null ? productsCount : 0,
                user.isPro(),
                user.getSex(),
                user.getEmail(),
                user.getIsTrusted());
    }

    protected List<CoreOSocialPostFeedViewDto> toCorePostDtoList(List<PostFeedViewApiDto> posts, Long countryId,
            String currencyCode, Set<CoreOSocialFeature> features) {

        if (posts == null) {
            return Collections.emptyList();
        }

        return callInTransaction.runInAnyReadOnlyTransaction(() -> {
            OSocialFeedItemsListInfo feedItemsListInfo = prepareFeedPostsListInfo(posts,
                    getCurrencyConverter(countryId, currencyCode));
            return posts.stream()
                    .map(i -> toCoreFeedViewPost(i, feedItemsListInfo, features))
                    .collect(Collectors.toList());
        });
    }

    protected List<CoreOSocialPostSearchViewDto> toCorePostSearchViewDtoList(
            List<PostSearchViewApiDto> posts,
            Long countryId,
            String currencyCode,
            Set<CoreOSocialFeature> features
    ) {

        if (posts == null) {
            return Collections.emptyList();
        }

        return callInTransaction.runInAnyReadOnlyTransaction(() -> {
            OSocialFeedItemsListInfo listInfo = prepareSearchPostsListInfo(posts,
                    getCurrencyConverter(countryId, currencyCode));
            return posts.stream()
                    .map(i -> toCoreSearchViewPost(i, listInfo, features))
                    .collect(Collectors.toList());
        });
    }

    protected CoreOSocialPostSearchViewDto toCoreSearchViewPost(
            PostSearchViewApiDto post,
            OSocialFeedItemsListInfo listInfo,
            Set<CoreOSocialFeature> features
    ) {
        String postText;
        if (FeatureUtils.hasFeature(features, CoreOSocialFeature.TAGGED_USERS)) {
            postText = TagUtils.addUsernamesToTaggedUsers(post.getText(), listInfo.getUserShortViewMap());
        } else {
            postText = TagUtils.replaceTaggedUserIdsToUsernames(post.getText(), listInfo.getUserShortViewMap());
        }

        return new CoreOSocialPostSearchViewDto(
                post.getId(),
                post.getCreatedAt().toZonedDateTime(),
                post.getPublishedAt() != null ? post.getPublishedAt().toZonedDateTime() : null,
                listInfo.getUser(post.getAuthorId()),
                postText,
                toCoreMediaList(post.getMedia()),
                Boolean.TRUE.equals(post.getMentionedUsersExists()),
                Boolean.TRUE.equals(post.getLikedByMe()),
                post.getLikesCount(),
                Boolean.TRUE.equals(post.getAddedToFavoritesByMe()),
                prepareCoreLink(post.getLink()),
                prepareCoreProductShortViewList(post.getProductIds(), listInfo),
                toCoreTagShortViewList(post.getTags()),
                post.getStatus(),
                post.getCommentsCount(),
                post.getRating(),
                toCoreRatingExplanation(post.getRatingExplanation()),
                post.getSharesCount());
    }

    protected CurrencyConverter getCurrencyConverter(Long countryId, String currencyCode) {
        if (!StringUtils.hasText(currencyCode) && countryId != null) {
            currencyCode = countryService.getCountryDTOCached(countryId)
                    .map(CountryDTO::getCurrency)
                    .map(CurrencyDTO::getIsoCode)
                    .orElse(null);
        }
        return currencyConverterFactory.createByCurrencyCodeToBase(currencyCode);
    }

    protected CoreOSocialCommentDto toCoreComment(CommentApiDto comment, Set<CoreOSocialFeature> features) {

        if (comment == null) {
            return null;
        }

        OSocialCommentsListInfo listInfo = prepareCommentsListInfo(comment);

        return toCoreCommentInternal(comment, listInfo, features);
    }

    private CoreOSocialCommentDto toCoreCommentInternal(CommentApiDto comment, OSocialCommentsListInfo listInfo,
            Set<CoreOSocialFeature> features) {

        if (comment == null) {
            return null;
        }

        String commentText;
        if (FeatureUtils.hasFeature(features, CoreOSocialFeature.TAGGED_USERS)) {
            commentText = TagUtils.addUsernamesToTaggedUsers(comment.getText(), listInfo.getUsersMap());
        } else {
            commentText = TagUtils.replaceTaggedUserIdsToUsernames(comment.getText(), listInfo.getUsersMap());
        }

        return new CoreOSocialCommentDto(
                comment.getId(),
                listInfo.getUserShortView(comment.getAuthorId()),
                commentText,
                comment.getCreatedAt().toZonedDateTime(),
                comment.getCreatedAtTs(),
                toCoreImageList(comment.getImages()),
                comment.getParentId(),
                toCoreCommentsInternal(comment.getChildren(), listInfo, features));
    }

    private List<CoreOSocialCommentDto> toCoreCommentsInternal(List<CommentApiDto> comments, OSocialCommentsListInfo listInfo,
            Set<CoreOSocialFeature> features) {

        if (comments == null) {
            return Collections.emptyList();
        }

        return comments.stream()
                .map(it -> toCoreCommentInternal(it, listInfo, features))
                .collect(Collectors.toList());
    }

    protected List<CoreOSocialCommentDto> toCoreComments(List<CommentApiDto> comments, Set<CoreOSocialFeature> features) {

        if (comments == null) {
            return Collections.emptyList();
        }

        OSocialCommentsListInfo listInfo = prepareCommentsListInfo(comments);

        return toCoreCommentsInternal(comments, listInfo, features);
    }

    private OSocialCommentsListInfo prepareCommentsListInfo(CommentApiDto comment) {
        return prepareCommentsListInfo(Collections.singletonList(comment));
    }

    private OSocialCommentsListInfo prepareCommentsListInfo(List<CommentApiDto> comments) {

        OSocialCommentsListInfo listInfo = new OSocialCommentsListInfo();

        Set<Long> userIds = comments.stream()
                .flatMap(it -> Stream.concat(it.getChildren().stream(), Stream.of(it)))
                .flatMap(it -> Stream.concat(Stream.of(it.getAuthorId()), TagUtils.findTaggedUserIds(it.getText()).stream()))
                .collect(Collectors.toSet());

        List<CoreOSocialUserShortViewDto> userShortViewList = userRepository.findAllById(userIds).stream()
                .map(this::toCoreUserShortView)
                .collect(Collectors.toList());

        listInfo.addUsers(userShortViewList);

        return listInfo;
    }

    protected Map<Long, UpdateUserRequestApiDto> prepareUpdateUserRequests(
            List<Long> userIds
    ) {
        // TODO: можно запрашивать оптимальнее, не вытягивая сущность юзера целиком
        Map<Long, UpdateUserRequestApiDto> usersMap = userRepository.findByIdJoiningEager(userIds).stream()
                .map(user -> new UpdateUserRequestApiDto()
                        .id(user.getId())
                        .nickname(user.getNickname())
                        .tooManyFollowingUsers(false))
                .collect(Collectors.toMap(UpdateUserRequestApiDto::getId, Functions.identity()));

        List<UserCommonTagBindingExtraData> userCommonTagsData = userCommonTagBindingRepository.getUserCommonTagsData(
                userIds,
                newArrayList(USER_COMMON_TAG_GROUP_USER_STATUS_CODE, USER_COMMON_TAG_GROUP_COMMUNITY_STATUS_CODE));
        userCommonTagsData.forEach(
                data -> {
                    UpdateUserRequestApiDto user = usersMap.get(data.getUserId());
                    if (data.getGroupCode().equals(USER_COMMON_TAG_GROUP_USER_STATUS_CODE)) {
                        String userStatusCode = data.getTagCode().replace(USER_COMMON_TAG_GROUP_USER_STATUS_CODE + "_", "");
                        user.addStatusCodesItem(userStatusCode);
                    } else if (data.getGroupCode().equals(USER_COMMON_TAG_GROUP_COMMUNITY_STATUS_CODE)) {
                        String communityStatusCode = data.getTagCode().replace(USER_COMMON_TAG_GROUP_COMMUNITY_STATUS_CODE + "_", "");
                        user.communityStatusCode(communityStatusCode);
                    }
                });

        List<Object[]> brandLikesRawData = brandLikeRepository.getBrandLikesRawData(userIds);
        brandLikesRawData.forEach(
                data -> {
                    long userId = ((BigInteger) data[0]).longValue();
                    long brandId = ((BigInteger) data[1]).longValue();
                    usersMap.get(userId).addFollowingBrandIdsItem(brandId);
                });

        Set<Long> defaultFollowerIds = getDefaultFollowerIds();

        List<Object[]> followingRawData = followingRepository.getRelatingFollowingRawData(
                userIds,
                userIds.stream()
                    .filter(id -> !defaultFollowerIds.contains(id))
                    .collect(Collectors.toList()));
        followingRawData.forEach(
                data -> {
                    long followingId = ((BigInteger) data[0]).longValue();
                    long followerId = ((BigInteger) data[1]).longValue();
                    UpdateUserRequestApiDto followingUser = usersMap.get(followingId);
                    if (followingUser != null) {
                        followingUser.addFollowerIdsItem(followerId);
                    }
                    UpdateUserRequestApiDto follower = usersMap.get(followerId);
                    if (follower != null) {
                        follower.addFollowingUserIdsItem(followingId);
                    }
                });

        usersMap.values().forEach(user -> {
            if (defaultFollowerIds.contains(user.getId()) ||
                    user.getFollowingUserIds() != null && user.getFollowingUserIds().size() > tooManyFollowingUsersThreshold) {
                user
                        .tooManyFollowingUsers(true)
                        .followingUserIds(emptySet());
            }
        });

        return usersMap;
    }

    private Set<Long> getDefaultFollowerIds() {

        if (defaultFollowerIdsStr == null) {
            return emptySet();
        }

        return Arrays.stream(defaultFollowerIdsStr.split(","))
                .map(String::trim)
                .map(Long::parseLong)
                .collect(Collectors.toSet());
    }

    protected boolean isProductCanBeContainedInPosts(Long productId) {
        return productRepository.findById(productId)
                .isPresent();
    }

    protected boolean isUserCanBeMentionedInPosts(Long userId) {
        return userRepository.findById(userId)
                .isPresent();
    }

    protected boolean isUserCanBeTaggedInPosts(Long userId) {
        return userRepository.findById(userId)
                .isPresent();
    }

    protected boolean isUserCanBeTaggedInComments(Long userId) {
        return userRepository.findById(userId)
                .isPresent();
    }

    protected <T> T callWithCheckedExceptionWrapping(Callable<T> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    protected String encodeSimilarProductsGetToken(String openCvPostId) {
        return Base64.getEncoder().encodeToString(openCvPostId.getBytes(StandardCharsets.UTF_8));
    }

    protected String decodeSimilarProductsGetToken(String similarProductsGetToken) {
        return new String(Base64.getDecoder().decode(similarProductsGetToken.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
    }

    protected List<Product> getProducts(List<Long> productIds) {
        Map<Long,  Product> pMap = productRepository.findAllById(productIds)
                .stream()
                .collect(Collectors.toMap(
                        Product::getId,
                        Function.identity()
                ));

        return productIds
                .stream()
                .map(pMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private OSocialFeedItemsListInfo prepareSearchPostsListInfo(
            List<PostSearchViewApiDto> posts,
            CurrencyConverter currencyConverter
    ) {

        if (posts == null) {
            return new OSocialFeedItemsListInfo();
        }

        Set<Long> userFullViewIds = newHashSet();
        Set<Long> userShortViewIds = newHashSet();
        Set<Long> productShortViewIds = newHashSet();

        posts.forEach(post -> {
            userFullViewIds.add(post.getAuthorId());
            userShortViewIds.addAll(TagUtils.findTaggedUserIds(post.getText()));
            productShortViewIds.addAll(post.getProductIds());
        });

        return prepareFeedItemsListInfo(userFullViewIds, userShortViewIds, productShortViewIds, currencyConverter);
    }

    protected ProductApiDto toProduct(Product product) {

        if (product == null) {
            return null;
        }

        Brand brand = product.getBrand();
        Category category = product.getCategory();
        return new ProductApiDto()
                .id(product.getId())
                .brand(
                        new BrandApiDto()
                                .id(brand.getId())
                                .name(brand.getName()))
                .category(
                        new CategoryApiDto()
                                .id(category.getId())
                                .name(category.getDisplayName())
                                .path(category.getParentCategoryPathStr()));
    }
}