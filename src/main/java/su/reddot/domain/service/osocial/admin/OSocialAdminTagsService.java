package su.reddot.domain.service.osocial.admin;

import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialTagAdminExtendedViewDto;
import su.reddot.domain.service.social.model.TagSortingOption;
import su.reddot.domain.service.social.model.TagStatus;

import java.util.List;

public interface OSocialAdminTagsService {

    Page<CoreOSocialTagAdminExtendedViewDto> getTagAdminExtendedViewPage(String name, TagSortingOption sortingOption,
                                                                         Integer pageNumber, Integer pageSize);

    List<CoreOSocialTagAdminExtendedViewDto> getTagAdminExtendedViewList(List<Long> ids);

    CoreOSocialTagAdminExtendedViewDto createTag(String tagName);

    CoreOSocialTagAdminExtendedViewDto updateTagName(long tagId, String newTagName);

    CoreOSocialTagAdminExtendedViewDto updateTagStatus(long tagId, TagStatus newTagStatus);

    CoreOSocialTagAdminExtendedViewDto updatePinnedTagOrder(long tagId, Long beforePinnedTagId);
}
