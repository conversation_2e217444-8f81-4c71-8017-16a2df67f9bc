package su.reddot.domain.service.osocial.admin;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.UserAuthorityBindingRepository;
import su.reddot.domain.dao.userban.UserBanRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.osocial.CoreOSocialAdminCommentDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialProductShortViewDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserDto;
import su.reddot.domain.service.dto.osocial.CoreOSocialUserShortViewDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialAdminPostActivityDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialAdminPostDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialAdminPostRatingExplanationDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialAdminPostRatingExplanationDto.CoreOSocialAdminPostRatingExplanationPartDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialAdminShortPostDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialAdminUserShortViewDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialBoostingDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialCreateCommentByAdminRequestDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialDeletePostByAdminRequestDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialDeletePostsByAdminRequestDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialOperationResultContainer;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialPatchPostByAdminRequestDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialPatchPostByAdminRequestDto.CoreOSocialLinkUpdateByAdminDataDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialPostHistoryDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialPostPropertyChangedDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialRejectPostByAdminRequestDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialRejectPostsByAdminRequestDto;
import su.reddot.domain.service.osocial.OSocialCommentsListInfo;
import su.reddot.domain.service.osocial.OSocialFeedItemsListInfo;
import su.reddot.domain.service.osocial.TagUtils;
import su.reddot.domain.service.social.api.AdminPostsControllerApi;
import su.reddot.domain.service.social.model.ActionType;
import su.reddot.domain.service.social.model.ActivityType;
import su.reddot.domain.service.social.model.AdminPostActivityApiDto;
import su.reddot.domain.service.social.model.AdminPostRatingExplanationApiDto;
import su.reddot.domain.service.social.model.AdminPostRatingExplanationPartApiDto;
import su.reddot.domain.service.social.model.BoostingApiDto;
import su.reddot.domain.service.social.model.BoostingUpdateByAdminDataApiDto;
import su.reddot.domain.service.social.model.ClearablePostPropertiesApiDto;
import su.reddot.domain.service.social.model.CommentAdminApiDto;
import su.reddot.domain.service.social.model.CreateCommentByAdminRequestApiDto;
import su.reddot.domain.service.social.model.DeletePostByAdminRequestApiDto;
import su.reddot.domain.service.social.model.DeletePostsByAdminRequestApiDto;
import su.reddot.domain.service.social.model.LinkUpdateByAdminDataApiDto;
import su.reddot.domain.service.social.model.OperationResultContainerPostAdminViewApiDto;
import su.reddot.domain.service.social.model.PageApiDtoAdminPostActivityApiDto;
import su.reddot.domain.service.social.model.PageApiDtoCommentAdminApiDto;
import su.reddot.domain.service.social.model.PageApiDtoPostAdminViewApiDto;
import su.reddot.domain.service.social.model.PatchPostByAdminRequestApiDto;
import su.reddot.domain.service.social.model.PostAdminViewApiDto;
import su.reddot.domain.service.social.model.PostHistoryApiDto;
import su.reddot.domain.service.social.model.PostHistorySortingOption;
import su.reddot.domain.service.social.model.PostStatus;
import su.reddot.domain.service.social.model.PostWithExtraDataSortingOption;
import su.reddot.domain.service.social.model.RejectPostByAdminRequestApiDto;
import su.reddot.domain.service.social.model.RejectPostsByAdminRequestApiDto;
import su.reddot.domain.service.social.model.UpdateUserRequestApiDto;
import su.reddot.infrastructure.util.CompletableFutureWrapper;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.google.api.client.util.Sets.newHashSet;
import static com.google.common.collect.Lists.newArrayList;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty("social-service.enabled")
public class DefaultOSocialAdminPostsService extends AbstractOSocialAdminService implements OSocialAdminPostsService {

    private final AdminPostsControllerApi adminPostsControllerApi;
    private final UserAuthorityBindingRepository userAuthorityBindingRepository;
    private final UserBanRepository userBanRepository;

    @Value("${social-service.socket-timeout}")
    private Duration socketTimeout;

    @Override
    public Page<CoreOSocialAdminPostDto> getPosts(
            Integer pageNumber,
            Integer pageSize,
            PostWithExtraDataSortingOption sortingOption,
            List<PostStatus> statuses,
            Boolean hidden,
            Boolean deleted,
            List<Long> authorIds,
            ZonedDateTime publishedAtFrom,
            ZonedDateTime publishedAtTo,
            List<Long> tagIds,
            List<Long> productIds,
            Boolean linkSet,
            Boolean boostingSet,
            String search,
            List<Long> ids
    ) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi
                                .getAdminPosts(
                                        pageNumber,
                                        pageSize,
                                        sortingOption,
                                        statuses,
                                        hidden,
                                        deleted,
                                        authorIds,
                                        publishedAtFrom != null ? publishedAtFrom.toOffsetDateTime() : null,
                                        publishedAtTo != null ? publishedAtTo.toOffsetDateTime() : null,
                                        tagIds,
                                        productIds,
                                        linkSet,
                                        boostingSet,
                                        search,
                                        ids)
                                .getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPostPage,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public Long getPostsCount(List<PostStatus> statuses, Boolean hidden, Boolean deleted, List<Long> authorIds) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi.getAdminPostsCount(
                                        statuses,
                                        hidden,
                                        deleted,
                                        authorIds)
                                .getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialAdminShortPostDto> getShortPostList(String query, List<Long> ids) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi
                                .getAdminPostShortList(query, ids)
                                .getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminShortPostDtoList,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminPostRatingExplanationDto getAdminPostRatingExplanation(long postId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi
                                .getPostRatingExplanation(postId)
                                .getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPostRatingExplanation,
                        osocialExecutor)
                .get(socketTimeout.getSeconds(), TimeUnit.SECONDS));
    }

    private CoreOSocialAdminPostRatingExplanationDto toCoreAdminPostRatingExplanation(
            AdminPostRatingExplanationApiDto explanation
    ) {
        return new CoreOSocialAdminPostRatingExplanationDto(
                explanation.getValue(),
                toCoreAdminPostRatingExplanationParts(explanation.getParts()));
    }

    private List<CoreOSocialAdminPostRatingExplanationPartDto> toCoreAdminPostRatingExplanationParts(
            List<AdminPostRatingExplanationPartApiDto> parts
    ) {
        if (parts == null) {
            return Collections.emptyList();
        }
        return parts.stream()
                .map(p ->
                        new CoreOSocialAdminPostRatingExplanationPartDto(
                                p.getValue(),
                                prepareExplanationPartDescription(p.getCode()),
                                p.getColor()))
                .collect(Collectors.toList());
    }

    private String prepareExplanationPartDescription(String code) {
        return messageSourceAccessor.getMessage("osocial.post.rating.explanation.part." + code, code);
    }

    @Override
    public Page<CoreOSocialAdminCommentDto> getPostComments(long postId, Integer pageNumber, Integer pageSize) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi
                                .getAdminPostComments(postId, pageNumber, pageSize)
                                .getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminCommentPage,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminCommentDto createPostComment(long postId,
            CoreOSocialCreateCommentByAdminRequestDto requestDto) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.createAdminPostComment(
                                postId, userIdOpt.get(), prepareCreateCommentByAdminRequest(requestDto)
                                ).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminComment,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    private CreateCommentByAdminRequestApiDto prepareCreateCommentByAdminRequest(
            CoreOSocialCreateCommentByAdminRequestDto requestDto) {
        Set<Long> taggedUserIds = new HashSet<>();
        if (StringUtils.isNotBlank(requestDto.getText())) {
            StringBuffer newTextBuffer = new StringBuffer();
            taggedUserIds = TagUtils.findTaggedUserIdsAndClearUsernamesIfNeeded(requestDto.getText(), newTextBuffer);
            requestDto.setText(newTextBuffer.toString());
        }

        List<Long> userIds = newArrayList(requestDto.getAuthorId());

        if (!taggedUserIds.isEmpty()) {
            userIds.addAll(taggedUserIds);
        }

        Map<Long, UpdateUserRequestApiDto> map = prepareUpdateUserRequests(userIds);

        UpdateUserRequestApiDto author = map.get(requestDto.getAuthorId());

        List<UpdateUserRequestApiDto> taggedUsers;
        if (!taggedUserIds.isEmpty()) {
            taggedUsers = taggedUserIds.stream()
                    .map(map::get)
                    .collect(Collectors.toList());
        } else {
            taggedUsers = newArrayList();
        }

        return new CreateCommentByAdminRequestApiDto()
                .text(requestDto.getText())
                .author(author)
                .imageFileIds(requestDto.getImageFileIds())
                .parentId(requestDto.getParentId())
                .taggedUsers(taggedUsers);
    }

    @Override
    public CoreOSocialAdminCommentDto deletePostComment(long postId, long commentId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.deleteAdminPostComment(
                                postId,
                                commentId,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminComment,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminCommentDto restorePostComment(long postId, long commentId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.restoreAdminPostComment(
                                postId,
                                commentId,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminComment,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    private Page<CoreOSocialAdminPostDto> toCoreAdminPostPage(PageApiDtoPostAdminViewApiDto page) {
        return page != null
                ? new Page<>(toCoreAdminPostDtoList(page.getItems()), page.getTotalPages(), page.getTotalAmount())
                : Page.emptyPage();
    }

    @Override
    public CoreOSocialAdminPostDto patchPost(long postId, CoreOSocialPatchPostByAdminRequestDto request) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .runAsync(
                        () -> validatePatchPostByAdminRequest(request),
                        osocialExecutor)
                .thenApplyAsync(getUserIdOptFunction(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.patchPostByAdmin(
                                postId,
                                userIdOpt.get(),
                                preparePatchPostByAdminRequest(request)).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    private void validatePatchPostByAdminRequest(CoreOSocialPatchPostByAdminRequestDto request) {

        if (request.getProductIds() != null && !request.getProductIds().isEmpty()) {
            request.getProductIds()
                    .forEach(id -> {
                        if (!isProductCanBeContainedInPosts(id)) {
                            throw new OskellyException(
                                    messageSourceAccessor.getMessage(
                                            "osocial.post.creating.exception.product-cannot-be-contained-in-post",
                                            new Object[] {id}));
                        }
                    });
        }

        if (request.getMentionedUserIds() != null && !request.getMentionedUserIds().isEmpty()) {
            request.getMentionedUserIds()
                    .forEach(id -> {
                        if (!isUserCanBeMentionedInPosts(id)) {
                            throw new OskellyException(
                                    messageSourceAccessor.getMessage(
                                            "osocial.post.creating.exception.user-cannot-be-mentioned-in-post",
                                            new Object[] {id}));
                        }
                    });
        }

        try {
            TagUtils.findTaggedUserIdsOrThrow(request.getText()).forEach(id ->{
                if (!isUserCanBeTaggedInPosts(id)) {
                    throw new OskellyException(
                            messageSourceAccessor.getMessage(
                                    "osocial.post.creating.exception.user-cannot-be-tagged-in-post",
                                    new Object[] {id}));
                }
            });
        } catch (NumberFormatException e) {
            throw new OskellyException(
                    messageSourceAccessor.getMessage("osocial.post.creating.exception.invalid-tagged-user-id-in-post"));
        }
    }

    private PatchPostByAdminRequestApiDto preparePatchPostByAdminRequest(CoreOSocialPatchPostByAdminRequestDto request) {
        Set<Long> taggedUserIds = null;
        if (StringUtils.isNotBlank(request.getText())) {
            StringBuffer newTextBuffer = new StringBuffer();
            taggedUserIds = TagUtils.findTaggedUserIdsAndClearUsernamesIfNeeded(request.getText(), newTextBuffer);
            request.setText(newTextBuffer.toString());
        }

        List<Long> userIds = new ArrayList<>();
        if (request.getMentionedUserIds() != null) {
            userIds.addAll(request.getMentionedUserIds());
        }

        if (taggedUserIds != null) {
            userIds.addAll(taggedUserIds);
        }

        Map<Long, UpdateUserRequestApiDto> map = prepareUpdateUserRequests(userIds);

        List<UpdateUserRequestApiDto> mentionedUsers = null;
        if (request.getMentionedUserIds() != null) {
            mentionedUsers = request.getMentionedUserIds().stream()
                    .map(map::get)
                    .collect(Collectors.toList());
        }

        List<UpdateUserRequestApiDto> taggedUsers = null;
        if (taggedUserIds != null) {
            taggedUsers = taggedUserIds.stream()
                    .map(map::get)
                    .collect(Collectors.toList());
        }

        List<Product> products = null;
        if (request.getProductIds() != null) {
            products = getProducts(request.getProductIds());
        }

        return new PatchPostByAdminRequestApiDto()
                .text(request.getText())
                .boosting(
                        request.getBoosting() != null
                                ? new BoostingUpdateByAdminDataApiDto()
                                    .rate(request.getBoosting().getRate())
                                    .expiresAt(request.getBoosting().getExpiresAt().toOffsetDateTime())
                                : null)
                .mediaFileIds(request.getMediaFileIds())
                .tagIds(request.getTagIds())
                .mentionedUsers(mentionedUsers)
                .products(products != null
                        ? products.stream().map(this::toProduct).filter(Objects::nonNull).collect(Collectors.toList())
                        : null)
                .link(request.getLink() != null ? toLinkUpdateByAdminData(request.getLink()) : null)
                .clearableProperties(
                        request.getClearableProperties() != null
                                ? new ClearablePostPropertiesApiDto()
                                    .boosting(request.getClearableProperties().getBoosting())
                                    .link(request.getClearableProperties().getLink())
                                    .text(request.getClearableProperties().getText())
                                : null)
                .taggedUsers(taggedUsers);
    }

    private LinkUpdateByAdminDataApiDto toLinkUpdateByAdminData(CoreOSocialLinkUpdateByAdminDataDto link) {
        if (link == null) {
            return null;
        }
        return new LinkUpdateByAdminDataApiDto()
                .url(link.getUrl())
                .text(link.getText());
    }

    @Override
    public CoreOSocialAdminPostDto deletePost(long postId, CoreOSocialDeletePostByAdminRequestDto request) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.deletePostByAdmin(
                                postId,
                                userIdOpt.get(),
                                prepareDeletePostByAdminRequest(request)).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    private DeletePostByAdminRequestApiDto prepareDeletePostByAdminRequest(CoreOSocialDeletePostByAdminRequestDto request) {
        return new DeletePostByAdminRequestApiDto()
                .deletionReason(request.getDeletionReason());
    }

    @Override
    public CoreOSocialAdminPostDto restorePost(long postId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.restorePostByAdmin(
                                postId,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminPostDto rejectPost(long postId, CoreOSocialRejectPostByAdminRequestDto request) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.rejectPostByAdmin(
                                postId,
                                userIdOpt.get(),
                                prepareRejectPostByAdminRequest(request)).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    private RejectPostByAdminRequestApiDto prepareRejectPostByAdminRequest(CoreOSocialRejectPostByAdminRequestDto request) {
        return new RejectPostByAdminRequestApiDto()
                .rejectionReason(request.getRejectionReason());
    }

    @Override
    public CoreOSocialAdminPostDto approvePost(long postId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.approvePostByAdmin(
                                postId,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminPostDto hidePost(long postId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.hidePostByAdmin(
                                postId,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminPostDto showPost(long postId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.showPostByAdmin(
                                postId,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> deletePosts(
            CoreOSocialDeletePostsByAdminRequestDto request) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.deletePostsByAdmin(
                                userIdOpt.get(),
                                prepareDeletePostsByAdminRequest(request)).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreOSocialOperationResultAdminPostDto,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> restorePosts(List<Long> postIds) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.restorePostsByAdmin(
                                postIds,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreOSocialOperationResultAdminPostDto,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> rejectPosts(
            CoreOSocialRejectPostsByAdminRequestDto request) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.rejectPostsByAdmin(
                                userIdOpt.get(),
                                prepareRejectPostsByAdminRequest(request)).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreOSocialOperationResultAdminPostDto,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> approvePosts(List<Long> postIds) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.approvePostsByAdmin(
                                postIds,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreOSocialOperationResultAdminPostDto,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> hidePosts(List<Long> postIds) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.hidePostsByAdmin(
                                postIds,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreOSocialOperationResultAdminPostDto,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> showPosts(List<Long> postIds) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsync(getUserIdOptSupplier(), osocialExecutor)
                .thenApplyAsyncWithTimeout(
                        userIdOpt -> adminPostsControllerApi.showPostsByAdmin(
                                postIds,
                                userIdOpt.get()).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreOSocialOperationResultAdminPostDto,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public CoreOSocialAdminShortPostDto getShortPost(long postId) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi.getAdminPostShort(postId).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminShortPost,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public Page<CoreOSocialAdminPostActivityDto> getPostActivity(
            Integer pageNumber,
            Integer pageSize,
            long postId,
            ActivityType type
    ) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi
                                .getPostActivity(
                                        postId,
                                        type,
                                        pageNumber,
                                        pageSize)
                                .getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCoreAdminPostActivityPage,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    @Override
    public List<CoreOSocialPostHistoryDto> getPostHistory(long postId, PostHistorySortingOption sortingOption) {
        return callWithExceptionConverting(() -> CompletableFutureWrapper
                .supplyAsyncWithTimeout(
                        () -> adminPostsControllerApi.getAdminPostHistory(postId, sortingOption).getData(),
                        osocialExecutor,
                        socketTimeout.getSeconds(),
                        TimeUnit.SECONDS)
                .thenApplyAsync(
                        this::toCorePostHistoryList,
                        osocialExecutor)
                .get(socketTimeout.getSeconds() * 2, TimeUnit.SECONDS));
    }

    private List<CoreOSocialPostHistoryDto> toCorePostHistoryList(List<PostHistoryApiDto> postHistoryApiDtos) {
        Set<Long> userShortViewIds = newHashSet();
        Set<Long> adminUserShortViewIds = newHashSet();
        Set<Long> productShortViewIds = newHashSet();

        postHistoryApiDtos.forEach(postHistoryDto -> {
            if (postHistoryDto.getUserId() != null) {
                userShortViewIds.add(postHistoryDto.getUserId());
                adminUserShortViewIds.add(postHistoryDto.getUserId());
            }
            if (postHistoryDto.getChangedProperty().getMentionedUserIds() != null) {
                userShortViewIds.addAll(postHistoryDto.getChangedProperty().getMentionedUserIds().getAdded());
                userShortViewIds.addAll(postHistoryDto.getChangedProperty().getMentionedUserIds().getDeleted());
            }
            if (postHistoryDto.getChangedProperty().getProductIds() != null) {
                productShortViewIds.addAll(postHistoryDto.getChangedProperty().getProductIds().getAdded());
                productShortViewIds.addAll(postHistoryDto.getChangedProperty().getProductIds().getDeleted());
            }
            if (postHistoryDto.getChangedProperty().getText() != null) {
                if (StringUtils.isNotBlank(postHistoryDto.getChangedProperty().getText().getOldValue())) {
                    userShortViewIds.addAll(
                            TagUtils.findTaggedUserIds(postHistoryDto.getChangedProperty().getText().getOldValue()));
                }
                if (StringUtils.isNotBlank(postHistoryDto.getChangedProperty().getText().getNewValue())) {
                    userShortViewIds.addAll(
                            TagUtils.findTaggedUserIds(postHistoryDto.getChangedProperty().getText().getNewValue()));
                }
            }
        });

        OSocialFeedItemsListInfo oSocialFeedItemsListInfo = callInTransaction.runInAnyReadOnlyTransaction(() ->
                prepareFeedItemsListInfo(
                        adminUserShortViewIds,
                        userShortViewIds,
                        productShortViewIds,
                        currencyConverterFactory.baseCurrencyConverter()));

        Map<Long, Set<AuthorityName>> usersRoles = callInTransaction.runInAnyReadOnlyTransaction(() -> {
            Map<Long, Set<AuthorityName>> usersRolesMap = new HashMap<>();
            userAuthorityBindingRepository.findAllByUserIdIn(adminUserShortViewIds)
                    .forEach(userAuthorityBinding -> usersRolesMap.computeIfAbsent(
                                    userAuthorityBinding.getUser().getId(), key -> new HashSet<>())
                            .add(userAuthorityBinding.getAuthority().getName()));
            return usersRolesMap;
        });

        Set<Long> bannedUserIds;
        if (!adminUserShortViewIds.isEmpty()) {
            bannedUserIds = userBanRepository.findUsersActiveAllBansOnAction(adminUserShortViewIds).stream()
                    .map(UserBan::getUserId)
                    .collect(Collectors.toSet());
        } else {
            bannedUserIds = Collections.emptySet();
        }

        return postHistoryApiDtos.stream()
                .filter(it -> !(it.getActionType() == ActionType.PROPERTY_CHANGED && it.getChangedProperty().getTaggedUserIds() != null))
                .map(postHistoryApiDto -> new CoreOSocialPostHistoryDto()
                        .setId(postHistoryApiDto.getId())
                        .setPostId(postHistoryApiDto.getPostId())
                        .setUser(Optional.ofNullable(postHistoryApiDto.getUserId())
                                .map(userId -> Optional.ofNullable(oSocialFeedItemsListInfo.getUser(userId))
                                        .map(userShortView -> toCoreAdminUserShortView(userShortView, usersRoles,
                                                bannedUserIds))
                                        .orElseGet(() -> {
                                            CoreOSocialAdminUserShortViewDto adminUserShortViewDto = new CoreOSocialAdminUserShortViewDto();
                                            adminUserShortViewDto.setId(userId);
                                            return adminUserShortViewDto;
                                        }))
                                .orElse(null))
                        .setActionType(postHistoryApiDto.getActionType())
                        .setCreatedAt(postHistoryApiDto.getCreatedAt().toZonedDateTime())
                        .setChangedProperty(new CoreOSocialPostPropertyChangedDto()
                                .setText(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getText())
                                        .map(text -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                TagUtils.replaceTaggedUserIdsToUsernames(text.getOldValue(),
                                                        oSocialFeedItemsListInfo.getUserShortViewMap()),
                                                TagUtils.replaceTaggedUserIdsToUsernames(text.getNewValue(),
                                                        oSocialFeedItemsListInfo.getUserShortViewMap())
                                        )).orElse(null))
                                .setStatus(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getStatus())
                                        .map(status -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                status.getOldValue(),
                                                status.getNewValue())).orElse(null))
                                .setMedia(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getMedia())
                                        .map(media -> new CoreOSocialPostPropertyChangedDto.ArrayPropertyChanged<>(
                                                toCoreMediaList(media.getAdded()),
                                                toCoreMediaList(media.getDeleted()))).orElse(null))
                                .setMentionedUsers(
                                        Optional.ofNullable(
                                                        postHistoryApiDto.getChangedProperty().getMentionedUserIds())
                                                .map(mentionedUserIds -> new CoreOSocialPostPropertyChangedDto.ArrayPropertyChanged<>(
                                                        mentionedUserIds.getAdded().stream()
                                                                .map(userId -> Optional.ofNullable(
                                                                                oSocialFeedItemsListInfo.getUserShortView(
                                                                                        userId))
                                                                        .orElse(new CoreOSocialUserShortViewDto().setId(
                                                                                userId)))
                                                                .collect(Collectors.toList()),
                                                        mentionedUserIds.getDeleted().stream()
                                                                .map(userId -> Optional.ofNullable(
                                                                                oSocialFeedItemsListInfo.getUserShortView(
                                                                                        userId))
                                                                        .orElse(new CoreOSocialUserShortViewDto().setId(
                                                                                userId)))
                                                                .collect(Collectors.toList()))).orElse(null))
                                .setProducts(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getProductIds())
                                        .map(productIds -> new CoreOSocialPostPropertyChangedDto.ArrayPropertyChanged<>(
                                                productIds.getAdded().stream().map(productId ->
                                                                Optional.ofNullable(oSocialFeedItemsListInfo.getProductShortView(productId))
                                                                        .orElse(new CoreOSocialProductShortViewDto().setId(productId)))
                                                        .collect(Collectors.toList()),
                                                productIds.getDeleted().stream().map(productId ->
                                                                Optional.ofNullable(oSocialFeedItemsListInfo.getProductShortView(productId))
                                                                        .orElse(new CoreOSocialProductShortViewDto().setId(productId)))
                                                        .collect(Collectors.toList()))).orElse(null))
                                .setTags(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getTags())
                                        .map(tags -> new CoreOSocialPostPropertyChangedDto.ArrayPropertyChanged<>(
                                                toCoreTagShortViewList(tags.getAdded()),
                                                toCoreTagShortViewList(tags.getDeleted()))).orElse(null))
                                .setLink(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getLink())
                                        .map(link -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                prepareCoreLink(link.getOldValue()),
                                                prepareCoreLink(link.getNewValue()))).orElse(null))
                                .setBoosting(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getBoosting())
                                        .map(boosting -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                prepareCoreBoosting(boosting.getOldValue()),
                                                prepareCoreBoosting(boosting.getNewValue()))).orElse(null))
                                .setDeletionReason(
                                        Optional.ofNullable(postHistoryApiDto.getChangedProperty().getDeletionReason())
                                                .map(deletionReason -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                        deletionReason.getOldValue(),
                                                        deletionReason.getNewValue())).orElse(null))
                                .setRejectionReason(
                                        Optional.ofNullable(postHistoryApiDto.getChangedProperty().getRejectionReason())
                                                .map(rejectionReason -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                        rejectionReason.getOldValue(),
                                                        rejectionReason.getNewValue())).orElse(null))
                                .setPublishedAt(
                                        Optional.ofNullable(postHistoryApiDto.getChangedProperty().getPublishedAt())
                                                .map(publishedAt -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                        Optional.ofNullable(publishedAt.getOldValue())
                                                                .map(OffsetDateTime::toZonedDateTime).orElse(null),
                                                        Optional.ofNullable(publishedAt.getNewValue())
                                                                .map(OffsetDateTime::toZonedDateTime).orElse(null)
                                                )).orElse(null))
                                .setDeletedAt(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getDeletedAt())
                                        .map(deletedAt -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                Optional.ofNullable(deletedAt.getOldValue())
                                                        .map(OffsetDateTime::toZonedDateTime).orElse(null),
                                                Optional.ofNullable(deletedAt.getNewValue())
                                                        .map(OffsetDateTime::toZonedDateTime).orElse(null)
                                        )).orElse(null))
                                .setHiddenAt(Optional.ofNullable(postHistoryApiDto.getChangedProperty().getHiddenAt())
                                        .map(hiddenAt -> new CoreOSocialPostPropertyChangedDto.SimplePropertyChanged<>(
                                                Optional.ofNullable(hiddenAt.getOldValue())
                                                        .map(OffsetDateTime::toZonedDateTime).orElse(null),
                                                Optional.ofNullable(hiddenAt.getNewValue())
                                                        .map(OffsetDateTime::toZonedDateTime).orElse(null)
                                        )).orElse(null))
                        )
                )
                .collect(Collectors.toList());
    }

    private CoreOSocialAdminUserShortViewDto toCoreAdminUserShortView(
            CoreOSocialUserDto coreOSocialUserDto, Map<Long, Set<AuthorityName>> usersRoles, Set<Long> bannedUserIds) {
        CoreOSocialAdminUserShortViewDto adminUserShortViewDto = new CoreOSocialAdminUserShortViewDto();
        adminUserShortViewDto.setId(coreOSocialUserDto.getId())
                .setNickname(coreOSocialUserDto.getNickname())
                .setAvatarPath(coreOSocialUserDto.getAvatarPath())
                .setPro(coreOSocialUserDto.isPro())
                .setSex(coreOSocialUserDto.getSex())
                .setEmail(coreOSocialUserDto.getEmail())
                .setIsTrusted(coreOSocialUserDto.getIsTrusted());

        return adminUserShortViewDto
                .setModerator(
                        Optional.ofNullable(usersRoles.get(coreOSocialUserDto.getId())).orElse(Collections.emptySet())
                                .stream().anyMatch(authorityName -> AuthorityName.OSOCIAL_MODERATOR == authorityName))
                .setAdmin(Optional.ofNullable(usersRoles.get(coreOSocialUserDto.getId())).orElse(Collections.emptySet())
                        .stream().anyMatch(authorityName -> AuthorityName.OSOCIAL_ADMIN == authorityName))
                .setBanned(bannedUserIds.contains(coreOSocialUserDto.getId()));
    }

    private CoreOSocialBoostingDto prepareCoreBoosting(BoostingApiDto boostingApiDto) {
        if (boostingApiDto == null) {
            return null;
        }
        return new CoreOSocialBoostingDto(
                boostingApiDto.getRate(),
                boostingApiDto.getExpiresAt() == null ? null : boostingApiDto.getExpiresAt().toZonedDateTime()
        );
    }

    private CoreOSocialAdminPostDto toCoreAdminPost(PostAdminViewApiDto post) {
        return callInTransaction.runInAnyReadOnlyTransaction(() -> {
            OSocialFeedItemsListInfo feedItemsListInfo = prepareAdminPostsListInfo(
                    Collections.singletonList(post));
            return toCoreAdminPost(post, feedItemsListInfo);
        });
    }

    private Page<CoreOSocialAdminCommentDto> toCoreAdminCommentPage(PageApiDtoCommentAdminApiDto page) {
        return page != null
                ? new Page<>(toCoreAdminComments(page.getItems()), page.getTotalPages(), page.getTotalAmount())
                : Page.emptyPage();
    }

    private List<CoreOSocialAdminCommentDto> toCoreAdminComments(List<CommentAdminApiDto> comments) {
        if (comments == null) {
            return Collections.emptyList();
        }

        OSocialCommentsListInfo listInfo = prepareAdminCommentsListInfo(comments);

        return toCoreAdminCommentsInternal(comments, listInfo);
    }

    private DeletePostsByAdminRequestApiDto prepareDeletePostsByAdminRequest(
            CoreOSocialDeletePostsByAdminRequestDto request) {
        return new DeletePostsByAdminRequestApiDto()
                .postIds(request.getPostIds())
                .deletionReason(request.getDeletionReason());
    }

    private RejectPostsByAdminRequestApiDto prepareRejectPostsByAdminRequest(
            CoreOSocialRejectPostsByAdminRequestDto request) {
        return new RejectPostsByAdminRequestApiDto()
                .postIds(request.getPostIds())
                .rejectionReason(request.getRejectionReason());
    }

    private List<CoreOSocialOperationResultContainer<CoreOSocialAdminPostDto>> toCoreOSocialOperationResultAdminPostDto(
            List<OperationResultContainerPostAdminViewApiDto> operationResultContainerPostAdminViewApiDtos) {
        return operationResultContainerPostAdminViewApiDtos.stream()
                .map(operationResultContainerPostAdminViewApiDto -> new CoreOSocialOperationResultContainer<>(
                        operationResultContainerPostAdminViewApiDto.getObjectId(),
                        operationResultContainerPostAdminViewApiDto.getResultCode(),
                        operationResultContainerPostAdminViewApiDto.getMessage(),
                        operationResultContainerPostAdminViewApiDto.getResult() != null ?
                                toCoreAdminPost(operationResultContainerPostAdminViewApiDto.getResult()) : null
                ))
                .collect(Collectors.toList());
    }

    private Page<CoreOSocialAdminPostActivityDto> toCoreAdminPostActivityPage(PageApiDtoAdminPostActivityApiDto page) {
        return page != null
                ? new Page<>(toCoreAdminPostActivityList(page.getItems()), page.getTotalPages(), page.getTotalAmount())
                : Page.emptyPage();
    }

    private List<CoreOSocialAdminPostActivityDto> toCoreAdminPostActivityList(List<AdminPostActivityApiDto> activityItems) {

        if (activityItems == null) {
            return Collections.emptyList();
        }

        List<Long> userIds = activityItems.stream()
                .map(AdminPostActivityApiDto::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        Map<Long, CoreOSocialUserShortViewDto> usersMap = prepareUserShortViewList(userIds)
                .stream()
                .collect(Collectors.toMap(CoreOSocialUserShortViewDto::getId, Function.identity()));

        return activityItems.stream()
                .map(item -> new CoreOSocialAdminPostActivityDto(
                        item.getUserId() != null ? usersMap.get(item.getUserId()) : null,
                        item.getRegisteredAt().toZonedDateTime(),
                        item.getType()))
                .collect(Collectors.toList());
    }
}
