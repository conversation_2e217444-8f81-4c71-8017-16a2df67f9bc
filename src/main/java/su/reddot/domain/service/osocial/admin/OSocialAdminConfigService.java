package su.reddot.domain.service.osocial.admin;

import su.reddot.domain.service.dto.osocial.admin.CoreOSocialGlobalConfigDto;
import su.reddot.domain.service.dto.osocial.admin.CoreOSocialPatchGlobalConfigRequestDto;

public interface OSocialAdminConfigService {
    CoreOSocialGlobalConfigDto getGlobalConfig();
    CoreOSocialGlobalConfigDto patchGlobalConfig(CoreOSocialPatchGlobalConfigRequestDto request);
}
