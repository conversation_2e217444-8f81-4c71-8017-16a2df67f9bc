package su.reddot.domain.service.osocial;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import su.reddot.domain.exception.BadCommentException;
import su.reddot.domain.exception.osocial.OSocialException;
import su.reddot.domain.service.textchecking.AbstractUndesirableTextChecker;

@Service
@Qualifier("osocialPostUndesirableTextChecker")
@ConditionalOnProperty("social-service.enabled")
@Slf4j
@RequiredArgsConstructor
public class OSocialPostUndesirableTextChecker extends AbstractUndesirableTextChecker {

	private final MessageSourceAccessor messageSourceAccessor;

	private final OSocialPostUndesirableTextCheckingProperties textCheckingProperties;

	@Override
	protected CheckingMethodParameters getCheckEmailParams() {
		return new CheckingMethodParameters(
				textCheckingProperties.isPerformEmailCheck(),
				() -> {
					throw new OSocialException(messageSourceAccessor.getMessage("exception.osocial.post.text.email-must-not-contain"));
				});
	}

	@Override
	protected CheckingMethodParameters getCheckPhoneNumbersParams() {
		return new CheckingMethodParameters(
				textCheckingProperties.isPerformPhoneNumberCheck(),
				() -> {
					throw new OSocialException(messageSourceAccessor.getMessage("exception.osocial.post.text.telephone-must-not-contain"));
				});
	}

	@Override
	protected CheckingMethodParameters getCheckSocialNetworksParams() {
		return new CheckingMethodParameters(
				textCheckingProperties.isPerformSocialNetworkCheck(),
				() -> {
					throw new OSocialException(messageSourceAccessor.getMessage("exception.osocial.post.text.unacceptable-words"));
				});
	}

	@Override
	protected CheckingMethodParameters getCheckSwearsParams() {
		return new CheckingMethodParameters(
				textCheckingProperties.isPerformSwearsCheck(),
				() -> {
					throw new OSocialException(messageSourceAccessor.getMessage("exception.osocial.post.text.unacceptable-words"));
				});
	}

	@Override
	protected CheckingMethodParameters getCheckLinkParams() {
		return new CheckingMethodParameters(
				textCheckingProperties.isPerformLinkCheck(),
				() -> {
					throw new OSocialException(messageSourceAccessor.getMessage("exception.osocial.post.text.link-must-not-contain"));
				});
	}

	@Override
	protected CheckingMethodParameters getCheckConfigurableStopList() {
		return new CheckingMethodParameters(
				textCheckingProperties.isPerformConfigurableStopListCheck(),
				() -> {
					throw new BadCommentException(messageSourceAccessor.getMessage("exception.post.configurable-stop-list-word"));
				});
	}
}
