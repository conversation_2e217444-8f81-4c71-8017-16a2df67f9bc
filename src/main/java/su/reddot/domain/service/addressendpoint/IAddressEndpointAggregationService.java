package su.reddot.domain.service.addressendpoint;

import su.reddot.domain.model.addressendpoint.AddressEndpointAggregation;

import java.util.List;

public interface IAddressEndpointAggregationService {
    AddressEndpointAggregation save(AddressEndpointAggregation addressEndpointAggregation);

    AddressEndpointAggregation update(AddressEndpointAggregation addressEndpointAggregation);

    boolean delete(AddressEndpointAggregation addressEndpointAggregation);

    List<AddressEndpointAggregation> findAllByUser(Long userId);
}
