package su.reddot.domain.service.integration.orderprocessing;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.size.SizeService;
import su.reddot.oskelly.orderprocessing.internal.web.dto.OrderItemSizeDTO;

@Slf4j
@Service
@RequiredArgsConstructor
public class OPProductService {

	private final SizeService sizeService;
	private final CategoryService categoryService;
	private final MessageSourceAccessor messageSourceAccessor;

	@Transactional
	public OrderItemSizeDTO getProductOskellySize(final Long categoryId, final Long sizeId) {
		Size size = sizeService.fromId(sizeId);
		SizeType sizeType = categoryService.getCategory(categoryId).getDefaultSizeType();
		return OrderItemDetailsDTOHelper.getOrderItemSize(size, sizeType, messageSourceAccessor);
	}
}
