package su.reddot.domain.service.integration.opencv;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import su.reddot.domain.exception.OpenCvException;
import su.reddot.domain.service.dto.opencv.ProductToOpenCvDTO;
import su.reddot.domain.service.dto.photosearch.LocationDTO;
import su.reddot.domain.service.dto.photosearch.OneSearchResultDTO;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.opencv.OpenCvClient;
import su.reddot.infrastructure.opencv.view.PostProductSearchResult;
import su.reddot.infrastructure.opencv.view.SearchResultResponse;
import su.reddot.presentation.api.v2.filter.SearchProductPhotoResultsResponse;

import java.time.Duration;
import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.OptionalInt;
import java.util.Set;

import static su.reddot.infrastructure.opencv.view.OpenCvStatus.FAILED;
import static su.reddot.infrastructure.opencv.view.OpenCvStatus.FINISHED;

@Service
@RequiredArgsConstructor
public class OpenCvService implements PhotoSearchService {
    public static int RETRY_DELAY_MILLIS = 300;
    public static String SEARCH_TIMER = "OpenCvService.searchTimer";
    private final OpenCvClient openCVClient;
    private final MessageSourceAccessor messageSourceAccessor;
    private final MicrometerService micrometerService;
    @Value("${app.integration.opencv.productsInGroupLimit}")
    protected int productsInGroupLimit;
    @Value("${app.integration.opencv.catalog.syncEnabled}")
    protected boolean catalogSyncEnabled;
    @Value("${app.integration.opencv.supportedCategoryIds}")
    protected List<Long> supportedCategoryIds;

    @Timed("OpenCvService.startSearch")
    public String startSearch(String imageLink) {
        return openCVClient.search(imageLink);
    }

    @Timed("OpenCvService.checkTask")
    public SearchProductPhotoResultsResponse checkTask(String taskId, long startedAt) {
        SearchResultResponse response = openCVClient.getTask(taskId);
        SearchProductPhotoResultsResponse searchProductPhotoResponse = new SearchProductPhotoResultsResponse();
        searchProductPhotoResponse.setRetryDelay(RETRY_DELAY_MILLIS);

        if (FINISHED.isSame(response.getStatus())) {
            parseSearchResult(response.getResult(), searchProductPhotoResponse);
            searchProductPhotoResponse.setRetryDelay(null);
            measureSearchDuration(startedAt);
        } else if (FAILED.isSame(response.getStatus())) {
            measureSearchDuration(startedAt);
            throw new OpenCvException(
                    messageSourceAccessor.getMessage("exception.photo-search.internal-error"),
                    "OpenCV have not found products by image. OpenCV job status: failed"
            );
        }

        return searchProductPhotoResponse;
    }

    private void parseSearchResult(PostProductSearchResult searchResult, SearchProductPhotoResultsResponse response) {
        int size = searchResult.getCenters().length;

        if (size != searchResult.getProductIds().length) {
            throw new OpenCvException(
                    messageSourceAccessor.getMessage("exception.photo-search.internal-error"),
                    "An error occurred while parsing result from OpenCV. The size of the centers array is not equal " +
                            "to the size of the of product_ids array."
            );
        }

        List<OneSearchResultDTO> convertedResult = new LinkedList<>();
        for (int i = 0; i < size; i++) {
            OneSearchResultDTO oneSearchResultDTO = getOneSearchResultDTO(searchResult, i);
            convertedResult.add(oneSearchResultDTO);
        }

        response.setObjects(convertedResult);
        response.setProductIds(collectProductIdsByRelevant(searchResult));
    }

    private OneSearchResultDTO getOneSearchResultDTO(PostProductSearchResult searchResult, int arrayNumber) {

        Long[] groupProductIds = searchResult.getProductIds()[arrayNumber];
        Double[] groupCenters = searchResult.getCenters()[arrayNumber];

        if (groupCenters.length < 2) {
            throw new OpenCvException(
                    messageSourceAccessor.getMessage("exception.photo-search.internal-error"),
                    "An error occurred while parsing result from OpenCV. The array of centers less than 2."
            );
        }

        return getOneSearchResultDTO(groupProductIds, new LocationDTO(groupCenters[0], groupCenters[1]));
    }

    public OneSearchResultDTO getOneSearchResultDTO(Long[] groupProductIds, LocationDTO locationDTO) {
        OneSearchResultDTO oneSearchResultDTO = new OneSearchResultDTO();

        boolean overLimit = groupProductIds.length > productsInGroupLimit;
        oneSearchResultDTO.setCount(overLimit ? productsInGroupLimit : groupProductIds.length);
        oneSearchResultDTO.setProductIds(
                overLimit ? Arrays.copyOfRange(groupProductIds, 0, productsInGroupLimit) : groupProductIds
        );
        oneSearchResultDTO.setLocation(locationDTO);
        return oneSearchResultDTO;
    }

    /**
     * Собирает productId со всех массивов в один массив по релевантности. Сначала в итоговый массив добавляются
     * все продукты с индексом 0, затем с индексом 1 и т.д.
     *
     * @param searchResult результат поиска OpenCV
     * @return массив productId
     */
    private Long[] collectProductIdsByRelevant(PostProductSearchResult searchResult) {
        return collectProductIdsByRelevant(searchResult.getProductIds());
    }

    /**
     * Собирает productId со всех массивов в один массив по релевантности. Сначала в итоговый массив добавляются
     * все продукты с индексом 0, затем с индексом 1 и т.д.
     *
     * @param productIds массив продуктов в разрезе сегментов
     * @return массив productId
     */
    public Long[] collectProductIdsByRelevant(Long[][] productIds) {
        OptionalInt optionalMaxSize = Arrays.stream(productIds).mapToInt(array -> array.length).max();

        if (!optionalMaxSize.isPresent()) {
            return new Long[0];
        }

        int maxSize = optionalMaxSize.getAsInt();
        Set<Long> mergedProductIds = new LinkedHashSet<>(maxSize + 1, 1.f);

        int count = 0;
        for (int index = 0; index < maxSize && count <= productsInGroupLimit; index++) {
            for (Long[] array : productIds) {
                if (array.length > index) {
                    mergedProductIds.add(array[index]);
                    count++;
                }
            }
        }

        Long[] array = new Long[mergedProductIds.size()];
        return mergedProductIds.toArray(array);
    }

    @Timed("OpenCvService.postProduct")
    public void postProduct(ProductToOpenCvDTO productDto) {
        if (catalogSyncEnabled) {
            // отправляет POST запрос и не проверяет результат по job_id
            openCVClient.postProduct(productDto);
        }
    }

    @Timed("OpenCvService.patchProduct")
    public void patchProduct(ProductToOpenCvDTO productDto) {
        if (catalogSyncEnabled) {
            openCVClient.patchProduct(productDto.getId(), productDto);
        }
    }

    public void updateProduct(ProductToOpenCvDTO productDto) {
        if (!supportedCategoryIds.contains(productDto.getCategoryId())) {
            return;
        }

        if (catalogSyncEnabled) {
            if (CollectionUtils.isEmpty(productDto.getImages())) {
                patchProduct(productDto);
            } else {
                postProduct(productDto);
            }
        }
    }

    public void measureSearchDuration(Duration searchDuration) {
        micrometerService.getTimerByName(OpenCvService.SEARCH_TIMER).record(searchDuration);
    }

    private void measureSearchDuration(long startedAt) {
        measureSearchDuration(Duration.ofMillis(System.currentTimeMillis() - startedAt));
    }
}
