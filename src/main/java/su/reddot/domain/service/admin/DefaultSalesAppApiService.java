package su.reddot.domain.service.admin;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.admin.assigment.AdminAssignApiClient;
import su.reddot.domain.service.admin.salesman.SalesmanApiClient;
import su.reddot.domain.service.admin.salesplan.AdminSalePlanApiClient;
import su.reddot.domain.service.admin.workschedule.AdminWorkScheduleClient;
import su.reddot.domain.service.admin.workschedule.AdminWorkScheduleSettingsClient;
import su.reddot.domain.service.clientsegment.ClientSegmentsServiceApiClient;
import su.reddot.domain.service.dto.productselectionrequest.RequestStatus;
import su.reddot.domain.service.salesapp.model.CounterSummaryDTO;
import su.reddot.domain.service.salesapp.model.ImportantEventDTO;
import su.reddot.domain.service.salesapp.model.ImportantEventUserInfoDTO;
import su.reddot.domain.service.salesapp.model.MessageDto;
import su.reddot.domain.service.salesapp.model.ProductSelectionContainingDTO;
import su.reddot.domain.service.salesapp.model.ProductSelectionRequestCreateDto;
import su.reddot.domain.service.salesapp.model.ProductSelectionRequestDto;
import su.reddot.domain.service.salesapp.model.PurchaserFilterDTO;
import su.reddot.domain.service.salesapp.model.PurchaserFullInfoDTO;
import su.reddot.domain.service.salesapp.model.PurchaserLimitedInfoDTO;
import su.reddot.domain.service.salesapp.model.PurchaserListShortInfoDTO;
import su.reddot.domain.service.salesapp.model.PurchaserRequestDTO;
import su.reddot.domain.service.salesapp.model.PurchaserSalesmanBindingDTO;
import su.reddot.domain.service.salesapp.model.PurchaserSizeDTO;
import su.reddot.domain.service.salesapp.model.ReceiptDTO;
import su.reddot.domain.service.salesapp.model.SalesmanChatsDto;
import su.reddot.domain.service.salesapp.model.SalesmanCreateDTO;
import su.reddot.domain.service.salesapp.model.SalesmanDto;
import su.reddot.domain.service.salesapp.model.SalesmanIdAndRole;
import su.reddot.domain.service.salesapp.model.SalesmanUpdateDTO;
import su.reddot.domain.service.salesapp.model.SelectionDetailsDTO;
import su.reddot.domain.service.salesapp.model.SelectionFilterDTO;
import su.reddot.domain.service.salesapp.model.SelectionListDTO;
import su.reddot.domain.service.salesapp.model.SelectionPurchaserViewDTO;
import su.reddot.domain.service.salesapp.model.SelectionPurchasersDTO;
import su.reddot.domain.service.salesapp.model.SelectionRequestDTO;
import su.reddot.domain.service.productselectionrequest.ProductSelectionRequestApiClient;
import su.reddot.domain.service.purchaser.PurchaserServiceApiClient;
import su.reddot.domain.service.salesapp.model.CommonNoteDTO;
import su.reddot.domain.service.salesapp.model.PurchaserNoteDTO;
import su.reddot.domain.service.salesapp.model.PurchaserNoteWithInfoDTO;
import su.reddot.domain.service.salesapp.model.SalesPlanCreateDto;
import su.reddot.domain.service.salesapp.model.SalesPlanDto;
import su.reddot.domain.service.salesapp.model.SalesmanPlansInfoDto;
import su.reddot.domain.service.salesapp.model.SalesmanShortInfoDto;
import su.reddot.domain.service.salesapp.model.SegmentDTO;
import su.reddot.domain.service.salesapp.model.SegmentFilterDTO;
import su.reddot.domain.service.salesapp.model.SegmentRequestDTO;
import su.reddot.domain.service.salesapp.model.StoreInfoDto;
import su.reddot.domain.service.salesapp.model.StoreSalesPlanDto;
import su.reddot.domain.service.salesapp.model.UserUploadDTO;
import su.reddot.domain.service.salesapp.model.UserIdWithVip;
import su.reddot.domain.service.salesapp.model.WorkScheduleDayUpdateDto;
import su.reddot.domain.service.salesapp.model.WorkScheduleResponseDto;
import su.reddot.domain.service.salesapp.model.WorkScheduleSettingRequestDto;
import su.reddot.domain.service.salesapp.model.WorkScheduleSettingResponseDto;
import su.reddot.domain.service.salesapp.model.WorkScheduleStoreSalesmanCountDto;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.domain.service.salesapp.model.AssignPurchaserRequest;
import su.reddot.presentation.api.v2.admin.whatsapp.WhatsAppApiClient;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@ConditionalOnProperty("purchaser-service.enabled")
public class DefaultSalesAppApiService implements SalesAppApiService {

    private final ClientSegmentsServiceApiClient clientSegmentsServiceApiClient;
    private final PurchaserServiceApiClient purchaserServiceApiClient;
    private final AdminWorkScheduleClient adminWorkScheduleClient;
    private final AdminWorkScheduleSettingsClient adminWorkScheduleSettingsClient;
    private final AdminSalePlanApiClient adminSalePlanApiClient;
    private final SalesmanApiClient salesmanApiClient;
    private final AdminAssignApiClient adminAssignApiClient;
    private final WhatsAppApiClient whatsAppApiClient;
    private final ProductSelectionRequestApiClient productSelectionRequestApiClient;
    private final UserRepository userRepository;
    private final StaticResourceBalancer staticResourceBalancer;

    @Override
    @Timed(value = "DefaultSalesAppApiService.createSegment")
    public SegmentDTO createSegment(SegmentRequestDTO clientSegment, Long userId) {
        return clientSegmentsServiceApiClient.createSegment(clientSegment, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllSegments")
    public Page<SegmentDTO> getAllSegments(
            SegmentFilterDTO filter,
            Long userId,
            Boolean assignedToCurrent,
            Pageable pageable
    ) {
        return clientSegmentsServiceApiClient.getAllSegments(
                filter, userId, assignedToCurrent, pageable.getPageNumber(), pageable.getPageSize()
        );
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSegment")
    public SegmentDTO getSegment(Long id) {
        return clientSegmentsServiceApiClient.getSegment(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.searchSegments")
    public Page<SegmentDTO> searchSegments(String query, Long userId, Boolean assignedToCurrent, Pageable pageable) {
        return clientSegmentsServiceApiClient.searchSegments(
                query, userId, assignedToCurrent, pageable.getPageNumber(), pageable.getPageSize()
        );
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateSegment")
    public SegmentDTO updateSegment(Long id, Long userId, SegmentRequestDTO clientSegment) {
        return clientSegmentsServiceApiClient.updateSegment(id, userId, clientSegment);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.deleteSegment")
    public void deleteSegment(Long id) {
        clientSegmentsServiceApiClient.deleteSegment(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.existsSegmentByName")
    public Boolean existsSegmentByName(String name, Long userId) {
        return clientSegmentsServiceApiClient.existsSegmentByName(name, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllSegmentIdsWithFilter")
    public List<Long> getAllSegmentIdsWithFilter(SegmentFilterDTO filter, Boolean assignedToCurrent, Long userId) {
        return clientSegmentsServiceApiClient.getAllIdsWithFilter(filter, assignedToCurrent, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllSegmentIdsBySearch")
    public List<Long> getAllSegmentIdsBySearch(String query, Long userId) {
        return clientSegmentsServiceApiClient.getAllIdsBySearch(query, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSegmentsByIds")
    public Page<SegmentDTO> getSegmentsByIds(List<Long> ids, Pageable pageable) {
        return clientSegmentsServiceApiClient.getByIds(ids, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllCommonNotes")
    public Page<CommonNoteDTO> getAllCommonNotes(Long userId, Pageable pageable) {
        return purchaserServiceApiClient.getAllCommonNotes(userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getOverallPurchaserNotes")
    public Page<PurchaserNoteWithInfoDTO> getOverallPurchaserNotes(Long userId, Pageable pageable) {
        return purchaserServiceApiClient.getOverallPurchaserNotes(userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllNotesForPurchaser")
    public Page<PurchaserNoteDTO> getAllNotesForPurchaser(Pageable pageable, Long purchaserId, Long userId) {
        return purchaserServiceApiClient.getAllNotesForPurchaser(pageable, purchaserId, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createPurchaserNote")
    public PurchaserNoteDTO createPurchaserNote(PurchaserNoteDTO note, Long userId) {
        return purchaserServiceApiClient.createPurchaserNote(note, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createCommonNote")
    public CommonNoteDTO createCommonNote(CommonNoteDTO note, Long userId) {
        return purchaserServiceApiClient.createCommonNote(note, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updatePurchaserNote")
    public PurchaserNoteDTO updatePurchaserNote(PurchaserNoteDTO note, Long noteId, Long userId) {
        return purchaserServiceApiClient.updatePurchaserNote(note, noteId, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateCommonNote")
    public CommonNoteDTO updateCommonNote(CommonNoteDTO note, Long noteId, Long userId) {
        return purchaserServiceApiClient.updateCommonNote(note, noteId, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.deleteNotes")
    public void deleteNotes(List<Long> noteIds) {
        purchaserServiceApiClient.deleteNotes(noteIds);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.detachNote")
    public CommonNoteDTO detachNote(Long noteId) {
        return purchaserServiceApiClient.detachNote(noteId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.attachNotes")
    public List<PurchaserNoteDTO> attachNotes(List<Long> noteIds, Long purchaserId) {
        return purchaserServiceApiClient.attachNotes(noteIds, purchaserId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.findPurchaserNotes")
    public Page<PurchaserNoteWithInfoDTO> findPurchaserNotes(String query, Long userId, Pageable pageable) {
        return purchaserServiceApiClient.findPurchaserNotes(query, userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.findCommonNotes")
    public Page<CommonNoteDTO> findCommonNotes(String query, Long userId, Pageable pageable) {
        return purchaserServiceApiClient.findCommonNotes(query, userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.searchWorkSchedules")
    public WorkScheduleResponseDto searchWorkSchedules(
            String salesmanName,
            List<Long> storeIds,
            String fromDate,
            String toDate,
            Integer page,
            Integer limit
    ) {
        WorkScheduleResponseDto response = adminWorkScheduleClient.searchWorkSchedules(
            salesmanName, storeIds, fromDate, toDate, page, limit
        );

        enrichWorkScheduleUsersAvatars(response);

        return response;
    }

    private void enrichWorkScheduleUsersAvatars(WorkScheduleResponseDto response) {
        List<Long> userIds = response.getWorkSchedules()
            .stream()
            .map(w -> w.getSalesman().getUserId())
            .collect(Collectors.toList());

        Map<Long, String> userAvatars = userRepository.findByIdIn(userIds)
            .stream()
            .collect(Collectors.toMap(User::getId, user -> Optional.ofNullable(user.getAvatarPath()).orElse("")));

        response
            .getWorkSchedules()
            .stream()
            .filter(workSchedule -> workSchedule.getSalesman() != null)
            .forEach(workSchedule -> {
                Long id = workSchedule.getSalesman().getUserId();
                String avatar = userAvatars.getOrDefault(id, "");
                String avatarFullPath = staticResourceBalancer.getImageFullPath(avatar);
                SalesmanShortInfoDto salesman = workSchedule.getSalesman();
                salesman.setAvatarPath(avatarFullPath);
            });
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateWorkScheduleDay")
    public void updateWorkScheduleDay(UUID workScheduleDayId, WorkScheduleDayUpdateDto workScheduleDay) {
        adminWorkScheduleClient.updateWorkScheduleDay(workScheduleDayId, workScheduleDay);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getStoreSalesmanCountList")
    public List<WorkScheduleStoreSalesmanCountDto> getStoreSalesmanCountList() {
        return adminWorkScheduleClient.getStoreSalesmanCountList();
    }

    @Override
    public List<StoreInfoDto> getAllowedStoreList() {
        return adminWorkScheduleClient.getAllowedStoreList();
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateSettings")
    public void updateSettings(Long salesmanId, WorkScheduleSettingRequestDto settings) {
        adminWorkScheduleSettingsClient.updateSettings(salesmanId, settings);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSettings")
    public WorkScheduleSettingResponseDto getSettings(Long salesmanId) {
        return adminWorkScheduleSettingsClient.getSettings(salesmanId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createSalesPlan")
    public SalesPlanDto createSalesPlan(String storeName, SalesPlanCreateDto salesPlan) {
        return adminSalePlanApiClient.createSalesPlanForStore(storeName, salesPlan);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateSalesPlan")
    public SalesPlanDto updateSalesPlan(Long id, SalesPlanCreateDto salesPlan) {
        return adminSalePlanApiClient.updateSalesPlan(id, salesPlan);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllPlans")
    public List<StoreSalesPlanDto> getAllPlans() {
        return adminSalePlanApiClient.getAllPlans();
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllSalesmenByStore")
    public List<Long> getAllSalesmenByStore(String storeName) {
        return adminSalePlanApiClient.getAllSalesmenByStore(storeName);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSalesmanPlans")
    public SalesmanPlansInfoDto getSalesmanPlans(Long id) {
        return adminSalePlanApiClient.getSalesmanById(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.deleteSalesPlan")
    public void deleteSalesPlan(Long id) {
        adminSalePlanApiClient.deleteSalesPlan(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createSelection")
    public SelectionRequestDTO createSelection(SelectionRequestDTO request, Long userId) {
        return purchaserServiceApiClient.createSelection(request, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateSelection")
    public SelectionRequestDTO updateSelection(SelectionRequestDTO request, Long id, Long userId) {
        return purchaserServiceApiClient.updateSelection(request, id, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.deleteSelection")
    public void deleteSelection(Long id) {
        purchaserServiceApiClient.deleteSelection(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSelectionDetails")
    public SelectionDetailsDTO getSelectionDetails(Long id) {
        return purchaserServiceApiClient.getSelectionDetails(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchasersList")
    public SelectionPurchasersDTO getPurchasersList(Long id) {
        return purchaserServiceApiClient.getPurchasersList(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllPlans")
    public Page<SelectionListDTO> getAllPlans(SelectionFilterDTO filter, Long userId, Pageable pageable) {
        return purchaserServiceApiClient.getAll(filter, userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getProductsInSelections")
    public List<ProductSelectionContainingDTO> getProductsInSelections(List<Long> productIds, Long userId) {
        return purchaserServiceApiClient.getProductsInSelections(productIds, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSelectionProductIdsForPurchaser")
    public List<Long> getSelectionProductIdsForPurchaser(Long userId) {
        return purchaserServiceApiClient.getSelectionProductIdsForPurchaser(userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSelectionsForPurchaser")
    public Page<SelectionListDTO> getSelectionsForPurchaser(Long purchaserId, Pageable pageable) {
        return purchaserServiceApiClient.getSelectionsForPurchaser(purchaserId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSelectionPurchaserView")
    public SelectionPurchaserViewDTO getSelectionPurchaserView(Long id, Long userId) {
        return purchaserServiceApiClient.getSelectionPurchaserView(id, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.addItemsToSelections")
    public void addItemsToSelections(
            List<Long> productIds,
            List<Long> segmentIds,
            List<Long> purchaserIds,
            List<Long> selectionIds,
            Long userId
    ) {
        purchaserServiceApiClient.addItemsToSelections(productIds, segmentIds, purchaserIds, selectionIds, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.countActiveSelections")
    public Long countActiveSelections(Long userId) {
        return purchaserServiceApiClient.countActiveSelections(userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllPurchasers")
    public Page<PurchaserListShortInfoDTO> getAllPurchasers(
            Boolean isVip,
            Boolean isActive,
            Long userId,
            Pageable pageable
    ) {
        return purchaserServiceApiClient.getAllPurchasers(isVip, isActive, userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchaserByPhoneNumber")
    public Page<PurchaserListShortInfoDTO> getPurchaserByPhoneNumber(
            String phoneNumber,
            Boolean isVip,
            Boolean isActive,
            Long userId,
            Pageable pageable
    ) {
        return purchaserServiceApiClient.getPurchaserByPhoneNumber(phoneNumber, isVip, isActive, userId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchasersByIds")
    public Page<PurchaserListShortInfoDTO> getPurchasersByIds(List<Long> ids, Pageable pageable) {
        return purchaserServiceApiClient.getByIds(ids, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createPurchaser")
    public PurchaserRequestDTO createPurchaser(PurchaserRequestDTO request, Long userId) {
        return purchaserServiceApiClient.createPurchaser(request, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updatePurchaser")
    public PurchaserRequestDTO updatePurchaser(PurchaserRequestDTO request, Long purchaserId, Long userId) {
        return purchaserServiceApiClient.updatePurchaser(request, purchaserId, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchaserLimitedInfo")
    public PurchaserLimitedInfoDTO getPurchaserLimitedInfo(Long purchaserId, Long userId) {
        return purchaserServiceApiClient.getLimitedInfo(purchaserId, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchaserFullInfo")
    public PurchaserFullInfoDTO getPurchaserFullInfo(Long clientId) {
        return purchaserServiceApiClient.getFullInfo(clientId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getImportantEvents")
    public Page<ImportantEventDTO> getImportantEvents(Long purchaserId, Pageable pageable) {
        return purchaserServiceApiClient.getImportantEvents(purchaserId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.completeImportantEvent")
    public void completeImportantEvent(Long id) {
        purchaserServiceApiClient.completeImportantEvent(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllImportantEventsForSalesman")
    public Page<ImportantEventUserInfoDTO> getAllImportantEventsForSalesman(Long salesmanId, Pageable pageable) {
        return purchaserServiceApiClient.getAllImportantEventsForSalesman(salesmanId, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createImportantEvent")
    public ImportantEventDTO createImportantEvent(ImportantEventDTO request, Long clientId) {
        return purchaserServiceApiClient.createImportantEvent(request, clientId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateImportantEvent")
    public ImportantEventDTO updateImportantEvent(ImportantEventDTO request, Long clientId, Long eventId) {
        return purchaserServiceApiClient.updateImportantEvent(request, clientId, eventId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.deleteImportantEvent")
    public void deleteImportantEvent(Long clientId, Long eventId) {
        purchaserServiceApiClient.deleteImportantEvent(clientId, eventId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchaserPersonalInfo")
    public PurchaserRequestDTO getPurchaserPersonalInfo(Long purchaserId) {
        return purchaserServiceApiClient.getPurchaserPersonalInfo(purchaserId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchasersWithFilter")
    public Page<PurchaserListShortInfoDTO> getPurchasersWithFilter(
            PurchaserFilterDTO filter,
            Long userId,
            Boolean assignedToCurrent,
            Pageable pageable
    ) {
        return purchaserServiceApiClient.getWithFilter(filter, userId, assignedToCurrent, pageable);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.checkIfExistsPurchaserByPhoneNumber")
    public PurchaserSalesmanBindingDTO checkIfExistsPurchaserByPhoneNumber(String phoneNumber, Long userId) {
        return purchaserServiceApiClient.checkIfExistsByPhoneNumber(phoneNumber, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchaserCounter")
    public CounterSummaryDTO getPurchaserCounter(Long purchaserId) {
        return purchaserServiceApiClient.getCounter(purchaserId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getPurchasersIdsWithFilter")
    public List<Long> getPurchasersIdsWithFilter(PurchaserFilterDTO filter, Boolean assignedToCurrent, Long userId) {
        return purchaserServiceApiClient.getIdsWithFilter(filter, assignedToCurrent, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createSalesman")
    public void createSalesman(SalesmanCreateDTO salesmanDto) {
        salesmanApiClient.createSalesman(salesmanDto);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.updateSalesman")
    public void updateSalesman(Long userId, SalesmanUpdateDTO salesmanDto) {
        salesmanApiClient.updateSalesman(userId, salesmanDto);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.deleteSalesman")
    public void deleteSalesman(Long userId) {
        salesmanApiClient.deleteSalesman(userId);
    }

    @Override
    public SalesmanDto getSalesman(Long userId) {
        return salesmanApiClient.getSalesman(userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.searchClients")
    public Collection<UserIdWithVip> searchClients(String salesmanId) {
        return adminAssignApiClient.getClientsBySalesman(salesmanId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.uploadPurchasers")
    public void uploadPurchasers(Collection<UserUploadDTO> users) {
        adminAssignApiClient.uploadPurchasersFromCSV(users);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllSalesmen")
    public Collection<SalesmanIdAndRole> getAllSalesmen(Long userId) {
        return adminAssignApiClient.getAllSalesmen(userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.redistributePurchasers")
    public void redistributePurchasers(Long saleId, Long userId) {
        adminAssignApiClient.redistributePurchasers(saleId, userId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.searchUnassignedClients")
    public Collection<UserIdWithVip> searchUnassignedClients() {
        return adminAssignApiClient.getUnassignedClients();
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.assignPurchaser")
    public void assignPurchaser(AssignPurchaserRequest request) {
        adminAssignApiClient.assignPurchaser(request);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getWhatsAppAvailableSalesmen")
    public List<SalesmanChatsDto> getWhatsAppAvailableSalesmen() {
        return whatsAppApiClient.getAvailableSalesmen();
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getWhatsAppChatMessages")
    public List<MessageDto> getWhatsAppChatMessages(Long userId, String chatId, int limit) {
        return whatsAppApiClient.getChatMessages(userId, chatId, limit);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getWhatsAppChatMessagesBeforeSearchedMessage")
    public List<MessageDto> getWhatsAppChatMessagesBeforeSearchedMessage(Long userId, String chatId, String messageId) {
        return whatsAppApiClient.getChatMessagesBeforeSearchedMessage(userId, chatId, messageId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createPurchasersBatch")
    public void createPurchasersBatch(List<PurchaserRequestDTO> request) {
        purchaserServiceApiClient.createPurchasersBatch(request);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.processReceipts")
    public void processReceipts(List<ReceiptDTO> receipts) {
        purchaserServiceApiClient.processReceipts(receipts);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllClothingSizes")
    public List<PurchaserSizeDTO> getAllClothingSizes() {
        return purchaserServiceApiClient.getAllClothingSizes();
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getAllFootwearSizes")
    public List<PurchaserSizeDTO> getAllFootwearSizes() {
        return purchaserServiceApiClient.getAllFootwearSizes();
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.createSelectionRequest")
    public ProductSelectionRequestDto createSelectionRequest(ProductSelectionRequestCreateDto request) {
        return productSelectionRequestApiClient.createSelectionRequest(request);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.getSelectionRequest")
    public ProductSelectionRequestDto getSelectionRequest(Long id) {
        return productSelectionRequestApiClient.getSelectionRequest(id);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.findAllProductSelectionRequests")
    public Page<ProductSelectionRequestDto> findAllProductSelectionRequests(Pageable pageable, RequestStatus status, String query) {
        return productSelectionRequestApiClient.findAll(pageable, status, query);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.addSelection")
    public void addSelection(Long requestId, Long selectionId) {
        productSelectionRequestApiClient.addSelection(requestId, selectionId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.sendToManager")
    public void sendToManager(Long requestId) {
        productSelectionRequestApiClient.sendToManager(requestId);
    }

    @Override
    @Timed(value = "DefaultSalesAppApiService.markAsSentToClient")
    public void markAsSentToClient(Long requestId) {
        productSelectionRequestApiClient.markAsSentToClient(requestId);
    }
}
