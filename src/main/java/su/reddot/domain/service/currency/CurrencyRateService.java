package su.reddot.domain.service.currency;

import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.service.dto.CurrencyRateDTO;
import su.reddot.domain.service.dto.currency.UpdateCurrencyRateRequest;

import java.math.BigDecimal;
import java.util.List;

public interface CurrencyRateService {
    List<CurrencyRate> getAllRates();

    CurrencyRate findCurrencyRateWithCurrencyIdAndCurrencyToId(long currencyId, long currencyToId);

    CurrencyRate update(long rateId, UpdateCurrencyRateRequest request);

    CurrencyRate updateRateValueByCurrencyId(long currencyId, long currencyToId, BigDecimal newRateValue, String rateSource);

    void save(CurrencyRate currencyRate);

    List<CurrencyRateDTO> getAllRatesDtos();
}
