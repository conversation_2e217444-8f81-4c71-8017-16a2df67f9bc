package su.reddot.domain.service.currency;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

@RequiredArgsConstructor
@Log4j
@Component
public class ProductBasePriceUpdateRunner {

    private final ProductRepository productRepository;
    private final CurrencyProductService currencyProductService;

    @Value("${app.product.price-in-currency.update.batch-size}")
    private Integer productBatchSize;

    @Transactional
    public void updateBasePrices() {
        Page<Product> productToUpdatePage = productRepository
                .findAllProductsForPriceUpdate(PageRequest.of(0, productBatchSize));

        log.info("Finds products to update current price: " + productToUpdatePage.getContent().size());

        currencyProductService.updateProductPrices(productToUpdatePage.getContent());

        Page<Product> productToUpdateRrpPricePage = productRepository
                .findAllProductsForRrpPriceUpdate(PageRequest.of(0, productBatchSize));

        log.info("Products for RRP price update: " + productToUpdateRrpPricePage.getContent().size());

        currencyProductService.updateProductRrpPrices(productToUpdateRrpPricePage.getContent());
    }
}
