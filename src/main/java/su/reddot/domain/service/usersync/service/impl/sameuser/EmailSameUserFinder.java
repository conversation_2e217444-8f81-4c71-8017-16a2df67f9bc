package su.reddot.domain.service.usersync.service.impl.sameuser;

import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.usersync.dto.UserSyncDataDTO;
import su.reddot.domain.service.usersync.service.UserWithSameEmailExistsException;

@Component
public class EmailSameUserFinder extends AbstractSameUserFinder {
    public EmailSameUserFinder(UserRepository userRepository, MessageSourceAccessor messageSourceAccessor) {
        super(userRepository, messageSourceAccessor);
    }

    @Override
    public void checkForSameUser(UserSyncDataDTO userSyncDataDTO) {
        User user = userRepository.findByEmail(userSyncDataDTO.getEmail());

        if (user != null) {
            throw new UserWithSameEmailExistsException(
                    messageSourceAccessor.getMessage(UserWithSameEmailExistsException.MESSAGE_KEY,
                            new Object[]{user.getId().toString(), user.getUid()}));
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
