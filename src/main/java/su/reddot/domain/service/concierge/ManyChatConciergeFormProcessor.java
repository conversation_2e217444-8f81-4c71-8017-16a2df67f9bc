package su.reddot.domain.service.concierge;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.concierge.ConciergeFormRepository;
import su.reddot.domain.model.concierge.ConciergeForm;
import su.reddot.domain.service.concierge.manychat.ManyChatService;
import su.reddot.infrastructure.manychat.dto.ManyChatResponseDTO;

import java.time.LocalDateTime;

import static su.reddot.domain.model.concierge.ConciergeForm.ManyChatStatus;

@Component
@Qualifier("ManyChat")
@Log4j
@RequiredArgsConstructor
public class ManyChatConciergeFormProcessor implements ConciergeFormProcessor {

    @Autowired
    private ManyChatConciergeFormProcessor self;
    private final ManyChatService manyChatService;
    private final ConciergeFormRepository conciergeFormRepository;

    @Override
    public void processConciergeForm(ConciergeForm conciergeForm) {
        Long formId = conciergeForm.getId();
        try {
            log.info("ManyChat. Start processing concierge form id: " + formId);
            if (conciergeForm.getManyChatStatus() == null || conciergeForm.getManyChatStatus() == ManyChatStatus.NEW_FORM) {
                processSubscriberCreation(conciergeForm);
            }
            if (conciergeForm.getManyChatStatus() == ManyChatStatus.USER_CREATED) {
                manyChatService.sendConciergeApplicationForm(conciergeForm);
                self.updateManyChatInfo(formId, ManyChatStatus.FORM_SAVED);
                log.info("ManyChat. Concierge form saved, formId " + formId);
            }
            if (conciergeForm.getManyChatStatus() == ManyChatStatus.FORM_SAVED) {
                manyChatService.sendInitMessage(conciergeForm);
                self.updateManyChatInfo(formId, ManyChatStatus.INIT_MESSAGE_SENT);
                log.info("ManyChat. Init message sent, formId " + formId);
            }
        } catch (RuntimeException e) {
            log.error("ManyChat. Error on manyChat integration: formId: " + formId, e);
            Integer newRetryCount = conciergeForm.getManyChatRetryCount() + 1;
            String errorMessage = e.getMessage();
            self.updateManyChatInfo(formId, newRetryCount, errorMessage);
        }
    }

    private void processSubscriberCreation(ConciergeForm conciergeForm) {
        Long formId = conciergeForm.getId();
        ManyChatResponseDTO.ManyChatDataDTO subscriber = manyChatService.findSubscriberByCustomPhone(conciergeForm);
        String subscriberId = subscriber == null ? manyChatService.createSubscriber(conciergeForm) : subscriber.getId();
        log.info("ManyChat subscriber created or found: " + subscriberId + ", formId " + formId);
        ManyChatStatus newStatus = ManyChatStatus.USER_CREATED;
        self.updateManyChatInfo(formId, newStatus, subscriberId);
    }

    @Transactional
    public void updateManyChatInfo(Long formId, ManyChatStatus newStatus) {
        conciergeFormRepository.updateManyChatInfo(formId, newStatus, LocalDateTime.now());
    }

    @Transactional
    public void updateManyChatInfo(Long formId, ManyChatStatus status, String subscriberId) {
        conciergeFormRepository.updateManyChatInfo(formId, status, subscriberId, LocalDateTime.now());
    }

    @Transactional
    public void updateManyChatInfo(Long formId, Integer retryCount, String errorMessage) {
        conciergeFormRepository.updateManyChatInfo(formId, retryCount, errorMessage);
    }

}
