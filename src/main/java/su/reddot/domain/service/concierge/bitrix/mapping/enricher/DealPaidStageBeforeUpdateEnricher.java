package su.reddot.domain.service.concierge.bitrix.mapping.enricher;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.concierge.BitrixDealStage;
import su.reddot.domain.model.concierge.OrderPositionBitrix;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.service.concierge.bitrix.BitrixService;
import su.reddot.domain.service.concierge.bitrix.requestbuilder.CreateBitrixRequestBuilderUtils;
import su.reddot.domain.service.concierge.bitrix.requestbuilder.dto.UpdateBitrixContactRequestDTO;
import su.reddot.domain.service.concierge.bitrix.requestbuilder.dto.UpdateBitrixDealRequestDTO;
import su.reddot.infrastructure.bitrix.dto.BitrixDealDTO;
import su.reddot.presentation.DeeplinkUtils;

import java.math.BigDecimal;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class DealPaidStageBeforeUpdateEnricher implements BitrixDealStageBeforeUpdateEnricher {

    @Value("${concierge.order-position-processing.bitrix.max-retry-count}")
    private Integer maxRetryCount;

    private final BitrixService bitrixService;
    private final DeeplinkUtils deeplinkUtils;

    @Override
    public BitrixDealStage getTargetStage() {
        return BitrixDealStage.DEAL_PAID;
    }

    @Override
    public void enrichFromOrderPositionBitrix(OrderPositionBitrix orderPositionBitrix,
                                              UpdateBitrixDealRequestDTO updateRequestDTO) {
        if (orderPositionBitrix.getIsNeedDealUpdateAfterHold()) {
            enrichOrderDataToUprateRequest(orderPositionBitrix, updateRequestDTO);
        }
        BigDecimal orderPositionAmount = orderPositionBitrix.getOrderPosition().getAmount();
        updateRequestDTO
                .setOrderId(orderPositionBitrix.getOrderPosition().getOrder().getId())
                .setOrderPositionAmount(orderPositionAmount.doubleValue());
    }

    private void enrichOrderDataToUprateRequest(OrderPositionBitrix orderPositionBitrix,
                                                UpdateBitrixDealRequestDTO updateRequestDTO) {
        Long bitrixDealId = orderPositionBitrix.getBitrixDealId();
        BitrixDealDTO bitrixDeal;
        try {
            bitrixDeal = bitrixService.getDealById(bitrixDealId);
        } catch (Exception e) {
            log.error("DealPaidStageBeforeUpdateEnricher.enrichOrderDataToUprateRequest error: can't find bitrix deal with id: " + bitrixDealId, e);
            orderPositionBitrix.addError(e.getMessage(), maxRetryCount);
            return;
        }

        String currentDealComments = Optional.ofNullable(bitrixDeal.getComments()).orElse("");

        OrderPosition orderPosition = orderPositionBitrix.getOrderPosition();
        String orderPositionComment = CreateBitrixRequestBuilderUtils.cookComment(orderPosition, deeplinkUtils);

        updateRequestDTO.setComment(currentDealComments + "\n\n" + orderPositionComment);

        Long bayerId = orderPosition.getOrder().getBuyer().getId();
        updateRequestDTO.setUpdateBitrixContactRequestDTO(new UpdateBitrixContactRequestDTO()
                .setBitrixContactId(bitrixDeal.getContactId())
                .setUserProfileLink(deeplinkUtils.getUserProfileDeeplink(String.valueOf(bayerId))));
    }
}
