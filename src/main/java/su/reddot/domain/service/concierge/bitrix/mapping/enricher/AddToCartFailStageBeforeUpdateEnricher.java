package su.reddot.domain.service.concierge.bitrix.mapping.enricher;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.concierge.BitrixDealStage;
import su.reddot.domain.model.concierge.BitrixEntityState;
import su.reddot.domain.model.concierge.OrderPositionBitrix;
import su.reddot.domain.service.concierge.bitrix.BitrixService;
import su.reddot.domain.service.concierge.bitrix.requestbuilder.CreateBitrixRequestBuilderUtils;
import su.reddot.domain.service.concierge.bitrix.requestbuilder.dto.UpdateBitrixDealRequestDTO;
import su.reddot.infrastructure.bitrix.dto.BitrixDealDTO;

import java.time.ZonedDateTime;

@Component
@RequiredArgsConstructor
public class AddToCartFailStageBeforeUpdateEnricher implements BitrixDealStageBeforeUpdateEnricher {

    private final BitrixService bitrixService;

    @Override
    public BitrixDealStage getTargetStage() {
        return BitrixDealStage.ADD_TO_CART_FAIL;
    }

    @Override
    public void enrichFromOrderPositionBitrix(OrderPositionBitrix orderPositionBitrix,
                                              UpdateBitrixDealRequestDTO updateRequestDTO) {
        String errorMessage = orderPositionBitrix.getAddToCartErrorMessage();
        if (StringUtils.isEmpty(errorMessage)) {
            orderPositionBitrix.setState(BitrixEntityState.FAILED);
            return;
        }

        orderPositionBitrix.addError(errorMessage, 1);

        Long bitrixDealId = orderPositionBitrix.getBitrixDealId();
        BitrixDealDTO bitrixDeal = bitrixService.getDealById(bitrixDealId);

        String oldAddToCartErrorMessages = bitrixDeal.getAddToCartErrorMessages() == null ? "" : bitrixDeal.getAddToCartErrorMessages() + "\n\n";
        String newAddToCartErrorMessages = oldAddToCartErrorMessages +
                CreateBitrixRequestBuilderUtils.cookAddToCartErrorMessage(errorMessage, ZonedDateTime.now());

        updateRequestDTO.setAddToCartErrorMessage(newAddToCartErrorMessages);
    }

}
