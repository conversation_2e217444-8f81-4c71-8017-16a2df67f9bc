package su.reddot.domain.service.duty.calculators;

import su.reddot.domain.model.bargain.Bargain;
import su.reddot.domain.model.duty.OrderDuty;
import su.reddot.domain.model.duty.OrderPositionDuty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.service.dto.duty.DutyDTO;
import su.reddot.domain.service.dto.order.OrderDTO;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

public interface DutyCalculator {
    List<DutyDTO> calculateDutiesByPriceWithCommission(BigDecimal priceWithCommission, BigDecimal priceWithoutCommission);
    List<DutyDTO> calculateDutiesByPriceWithoutCommission(BigDecimal priceWithCommission, BigDecimal priceWithoutCommission);
    List<DutyDTO> calculateDutiesByPriceWithAndWithoutDuties(BigDecimal priceWithDuties, BigDecimal priceWithoutDuties);
    List<OrderPositionDuty> recalculateOrderPositionDutiesOnHold(OrderPosition orderPosition);
    List<OrderPositionDuty> calculateOrderPositionDuties(OrderPosition orderPosition);
    List<OrderPositionDuty> recalculateOrderPositionDutiesAfterDiscount(OrderPosition orderPosition);
    BigDecimal calculateBargainDutiesAmount(@NotNull Bargain bargain);
    BigDecimal fixCustomCommissionFromAdmin(@NotNull BigDecimal amount);

    // Order duties

    List<DutyDTO> calculateOrderDTODuties(@NotNull OrderDTO order);
    List<OrderDuty> calculateOrderDutiesOnInit(@NotNull Order order);
    List<OrderDuty> recalculateOrderDutiesOnConfirm(@NotNull Order order);
    List<OrderDuty> recalculateOrderDutiesOnSaleResolution(@NotNull Order order, @NotNull BigDecimal actualAmountToPay);

    interface DutyCalculatorParams {
        boolean isValid();
    }
}