package su.reddot.domain.service.filter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import io.micrometer.core.annotation.Timed;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.ProductModelDTO;
import su.reddot.domain.service.dto.order.OrderSourceDTO;
import su.reddot.domain.service.filter.model.ProductFiltrationContext;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.model.sorting.ProductSorting;
import su.reddot.domain.service.filter.processor.filter.FilterProcessor;
import su.reddot.domain.service.filter.processor.filter.FilterProcessorProvider;
import su.reddot.domain.service.filter.processor.filter.impl.CategoryFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.NewResaleValues;
import su.reddot.domain.service.filter.processor.sorting.SortingProcessor;
import su.reddot.domain.service.filter.processor.sorting.SortingProcessorProvider;
import su.reddot.domain.service.filter.value.FilterValue;
import su.reddot.domain.service.filter.value.impl.IdListValue;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;
import su.reddot.presentation.api.v2.filter.ProductFilterItemsRequest;
import su.reddot.presentation.api.v2.filter.ProductFilterRequest;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

public abstract class FilterTransformationService {

    @Autowired
    private ProductFiltrationEngineSupport productFiltrationEngineSupport;

    protected FilterSpecification createFilterSpecificationInternal(ProductFiltrationContext filtrationContext) {
        FilterSpecification spec = new FilterSpecification();
        Set<String> filterKeys = new HashSet<>();
        if (filtrationContext != null && StringUtils.isNotBlank(filtrationContext.getCurrencyCode())) {
            spec.currencyCode(filtrationContext.getCurrencyCode());
        }
        if (filtrationContext != null && MapUtils.isNotEmpty(filtrationContext.getFilterValues())) {
            filterKeys.addAll(filtrationContext.getFilterValues().keySet());
        }
        if (filtrationContext != null && MapUtils.isNotEmpty(filtrationContext.getPresetValues())) {
            filterKeys.addAll(filtrationContext.getPresetValues().keySet());
        }
        if (CollectionUtils.isNotEmpty(filterKeys)) {
            filterKeys.forEach(filterKey -> {
                FilterProcessor<? extends FilterValue> filterProcessor =
                        getFilterProcessorProvider().findProcessorOrThrow(filterKey);
                filterProcessor.appendFilterSpecification(spec, filterKey, filtrationContext);
            });
        }
        return spec;
    }

    @Timed(value = "FilterTransformationService.fillFiltrationContext")
    public void fillFiltrationContext(ProductFiltrationContext filtrationContext,
            ProductFilterInfoRequest request) {

        if (request != null && request.getSearch() != null) {
            filtrationContext.setSearchQuery(request.getSearch().getQuery());
        }

        if (request != null) {
            filtrationContext.setBaseCategory(request.getBaseCategory());
            filtrationContext.setCountryId(request.getCountryId());
        }

        if (request != null && StringUtils.isNotBlank(request.getCurrencyCode())) {
            filtrationContext.setCurrencyCode(request.getCurrencyCode());
        }

        if (request != null && MapUtils.isNotEmpty(request.getFilters())) {
            fillFilterValues(request.getFilters(), filtrationContext);
        }

        if (request != null && MapUtils.isNotEmpty(request.getHiddenFilters())) {
            fillFilterValues(request.getHiddenFilters(), filtrationContext);
        }

        if (request != null && MapUtils.isNotEmpty(request.getPresets())) {
            fillFilterValues(request.getPresets(), filtrationContext::addPresetValue);
        }

        // если нет фильтра по категориям, но есть baseCategory или предустановки по категории,
        // то надо добавлять пустой фильтр по категориям, чтобы сработал обработчик
        if ((filtrationContext.getBaseCategory() != null
                || filtrationContext.getPresetValues().containsKey(CategoryFilterProcessor.FILTER_CODE))
                && !filtrationContext.getFilterValues().containsKey(CategoryFilterProcessor.FILTER_CODE)) {
            IdListValue value = new IdListValue(Collections.emptyList());
            filtrationContext.addFilterValue(CategoryFilterProcessor.FILTER_CODE, value);
        }
    }

    public void fillFilterValues(Map<String, JsonNode> filterJsonValues,
                                 ProductFiltrationContext filtrationContext) {
        fillFilterValues(filterJsonValues, filtrationContext::addFilterValue);
    }

    public void fillFilterValues(Map<String, JsonNode> filterJsonValues,
                                 BiConsumer<String, FilterValue> filterValueFiller) {
        for (Map.Entry<String, JsonNode> filterEntry : filterJsonValues.entrySet()) {
            FilterProcessor<? extends FilterValue> filterProcessor =
                    getFilterProcessorProvider().findProcessorOrThrow(filterEntry.getKey());
            FilterValue filterValue = filterProcessor.parseValue(filterEntry.getValue());
            if (filterValue != null) {
                filterValueFiller.accept(filterEntry.getKey(), filterValue);
            }
        }
    }

    @Timed(value = "FilterTransformationService.fillFiltrationContext")
    public void fillFiltrationContext(ProductFiltrationContext filtrationContext,
            ProductFilterItemsRequest request) {

        fillFiltrationContext(filtrationContext, (ProductFilterInfoRequest) request);

        //Пришлось добавить сюда опции фильтрации, из-за особенностей архитектуры
        ProductFiltrationEngine.FiltrationEngineOptions options = new ProductFiltrationEngine.FiltrationEngineOptions();
        productFiltrationEngineSupport.fillFiltrationEngineOptions(options);
        productFiltrationEngineSupport.fillSearchExperiments(options);

        SortingProcessor sortingProcessor = getSortingProcessorProvider().findProcessorOrThrow(request.getSorting(), filtrationContext, options);
        filtrationContext.setSorting(sortingProcessor.getCode());
    }

    @Timed(value = "FilterTransformationService.fillFiltrationContext")
    public void fillFiltrationContext(ProductFiltrationContext filtrationContext,
            ProductFilterRequest request) {

        fillFiltrationContext(filtrationContext, (ProductFilterItemsRequest) request);

        filtrationContext.setWithValues(Boolean.TRUE.equals(request.getWithAvailableValues()));
    }

    /**
     * Преобразование доступных фильтров ядра в список фильтров нового формата
     */
    @Timed(value = "FilterTransformationService.transformFilters")
    public List<ProductFilter> transformFilters(AvailableFilters availableFilters,
            ProductFiltrationContext filtrationContext) {
        List<ProductFilter> filters = new ArrayList<>();
        getFilterProcessorProvider().findAllOrdered()
                .stream()
                .filter(filterProcessor -> filtrationContext.getOnlyFilterTypes().isEmpty() ||
                                filtrationContext.getOnlyFilterTypes().contains(filterProcessor.getType()))
                .filter(filterProcessor ->
                        !filtrationContext.getIgnoredFilterTypes().contains(filterProcessor.getType()))
                .forEach(filterProcessor ->
                        filters.addAll(filterProcessor.getFilters(availableFilters, filtrationContext)));

        return filters;
    }

    @Timed(value = "FilterTransformationService.transformSorting")
    public List<ProductSorting> transformSorting(ProductFiltrationContext filtrationContext, @Nullable ProductFiltrationEngine.FiltrationEngineOptions options) {
        return getSortingProcessorProvider()
                .findVisibleOrdered(filtrationContext, options)
                .stream()
                .map(processor -> processor.getSorting(filtrationContext))
                .collect(Collectors.toList());
    }

    protected abstract FilterProcessorProvider getFilterProcessorProvider();

    protected abstract SortingProcessorProvider<? extends SortingProcessor> getSortingProcessorProvider();

    /**
     * Перечень атрибутов, по которым можно отфильтровать множество объектов.
     * Если атрибут задан в виде списка, то фильтрация товара по этому атрибуту
     * происходит с использованием операции ИЛИ: чтобы попасть в результат выборки,
     * товар должен удовлетворять хотя бы одному значению атрибута.
     */
    @Getter
    @Setter
    @Accessors(fluent = true, chain = true)
    @ToString(doNotUseGetters = true, includeFieldNames = true)
    @EqualsAndHashCode
    public static class FilterSpecification {

        /**
         * Список id товара, для выборки списка товаров по id
         * Просьба добавить от разработчиков mob.dev для реализации посадочных страниц
         */
        private Collection<Long> productsIds = Collections.emptyList();

        private Collection<Long> productRequestIds = Collections.emptyList();

        private Collection<Long> productRequestUserIds = Collections.emptyList();

        private Collection<Long> productResponseUserIds = Collections.emptyList();

        /**
         * Базовая категория в виде строки
         */
        private String baseCategoryString = null;

        /**
         * Список категорий, к которым должен принадлежать товар.
         * При выборке используется оператор ИЛИ:
         * для заданных категорий [1, 2, 3] будет выбран товар,
         * который находится в категории 1 ИЛИ товар, который находится
         * в категории 2 ИЛИ товар, который находится в категории 3
         */
        private Collection<Long> categoriesIds = Collections.emptyList();

        /**
         * Список категорий, к которым НЕ должен принадлежать товар.
         */
        private Collection<Long> exceptCategoriesIds = Collections.emptyList();

        /**
         * Состояние, в котором должен находиться товар, чтобы попасть в выборку
         */
        private ProductState state;

        /**
         * Состояния, в которых должен находиться товар, чтобы попасть в выборку
         */
        private Collection<ProductState> states;

        /**
         * Список конкретных значений атрибутов, которыми должен обладать товар.
         */
        private Collection<Long> interestingAttributeValues = Collections.emptyList();

        /**
         * Список размеров, которые должен иметь товар.
         */
        private Collection<Long> interestingSizes = Collections.emptyList();

        /**
         * Список типов размеров, которые установлены для товара.
         */
        private Collection<SizeType> interestingSizeTypes = Collections.emptyList();

        /**
         * Список брендов, одному из которых должен принадлежать товар
         */
        private Collection<Long> interestingBrands = Collections.emptyList();

        /**
         * Список брендов, к которым не должен принадлежать товар
         */
        private Collection<Long> exceptBrands = Collections.emptyList();

        /**
         * Список моделей вещей, одной из которых должен принадлежать продукт
         */
        private Collection<Long> interestingProductModels = Collections.emptyList();

        /**
         * Список моделей вещей, к которым не должен принадлежать товар
         */
        private Collection<Long> exceptProductModels = Collections.emptyList();

        /**
         * Список продавцов, которым не должен принадлежать товар
         */
        private Collection<Long> exceptSellers = Collections.emptyList();

        /**
         * Список интересущих состояний товара
         */
        private Collection<Long> interestingConditions = Collections.emptyList();

        /**
         * Список типов продавцов, которым должен принадлежать продавец
         */
        private Collection<SellerType> interestingSellerTypes = Collections.emptyList();

        /**
		 * Список типов продавцов, которым не должен принадлежать продавец
		 */
        private Collection<SellerType> exceptSellerTypes = Collections.emptyList();

        /**
		 * Список тегов пользователя, которым не должен принадлежать продавец
		 */
        private Collection<String> exceptUserTags = Collections.emptyList();

        /**
         * Список id вторых главных в которых нужно искать продукты
         */
        private Collection<String> interestingBannerSettingIds;

        /**
         * Интересущий пол (мужской/женский)
         */
        private User.Sex sex;

        /**
         * Идентификатор продавца, которому должен принадлежать товар
         */
        private Long sellerId;

        /**
         * Идентификаторы продавцов, которым должен принадлежать товар
         */
        private Collection<Long> sellerIds;

        private Boolean isPro;

        private Boolean isVip;

        private BigDecimal startPrice;

        private BigDecimal endPrice;

        private String currencyCode;

        private Boolean isDescriptionExists;

        /* -- Фильтровать по наличию булевого флага (тега) -- */

        /**
         * Только винтажные товары
         */
        private Boolean isVintage;

        /**
         * Только совершенно новые товары
         */
        private Boolean isBrandNew;

        /**
         * Только товары, которые продаются по сниженной цене
         */
        private Boolean isOnSale;

        /**
         * Только товары, по которым стартовая цена выше текущей.
         * В отличае от isOnSale выбираются только те товары, у который start_price > current_price,
         * тогда как для isOnSale выбираются так же и товары у который rrp > current_price, а реального снижения не было
         */
        private Boolean isStartPriceHigher;

        /**
         * Только товары с отметкой "Наш выбор"
         */
        private Boolean hasOurChoice;

        /**
         * Только товары Эксклюзивной Селекции
         */
        private Boolean isExclusiveSelection;

        /**
         * Только товары с отметкой beegz
         */
        private Boolean isBeegz;

        /**
         * Только товары с отметкой "Новая коллекция"
         */
        private Boolean isNewCollection;

        private String sellerEmailSubstring;

        /**
         * В случае true - только записи с rrp_price != null
         * В случае false - только записи с rrp_price == null
         * В случае null - все записи
         */
        private Boolean withRrp;

        /**
         * Только опубикованные до указанной метки времени (секунды)
         **/
        private Long isPublishedBeforeTimestamp;

        /**
         * Товар был ранее опубликован или опубликован сейчас (поле publish_time != null)
         **/
        private Boolean wasPublished;

        /**
         * Только опубикованные после указанной метки времени (секунды)
         **/
        private Long isPublishedAfterTimestamp;

        /**
         * список ID ретушеров
         */
        private Collection<Long> retoucherIds;

        /**
         * Находится ли товар на складе Oskelly
         */
        private Boolean isInStock;

        /**
         * Находится ли товар в бутике Oskelly
         */
        private Boolean isInBoutique;

        /**
         * Имеются ли слайды (сторис) для товара
         */
        private Boolean hasSlides;

        /**
         * Подстрока для поиска по артикулу производителя
         */
        private String vendorCodeContains;

        /**
         * Подстрока для поиска по артикулу бутика
         */
        private String storeCodeContains;

        /**
         * Список тегов местонахождения
         */
        private Collection<Long> locationTags;

        /**
         * Список тегов местонахождения, которые соответствуют только бутикам
         */
        private Collection<Long> boutiqueLocationTags;

        /**
         * Флаг выдачи товаров, располагающихся только оффлайн
         */
        private boolean offlineOnly;

        /**
         * Список источников заказов
         */
        private Collection<Long> orderSourceInfoIds;

        private Collection<OrderState> orderStates;

        private Collection<OrderSourceDTO> orderSources;

        private NewResaleValues newResaleSelection;

        public Collection<ProductState> states() {
            if (states == null && state == null) return Collections.emptyList();
            Set<ProductState> result = new HashSet<>();
            if (states != null) {
                for (ProductState s : states) {
                    if (s != null) result.add(s);
                }
            }
            if (state != null) result.add(state);
            return result;
        }

        public boolean statesContain(ProductState productState) {
            return states().contains(productState);
        }

        public boolean statesContainAnyOfStatesExceptOthers(ProductState... productStates) {
            Collection<ProductState> _states = states();
            if (_states.isEmpty()) return false;
            if (productStates != null) {
                for (ProductState productState : productStates) {
                    _states.remove(productState);
                }
            }
            return _states.isEmpty();
        }

        public boolean statesContainOthersExceptOne(ProductState exceptState) {
            return statesContainOthersExceptFew(exceptState);
        }

        public boolean statesContainOthersExceptFew(ProductState... exceptStates) {
            Collection<ProductState> _states = states();
            if (_states.isEmpty()) return false;
            if (exceptStates != null) {
                for (ProductState exceptState : exceptStates) {
                    _states.remove(exceptState);
                }
            }
            return !_states.isEmpty();
        }

        public Collection<Long> sellerIds() {
            if (this.sellerId == null && CollectionUtils.isEmpty(this.sellerIds)) {
                return Collections.emptyList();
            }

            Set<Long> result = new HashSet<>();

            if (this.sellerIds != null) {
                for (Long id : this.sellerIds) {
                    if (id != null) {
                        result.add(id);
                    }
                }
            }

            if (this.sellerId != null) {
                result.add(this.sellerId);
            }

            return result;
        }
    }

    @RequiredArgsConstructor
    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Accessors(chain = true)
    public static class AvailableFilters {
        private List<Long> filter = new ArrayList<>();

        private List<Long> category = new ArrayList<>();

        private List<Long> size = new ArrayList<>();

        private List<Long> brand = new ArrayList<>();

        private List<ProductModelDTO> productModel = new ArrayList<>();

        private List<Long> productCondition = new ArrayList<>();

        /** Только винтажные товары */
        private boolean isVintage = false;

        /** Только товары, которые продаются по сниженной цене (с учетом RRP) */
        private boolean isOnSale = false;

        /** Только товары, по которой стартовая цена выше текущей (RRP не считается) */
        private boolean isStartPriceHigher = false;

        /** Только товары с отметкой "Наш выбор" */
        private boolean hasOurChoice = false;

        /** Только товары Эксклюзивной Селекции (Закрытая распродажа) */
        private boolean isExclusiveSelection = false;

        /** Только товары с отметкой "Beegz" */
        private boolean isStreetwear = false;

        /** Только товары с отметкой "Новая коллекция" */
        private boolean isNewCollection = false;

        /** Только товары с тегом "Склад" */
        private Boolean isInStock = false;

        /** Только товары с отметкой "В бутике" */
        private Boolean isInBoutique = false;

        /** Только товары с ценой выше */
        private BigDecimal startPrice;

        /** Только товары с ценой ниже */
        private BigDecimal endPrice;

        /** Количество доступных по фильру товаров */
        private Long productCount;

        private List<Long> locationTags = new ArrayList<>();

        private List<Long> boutiqueLocationTags = new ArrayList<>();

        private List<Long> sourceInfoIds = new ArrayList<>();

        private List<Long> sellerTypes = new ArrayList<>();
    }
}
