package su.reddot.domain.service.filter.processor.filter.impl;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.service.dto.ProductModelDTO;
import su.reddot.domain.service.dto.productrequest.ProductRequestDTO;
import su.reddot.domain.service.filter.FilterTransformationService;
import su.reddot.domain.service.filter.model.ProductFiltrationContext;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.model.filter.impl.MultiListFilter;
import su.reddot.domain.service.filter.model.filter.impl.list.ListSection;
import su.reddot.domain.service.filter.model.filter.impl.list.ListSectionEntry;
import su.reddot.domain.service.filter.model.filter.impl.list.ListValue;
import su.reddot.domain.service.filter.value.impl.IdListValue;
import su.reddot.domain.service.product.AvailableFilterTypes;
import su.reddot.domain.service.product.AvailableProductFilterTypes;
import su.reddot.domain.service.product.ProductModelService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ModelFilterProcessor extends IdsFilterProcessor {

    public static final String TYPE = "MODEL";

    public static final String FILTER_CODE = "model";

    public static final String FILTER_NAME = "filter.processor.ModelFilterProcessor.filterName";

    private final ProductModelService productModelService;

    private final List<Class<?>> filteredObjectClasses = ImmutableList.of(Product.class, ProductRequestDTO.class);

    public ModelFilterProcessor(MessageSourceAccessor messageSourceAccessor, ProductModelService productModelService) {
        super(messageSourceAccessor);
        this.productModelService = productModelService;
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    protected List<Class<?>> getFilteredObjectClasses() {
        return filteredObjectClasses;
    }

    @Override
    public boolean isApplicable(String filterCode) {
        return FILTER_CODE.equals(filterCode);
    }

    @Override
    public int getProductFilterOrder() {
        return 70;
    }

    @Override
    public void appendFilterSpecification(FilterTransformationService.FilterSpecification filterSpecification,
                                          String filterCode, IdListValue values, IdListValue presets,
                                          ProductFiltrationContext filtrationContext) {
        if (values != null && CollectionUtils.isNotEmpty(values.getValues())) {
            filterSpecification.interestingProductModels(values.getValues());
        } else if (presets != null && CollectionUtils.isNotEmpty(presets.getValues())) {
            filterSpecification.interestingProductModels(presets.getValues());
        }
    }

    @Override
    @Timed(value = "ModelFilterProcessor.getFilters")
    public List<ProductFilter> getFilters(FilterTransformationService.AvailableFilters availableFilters,
                                          ProductFiltrationContext filtrationContext) {
        return getFilterInternal(
                availableFilters, filtrationContext,
                Boolean.TRUE.equals(filtrationContext.isWithValues())
        ).map(Collections::singletonList)
         .orElseGet(Collections::emptyList);
    }

    @Override
    @Timed(value = "ModelFilterProcessor.getFilter")
    public Optional<ProductFilter> getFilter(String filterCode, FilterTransformationService.AvailableFilters availableFilters,
                                             ProductFiltrationContext filtrationContext) {
        if (!FILTER_CODE.equals(filterCode)) {
            return Optional.empty();
        }

        return getFilterInternal(availableFilters, filtrationContext, true);
    }

    @Override
    protected Set<AvailableFilterTypes> requiredAvailableFiltersForce(Class<?> filteredObjectClass) {
        return Collections.singleton(AvailableProductFilterTypes.MODELS);
    }

    private Optional<ProductFilter> getFilterInternal(FilterTransformationService.AvailableFilters availableFilters,
                                                      ProductFiltrationContext filtrationContext,
                                                      boolean withValues) {
        if (availableFilters != null && availableFilters.getProductModel() != null) {

            Set<Long> selectedModelIds = new HashSet<>(getFilterIds(FILTER_CODE, filtrationContext));
            Set<Long> presetModelIds = new HashSet<>(getPresetIds(FILTER_CODE, filtrationContext));

            Set<ProductModelDTO> availableModels = new HashSet<>();

            // если есть преднастройки, то ограничиваем список доступных моделей ими
            if (CollectionUtils.isNotEmpty(presetModelIds)) {
                availableFilters.getProductModel().stream()
                                .filter(model -> presetModelIds.contains(model.getId()))
                                .forEach(availableModels::add);
            } else {
                availableModels.addAll(availableFilters.getProductModel());
            }

            selectedModelIds.stream()
                            .map(productModelService::getProductModelByIdCached)
                            .map(ProductModelDTO::new)
                            .forEach(availableModels::add);

            if (availableModels.isEmpty()) {
                return Optional.empty();
            }

            if (!withValues) {
                List<ListValue> selectedValues =
                        availableModels.stream()
                                       .filter(model -> selectedModelIds.contains(model.getId()))
                                       .map(model -> new ListValue(model.getId(), model.getName()))
                                       .collect(Collectors.toList());
                return Optional.of(
                        new MultiListFilter(
                                FILTER_CODE, messageSourceAccessor.getMessage(FILTER_NAME),
                                null, null, null, null,
                                selectedValues,
                                Collections.singletonList(messageSourceAccessor.getMessage(FILTER_NAME)), true));
            }
            List<ListValue> selectedValues = new ArrayList<>();
            List<ListSectionEntry> entries =
                    availableModels.stream()
                                   .map(productModel -> {
                                       boolean isSelected = false;
                                       if (selectedModelIds.contains(productModel.getId())) {
                                           isSelected = true;
                                           selectedValues.add(
                                                   new ListValue(productModel.getId(), productModel.getName()));
                                       }
                                       return new ListSectionEntry(
                                               productModel.getId(), productModel.getName(), null, isSelected, null);
                                   })
                                   .sorted(Comparator.comparing(ListSectionEntry::getValue))
                                   .collect(Collectors.toList());

            return Optional.of(
                    new MultiListFilter(
                            FILTER_CODE, messageSourceAccessor.getMessage(FILTER_NAME), null, null,
                            Collections.singletonList(
                                    new ListSection(messageSourceAccessor.getMessage(FILTER_NAME), entries)),
                            null, selectedValues,
                            Collections.singletonList(messageSourceAccessor.getMessage(FILTER_NAME)), true));
        }
        return Optional.empty();
    }
}
