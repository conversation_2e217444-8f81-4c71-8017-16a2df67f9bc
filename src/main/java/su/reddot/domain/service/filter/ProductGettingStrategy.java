package su.reddot.domain.service.filter;


import lombok.NonNull;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.filter.ProductFiltrationEngine.GetProductsOptions;
import su.reddot.domain.service.filter.ProductFiltrationEngine.ProductFiltrationPageRequest;

import static su.reddot.domain.service.product.ProductService.FilterSpecification;

public interface ProductGettingStrategy {

    Page<Product> getRawProductPage(
        @NonNull FilterSpecification spec,
        @NonNull ProductFiltrationPageRequest pageRequest,
        @NonNull GetProductsOptions options
    );

    Page<ProductDTO> getProductPage(
        @NonNull FilterSpecification spec,
        @NonNull ProductFiltrationPageRequest pageRequest,
        @NonNull GetProductsOptions options
    );

    Page<Long> getProductIdPage(
        @NonNull FilterSpecification spec,
        @NonNull ProductFiltrationPageRequest pageRequest,
        @NonNull GetProductsOptions options
    );
}