package su.reddot.domain.service.filter;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Getter
public class FilterSpellCheckConfig {
    @Value("${external-spellcheck-service.enabled}")
    private boolean enabled;
    @Value("${external-spellcheck-service.flagr-flag}")
    private String flagrFlag;
    @Value("${external-spellcheck-service.min-symbols-to-check}")
    private Integer minSymbolsToCheck;
}
