package su.reddot.domain.service.filter;

import com.google.common.collect.ImmutableList;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.model.filter.impl.BooleanFilter;
import su.reddot.presentation.api.v2.filter.Source;

import java.util.List;
import java.util.stream.Collectors;

import static org.apache.cxf.common.util.CollectionUtils.isEmpty;

public abstract class HotFiltersRetriever {

    public List<String> getHotFilterCodes(
            Source source,
            List<ProductFilter> availableFilters
    ) {
        return filterByAvailableFilters(getAllHotFilterCodes(source), availableFilters);
    }

    protected abstract List<String> getAllHotFilterCodes(Source source);

    private List<String> filterByAvailableFilters(List<String> filterCodes, List<ProductFilter> availableFilters) {

        if (isEmpty(filterCodes)) {
            return ImmutableList.of();
        }

        if (isEmpty(availableFilters)) {
            return filterCodes;
        }

        return filterCodes.stream()
                .filter(it ->
                        availableFilters.stream()
                                .filter(availableFilter -> availableFilter.getCode().equals(it))
                                .findAny()
                                .map(availableFilter -> {
                                    if (availableFilter instanceof BooleanFilter) {
                                        return ((BooleanFilter) availableFilter).getIsEnabled();
                                    } else {
                                        return true;
                                    }
                                })
                                .orElse(false))
                .collect(Collectors.toList());
    }
}