package su.reddot.domain.service.productrequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.productResponse.MotivateCreateProductResponseNotification;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.experiments.ExperimentDTO;
import su.reddot.domain.service.dto.productrequest.ProductResponseUsersRequestParams;
import su.reddot.domain.service.experiments.ExperimentsService;
import su.reddot.domain.service.master.MasterService;
import su.reddot.domain.service.master.MasterServiceRequest;
import su.reddot.domain.service.notification.NotificationService;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class MotivateCreateProductResponseNotificationRunner {

    private final static String MASTER_URI = "/api/v2/master/notification/productRequestNotifications";

    private final UserRepository userRepository;
    private final NotificationRepository<Notification> notificationRepository;
    private static final List<String> TARGET_DEVICE_DTYPES =
            Arrays.asList(DeviceDtype.AppleDevice.name(), DeviceDtype.AndroidDevice.name());
    private final ProductRequestService productRequestService;
    private final DeviceRepository deviceRepository;
    private final NotificationService notificationService;
    private final MasterService masterService;
    private final ExperimentsService experimentsService;

    public Integer createMotivateCreateProductRequestNotifications(Integer delayBetweenNotificationDays, Integer batchSize) {
        PageRequest firstPage = PageRequest.of(0, batchSize);
        ZonedDateTime registeredAfter = getAfterTime(90);
        Page<User> userPage = userRepository.findUserWithoutProducts(registeredAfter.toLocalDateTime(), delayBetweenNotificationDays, firstPage);
        ZonedDateTime afterTime = getAfterTime(delayBetweenNotificationDays);
        int resultCount = 0;
        while (true) {
            List<Notification> notifications = processPage(userPage, afterTime);
            resultCount += notifications.size();
            if (userPage.hasNext()) {
                userPage = userRepository.findUserWithoutProducts(registeredAfter.toLocalDateTime(), delayBetweenNotificationDays, userPage.nextPageable());
            } else {
                return resultCount;
            }
        }
    }

    private List<Notification> processPage(Page<User> userPage, ZonedDateTime afterTime) {
        List<User> usersWithoutProduct = userPage.getContent();

        Set<Long> userIdsWithExperiment = experimentsService.filterUsersWithExperiment(usersWithoutProduct.stream()
                        .map(User::getId)
                        .collect(Collectors.toList()),
                ExperimentsService.PRODUCT_REQUEST_KEY
        );
        Set<Long> userIdsWithNotification = notificationRepository.findAllByUserAndDTypeAndCreateTimeAfter(
                        userIdsWithExperiment,
                        MotivateCreateProductResponseNotification.class.getSimpleName(),
                        afterTime
                ).stream()
                .map(it -> it.getUser().getId())
                .collect(Collectors.toSet());

        List<User> usersWithoutNotification = usersWithoutProduct.stream()
                .filter(it -> userIdsWithExperiment.contains(it.getId()))
                .filter(it -> !userIdsWithNotification.contains(it.getId()))
                .collect(Collectors.toList());

        ProductResponseUsersRequestParams params = new ProductResponseUsersRequestParams();
        params.setUserIds(usersWithoutNotification.stream().map(User::getId).collect(Collectors.toList()));
        Set<Long> productRequestUserIds = usersWithoutNotification.isEmpty() ?
                Collections.emptySet() : productRequestService.getProductResponseUserIdsByParams(params);

        List<Notification> notificationForSave = usersWithoutNotification.stream()
                .filter(it -> !productRequestUserIds.contains(it.getId()))
                .map(it -> new MotivateCreateProductResponseNotification()
                        .setCreateTime(ZonedDateTime.now())
                        .setUser(it)
                ).collect(Collectors.toList());

        if (notificationForSave.isEmpty()) return Collections.emptyList();

        sendToMaster(notificationForSave);
        log.info("createMotivateCreateProductRequestNotifications: saved {} notifications. Page {}/{}", notificationForSave.size(), userPage.getNumber() + 1, userPage.getTotalPages());
        return notificationForSave;
    }

    public Integer createMotivateCreateProductRequestNotificationsByGuestToken(Integer delayBetweenNotificationDays, Integer batchSize) {
        PageRequest firstPage = PageRequest.of(0, batchSize);
        Page<String> deviceGuestTokenPage = deviceRepository.findAllDeviceGuestTokensWithUserIdIsNullByDtypeIn(TARGET_DEVICE_DTYPES, delayBetweenNotificationDays, firstPage);
        ZonedDateTime afterTime = getAfterTime(delayBetweenNotificationDays);
        int resultCount = 0;
        while (true) {
            List<Notification> notifications = processDeviceGuestTokenPage(deviceGuestTokenPage, afterTime);
            resultCount += notifications.size();
            if (deviceGuestTokenPage.hasNext()) {
                deviceGuestTokenPage = deviceRepository.findAllDeviceGuestTokensWithUserIdIsNullByDtypeIn(
                        TARGET_DEVICE_DTYPES,
                        delayBetweenNotificationDays,
                        deviceGuestTokenPage.nextPageable());
            } else {
                return resultCount;
            }
        }
    }

    private List<Notification> processDeviceGuestTokenPage(Page<String> guestTokenPage, ZonedDateTime afterTime) {
        List<String> guestTokens = guestTokenPage.getContent();

        List<ExperimentDTO> experiments = experimentsService.getExperimentsForAllGuestTokenDevices(
                guestTokens, ExperimentsService.PRODUCT_REQUEST_KEY);
        List<String> guestTokensWithExperiment = new ArrayList<>(experimentsService.filterGuestTokensWithExperiment(experiments));

        Set<String> guestTokenWithNotification = notificationRepository.findAllByGuestTokenAndDTypeAndCreateTimeAfter(
                        guestTokensWithExperiment,
                        MotivateCreateProductResponseNotification.class.getSimpleName(),
                        afterTime
                ).stream()
                .map(Notification::getGuestToken)
                .collect(Collectors.toSet());

        List<Notification> notificationForSave = guestTokens.stream()
                .filter(guestTokensWithExperiment::contains)
                .filter(it -> !guestTokenWithNotification.contains(it))
                .map(it -> new MotivateCreateProductResponseNotification()
                        .setGuestToken(it)
                        .setCreateTime(ZonedDateTime.now())
                ).collect(Collectors.toList());

        sendToMaster(notificationForSave);
        log.info("CreateMotivateCreateProductRequestNotificationsByGuestToken: saved {} notifications. Page {}/{}", notificationForSave.size(), guestTokenPage.getNumber() + 1, guestTokenPage.getTotalPages());
        return notificationForSave;
    }

    private ZonedDateTime getAfterTime(Integer delayBetweenNotificationDays) {
        return ZonedDateTime.now().minusDays(delayBetweenNotificationDays);
    }

    private void sendToMaster(List<Notification> notifications) {
        MasterServiceRequest masterServiceRequest = new MasterServiceRequest()
                .setUrl(MASTER_URI)
                .setMediaType(MediaType.APPLICATION_JSON)
                .setRequestEntityObject(notifications.stream()
                        .map(notificationService::getNotificationDTO)
                        .collect(Collectors.toList()));
        masterService.send(masterServiceRequest);
    }
}
