package su.reddot.domain.service.productrequest;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.service.catalog.AttributeTree;
import su.reddot.domain.service.dto.ProductModelDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Accessors(chain = true)
public class AvailableProductRequestFilters {
    private List<Long> filter = new ArrayList<>();

    private List<Long> category = new ArrayList<>();

    private List<Long> size = new ArrayList<>();

    private AttributeTree attributeTree;

    private List<Long> brand = new ArrayList<>();

    private List<ProductModelDTO> productModel = new ArrayList<>();

    private List<Long> productCondition = new ArrayList<>();

    /**
     * Только товары с ценой выше
     */
    private BigDecimal startPrice;

    /**
     * Только товары с ценой ниже
     */
    private BigDecimal endPrice;
}
