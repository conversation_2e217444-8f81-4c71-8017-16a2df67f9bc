package su.reddot.domain.service.expertise;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.expertise.ExpertiseReconciliationNegativeReasonRepository;
import su.reddot.domain.model.order.ExpertiseReconciliationNegativeReason;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExpertiseReconciliationNegativeReasonService {
    private final ExpertiseReconciliationNegativeReasonRepository repository;

    @Cacheable(value = "ExpertiseReconciliationNegativeReasonService.getAllReasons")
    public List<ExpertiseReconciliationNegativeReason> getAllReasons() {
        return repository.findAll();
    }

    public Optional<ExpertiseReconciliationNegativeReason> getReasonById(Long id) {
        return repository.findById(id);
    }
}
