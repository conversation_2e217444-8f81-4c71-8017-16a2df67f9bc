package su.reddot.domain.service.productpublication.ftp;

import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import su.reddot.domain.service.productpublication.exception.FtpConnectionException;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.infrastructure.service.imageProcessing.ImageProcessor;
import su.reddot.infrastructure.service.imageProcessing.ProcessingType;
import su.reddot.infrastructure.service.imageProcessing.storage.FileMetadata;
import su.reddot.infrastructure.service.imageProcessing.storage.ImageFileService;
import su.reddot.infrastructure.util.HashBuilder;
import su.reddot.infrastructure.util.Utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class DefaultFtpService implements FtpService{

	/** Количество попыток на смену/чтение директории */
	private static final int FTP_ATTEMPTS_COUNT = 3;

	/** Время, через которое временный файл считается устаревшим и его можно удалять */
	private static final int TMP_FILES_EXPIRATION_HOUR = 24;

    @Value("${app.publication.images-ftp-host}")
    @Setter
    private String ftpHost;

	@Value("${app.publication.images-ftp-login}")
	@Setter
	private String ftpLogin;

	@Value("${app.publication.images-ftp-passw}")
	@Setter
	private String ftpPassw;

    @Value("${app.publication.images-ftp-tmp-path}")
    @Setter
    private String tmpImagesPath;

	@Value("${resources.images.urlPrefix}")
	@Setter
	private String imagesUrlPrefix;

	private FTPClient ftpClient;

	private final ImageProcessor imageProcessor;

	private final ImageFileService imageFileService;

	private final StaticResourceBalancer staticResourceBalancer;

	private final MessageSourceAccessor messageSourceAccessor;

	@Override
    public synchronized List<FtpDto> getFolders(String path) {
        try {
	        FTPClient client = getConnectedFtpClient(path);
	        List<FtpDto> folderNames = new ArrayList<>();
	        FTPFile[] dirs = client.listDirectories();
	        for (FTPFile dir : dirs) {
		        FtpDto ftpDto = new FtpDto();
		        ftpDto.setName(dir.getName());
				ftpDto.setCreated(new SimpleDateFormat("dd.MM.yy " + messageSourceAccessor.getMessage("service.DefaultAdminOrdersService.") + " HH:mm").format(dir.getTimestamp().getTime()));
		        folderNames.add(ftpDto);
	        }
            return folderNames;
        } catch (IOException e) {
            throw new FtpConnectionException(messageSourceAccessor.getMessage("service.DefaultFtpService.ErrorWhenConnectToFolder", new Object[]{path}), e);
        }
    }

	@Override
	public synchronized List<FtpDto> getPhotos(String path) {
		try {
			FTPClient client = getConnectedFtpClient(path);
			List<FtpDto> photoList = new ArrayList<>();
			FTPFile[] files = client.listFiles();
			String transformedPath = transformPath(path);

			for (FTPFile file : files) {
				String filePath = String.join("",
						tmpImagesPath, transformedPath, "/",
						ProcessingType.ORIG.getPrefix(), "-",
						transformImageName(transformedPath, file.getSize(), file.getName()));
				if (!imageFileService.getMetadata(filePath).isPresent()) {
					// значит файла скорее всего в хранилище нет, нужно загрузить
					try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
						client.retrieveFile(file.getName(), out);
						try (ByteArrayInputStream in = new ByteArrayInputStream(out.toByteArray())) {
							imageFileService.save(in, filePath);
							in.reset();
							try (InputStream resizedImage = imageProcessor.resize(in, ProcessingType.TINY.getProps())) {
								imageFileService.save(resizedImage, getTinyPathFromOrig(filePath));
							}
						}
					}
				}

				FtpDto ftpDto = new FtpDto();
				ftpDto.setName(file.getName());
				ftpDto.setPath(filePath);
				ftpDto.setUrl(staticResourceBalancer.getImageFullPath(getTinyPathFromOrig(filePath)));
				photoList.add(ftpDto);
			}

			return photoList;
		} catch (IOException e) {
			throw new FtpConnectionException(
					String.format("Ошибка при обработке изображения с файловой системы: %s", e.getMessage()), e);
		}
	}

	@Override
	public void clean(){
		removeExpiredtmpFilesAndDirs(tmpImagesPath);
	}

	private String getTinyPathFromOrig(String origPath){
		return origPath.replaceFirst(
				ProcessingType.ORIG.getPrefix() + "-",
				ProcessingType.TINY.getPrefix() + "-");
	}

	/**
	 * Трансформирует путь, заменяя кириллицу на транслит, а пробелы на тире
	 * @param path
	 * @return
	 */
	protected String transformPath(String path){
		if(Strings.isNullOrEmpty(path)) return path;
		return Utils.translitString(path).replace(" ", "-").trim();
    }

	/**
	 * Трансформирует имя файла в зависимости от пути, где он расположен
	 * @param path
	 * @param imageName
	 * @return
	 */
	protected String transformImageName(String path, long size, String imageName){
		String hash = "";
		try {
			hash = HashBuilder.MD5(path + ":" + size + "/" + imageName);
		} catch (NoSuchAlgorithmException | UnsupportedEncodingException e) {
			log.error(e.getMessage(), e);
		}
		imageName = transformPath(imageName);
		int lastPointIndex = imageName.lastIndexOf('.');
		if(lastPointIndex > 0){
			imageName = imageName.substring(0, lastPointIndex) + "-" + hash + "." + imageName.substring(lastPointIndex + 1);
		}
		else{
			imageName = imageName + "-" + hash;
		}
		return imageName;
	}

    protected  synchronized  FTPClient getConnectedFtpClient(String path){
		cd(path, 0);
		return getConnectedFtpClient();
    }




    protected synchronized FTPClient getConnectedFtpClient(){
		if(ftpClient == null || !ftpClient.isConnected() || !ftpClient.isAvailable()){
			try {
				if (ftpClient != null) disconnect();
				ftpClient = new FTPClient();
				ftpClient.setControlEncoding("UTF-8");
				ftpClient.configure(new FTPClientConfig(FTPClientConfig.SYST_UNIX));
				ftpClient.connect(ftpHost);
				if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
					disconnect();
					throw new FtpConnectionException("Невозможно соединиться с FTP сервером");
				}
				if (ftpClient.login(ftpLogin, ftpPassw)) {
					ftpClient.enterLocalPassiveMode();
				} else {
					throw new FtpConnectionException("Невозможно авторизиоваться на FTP сервере");
				}
				//Без этого некоторые JPEG'и приходят битыми
				ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			}
			catch (IOException e){
				log.error(e.getMessage());
				throw new FtpConnectionException("Проблемы с FTP соединением");
			}
		}
		return ftpClient;
    }

    protected synchronized void cd(String path, int attemptNumber){
	    FTPClient client = getConnectedFtpClient();
	    try {
		    if (!client.changeWorkingDirectory("/" + path)) {
			    throw new FtpConnectionException(String.format("Дериктория %s не найдена.", path));
		    }
	    } catch (IOException e) {
	    	disconnect();
	    	if(attemptNumber < FTP_ATTEMPTS_COUNT){
			    cd(path, attemptNumber + 1);
	    		return;
		    }
		    log.error(e.getMessage());
		    throw new FtpConnectionException("Невозможно сменить директорию", e);
	    }
    }

    private void disconnect() {
		if(ftpClient == null) return;
        try {
            ftpClient.logout();
        } catch (IOException e) { }
	    try {
		    ftpClient.disconnect();
	    } catch (IOException e) { }
	    ftpClient = null;
    }

	/**
	 * Удаляет устаревшие файлы и пустые папки (кроме корневой).
	 * Используется для очистки директории для хранения временных файлов, которые создаются для отображения изображений,
	 * загруженных с ФТП-сервера.
	 */
	private void removeExpiredtmpFilesAndDirs(String dirPath){
		if(Strings.isNullOrEmpty(dirPath)) return;
		try {
			List<String> list = imageFileService.list(dirPath);
			if (list == null || list.isEmpty()) {
				return;
			}
			for (String entry : list) {
				try {
					Optional<FileMetadata> metadata = imageFileService.getMetadata(entry);
					if (metadata.isPresent() && metadata.get().getIsDirectory()) {
						removeExpiredtmpFilesAndDirs(entry);
						List<String> subList = imageFileService.list(entry);
						if (subList == null || subList.isEmpty()) {
							imageFileService.delete(entry);
						}
					} else if (!metadata.isPresent() || isExpired(metadata.get().getLastModified())) {
						imageFileService.delete(entry);
					}
				} catch (IOException e) {
					log.error(String.format("Ошибка при работе с файлом %s: %s", entry, e.getMessage()), e);
				}
			}
		} catch (IOException e) {
			log.error(String.format("Ошибка при получении списка файлов директории %s: %s", dirPath, e.getMessage()), e);
		}
    }

	private boolean isExpired(Long lastUpdated) {
		if (lastUpdated == null) {
			return true;
		}
		long expirationTimeSeconds = lastUpdated/1000 + TMP_FILES_EXPIRATION_HOUR * 3600;
		return ZonedDateTime.now().toEpochSecond() > expirationTimeSeconds;
	}

}