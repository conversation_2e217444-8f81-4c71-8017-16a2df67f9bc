package su.reddot.domain.service.productpublication;

import com.google.common.collect.ImmutableSet;
import groovy.lang.Lazy;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.data.querydsl.QSort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import su.reddot.domain.dao.SizeRepository;
import su.reddot.domain.dao.category.PublicationPhotoSampleRepository;
import su.reddot.domain.dao.commission.CommissionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductAttributeValueBindingRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.AddressEndpointException;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.exception.ProductNotFoundException;
import su.reddot.domain.exception.userban.PublishBanException;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.activity.product.publication.CreateDraftActivity;
import su.reddot.domain.model.activity.product.publication.DeleteProductActivity;
import su.reddot.domain.model.activity.product.publication.EditDraftActivity;
import su.reddot.domain.model.activity.product.publication.EditOnModerationActivity;
import su.reddot.domain.model.activity.product.publication.EditProductActivity;
import su.reddot.domain.model.activity.product.publication.EditSecondEditionActivity;
import su.reddot.domain.model.activity.product.publication.SendToModerationActivity;
import su.reddot.domain.model.activity.product.publication.UploadDefectPhotoActivity;
import su.reddot.domain.model.activity.product.publication.UploadProductPhotoActivity;
import su.reddot.domain.model.address.Currency;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.addressendpoint.AddressEndpointAggregation;
import su.reddot.domain.model.attribute.Attribute;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.model.category.Category;
import su.reddot.domain.model.category.PublicationPhotoSample;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.localization.SizeTypeLocalized;
import su.reddot.domain.model.product.DefectImage;
import su.reddot.domain.model.product.Image;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductAttributeValueBinding;
import su.reddot.domain.model.product.ProductCondition;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductModel;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.rule.Rule;
import su.reddot.domain.model.season.Season;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.activity.ActivityService;
import su.reddot.domain.service.additionalsize.AdditionalSizeService;
import su.reddot.domain.service.additionalsize.CategoryAdditionalSizeBindingView;
import su.reddot.domain.service.address.CountryService;
import su.reddot.domain.service.addressendpoint.AddressEndpointAggregationService;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.attribute.AttributeService;
import su.reddot.domain.service.brand.BrandService;
import su.reddot.domain.service.catalog.CatalogAttribute;
import su.reddot.domain.service.catalog.CategoryService;
import su.reddot.domain.service.catalog.CategoryTree;
import su.reddot.domain.service.commission.CommissionOptional;
import su.reddot.domain.service.commission.CommissionService;
import su.reddot.domain.service.currency.ConversionResult;
import su.reddot.domain.service.currency.CurrencyConverter;
import su.reddot.domain.service.currency.CurrencyConverterFactory;
import su.reddot.domain.service.currency.CurrencyProductService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.currency.PriceAndCommission;
import su.reddot.domain.service.dto.AddressEndpointAggregationDTO;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.CategoryDTO;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.PageRequest;
import su.reddot.domain.service.dto.ProductConditionDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductDTOIntegrationLite;
import su.reddot.domain.service.dto.ProductImageDTO;
import su.reddot.domain.service.dto.ProductPriceDTO;
import su.reddot.domain.service.dto.ProductRejectReasonDTO;
import su.reddot.domain.service.dto.SizeValueDTO;
import su.reddot.domain.service.dto.duty.DutyDTO;
import su.reddot.domain.service.duty.DutyService;
import su.reddot.domain.service.filter.ProductMapper;
import su.reddot.domain.service.filter.ProductMapper.MapProductsOptions;
import su.reddot.domain.service.image.ImageService;
import su.reddot.domain.service.product.PriceChangedEvent;
import su.reddot.domain.service.product.PriceDroppedEvent;
import su.reddot.domain.service.product.ProductListInfo;
import su.reddot.domain.service.product.ProductModelService;
import su.reddot.domain.service.product.ProductRejectReasonService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.product.tag.UnifiedUserTagProductModificator;
import su.reddot.domain.service.productpublication.exception.OperationNotAllowedException;
import su.reddot.domain.service.productpublication.exception.ProductDoesNotBelongToUserException;
import su.reddot.domain.service.productpublication.exception.ProductNotEditableException;
import su.reddot.domain.service.productpublication.exception.ProductPublicationException;
import su.reddot.domain.service.productpublication.exception.ProductStateChangeOperationNotAllowedException;
import su.reddot.domain.service.productpublication.exception.RequiredAttributeNotSetException;
import su.reddot.domain.service.productpublication.exception.UnauthorizedUserException;
import su.reddot.domain.service.productpublication.exception.WrongAdditionalSizeException;
import su.reddot.domain.service.productpublication.exception.WrongAttributeValueException;
import su.reddot.domain.service.productpublication.exception.WrongBrandException;
import su.reddot.domain.service.productpublication.exception.WrongCategoryException;
import su.reddot.domain.service.productpublication.exception.WrongConditionException;
import su.reddot.domain.service.productpublication.exception.WrongImageException;
import su.reddot.domain.service.productpublication.exception.WrongPriceException;
import su.reddot.domain.service.productpublication.exception.WrongProductException;
import su.reddot.domain.service.productpublication.exception.WrongProductModelException;
import su.reddot.domain.service.productpublication.exception.WrongSeasonException;
import su.reddot.domain.service.productpublication.exception.WrongSizeException;
import su.reddot.domain.service.season.SeasonService;
import su.reddot.domain.service.staticresource.StaticResourceBalancer;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;
import su.reddot.domain.service.util.XssSanitizer;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.service.imageProcessing.ProcessingType;
import su.reddot.infrastructure.util.Utils;

import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.google.common.base.Strings.isNullOrEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static su.reddot.domain.model.product.ProductState.DELETED;
import static su.reddot.domain.model.product.ProductState.DRAFT;
import static su.reddot.domain.model.product.ProductState.NEED_MODERATION;
import static su.reddot.domain.model.product.ProductState.NEED_RETOUCH;
import static su.reddot.domain.model.product.ProductState.PUBLISHED;
import static su.reddot.domain.model.product.ProductState.REJECTED;
import static su.reddot.domain.model.product.ProductState.RETOUCH_DONE;
import static su.reddot.domain.model.product.ProductState.SECOND_EDITION;
import static su.reddot.domain.model.product.ProductState.SOLD;
import static su.reddot.presentation.Utils.getProductPhotoSampleFullPath;
import static su.reddot.presentation.Utils.roundToTens;

@Service
@Slf4j
@RequiredArgsConstructor
public class DefaultProductPublicationService implements ProductPublicationService {

	private final ProductRepository productRepository;
	private final SizeRepository sizeRepository;
	private final ProductAttributeValueBindingRepository productAttributeValueBindingRepository;
	private final PublicationPhotoSampleRepository publicationPhotoSampleRepository;
	private final BrandService brandService;
	private final ImageService imageService;
	private final ProductService productService;
	private final ProductMapper productMapper;
	private final UserBanService userBanService;
	private final ProductItemService productItemService;
	private final CategoryService categoryService;
	private final AdditionalSizeService additionalSizeService;
	private final SecurityService securityService;
	private final ProductRejectReasonService productRejectReasonService;
	private final CommissionService commissionService;
	private final AddressEndpointService addressEndpointService;
	private final AttributeService attributeService;
	private final ActivityService activityService;
	private final StaticResourceBalancer staticResourceBalancer;
	private final AddressEndpointAggregationService addressEndpointAggregationService;
	private final DutyService dutyService;
	private final ModelMapper modelMapper;
	private final ApplicationEventPublisher pub;
	private final ProductModelService productModelService;
	private final UserService userService;
	private final MessageSourceAccessor messageSourceAccessor;
	private final CurrencyService currencyService;
	private final CountryService countryService;
	private final CurrencyConverterFactory currencyConverterFactory;
	private final UnifiedUserTagProductModificator userTagProductModificator;
	private final CurrencyProductService currencyProductService;
	private final XssSanitizer xssSanitizer;
	private final OrderRepository orderRepository;
	private final ProductAccessHandler productAccessHandler;
	private final CommissionRepository commissionRepository;
	private final SeasonService seasonService;

	@Autowired @Lazy
	private ProductPublicationService self;

	@Value("${resources.images.productMaxImagesCount}")
	private int productMaxImagesCount;

	@Value("${resources.images.productMaxImageFileSizeMb}")
	private int productMaxImageFileSizeMb;

	@Value("${resources.images.productMaxDefectImagesCount}")
	private int defectMaxImagesCount;

	@Value("${app.publication.moderation-timeout-hours}")
	private long moderationTimeoutHours;

	//Минимальная цена для публикации
	@Value("${app.publication.min-price}")
	private int minPrice;

	//Категории в которых можно публиковать товары дешевле minPrice
	@Value("${app.publication.zero-min-price-category-ids}")
	List<Long> zeroMinPriceCategoryIds;

	//В эти категории могут публиковать только бутики (Бьюти и Старгифт)
	@Value("${app.publication.phys-disabled-category-ids}")
	List<Long> physDisabledCategoryIds;


	private UserProductStateAllowedOperations orderStateAllowedOperations = new UserProductStateAllowedOperations();

	private static final int DEFAULT_PAGE_LENGTH = ProductService.MAX_PAGE_LENGTH;

	private List<Rule> publicationRules = new ArrayList<>();

	@PostConstruct
	public void init() {

		//По умолчанию любой пользователь может публиковать товары в любой категории
		publicationRules.add(
				new Rule()
						.setCategoryId(CategoryService.ROOT_CATEGORY_ID)
						.setAvailable(true)
						.setMinPrice(minPrice));

		//Непрошники не могут публиковать в Бьюти и StarGift
		for (long categoryId : physDisabledCategoryIds) {
			publicationRules.add(
					new Rule()
							.setAvailable(false)
							.setCategoryId(categoryId)
							.setProOrApprovedPhys(false));
		}

		//Категории, в которые можно публиковать товары с цеой меньше minPrice
		for (long categoryId : zeroMinPriceCategoryIds) {
			publicationRules.add(
					new Rule()
							.setAvailable(true)
							.setCategoryId(categoryId)
							.setMinPrice(1));
		}
	}

	private User getSeller(){
		User user = securityService.getCurrentAuthorizedUser();
		if(user == null) throw new UnauthorizedUserException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.UserNotAuthorized"));
		return user;
	}
	private void assertProductEditable(@NonNull Product product){
		if (product.getProductState().equals(SOLD)) throw new ProductNotEditableException(product.getId(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ForbidToEditSoldItem"));
		if (product.getProductState().equals(DELETED)) throw new ProductNotEditableException(product.getId(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ForbidToEditDeletedItem"));
		if (product.getProductState().equals(REJECTED)) throw new ProductNotEditableException(product.getId(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ForbidToEditDeclinedItem"));
	}
	private void assertProductBelongsToUser(@NonNull Product product, @NonNull User seller){
		Long userId = seller.getId();
		if(!product.getSeller().getId().equals(userId)) {
			throw new ProductDoesNotBelongToUserException(product.getId(), userId);
		}
	}
	private void assertProductExists(@NonNull Product product){
		if(product.getProductState() == ProductState.DELETED) throw new ProductNotFoundException(messageSourceAccessor.getMessage("exception.not-found.product", new Object[]{product.getId()}));
	}
	private void assertCorrectId(Long id, Supplier<? extends OskellyException> exceptionSupplier){
		if(id == null || id < 1) throw exceptionSupplier.get();
	}
	private Product getProductForPublication(Long productId, Long brandId, Long categoryId, User seller){
		if(productId == null){ //create new one
			if(brandId == null) throw new WrongBrandException(null, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.BrandNotSpecified"));
			assertCorrectId(brandId, () -> new WrongBrandException(brandId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectId")));
			if(categoryId == null) throw new WrongCategoryException(null, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.CategoryNotSpecified"));
			if(!getCategoryTree().containsCategory(categoryId)) throwForbiddenCategory(categoryId);
			assertCorrectId(categoryId, () -> new WrongCategoryException(categoryId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectId")));
			Product product = new Product();
			product.setProductState(DRAFT);
			product.setSeller(seller);
			setBrand(product, brandId);
			setCategory(product, categoryId);
			product.setCreateTime(ZonedDateTime.now());
			product.setChangeTime(ZonedDateTime.now());
			userTagProductModificator.modifyProductRegardingTags(seller, product);
			Product createdProduct = productRepository.save(product);
			activityService.save(new CreateDraftActivity(createdProduct.getId()));
			return createdProduct;
		}
		Product product = productService.getRawProduct(productId, ProductService.UserType.HUMAN).orElseThrow(() -> new WrongProductException(productId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ProductNotFound")));
		assertProductBelongsToUser(product, seller);
		assertProductEditable(product);
		return product;
	}
	private Product getProductForRemovement(@NonNull Long productId){
		Product product = productService.getRawProduct(productId, ProductService.UserType.HUMAN).orElseThrow(() -> new WrongProductException(productId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ProductNotFound")));
		assertProductBelongsToUser(product, getSeller());
		return product;
	}
	private void assertCategoryIsLeaf(@NonNull Category category){
		// If category has children, and children are not deleted
		if (!category.isLeaf()) {
			throw new WrongCategoryException(category.getId(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.CanChooseOnlyFinalCategory"));
		}
	}
	private void setCategory(@NonNull Product product, Long categoryId){
		if(categoryId == null) return;
		Category category = findAvailableCategoryByIdOrThrow(categoryId);
		if(product.getProductState() == ProductState.PUBLISHED && categoryService.getCategory(product.getCategoryId()) != null && !product.getCategoryId().equals(categoryId))
			throw new WrongCategoryException(categoryId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ForbidToChangeCatgoryForPublishedItem"));
		product.setCategoryId(category.getId());
	}

	@Override
	public Category findAvailableCategoryByIdOrThrow(@NonNull Long categoryId) {
		if(!getCategoryTree().containsCategory(categoryId)) throwForbiddenCategory(categoryId);
		Category category = categoryService.getCategory(categoryId);
		if(category == null) throw new WrongCategoryException(categoryId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.CategoryNotFound"));
		assertCategoryIsLeaf(category);
		return category;
	}

	private void throwForbiddenCategory(Long categoryId){
		throw new WrongCategoryException(categoryId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.WrongCategoryForPublishing"));
	}
	private void setBrand(@NonNull Product product, Long brandId){
		if(brandId == null) return;
		product.setBrand(brandService.findAvailableBrandByIdOrThrow(brandId));
	}

	private void setProductModel(@NonNull Product product, Long productModelId) {
		if (productModelId == null) return;
		ProductModel productModel = null;
		if (productModelId > 0) {
			productModel = productModelService.getProductModelById(productModelId);
			if (productModel == null) {
				log.error("ProductModel with id {} NOT FOUND!", productModelId);
				return;
			}
			if (productModel.getBrands() == null || productModel.getBrands().stream().noneMatch(b -> Objects.equals(b.getId(), product.getBrand().getId()))) {
				throw new WrongProductModelException(productModelId, "Модель не соответствует выбранному бренду " + product.getBrand().getId());
			}
		}
		product.setProductModel(productModel);
	}
	private List<AttributeValue> validateAttributeValues(Category category, List<Long> attributeValueIds){
		if(category == null) throw new WrongCategoryException(null, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.NotPossibleSetAttributeWhileCategoryEmpty"));
		List<Long> availableAttributeIds = categoryService.getAllAttributes(category.getId()).stream().map(ca -> ca.getAttribute().getId()).collect(Collectors.toList());
		List<AttributeValue> attributeValues = new ArrayList<>();
		List<Attribute> filledAttributes = new ArrayList<>();
		for(Long attributeValueId : attributeValueIds){
			AttributeValue attributeValue = attributeService.getAttributeValue(attributeValueId);
			if (attributeValue == null) throw new WrongAttributeValueException(attributeValueId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.AttributeValueNotFound"));
			if (!availableAttributeIds.contains(attributeValue.getAttributeId())) throw new WrongAttributeValueException(attributeValueId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.AttributeConflictWithCategory", new Object[]{attributeValue.getAttributeId(), category.getId()}));
			attributeValues.add(attributeValue);
			filledAttributes.add(attributeService.getAttribute(attributeValue.getAttributeId()));
		}
		for(Long availableAttributeId : availableAttributeIds){
			Attribute availableAttribute = attributeService.getAttribute(availableAttributeId);
			if(availableAttribute.getIsRequired() && !filledAttributes.contains(availableAttribute))
				throw new RequiredAttributeNotSetException(availableAttribute.getId(), availableAttribute.getName());
		}
		return attributeValues;
	}
	private void deleteAllAttributeValues(@NonNull Product product){
		product.getAttributeValues().clear();
		productAttributeValueBindingRepository.deleteByProduct(product);
		productAttributeValueBindingRepository.flush();
	}
	private void setAttributes(@NonNull Product product, List<Long> attributeValueIds){
		if(attributeValueIds == null) return;
		//Сначала проверим аттрибуты на корректность значений
		List<AttributeValue> attributeValues = validateAttributeValues(categoryService.getCategory(product.getCategoryId()), attributeValueIds);
		//Затем удалим старые значения
		deleteAllAttributeValues(product);
		//Потом добавляем новые
		attributeValues.forEach(attributeValue -> {
			ProductAttributeValueBinding binding = new ProductAttributeValueBinding(product, attributeValue);
			productAttributeValueBindingRepository.save(binding);
			product.getAttributeValues().add(binding);
		});
	}

	private void setSizeType(@NonNull Product product, SizeTypeLocalized sizeType) {
		if (sizeType == null || sizeType.getSizeType() == null) return;
		product.setSizeType(sizeType.getSizeType());
	}

	private void assertCountPositive(Long sizeId, Integer count) {
		if (count == null || count <= 0)
			throw new WrongSizeException(sizeId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectAmount", new Object[]{count}));
	}

	private Size validateSize(@NonNull Product product, SizeValueDTO sizeValue) {

		if (sizeValue == null) {
			throw new WrongSizeException(null,
					messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectValue"));
		}

		Size size = sizeRepository.findById(sizeValue.getId())
				.orElseThrow(() ->
					new WrongSizeException(sizeValue.getId(),
							messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SizeNotFound")));

		assertCountPositive(size.getId(), sizeValue.getCount());
		validateAdditionalSizes(sizeValue.getAdditionalSizeValues(), categoryService.getCategory(product.getCategoryId()));

		return size;
	}

	private void validateAdditionalSizes(Map<Long, Integer> additionalSizes, Category category){
		if(category == null) throw new WrongCategoryException(null, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ImpossibleSetAdditionalSizeForUnknownCategory"));
		List<CategoryAdditionalSizeBindingView> availableAdditionalSizes = additionalSizeService.getAdditionalSizesForCategory(category.getId(), null);
		List<Long> availableAdditionalSizeIds = additionalSizeService.getAdditionalSizeIdsForCategory(category.getId());
		if(additionalSizes != null && !additionalSizes.isEmpty()){
			for(Long additionalSizeId : additionalSizes.keySet()){
				if(!availableAdditionalSizeIds.contains(additionalSizeId)) throw new WrongAdditionalSizeException(additionalSizeId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectAdditionalSizeForCategory", new Object[]{category.getId()}));
			}
		}
		for(CategoryAdditionalSizeBindingView categoryAdditionalSizeBindingView : availableAdditionalSizes){
			if(categoryAdditionalSizeBindingView.getIsRequired() && (additionalSizes == null || additionalSizes.get(categoryAdditionalSizeBindingView.getId()) == null)){
				//Было решено временно не проверять заполненность обязательных атрибутов.
				//throw new WrongAdditionalSizeException(categoryAdditionalSizeBindingView.getId(), "Не указан обязательный доп. размер " + categoryAdditionalSizeBindingView.getName());
			}
		}
	}
	@Override
	public void validateSizeCount(ProductItem productItem, long sizeCount) {
		List<Long> boutiqueOrderIds = orderRepository.findActiveBoutiqueOrderIdsByProductItem(productItem.getId());
		long minSizeCount = boutiqueOrderIds.size();
		if (sizeCount < minSizeCount) {
			String boutiqueOrderIdsString = boutiqueOrderIds.stream()
					.map(String::valueOf)
					.collect(Collectors.joining(", "));
			String error = messageSourceAccessor.getMessage(
					"service.DefaultAdminProductService.RemovingSizeUsedInBoutiqueOrderError",
					new Object[] {sizeCount, minSizeCount, boutiqueOrderIdsString});
			log.error(error);
			throw new OskellyException(error);
		}
	}



	private void setSizesWithPrices(@NonNull Product product, ProductDTO request, ProductState newState) {
		List<SizeValueDTO> sizes = request.getSizes();
		if (sizes == null) {
			// для обратной совместимости пока не присылают цены на конкретные размеры
			if (request.getPrice() != null || request.getCurrentPriceInCurrency() != null && product.getProductItems() != null) {
				product.getProductItems().forEach(productItem -> {
					setProductItemPriceFromRequest(request, newState, productItem);
					productItemService.save(productItem);
				});
			}
			return;
		}
		if (product.getProductItems() == null) {
			product.setProductItems(new ArrayList<>());
		}

		// мержим имеющиеся items, при этом суммируем количества, оставляем единственный исходный размер
		List<ProductItem> duplicateItemsToDelete = new ArrayList<>();
		Map<Size, ProductItem> size2ProductItem = product.getProductItems().stream()
				.filter(pi -> pi.getDeleteTime() == null)
				.collect(
						Collectors.toMap(
								ProductItem::getSize,
								pi -> pi,
								(item1, item2) -> {
									item1.setCount(item1.getCount() + item2.getCount());
									if (isNotEmpty(item2.getCustomSizeType()) || isNotEmpty(item2.getCustomSizeValue())) {
										item1.setCustomSizeType(item2.getCustomSizeType());
										item1.setCustomSizeValue(item2.getCustomSizeValue());
									}
									duplicateItemsToDelete.add(item2);
									return item1;
								}
						));

		// мержим прилетевшие sizes, при этом суммируем количества, оставляем единственный исходный размер
		List<Size> savedSizes = new ArrayList<>();
		Map<Long, SizeValueDTO> sizesMap = sizes.stream()
				.collect(
						Collectors.toMap(
								SizeValueDTO::getId,
								Function.identity(),
								(size1, size2) -> {
									size1.setCount(size1.getCount() + size2.getCount());
									if (isNotEmpty(size2.getProductCustomSizeType()) || isNotEmpty(size2.getProductCustomSizeValue())) {
										size1.setProductCustomSizeType(size2.getProductCustomSizeType());
										size1.setProductCustomSizeValue(size2.getProductCustomSizeValue());
									}
									return size1;
								}
						));

		for (SizeValueDTO sizeValue : sizesMap.values()) {
			Size size = validateSize(product, sizeValue);
			ProductItem productItem = size2ProductItem.get(size);

			if (productItem == null) { //create new one
				productItem = new ProductItem();
				productItem.setProduct(product);
				productItem.setSize(size);
				if (sizeValue.getPayoutInfo() == null) {
					productItem.setCurrentPrice(product.getCurrentPrice());
					productItem.setCurrentPriceWithoutCommission(product.getCurrentPriceWithoutCommission());
					productItem.setCommission(product.getCommission());
					productItem.setCurrentPriceCurrencyId(product.getCurrentPriceCurrencyId());
					productItem.setCurrentPriceInCurrencyWithoutCommission(product.getCurrentPriceInCurrency());
				}
				productItem = productItemService.save(productItem);
				product.getProductItems().add(productItem);
			}

			validateSizeCount(productItem, sizeValue.getCount());
			productItem.setCount(sizeValue.getCount());

			// обновляем кастомные тип и размер вместе, если в запросе имеется хотя бы что-то из них
			if (isNotEmpty(sizeValue.getProductCustomSizeType()) || isNotEmpty(sizeValue.getProductCustomSizeValue())) {
				productItem.setCustomSizeType(sizeValue.getProductCustomSizeType());
				productItem.setCustomSizeValue(sizeValue.getProductCustomSizeValue());
			}

            updateProductItemSellerParams(product, productItem, sizeValue);

			Long currencyId = request.getCurrentPriceCurrencyId();
			if (sizeValue.getPayoutInfo() != null && sizeValue.getPayoutInfo().getValue() != null) {
				BigDecimal price = sizeValue.getPayoutInfo().getValue();
				if (currencyId != null) {
					setProductItemPriceInCurrency(productItem, currencyId, price, newState);
				} else {
					setProductItemPrice(productItem, price, newState);
					cleanProductItemPriceInCurrency(productItem);
				}
			} else {
				// для обратной совместимости пока не присылают цены на конкретные размеры
				setProductItemPriceFromRequest(request, newState, productItem);
			}

			productItemService.save(productItem);
			savedSizes.add(size);

			if (sizeValue.getAdditionalSizeValues() != null) {
				additionalSizeService.updateAdditionalSizeValuesForProductItem(
						productItem,
						sizeValue.getAdditionalSizeValues()
				);
			}
		}

		//Удаляем лишние
		size2ProductItem.forEach((size, productItem) -> {
			if (!savedSizes.contains(size)) {
				productItemService.delete(productItem);
				product.getProductItems().remove(productItem);
			}
		});
		duplicateItemsToDelete.forEach(productItem -> {
			productItemService.delete(productItem);
			product.getProductItems().remove(productItem);
		});
	}

	private void setProductItemPriceFromRequest(ProductDTO request, ProductState newState, ProductItem productItem) {
		Long currencyId = request.getCurrentPriceCurrencyId();
		if (currencyId != null && request.getCurrentPriceInCurrency() != null) {
			setProductItemPriceInCurrency(productItem, currencyId, request.getCurrentPriceInCurrency(), newState);
		} else {
			setProductItemPrice(productItem, request.getPrice(), newState);
			if (request.getPrice() != null) {
				cleanProductItemPriceInCurrency(productItem);
			}
		}
	}

	private void updateProductItemSellerParams(Product product, ProductItem productItem, SizeValueDTO sizeValue) {

        if (!product.isCrossBorder()) {
			return;
		}

        if (sizeValue.getLink() != null) {
            productItem.setSellerLink(sizeValue.getLink());
        }

        if (sizeValue.getPayoutInfo() == null) {
            return;
        }

        if (sizeValue.getPayoutInfo().getValue() != null) {
            productItem.setSellerPrice(sizeValue.getPayoutInfo().getValue());
        }

        if (sizeValue.getPayoutInfo().getCurrencyCode() != null) {
            productItem.setSellerCurrencyCode(sizeValue.getPayoutInfo().getCurrencyCode());
        }
    }

	private void setImages(@NonNull Product product, List<ProductImageDTO> images){
		if(images == null) return;

		//Сначала сохраним новые фотографии без id, содержащие пути (фото, загружаемые через ФТП-сервер)
		for(ProductImageDTO dto : images){
			// Картинки с id нас не интересуют, т.к. они уже сохранены на сервере
			// Так же пропускает картинки с пустыми путями и пустым order, т.к. сохранить их невозможно
			if(dto.getId() != null || isNullOrEmpty(dto.getPath()) || dto.getOrder() == null) continue;
			Image image = imageService.saveImageByProduct(
					product,
					dto.getPath().replaceFirst(
							ProcessingType.TINY.getPrefix() + "-",
							ProcessingType.ORIG.getPrefix() + "-")
					   .replace("/img/", "").replace("..", ""),
					dto.getOrder());
			if(image != null && image.getId() != null) dto.setId(image.getId());
			product.getImages().add(image);
		}

		imageService.setProductImagesOrder(product, images.stream().map(i -> i.getId()).collect(Collectors.toList()));
		//Удалим изображения товара, которых нет в пришедшем списке
		List<Long> requestImageIds = images.stream().filter(i -> i.getId() != null).map(i -> i.getId()).collect(Collectors.toList());
		List<Image> productImages = imageService.getRawImages(product);
		List<Image> images2remove = new ArrayList<>();
		for(Image image : productImages){
			if(!requestImageIds.contains(image.getId())){//Удаляем это изображение
				images2remove.add(image);
				imageService.deleteImage(image.getId());
				product.removeImageById(image.getId());
			}
		}
		productImages.removeAll(images2remove);
	}
	private void setDefectImages(@NonNull Product product, List<ProductImageDTO> images){
		if(images == null) return;

		//Устанавливаем порядок и заголовки изображений дефектов
		imageService.setDefectImagesOrderAndTitles(product, images);

		//Удалим изображения дефектов, которых нет в пришедшем списке
		List<Long> requestImageIds = images.stream().filter(i -> i.getId() != null).map(i -> i.getId()).collect(Collectors.toList());
		List<DefectImage> defectImages = imageService.getRawDefectImages(product);
		List<DefectImage> images2remove = new ArrayList<>();
		for(DefectImage image : defectImages){
			if(!requestImageIds.contains(image.getId())){//Удаляем это изображение
				images2remove.add(image);
				imageService.deleteDefectImage(image.getId());
				product.removeDefectImageById(image.getId());
			}
		}
		defectImages.removeAll(images2remove);
	}
	private void setDescription(@NonNull Product product, String description){
		if(description == null) return;
		product.setDescription(xssSanitizer.sanitize(description));
	}
	private void setVintage(@NonNull Product product, Boolean isVintage){
		if(isVintage == null) return;
		product.setVintage(isVintage);
	}
	private void setModel(@NonNull Product product, String model){
		if(model == null) return;
		product.setModel(model);
	}
	private void setOrigin(@NonNull Product product, String origin){
		if(origin == null) return;
		product.setOrigin(origin);
	}
	private void setPurchasePrice(@NonNull Product product, BigDecimal purchasePrice, String currencyCode){
		if(purchasePrice == null) return;

		CurrencyConverter converter = currencyConverterFactory.createByCurrencyCodeToBase(currencyCode);
		product.setPurchasePrice(converter.revertConversionFromBase(purchasePrice).getConvertResult());
	}
	private void setPurchaseYear(@NonNull Product product, Integer purchaseYear){
		if(purchaseYear == null) return;
		product.setPurchaseYear(purchaseYear);
	}
	private void setSerialNumber(@NonNull Product product, String serialNumber){
		if(serialNumber == null) return;
		product.setSerialNumber(serialNumber);
	}
	private void setVendorCode(@NonNull Product product, String vendorCode){
		if (vendorCode == null) return;
		product.setVendorCode(vendorCode);
	}
	private void setStoreCode(@NonNull Product product, String storeCode){
		if (storeCode == null) return;
		product.setStoreCode(storeCode);
	}
	private void setCondition(@NonNull Product product, Long conditionId){
		if(conditionId == null) return;
		product.setProductConditionId(findConditionOrThrow(conditionId).getId());
	}

	public ProductCondition findConditionOrThrow(Long conditionId) {
		assertCorrectId(conditionId, () -> new WrongConditionException(conditionId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectId")));
		ProductCondition productCondition = productService.getProductCondition(conditionId);
		if(productCondition == null) throw new WrongConditionException(conditionId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ProductConditionNotFound"));
		return productCondition;
	}

	private void setSourceLink(@NonNull Product product, String sourceLink){
		if (StringUtils.isEmpty(sourceLink)) return;
		if (StringUtils.isEmpty(sourceLink.trim())) return;
		if (sourceLink.length() >= 4000){
			throw new OskellyException("Source link to external sites must be less than 4000 symbols");
		}
		product.setSourceLink(sourceLink);
	}

	private void setSalesChannel(Product product, SalesChannel salesChannel) {
		if (salesChannel == null) return;
		product.setSalesChannel(salesChannel);
	}

	private void setSeason(@NonNull Product product, Long seasonId) {
		if (seasonId == null) return;
		Season season = seasonService.findById(seasonId)
				.orElseThrow(() -> new WrongSeasonException(seasonId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SeasonNotFound")));
		product.setSeason(season);
	}

	private void setCarryOver(@NonNull Product product, Boolean isCarryOver){
		if (isCarryOver == null) return;
		if (isCarryOver && !product.isCarryOver()) {
			product.setCarryOverTime(LocalDateTime.now());
		} else if (!isCarryOver) {
			product.setCarryOverTime(null);
		}
	}

	@Override
	public Integer getMinPriceForCategory(Long categoryId) {
		int minCategoryPrice = minPrice;
		CategoryDTO category = getCategoryTree().getCategoryById(categoryId);
		if(category != null && category.getMinPrice() != null) minCategoryPrice = category.getMinPrice();
		return minCategoryPrice;
	}

	private void validatePrice(Long categoryId, BigDecimal price){
		//Проверяем цену на минимальное значение
		Integer minPriceForCategory = getMinPriceForCategory(categoryId);
		if(price.intValue() < minPriceForCategory) {
			Currency baseCurrency = currencyService.getBaseCurrencyCached();
			throw new WrongPriceException(
					price,
					baseCurrency.getSign(),
					messageSourceAccessor,
					messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectMinCategoryPrice", new Object[]{minPriceForCategory + baseCurrency.getSign()})
			);
		}
	}

	private boolean priceChangeAttempt(Product product, BigDecimal price) {
		return price != null && product.getCurrentPrice() != null
				&& price.compareTo(product.getCurrentPrice()) != 0;
	}

    private boolean priceChangeAttempt(BigDecimal currentPrice, BigDecimal price) {
        return price != null && currentPrice != null
                && price.compareTo(currentPrice) != 0;
    }

	private void setPrice(@NonNull Product product, BigDecimal price, ProductState newState) {
		Set<ProductState> ignorePriceValidationStates = ImmutableSet.of(ProductState.HIDDEN, DRAFT, DELETED);
		if(price == null){
			//Если цена не устанавливается, то нужно проверить цену в самом товаре. Возможно категория изменилась и старая цена не проходит.
			if(product.getCurrentPrice() != null
					&& (newState == null || !ignorePriceValidationStates.contains(newState)))  {
				validatePrice(product.getCategoryId(), product.getCurrentPrice());
			}
			return;
		}
		if (priceChangeAttempt(product, price)) {
			if (SalesChannel.BOUTIQUE_AND_WEBSITE.equals(product.getSalesChannel())
					|| SalesChannel.BOUTIQUE.equals(product.getSalesChannel())) {
				// если цена товара поменялась, но при этом товар в оффлайн-бутика, то надо выдавать ошибку
				throw new ProductPublicationException("Недопустимо обновлять цену товара, находящегося в оффлайн-бутике");
			}

			//нельзя менять цену у товаров с кастомной комиссией
			if (product.isCustomCommission()) {
				throw new ProductPublicationException(
						messageSourceAccessor.getMessage("service.DefaultProductPublicationService.customCommissionPriceEditDenied"));
			}
		}

		if(newState == null || !ignorePriceValidationStates.contains(newState))  {
			//Проверяем цену на минимальное значение
			validatePrice(product.getCategoryId(), price);
		}

		if(product.getCurrentPrice() != null
				&& product.getProductState() == ProductState.PUBLISHED
				&& (0.95 * product.getCurrentPrice().doubleValue()) >=  price.doubleValue() ) {
			LocalDateTime now = LocalDateTime.now();
			product.setPublishTime(now);
			product.setPromotionTime(now);
		}

		product.setCurrentPrice(roundToTens(price));
		product.setCurrentPriceWithoutCommission(getPriceWithoutCommission(product, price));
	}

    private void setProductItemPrice(@NonNull ProductItem productItem, BigDecimal price, ProductState newState) {
		Set<ProductState> ignorePriceValidationStates = ImmutableSet.of(ProductState.HIDDEN, DRAFT, DELETED);
        Product product = productItem.getProduct();
		if (price == null) {
			//Если цена не устанавливается, то нужно проверить цену в самом товаре. Возможно категория изменилась и старая цена не проходит.
			if (productItem.getCurrentPrice() != null
					&& (newState == null || !ignorePriceValidationStates.contains(newState))) {
				validatePrice(product.getCategoryId(), productItem.getCurrentPrice());
			}
			return;
		}
		if (priceChangeAttempt(productItem.getCurrentPrice(), price)) {
			if (SalesChannel.BOUTIQUE_AND_WEBSITE.equals(product.getSalesChannel())
					|| SalesChannel.BOUTIQUE.equals(product.getSalesChannel())) {
				// если цена товара поменялась, но при этом товар в оффлайн-бутика, то надо выдавать ошибку
				throw new ProductPublicationException("Недопустимо обновлять цену товара, находящегося в оффлайн-бутике");
			}

			//нельзя менять цену у товаров с кастомной комиссией
			if (productItem.isCustomCommission()) {
				throw new ProductPublicationException(
						messageSourceAccessor.getMessage("service.DefaultProductPublicationService.customCommissionPriceEditDenied"));
			}
		}

		if (newState == null || !ignorePriceValidationStates.contains(newState)) {
			//Проверяем цену на минимальное значение
			validatePrice(product.getCategoryId(), price);
		}

		if (productItem.getCurrentPrice() != null
				&& product.getProductState() == ProductState.PUBLISHED
				&& (0.95 * productItem.getCurrentPrice().doubleValue()) >= price.doubleValue()) {
			LocalDateTime now = LocalDateTime.now();
			product.setPublishTime(now);
			product.setPromotionTime(now);
		}

		productItem.setCurrentPrice(roundToTens(price));
		productItem.setCurrentPriceWithoutCommission(getProductItemPriceWithoutCommission(productItem, price));
	}

	@Override
	public void setPriceInCurrency(@NonNull Product product,
								   Long currentPriceCurrencyId,
								   BigDecimal currentPriceInCurrency,
								   ProductState newState) {
		if (currentPriceInCurrency != null && currentPriceInCurrency.compareTo(BigDecimal.ZERO) < 1)
			throw new ProductPublicationException("Цена в валюте равна нулю или меньше нуля");

		CommissionGrid commissionGrid = product.getSeller().getCommissionGrid();
		ConversionResult priceWithoutCommission = currencyProductService.convertToBase(currentPriceCurrencyId, currentPriceInCurrency,
				product.isCrossBorder());
		BigDecimal priceWithCommission = commissionService.calculatePriceWithCommission(
				priceWithoutCommission.getConvertResult(), commissionGrid, product.getSalesChannel()).getPriceWithCommission();

		Long pickupCountryId = product.getSeller().getPickupCountry() != null ? product.getSeller().getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
		Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

		List<DutyDTO> duties = dutyService.calculateDutiesByPriceWithoutCommission(
				priceWithCommission,
				priceWithoutCommission.getConvertResult(),
				product.getSeller(),
				pickupCountryId,
				deliveryCountryId);
		if (duties.size() > 0) {
			BigDecimal dutiesAmount = duties.stream().map(DutyDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
			priceWithCommission = priceWithoutCommission.getConvertResult().add(dutiesAmount);
		}

		setPrice(product, priceWithCommission, newState);

		product.setCurrentPriceCurrencyId(currentPriceCurrencyId);
		product.setCurrentPriceInCurrency(currentPriceInCurrency);
		product.setLastPriceConvertRate(priceWithoutCommission.getEffectiveRate());
		product.setLastPriceConvertTime(ZonedDateTime.now());
	}

	@Override
	public void setProductItemPriceInCurrency(@NonNull ProductItem productItem,
                                              Long currentPriceCurrencyId,
                                              BigDecimal currentPriceInCurrency,
                                              ProductState newState) {
		if (currentPriceInCurrency != null && currentPriceInCurrency.compareTo(BigDecimal.ZERO) < 1)
			throw new ProductPublicationException("Цена в валюте равна нулю или меньше нуля");

		Product product = productItem.getProduct();
		CommissionGrid commissionGrid = product.getSeller().getCommissionGrid();
		BigDecimal priceWithoutCommission = currencyProductService.convertToBase(currentPriceCurrencyId, currentPriceInCurrency,
				product.isCrossBorder()).getConvertResult();
		BigDecimal priceWithCommission = commissionService.calculatePriceWithCommission(
				priceWithoutCommission, commissionGrid, product.getSalesChannel()).getPriceWithCommission();

		Long pickupCountryId = product.getSeller().getPickupCountry() != null ? product.getSeller().getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
		Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

		List<DutyDTO> duties = dutyService.calculateDutiesByPriceWithoutCommission(
				priceWithCommission,
				priceWithoutCommission,
				product.getSeller(),
				pickupCountryId,
				deliveryCountryId);
		if (duties.size() > 0) {
			BigDecimal dutiesAmount = duties.stream().map(DutyDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
			priceWithCommission = priceWithoutCommission.add(dutiesAmount);
		}

		setProductItemPrice(productItem, priceWithCommission, newState);

		productItem.setCurrentPriceCurrencyId(currentPriceCurrencyId);
		productItem.setCurrentPriceInCurrencyWithoutCommission(currentPriceInCurrency);
		productItem.setLastPriceConvertTime(ZonedDateTime.now());
	}

	private void cleanPriceInCurrency(@NonNull Product product) {
		product.setCurrentPriceCurrencyId(null);
		product.setCurrentPriceInCurrency(null);
		product.setLastPriceConvertRate(null);
		product.setLastPriceConvertTime(null);
	}

	private void cleanProductItemPriceInCurrency(@NonNull ProductItem productItem) {
		productItem.setCurrentPriceCurrencyId(null);
		productItem.setCurrentPriceInCurrencyWithoutCommission(null);
		productItem.setLastPriceConvertRate(null);
		productItem.setLastPriceConvertTime(null);
	}

	private void setRrpPrice(@NonNull Product product, BigDecimal rrpPrice, String currencyCode){
		if(rrpPrice == null || rrpPrice.intValue() == 0) return;
		assertPositiveValue(rrpPrice, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectRpr"));

		CurrencyConverter converter = currencyConverterFactory.createByCurrencyCodeToBase(currencyCode);
		product.setRrpPrice(converter.revertConversionFromBase(rrpPrice).getConvertResult());
	}

	private void cleanRrpPriceInCurrency(@NonNull Product product) {
		product.setRrpPriceCurrencyId(null);
		product.setRrpPriceInCurrency(null);
		product.setLastRrpPriceConvertTime(null);
	}

	private void setPickupAddressEndpoint(@NonNull Product product, Long addressEndpointId){
		if(addressEndpointId == null) return;
		AddressEndpoint addressEndpoint = addressEndpointService.findById(addressEndpointId);
		//Точка не принадлежит продавцу товара, он не может ее указывать
		if(!Objects.equals(product.getSeller().getId(), addressEndpoint.getUser().getId())){
			throw new AddressEndpointException(messageSourceAccessor.getMessage("exception.address.delete-no-rights"));
		}
		product.setPickupAddressEndpoint(addressEndpoint);
	}

	private void setPickupAddressEndpointAggregation(@NonNull Product product, Long addressEndpointAggregationId){
		if(addressEndpointAggregationId == null) return;
		AddressEndpointAggregation addressEndpointAggregation = addressEndpointAggregationService.findById(addressEndpointAggregationId);
		//Точка не принадлежит продавцу товара, он не может ее указывать
		if(!Objects.equals(product.getSeller().getId(), addressEndpointAggregation.getUser().getId())){
			throw new AddressEndpointException(messageSourceAccessor.getMessage("exception.address.delete-no-rights"));
		}
		product.setPickupAddressEndpointAggregation(addressEndpointAggregation);
		product.setPickupAddressEndpoint(addressEndpointAggregation.getPhysicalAddress());
	}

	private BigDecimal getPriceWithoutCommission(@NonNull Product product, @NonNull BigDecimal price) {
		return commissionService.calculatePriceWithoutCommission(
				price,
				product.getCurrentPriceWithoutCommission(),
				product.getSalesChannel(),
				product.isCustomCommission(),
				product.getSeller().getCommissionGrid());
	}

	private BigDecimal getProductItemPriceWithoutCommission(@NonNull ProductItem productItem, @NonNull BigDecimal price) {
		return commissionService.calculatePriceWithoutCommission(
				price,
				productItem.getCurrentPriceWithoutCommission(),
				productItem.getProduct().getSalesChannel(),
				productItem.isCustomCommission(),
                productItem.getProduct().getSeller().getCommissionGrid());
	}

	private void assertNotNull(Object object, String message){
		if(object == null) throw new ProductPublicationException(message);
	}
	private void validateSizes(@NonNull Product product){
		if(product.getSizeType() == SizeType.NO_SIZE) return;
		List<ProductItem> productItems = product.getProductItems().stream()
				.filter(pi -> (pi.getDeleteTime() == null))
				.collect(Collectors.toList());
		if(productItems.isEmpty()) throw new ProductPublicationException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SizesNotFound"));
		for(ProductItem productItem : productItems){
			assertNotNull(productItem.getSize(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SizeNotSpecifiedForProductItem", new Object[]{productItem.getId()}));
			assertCountPositive(productItem.getSize().getId(), productItem.getCount());
			validateAdditionalSizes(productItem.getAdditionalSizeValuesAsMap(), categoryService.getCategory(product.getCategoryId()));
		}
	}
	private void assertProductHasRequiredImages(@NonNull Product product){
		if(product.getImages().stream().filter(i -> i.getImagePath() != null).count() >= 2) return;
		throw new ProductPublicationException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.MandatoryImagesNotSpecified"));
	}
	private void assertNonEmptyStringValue(String value, String message){
		if(value == null || value.trim().equals("")) throw new ProductPublicationException(message);
	}
	private void assertPositiveValue(BigDecimal value, String message){
		if(value == null || value.intValue() <= 0) {
			Currency baseCurrency = currencyService.getBaseCurrencyCached();
			throw new WrongPriceException(value, baseCurrency.getSign(), message);
		}
	}
	private void assertProductIsReadyForModeration(@NonNull Product product){
		assertNotNull(categoryService.getCategory(product.getCategoryId()), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.CategoryNotSpecifiedV2"));
		assertCategoryIsLeaf(categoryService.getCategory(product.getCategoryId()));
		assertNotNull(product.getBrand(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.BrandNotSpecifiedV2"));
		validateAttributeValues(categoryService.getCategory(product.getCategoryId()), product.getAttributeValues().stream().map(av -> av.getAttributeValue().getId()).collect(Collectors.toList()));
		assertNotNull(product.getSizeType(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SizeTypeNotSpecified"));
		validateSizes(product);
		assertProductHasRequiredImages(product);
		assertNonEmptyStringValue(product.getDescription(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ItemDescriptionNotSpecified"));
		assertNotNull(productService.getProductCondition(product.getProductConditionId()), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ProductConditionNotSpecified"));
		assertPositiveValue(product.getCurrentPrice(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectPrice"));
	}
	//Возвращает новый статус для товара исходя из необходимости проводить подерацию
	//В случае, когда у опубликованного товара изменяется только цена и в меньшую сторону,
	//повторная модерация не нужна
	private ProductState getNewProductState(ProductDTO productDTO, ProductDTO request, User owner){
		ProductState requestState = request.getProductState();
		if(requestState == null) requestState = productDTO.getProductState();

		//Продавец вправе сам ставить любой статус товара, если он не выше статуса модерации (ретушь, опубликован)
		if(!isAfterModeration(requestState)) return requestState;
		boolean needModeration = false;

		//Категория изменилась. Требуется модерация.
		if(request.getCategoryId() != null && !request.getCategoryId().equals(productDTO.getCategoryId())) needModeration = true;
		//Бренд изменился. Требуется модерация.
		if(request.getBrandId() != null && !request.getBrandId().equals(productDTO.getBrandId())) needModeration = true;
		//Атрибуты изменились. Требуется модерация.
		if(request.getAttributeValueIds() != null && !Utils.equalsIgnoreOrder(request.getAttributeValueIds(), productDTO.getAttributeValueIds())) needModeration = true;
		//Тип размера изменился. Требуется модерация.
		//Решили исключить модерацию в этом случае
		//if(request.getSizeType() != null && request.getSizeType() != productDTO.getSizeType()) needModeration = true;
		//Размеры изменились. Требуется модерация.
		//Решили исключить модерацию в этом случае
		//if(request.getSizes() != null && !Utils.equalsIgnoreOrder(request.getSizes(), productDTO.getSizes())) needModeration = true;
		//Изображения изменились или порядок изображений изменился. Требуется модерация.
		if(request.getImages() != null && !request.getImages().equals(productDTO.getImages())) needModeration = true;
		//Описание изменилась. Требуется модерация только для физиков.
		if(request.getDescription() != null && !request.getDescription().equals(productDTO.getDescription())
				&& !owner.isPro()) needModeration = true;
		//Винтажность изменилась. Требуется модерация.
		//Решили исключить модерацию в этом случае
		//if(request.getIsVintage() != null && !request.getIsVintage().equals(productDTO.getIsVintage())) needModeration = true;
		//Модель изменилась. Требуется модерация.
		//Решили исключить модерацию в этом случае
		//if(request.getModel() != null && !request.getModel().equals(productDTO.getModel())) needModeration = true;
		//Происхождение изменилось. Требуется модерация.
		if(request.getOrigin() != null && !request.getOrigin().equals(productDTO.getOrigin())) needModeration = true;
		//Стоимость приобретения изменилась. Требуется модерация.
		if(request.getPurchasePrice() != null && !request.getPurchasePrice().equals(productDTO.getPurchasePrice())) needModeration = true;
		//Год приобретения изменился. Требуется модерация.
		if(request.getPurchaseYear() != null && !request.getPurchaseYear().equals(productDTO.getPurchaseYear())) needModeration = true;
		//Серийный номер изменился. Требуется модерация.
		//Решили исключить модерацию в этом случае
		//if(request.getSerialNumber() != null && !request.getSerialNumber().equals(productDTO.getSerialNumber())) needModeration = true;
		//Состояние товара изменилось. Требуется модерация.
		//Решили исключить модерацию в этом случае
		//if(request.getConditionId() != null && !request.getConditionId().equals(productDTO.getConditionId())) needModeration = true;

		//Цена изменилась и стала выше (копейки не считаем). Требуется модерация только для физиков.
		if(request.getPrice() != null && (productDTO.getPrice() == null || request.getPrice().intValue() > productDTO.getPrice().intValue())
				&& !owner.isPro()) needModeration = true;

		return getNewStateForProduct(requestState, productDTO.getProductState(), needModeration);
	}
	private ProductState getNewStateForProduct(ProductState requestedState, ProductState currentState, boolean needModeration){
		//Если мы пытаемся изменить стейт товара и он становится выше модерации, то модерация нужна в любом случае
		if(isAfterModeration(requestedState) && (needModeration || requestedState != currentState)) return ProductState.NEED_MODERATION;
		return requestedState;
	}
	private boolean isAfterModeration(ProductState productState){
		return productState == ProductState.NEED_RETOUCH || productState == ProductState.RETOUCH_DONE || productState == ProductState.PUBLISHED;
	}

	private void setState(@NonNull Product product, ProductState productState){
		if(productState == null){
			//Не меняем статус в случае редактирования черновика или повторной модерации
			if(product.getProductState() == ProductState.SECOND_EDITION || product.getProductState() == ProductState.DRAFT) return;
			productState = DRAFT;
		}
		if(!orderStateAllowedOperations.isAllowed(product.getProductState(), productState)) throw new ProductStateChangeOperationNotAllowedException(product.getProductState(), productState);

		if(productState == NEED_MODERATION){ // отправка на модерацию
			//Перед отправкой на модерацию компонуем картинки таким образом, чтобы в нумерации не было пробелов
			imageService.updateImagesOrder(product);
			assertProductIsReadyForModeration(product);
			productService.updateState(product.getId(), productState, ProductService.UserType.HUMAN);
		}
		else{ // другой статус
			productService.updateState(product.getId(), productState, ProductService.UserType.HUMAN);
		}
	}

	@Override
	@Transactional
	public Long publishProduct(@NonNull ProductDTO request) {

		User seller = securityService.getCurrentAuthorizedUser();

		productAccessHandler.checkPublicationPermission(request.getProductId());

		return publishProductInternal(request, seller, null);
	}

	// TODO: метод вызывается только из DefaultSaleRequestService, в идеале выделить каким-то образом
	//  специфику для заявок на консьерж: класс-адаптер | доп. параметры | ...
	//  Аналогичный метод:
	//  updateProductImage(su.reddot.domain.model.user.User, java.lang.Long, java.lang.String, java.lang.Integer)
	@Override
	@Transactional
	public Long publishProduct(@NonNull ProductDTO request, User seller, Consumer<Product> preSaveModification) {

		return publishProductInternal(request, seller, preSaveModification);
	}

	private Long publishProductInternal(@NonNull ProductDTO request, User seller, Consumer<Product> preSaveModification) {

		if (userBanService.isCanNotPublish(seller.getId()).isBanned()) {
			throw new PublishBanException();
		}


		Product product = getProductForPublication(
				request.getProductId(),
				request.getBrandId(),
				request.getCategoryId(),
				seller);

		String currencyCode = request.getCurrencyCode();
		ProductDTO productDTO = toProduct(product, null, currencyCode, seller);
		ProductState newState = getNewProductState(productDTO, request, seller);
		ProductState currentState = productDTO.getProductState();

		if (!currencyService.isCurrencyCodeBaseCached(currencyCode) && request.getPrice() != null) {
			CurrencyDTO currencyDTO = currencyService.getCurrencyDTOByCodeCached(currencyCode);

			CommissionGrid commissionGrid = seller.getCommissionGrid();
			// FIXME: нельзя давать пользователю обновлять salesChannel, используя api публикации
			SalesChannel salesChannel = Optional.ofNullable(request.getSalesChannel()).orElse(product.getSalesChannel());
			BigDecimal priceWithCommission = currencyProductService.convertToBase(currencyDTO.getId(), request.getPrice(),
					product.isCrossBorder()).getConvertResult();
			BigDecimal priceWithoutCommission = commissionService.calculatePriceWithoutCommission(
					priceWithCommission, commissionGrid, salesChannel);
			BigDecimal priceWithoutCommissionInCurrency = currencyProductService.convertFromBase(currencyDTO.getId(), priceWithoutCommission);

			request.setCurrentPriceInCurrency(priceWithoutCommissionInCurrency);
			request.setCurrentPriceCurrencyId(currencyDTO.getId());
			request.setPrice(null);
			request.setPriceWithoutCommission(null);
		}

		setCategory(product, request.getCategoryId());
		setBrand(product, request.getBrandId());
		setProductModel(product, request.getProductModelId() != null ? request.getProductModelId() :
				request.getProductModel() != null ? request.getProductModel().getId() : null);
		setAttributes(product, request.getAttributeValueIds());
		setSizeType(product, request.getSizeType());
		setImages(product, request.getImages());
		setDefectImages(product, request.getDefectImages());
		setDescription(product, request.getDescription());
		setVintage(product, request.getIsVintage());
		setModel(product, request.getModel());
		setOrigin(product, request.getOrigin());
		setPurchasePrice(product, request.getPurchasePrice(), currencyCode);
		setPurchaseYear(product, request.getPurchaseYear());
		setSerialNumber(product, request.getSerialNumber());
		setVendorCode(product, request.getVendorCode());
		setStoreCode(product, request.getStoreCode());
		setCondition(product, request.getConditionId());
		setSourceLink(product, request.getSourceLink());
		// FIXME: нельзя давать пользователю обновлять salesChannel, используя api публикации
		setSalesChannel(product, request.getSalesChannel());
		setSeason(product, request.getSeasonId());
		setCarryOver(product, request.getIsCarryOver());

		BigDecimal oldPriceInBaseCurrency = product.getCurrentPrice();

		if (request.getCurrentPriceCurrencyId() != null && request.getCurrentPriceInCurrency() != null) {
			setPriceInCurrency(
					product,
					request.getCurrentPriceCurrencyId(),
					request.getCurrentPriceInCurrency(),
					newState
			);
		} else {
			setPrice(product, request.getPrice(), newState);
			if (request.getPrice() != null) {
				cleanPriceInCurrency(product);
			}
		}
		setSizesWithPrices(product, request, newState);

		BigDecimal newPriceInBaseCurrency = product.getCurrentPrice();

		if (request.getRrpPriceInCurrency() != null && request.getRrpPriceCurrencyId() != null) {
			product.setRrpPriceInCurrency(request.getRrpPriceInCurrency());
			product.setRrpPriceCurrencyId(request.getRrpPriceCurrencyId());
			product.setLastRrpPriceConvertTime(ZonedDateTime.now());
			ConversionResult rrpPriceInBase = currencyProductService.convertToBaseWithoutCommission(
					request.getRrpPriceCurrencyId(), request.getRrpPriceInCurrency());
			if (rrpPriceInBase != null) {
				product.setRrpPrice(rrpPriceInBase.getConvertResult());
			}
		} else {
			setRrpPrice(product, request.getRrpPrice(), currencyCode);
			if (request.getRrpPrice() != null) {
				cleanRrpPriceInCurrency(product);
			}
		}

		setPickupAddressEndpoint(product, request.getPickupAddressEndpointId());
		setPickupAddressEndpointAggregation(product, request.getPickupAddressEndpointAggregationId());

		if (!product.isSelectedForConcierge() && request.getIsConcierge() != null && request.getIsConcierge()) {
			product.setSelectedConciergeTime(LocalDateTime.now());
		}
		if (Objects.nonNull(request.getCountryOfOrigin()) && Objects.nonNull(request.getCountryOfOrigin().getId())) {
			product.setCountryOfOrigin(countryService.findById(request.getCountryOfOrigin().getId()).orElse(null));
		} else {
			product.setCountryOfOrigin(null);
		}

		if (currentState == ProductState.DRAFT) {
			activityService.save(new EditDraftActivity(product.getId()));
		} else if (currentState == ProductState.PUBLISHED) {
			activityService.save(new EditProductActivity(product.getId()));
		} else if (currentState == ProductState.SECOND_EDITION) {
			activityService.save(new EditSecondEditionActivity(product.getId()));
		} else if (currentState == ProductState.NEED_MODERATION) {
			activityService.save(new EditOnModerationActivity(product.getId()));
		}

		if (newState != currentState) {
			setState(product, newState);

			if (newState == ProductState.NEED_MODERATION) {
				activityService.save(new SendToModerationActivity(product.getId()));
			}
		}

		if (product.getCurrentPrice() != null && product.getCurrentPriceWithoutCommission() != null) {
			product.setCommission(commissionService.getCommissionForNewProduct(product));
		}
		product.getProductItems().forEach(productItem -> {
			if (productItem.getCurrentPrice() != null && productItem.getCurrentPriceWithoutCommission() != null) {
				productItem.setCommission(commissionService.getCommissionForNewProduct(productItem));
			}
		});

		if (preSaveModification != null) {
			preSaveModification.accept(product);
		}

		product = productService.saveProduct(product, ProductService.UserType.HUMAN);

		publishPriceChangesEvents(product, oldPriceInBaseCurrency, newPriceInBaseCurrency);

		return product.getId();
	}

	public void publishPriceChangesEvents(Product product, BigDecimal oldPrice, BigDecimal newPrice) {
		//При любом снижении цены уведомляем подписчиков (только для опубликованных товаров)
		if (newPrice != null && oldPrice != null && product.getProductState() == ProductState.PUBLISHED) {
			if (oldPrice.intValue() > newPrice.intValue()) {
				pub.publishEvent(new PriceDroppedEvent(product, oldPrice, newPrice));
			} else if (oldPrice.compareTo(newPrice) != 0) { // .equals() сравнивает еще и scale, что для конкретного случая лишнее
				pub.publishEvent(new PriceChangedEvent(product, oldPrice, newPrice));
			}
		}
	}

	@Override
	public ProductImageDTO updateProductImage(@NonNull Long productId, @NonNull MultipartFile image,
											  @NonNull Integer imageOrder) {

		User seller = securityService.getCurrentAuthorizedUser();

		productAccessHandler.checkUpdatePermission(productId);

		if (image.getSize() > productMaxImageFileSizeMb * 1024 * 1024) {
			throw new ProductPublicationException(
					messageSourceAccessor.getMessage("service.DefaultProductPublicationService.FileTooLarge",
							new Object[] {productMaxImageFileSizeMb}));
		}

		return updateProductImage(
				productId,
				imageOrder,
				seller,
				(product) -> imageService.saveImageByProduct(product, image, imageOrder));
	}

	// TODO: метод вызывается только из DefaultSaleRequestService, в идеале выделить каким-то образом
	//  специфику для заявок на консьерж: класс-адаптер | доп. параметры | ...
	//  Аналогичный метод:
	//  publishProduct(su.reddot.domain.service.dto.ProductDTO, su.reddot.domain.model.user.User, java.util.function.Consumer<su.reddot.domain.model.product.Product>)
	@Override
	public ProductImageDTO updateProductImage(@NonNull User seller, @NonNull Long productId, @NonNull String filePath,
											  @NonNull Integer imageOrder) {

		return updateProductImage(
				productId,
				imageOrder,
				seller,
				(product) -> imageService.saveImageByProduct(product, filePath, imageOrder));
	}

	private ProductImageDTO updateProductImage(@NonNull Long productId, @NonNull Integer imageOrder,
											   @NonNull User seller, Function<Product, Image> saveImageFunction) {

		if (userBanService.isCanNotPublish(seller.getId()).isBanned()) {
			throw new PublishBanException();
		}

		if (imageOrder < 1 || imageOrder > productMaxImagesCount) {
			throw new ProductPublicationException(messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.IncorrectItemPhotoNumber", new Object[] {imageOrder}));
		}
		Product product = getProductForPublication(productId, null, null, seller);
		Image imageObj = saveImageFunction.apply(product);
		product.replaceImage(imageObj);
		if (product.getProductState() == ProductState.PUBLISHED && imageOrder == 1) {
			setState(product, ProductState.NEED_MODERATION);
		}
		activityService.save(new UploadProductPhotoActivity(product.getId()));
		return new ProductImageDTO(
				imageObj.getId(),
				staticResourceBalancer.getImageFullPath(imageObj.getImagePath(ProcessingType.ITEM)),
				imageObj.getPhotoOrder());
	}

	@Override
	public ProductImageDTO pushProductImage(@NonNull Long productId, @NonNull User seller,
											 @NonNull String path) {

		productAccessHandler.checkUpdatePermission(productId);

		if (userBanService.isCanNotPublish(seller.getId()).isBanned()) {
			throw new PublishBanException();
		}

		Product product = getProductForPublication(productId, null, null, seller);
		if (product.getImages() != null && product.getImages().size() >= productMaxImagesCount) {
			throw new ProductPublicationException(messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.IncorrectItemPhotoNumber", new Object[] {product.getImages().size() + 1}));
		}
		List<Image> images = imageService.pushImageToProduct(product, path);
		product.setImages(images);
		if (product.getProductState() == ProductState.PUBLISHED) {
			setState(product, ProductState.NEED_MODERATION);
		}
		activityService.save(new UploadProductPhotoActivity(product.getId()));
		return new ProductImageDTO(
				images.get(0).getId(),
				staticResourceBalancer.getImageFullPath(images.get(0).getImagePath(ProcessingType.ITEM)),
				images.get(0).getPhotoOrder());
	}

	@Override
	public ProductImageDTO updateDefectImage(@NonNull Long productId, @NonNull MultipartFile image, @NonNull Integer imageOrder, @NonNull String comment){

		User seller = getSeller();

		productAccessHandler.checkUpdatePermission(productId);

		if (imageOrder < 1 || imageOrder > defectMaxImagesCount) {
			throw new ProductPublicationException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectDefectPhotoNumber", new Object[]{imageOrder}));
		}
		if(image.getSize() > productMaxImageFileSizeMb * 1024 * 1024) throw new ProductPublicationException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.FileTooLarge", new Object[]{productMaxImageFileSizeMb}));
		Product product = getProductForPublication(productId, null, null, seller);
		DefectImage imageObj = imageService.saveDefectImageByProduct(product, image, imageOrder, comment);
		product.replaceDefectImage(imageObj);
		activityService.save(new UploadDefectPhotoActivity(product.getId()));
		return new ProductImageDTO(
				imageObj.getId(),
				staticResourceBalancer.getImageFullPath(imageObj.getImagePath(ProcessingType.ITEM)),
				imageObj.getPhotoOrder(),
				imageObj.getComment(),
				null);
	}

	@Override
	public ProductImageDTO updateDefectImageComment(@NonNull Long productId, @NonNull Integer imageOrder, @NonNull String comment){

		User seller = getSeller();

		productAccessHandler.checkUpdatePermission(productId);

		Product product = getProductForPublication(productId, null, null, seller);
		DefectImage imageObj = imageService.updateDefectImageComment(product, imageOrder, comment);
		activityService.save(new UploadDefectPhotoActivity(product.getId()));
		return new ProductImageDTO(
				imageObj.getId(),
				staticResourceBalancer.getImageFullPath(imageObj.getImagePath(ProcessingType.ITEM)),
				imageObj.getPhotoOrder());
	}

	@Override
	public ProductDTO getProduct(@NonNull Long productId, String currencyCode){
		Product product = productService.getRawProduct(productId, ProductService.UserType.HUMAN).orElseThrow(() -> new ProductNotFoundException(messageSourceAccessor.getMessage("exception.not-found.product", new Object[]{productId})));
		assertProductBelongsToUser(product, getSeller());
		return toProduct(product, null, currencyCode, getSeller());
	}

	@Override
	public Page<ProductDTO> getProductsByStoreCode(@NonNull String storeCode, PageRequest pageRequest, String currencyCode) {
		return Utils.getPageFromSpringPage(
				productRepository
						.getProductsBySellerAndStoreCode(
								getSeller(),
								storeCode,
								Utils.getPageable(pageRequest, new QSort(QProduct.product.id.desc())))
						.map(product -> productMapper.toDTOForCurrentUser(product, new MapProductsOptions())));
	}


	private CategoryDTO getCategoryDTO(Category category){
		return category == null ? null :
				new CategoryDTO().setId(category.getId()).setDisplayName(category.getDisplayName()).setFullName(category.getFullName());
	}

	private BrandDTO getBrandDTO(Brand brand){
		return brand == null ? null :
				new BrandDTO().setId(brand.getId()).setName(brand.getName());
	}

	private ProductDTO toProduct(@NonNull Product product, ProductListInfo productListInfo, String currencyCode, User seller) {
		assertProductExists(product);
		assertProductBelongsToUser(product, seller);
		// оставил getCurrentAuthorizedUser() потому что DTO кастомизирует по текущему пользователю
		ProductDTO dto = productMapper.toDTOForCurrentUser(
				product,
				new MapProductsOptions().setCurrencyCode(currencyCode),
				productListInfo);
		CurrencyConverter converter = currencyConverterFactory.createByCurrencyCodeToBase(currencyCode);
		//Новое поле rrpPrice с рекомендованной ценой
		dto.setRrpPrice(converter.convertFromBase(product.getRrpPrice()));
		dto.setDescription(product.getDescription());
		if (product.getCreateTime() != null) {
			dto.setCreateTimestamp(product.getCreateTime().toEpochSecond());
		}
		if (product.getChangeTime() != null) {
			dto.setChangeTimestamp(product.getChangeTime().toEpochSecond());
		}
		if (product.getProductStateTime() != null) {
			dto.setProductStateTimestamp(product.getProductStateTime().toEpochSecond());
		}
		dto.setParentCategories(categoryService.getAllParentCategoriesAsDTO(product.getCategoryId()));

		//Заполняем точку забора вещи
		if (product.getPickupAddressEndpoint() != null) {
			dto.setPickupAddressEndpointId(product.getPickupAddressEndpoint().getId());
			dto.setPickupAddressEndpoint(addressEndpointService.getAddressEndpointDTO(product.getPickupAddressEndpoint()));
		}

		//Заполняем точку забора вещи
		if (product.getPickupAddressEndpointAggregation() != null) {
			dto.setPickupAddressEndpointAggregationId(product.getPickupAddressEndpointAggregation().getId());
			dto.setPickupAddressEndpointAggregation(modelMapper.map(addressEndpointAggregationService.findById(product.getPickupAddressEndpointAggregation().getId()), AddressEndpointAggregationDTO.class));
		}

		//Если имеем дело с черновиком, отклоненным или отправленным на повторное редактирование, то возможно есть причина отклонения, которую мы добавим в DTO
		if (product.getProductState() == DRAFT
				|| product.getProductState() == SECOND_EDITION
				|| product.getProductState() == REJECTED) {

			ProductRejectReasonDTO rejectReasonDTO =
					productRejectReasonService.findLastRejectReasonAsDTO(product.getId());
			dto.setRejectReason(rejectReasonDTO);
			//Так же добавим информацию о том, готов ли этот товар к модерации
			//Кроме того, добавим информацию о незаполненных полях
			fillModerationConsistencyFields(dto);
		}

		//Если товар на модерации, то добавляем инфу о том, сколько осталось ждать
		if ((product.getProductState() == NEED_MODERATION
				|| product.getProductState() == NEED_RETOUCH
				|| product.getProductState() == RETOUCH_DONE) && product.getSendToModeratorTime() != null) {

			Duration duration = Duration.between(product.getSendToModeratorTime(), ZonedDateTime.now());
			dto.setModerationHoursRemains(moderationTimeoutHours - duration.toHours());
		}

		ZonedDateTime createTime = product.getCreateTime() != null ? product.getCreateTime() : ZonedDateTime.now();
		//В старых товарах поле create_time не заполнено, поэтому разумнее проверять по полю publish_time
		Optional<Product> previousPublishedProduct =
				productRepository.findFirstBySellerAndProductStateAndPublishTimeBefore(product.getSeller(), PUBLISHED,
						Utils.zonedDateTimeToLocalDateTime(createTime));
		dto.setProductWasPublishedByNewPublisher(!previousPublishedProduct.isPresent());
		return dto;
	}

	@Override
	public List<ProductDTO> getProductsByState(ProductState state, String currencyCode){
		return getProductsByState(state, null, currencyCode);
	}

	@Override
	public List<ProductDTO> getProductsByState(ProductState state, ProductService.SortAttribute sortAttribute, String currencyCode){
		return getProductsPage(Arrays.asList(state), null, sortAttribute, currencyCode).getItems();
	}

	@Override
	public List<ProductDTO> getDrafts(String currencyCode){
		return getProductsByState(ProductState.DRAFT, ProductService.SortAttribute.CHANGE_TIME_DESC, currencyCode);
	}

	@Override
	public Page<ProductDTO> getDraftsPage(PageRequest pageRequest, String currencyCode){
		return getProductsPage(Arrays.asList(ProductState.DRAFT), pageRequest, ProductService.SortAttribute.CHANGE_TIME_DESC, currencyCode);
	}

	@Override
	public List<ProductDTO> getProducts(List<ProductState> states, String currencyCode) {
		return getProductsPage(states, null, null, currencyCode).getItems();
	}

	@Override
	public Page<ProductDTO> getProductsPage(List<ProductState> states, PageRequest pageRequest,
			ProductService.SortAttribute sortAttribute, String currencyCode) {
		if (states != null && states.contains(ProductState.DELETED) && states.size() == 1) {
			return Page.emptyPage();
		}
		if (pageRequest == null) {
			pageRequest = PageRequest.of(1, DEFAULT_PAGE_LENGTH);
		}
		if (sortAttribute == null) {
			sortAttribute = ProductService.SortAttribute.PRODUCT_STATE_TIME_DESC;
		}
		ProductService.FilterSpecification filterSpecification =
				new ProductService.FilterSpecification()
						.sellerId(getSeller().getId())
						.states(states);

		Page<Product> productsPage =
				productService.getRawProducts(filterSpecification, pageRequest.getPage(), sortAttribute,
						new ProductService.ViewQualification().pageLength(pageRequest.getPageSize()),
						ProductService.UserType.HUMAN);

		ProductListInfo productDTOList = productMapper.prepareProductListInfo(
				productsPage.getItems(),
				securityService.getCurrentAuthorizedUser(),
				null, null, false, currencyCode);

		return productsPage.map(product -> toProduct(product, productDTOList, currencyCode, getSeller()));
	}

	@Override
	public Page<ProductDTO> getProductsPage(ProductState state, PageRequest pageRequest, ProductService.SortAttribute sortAttribute, String currencyCode){
		return getProductsPage(Arrays.asList(state), pageRequest, sortAttribute, currencyCode);
	}

	@Override
	public Page<ProductDTOIntegrationLite> getIntegrationLiteProductsPage(List<ProductState> states,
																		  PageRequest pageRequest) {
		if (states != null && states.contains(ProductState.DELETED) && states.size() == 1) {
			return Page.emptyPage();
		}
		if (pageRequest == null) {
			pageRequest = PageRequest.of(1, DEFAULT_PAGE_LENGTH);
		}

		ProductService.FilterSpecification filterSpecification =
				new ProductService.FilterSpecification()
						.sellerId(getSeller().getId())
						.states(states);

		Page<Product> productsPage =
				productService.getRawProducts(filterSpecification, pageRequest.getPage(),
						ProductService.SortAttribute.ID,
						new ProductService.ViewQualification().pageLength(pageRequest.getPageSize()),
						ProductService.UserType.HUMAN);

		return productsPage.map(product -> toIntegrationLiteDTO(product, getSeller()));
	}

	private ProductDTOIntegrationLite toIntegrationLiteDTO(Product product, User seller) {
		assertProductExists(product);
		assertProductBelongsToUser(product, seller);

		return new ProductDTOIntegrationLite()
				.setProductId(product.getId())
				.setBrandId(Optional.ofNullable(product.getBrand()).map(Brand::getId).orElse(null))
				.setCategoryId(product.getCategoryId())
				.setConditionId(product.getProductConditionId())
				.setProductState(product.getProductState())
				.setStoreCode(product.getStoreCode());
	}

	@Override
	public int countProductsByStates(ProductState... states){
		ProductService.FilterSpecification filterSpecification = new ProductService.FilterSpecification()
				.sellerId(getSeller().getId())
				.states(Arrays.asList(states));
		return (int) productService.countProducts(filterSpecification, ProductService.UserType.HUMAN);
	}

	@Override
	public int getProductsCount(ProductState state){
		if(state == ProductState.DELETED) return 0;
		return countProductsByStates(state);
	}

	@Override
	public Map<ProductState, Integer> getProductsCounts(List<ProductState> productStates){
		if(productStates == null || productStates.isEmpty()) productStates = Arrays.asList(ProductState.values());
		Map<ProductState, Integer> result = new HashMap<>();
		for(ProductState state : productStates){
			result.put(state, getProductsCount(state));
		}
		return result;
	}

	@Override
	public Long deleteProduct(@NonNull Long productId){

		productAccessHandler.checkDeletePermission(productId);

		Product product = getProductForRemovement(productId);
		productService.delete(product, ProductService.UserType.HUMAN);
		activityService.save(new DeleteProductActivity(product.getId()));
		return productId;
	}

	@Override
	public Long deleteProductImage(@NonNull Long productId, @NonNull Long imageId){

		User seller = getSeller();

		productAccessHandler.checkUpdatePermission(productId);

		Product product = getProductForPublication(productId, null, null, seller);
		List<Long> productImageIds = product.getImages().stream().map(i -> i.getId()).collect(Collectors.toList());
		if(!productImageIds.contains(imageId)) throw new WrongImageException(imageId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ImageNotRelatedToItem", new Object[]{productId}));
		imageService.deleteImage(imageId);
		imageService.updateImagesOrder(product);
		return imageId;
	}

	@Override
	public Long deleteDefectImage(@NonNull Long productId, @NonNull Long imageId){

		User seller = getSeller();

		productAccessHandler.checkUpdatePermission(productId);

		Product product = getProductForPublication(productId, null, null, seller);
		List<Long> defectImageIds = product.getDefectImages().stream().map(i -> i.getId()).collect(Collectors.toList());
		if(!defectImageIds.contains(imageId)) throw new WrongImageException(imageId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ImageNotRelatedToItem", new Object[]{productId}));
		imageService.deleteDefectImage(imageId);
		imageService.updateDefectImagesOrder(product);
		return imageId;
	}

	@Override
	public CommissionGrid getCurrentSellerCommissionGrid() {
		return getSeller().getCommissionGrid();
	}

	@Override
	public CommissionGrid getCommissionGridBySellerId(Long sellerId) {
		User currentUser = securityService.getCurrentAuthorizedUser();

		if (!currentUser.isAdmin() && !currentUser.isModerator()) {
			throw new OperationNotAllowedException("Данная операция недоступна");
		}

		return userService.getOne(sellerId).getCommissionGrid();
	}

	private CommissionGrid getCommissionGridBySellerIdWithDefault(Long sellerId) {
		if (sellerId == null) {
			return getCurrentSellerCommissionGrid();
		} else {
			return getCommissionGridBySellerId(sellerId);
		}
	}

	@Override
	public double getCommission(BigDecimal priceWithCommission, SalesChannel salesChannel, Long sellerId) {
		return commissionService.getCommissionByPriceWithCommission(
				priceWithCommission,
				getCommissionGridBySellerIdWithDefault(sellerId),
				salesChannel
		).getValueNullSafe(salesChannel).doubleValue();
	}

    @Override
    public double getCustomCommission(BigDecimal priceWithCommission, BigDecimal priceWithoutCommission) {
        return commissionService.calculateCustomCommission(priceWithCommission, priceWithoutCommission).doubleValue();
    }

    @Override
	public BigDecimal getPriceWithCommission(BigDecimal priceWithoutCommission,
											 SalesChannel salesChannel,
											 Long sellerId) {
		return commissionService.calculatePriceWithCommission(priceWithoutCommission,
				getCommissionGridBySellerIdWithDefault(sellerId), salesChannel).getPriceWithCommission();
	}

	@Override
	public BigDecimal getPriceWithoutCommission(BigDecimal priceWithCommission,
												SalesChannel salesChannel,
												Long sellerId) {
		return commissionService.calculatePriceWithoutCommission(priceWithCommission,
				getCommissionGridBySellerIdWithDefault(sellerId), salesChannel);
	}

	@Override
	public BigDecimal getPriceWithoutCommissionInBaseCurrency(Long currencyId, BigDecimal priceInCurrency, boolean isCrossBorder) {
		if (currencyId == null)
			throw new OskellyException("currencyId is mandatory!");
		if (priceInCurrency == null)
			throw new OskellyException("priceInCurrency is mandatory!");
		if (priceInCurrency.compareTo(BigDecimal.ZERO) < 1)
			throw new OskellyException("priceInCurrency must be greater than 0!");

		return currencyProductService.convertToBase(currencyId, priceInCurrency, isCrossBorder).getConvertResult();
	}

	@Override
	public ProductPriceDTO getConvertedToBasePrice(Long productId,
												   Long currencyId,
												   BigDecimal priceInCurrency,
												   Boolean isCustomCommission,
												   BigDecimal customCommissionValue) {
		if (currencyId == null)
			throw new OskellyException("currencyId is mandatory!");
		if (priceInCurrency == null)
			throw new OskellyException("priceInCurrency is mandatory!");
		if (priceInCurrency.compareTo(BigDecimal.ZERO) < 1)
			throw new OskellyException("priceInCurrency must be greater than 0!");
		if (isCustomCommission == null)
			throw new OskellyException("isCustomCommission is mandatory!");
		if (Boolean.TRUE.equals(isCustomCommission) && customCommissionValue == null)
			throw new OskellyException("customCommissionValue must not be null if custom commission TRUE!");

		Product product = productRepository.findById(productId).get();

		if (isCustomCommission) {
			// from old admin we got customCommissionValue which includes duty percent
			// but our code expects clear commission percent,
			// so we recalculate it
			Long pickupCountryId = product.getSeller().getPickupCountry() != null ? product.getSeller().getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
			Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

			customCommissionValue = dutyService.fixCustomCommissionFromAdmin(
					customCommissionValue,
					pickupCountryId,
					deliveryCountryId
			).orElse(customCommissionValue);
		}

		PriceAndCommission priceAndCommission = currencyProductService.calculateProductPriceAndCommission(
				product,
				currencyId,
				priceInCurrency,
				isCustomCommission,
				customCommissionValue);

		return new ProductPriceDTO()
				.setPriceWithCommission(priceAndCommission.getPriceWithCommission())
				.setPriceWithoutCommission(priceAndCommission.getPriceWithoutDuties())
				.setCommission(priceAndCommission.getCommissionValue())
				.setDutiesAmount(priceAndCommission.getDutiesAmount());
	}

	@Override
	public Conversion getConversionValue(final BigDecimal priceWithoutCommission, final BigDecimal priceWithCommission,
										 final SalesChannel salesChannel, final Long sellerId, final String currencyCode) {


		return getConversionValueInternal(priceWithoutCommission, priceWithCommission, salesChannel,
				sellerId, currencyCode,
				/*
				Внутри этого метода делается проверка на то, что текущий авторизованный пользователь
				ADMIN или модератор. Если это не так, то exception.

				Специально ли так ограничили getConversionValue?
				 */
				getCommissionGridBySellerIdWithDefault(sellerId));
	}

	public Conversion getConversionValueInternal(final BigDecimal priceWithoutCommission, final BigDecimal priceWithCommission,
												 final SalesChannel salesChannel, final Long sellerId, final String currencyCode,
												 CommissionGrid sellerCommissionGrid) {

		if ((priceWithoutCommission != null && priceWithCommission != null)
				|| (priceWithoutCommission == null && priceWithCommission == null)) {
			throw new OskellyException(
					"Для конверсии должен быт задан один и только один из двух возможных параметров."
			);
		}

		User seller = (sellerId == null) ? securityService.getCurrentAuthorizedUser() : userService.getOne(sellerId);

		if (sellerCommissionGrid == null) {
			sellerCommissionGrid = seller.getCommissionGrid();
		}

		CurrencyConverter converter = currencyConverterFactory.createByCurrencyCodeToBase(currencyCode);

		BigDecimal priceWithoutCommissionInBaseCurrency = converter.revertConversionFromBase(priceWithoutCommission)
				.getConvertResult();
		BigDecimal priceWithCommissionInBaseCurrency = converter.revertConversionFromBase(priceWithCommission)
				.getConvertResult();

		boolean calculateFromPriceWithoutCommission = priceWithCommissionInBaseCurrency == null;
		if (priceWithCommissionInBaseCurrency == null) {
			priceWithCommissionInBaseCurrency = commissionService.calculatePriceWithCommission(
					priceWithoutCommissionInBaseCurrency, sellerCommissionGrid, salesChannel, 2).getPriceWithCommission();
		}

		priceWithoutCommissionInBaseCurrency = commissionService.calculatePriceWithoutCommission(priceWithCommissionInBaseCurrency, sellerCommissionGrid, salesChannel, 2);

		// apply duties and recalculate price with commission properly,
		// including VAT
		Conversion baseCurrencyConversion = new Conversion()
				.setPriceWithCommission(priceWithCommissionInBaseCurrency)
				.setPriceWithoutCommission(priceWithoutCommissionInBaseCurrency);
		applyDutiesToConversion(baseCurrencyConversion, seller, calculateFromPriceWithoutCommission);

		CommissionOptional commission = commissionService.getCommissionByPriceWithCommission(
				baseCurrencyConversion.getPriceWithCommission(),
				sellerCommissionGrid,
				salesChannel);

		Conversion conversion = new Conversion();

		conversion.setPriceWithCommission(
				priceWithCommission != null
						? priceWithCommission
						: converter.convertFromBase(baseCurrencyConversion.getPriceWithCommission().setScale(0, RoundingMode.HALF_UP)));

		conversion.setPriceWithoutCommission(converter.convertFromBase(baseCurrencyConversion.getPriceWithoutCommission().setScale(0, RoundingMode.HALF_UP)));

		conversion.setCommission(converter.convertFromBase(commission.getValueNullSafe(salesChannel)).doubleValue());

		conversion.setFixedAmount(converter.convertFromBase(commission.getFixedAmountNullSafe()));

		conversion.setCommissionId(commission.getCommission() != null ?
				commission.getCommission().getId() : null);

		// если установлен канал продаж Бутик, то надо округлять до сотен и выставлять кастомную комиссию
		if (SalesChannel.BOUTIQUE_AND_WEBSITE.equals(salesChannel) || SalesChannel.BOUTIQUE.equals(salesChannel)) {
			conversion.setPriceWithCommission(
					commissionService.roundPriceToHundred(
							conversion.getPriceWithCommission())
			);
			conversion.setCommission(
					commissionService.calculateCustomCommission(
							conversion.getPriceWithCommission(),
							conversion.getPriceWithoutCommission()).doubleValue());
		}
		// а для складского канала продаж необходимо, чтобы цена на сайте была тоже округлена,
		// но при этом без кастомной комиссии. Поэтому после округления цены на сайте надо пересчитывать цену на руки
		if (SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE.equals(salesChannel)) {
			conversion.setPriceWithCommission(
					commissionService.roundPriceToHundred(
							conversion.getPriceWithCommission())
			);
			conversion.setPriceWithoutCommission(commissionService.calculatePriceWithoutCommission(
					conversion.getPriceWithCommission(),
					sellerCommissionGrid,
					salesChannel
			));
		}

		conversion.setDutiesAmount(converter.convertFromBase(baseCurrencyConversion.getDutiesAmount()));

		return conversion;
	}

	private void applyDutiesToConversion(@NotNull Conversion conversion, @NotNull User seller, boolean basedOnPriceWithoutCommission) {
		if (basedOnPriceWithoutCommission) {
			Long pickupCountryId = seller.getPickupCountry() != null ? seller.getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
			Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

			List<DutyDTO> duties = dutyService.calculateDutiesByPriceWithoutCommission(
					conversion.getPriceWithCommission(),
					conversion.getPriceWithoutCommission(),
					seller,
					pickupCountryId,
					deliveryCountryId);
			if (duties.size() > 0) {
				BigDecimal dutiesAmount = duties.stream().map(DutyDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				conversion.setPriceWithCommission(conversion.getPriceWithoutCommission().add(dutiesAmount));
				conversion.setDutiesAmount(dutiesAmount);
			}
		} else {
			Long pickupCountryId = seller.getPickupCountry() != null ? seller.getPickupCountry().getId() : dutyService.getDefaultPickupCountryId();
			Long deliveryCountryId = dutyService.getDefaultDeliveryCountryId();

			List<DutyDTO> duties = dutyService.calculateDutiesByPriceWithCommission(
					conversion.getPriceWithCommission(),
					conversion.getPriceWithoutCommission(),
					seller,
					pickupCountryId,
					deliveryCountryId);
			if (duties.size() > 0) {
				BigDecimal dutiesAmount = duties.stream().map(DutyDTO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
				conversion.setPriceWithoutCommission(conversion.getPriceWithCommission().subtract(dutiesAmount));
				conversion.setDutiesAmount(dutiesAmount);
			}
		}
	}

	@Override
	public int getDraftStep(@NonNull Long productId){
		User seller = getSeller();
		Product product = getProductForPublication(productId, null, null, seller);
		assertProductBelongsToUser(product, seller);
		if(product.getProductState() != ProductState.DRAFT) throw new ProductPublicationException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ItemIsNotDraft", new Object[]{productId}));
		try{
			validateAttributeValues(categoryService.getCategory(product.getCategoryId()), product.getAttributeValues().stream().map(av -> av.getAttributeValue().getId()).collect(Collectors.toList()));
			assertNotNull(product.getSizeType(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SizeTypeNotSpecified"));
			validateSizes(product);
		}
		catch(OskellyException e){ return 1; }
		try{
			assertProductHasRequiredImages(product);
		}
		catch(OskellyException e){ return 2; }
		try{
			assertNonEmptyStringValue(product.getDescription(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ItemDescriptionNotSpecified"));
		}
		catch(OskellyException e){ return 3; }
		try{
			assertNotNull(productService.getProductCondition(product.getProductConditionId()), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.ProductConditionNotSpecified"));
			assertPositiveValue(product.getCurrentPrice(), messageSourceAccessor.getMessage("service.DefaultProductPublicationService.IncorrectPrice"));
		}
		catch(OskellyException e){ return 4; }
		try{
			;
		}
		catch(OskellyException e){ return 5; }
		return 0;
	}

	@Override
	public List<ProductImageDTO> getProductPhotoSamples(Long categoryId){
		if (categoryId == null || categoryId == 1L) return Collections.emptyList();
		List<ProductImageDTO> result = publicationPhotoSampleRepository.findByCategoryIdOrderByPhotoOrderAsc(categoryId)
				.stream().map(ps -> getPhotoSampleDTO(ps)).collect(Collectors.toList());
		if(result.isEmpty()){
			Category category = categoryService.getCategory(categoryId);
			if(category != null && category.getId() != 1L) return getProductPhotoSamples(category.getParent().getId());
		}
		return result;
	}

	@Override
	public ProductImageDTO getPhotoSampleDTO(PublicationPhotoSample sample){
		if(sample == null) return null;
		return new ProductImageDTO().setOrder(sample.getPhotoOrder()).setPath(staticResourceBalancer.transform(getProductPhotoSampleFullPath(sample.getImagePath())));
	}

	@Override
	public CategoryTree getCategoryTree(){

		User user = securityService.getCurrentAuthorizedUser();

		boolean authorized = user != null;
		boolean pro = user != null && user.isPro();
		boolean canPublishToDisabledCategories = user != null && user.canPublishToDisabledCategories();

		return self.getCategoryTreeCached(authorized, pro, canPublishToDisabledCategories);
	}

	@Cacheable(value = "ProductPublicationService.getCategoryTreeCached")
	@Override
	public CategoryTree getCategoryTreeCached(boolean authorized, boolean proUser,
			boolean approvedUserForPublishToDisabledCategories){
		CategoryTree categoryTree = categoryService.getGlobalCategoryTree().clone();
		removeUnavailableCategories(categoryTree, authorized, proUser, approvedUserForPublishToDisabledCategories);
		setMinPrice(categoryTree);
		return categoryTree;
	}

	@Override
	public List<CategoryDTO> getChildrenCategories(Long categoryId){
		CategoryDTO categoryDTO = getCategoryTree().getCategoryById(categoryId);
		if(categoryDTO == null) throw new WrongCategoryException(categoryId, messageSourceAccessor.getMessage("service.DefaultProductPublicationService.InvalidCategory"));
		return categoryDTO.getChildren();
	}

	private void setMinPrice(CategoryTree categoryTree) {
		for (Rule rule : publicationRules) {
			if (rule.getMinPrice() != null) {
				categoryTree.getAllSubtreeCategories(rule.getCategoryId())
						.forEach(c -> c.setMinPrice(rule.getMinPrice()));
			}
		}
	}

	@Override
	public List<ProductConditionDTO> getProductConditions() {
		return productService.getProductConditionDTOsCached();
	}

	private void removeUnavailableCategories(CategoryTree categoryTree, boolean authorized, boolean proUser,
			boolean approvedPhysForPublishToDisabledCats) {

		for (Rule rule : publicationRules) {

			// Пропускаем правило, если оно не уменьшает доступность категорий
			if (rule.isAvailable()) continue;

			if (!authorized) {
				// Если пользователь не авторизован, для него исключаем все что можно исключить, чтобы гость видел минимальное дерево
				// Иначе если в качестве условия в правиле указан тип пользователя "pro или физик, одобренный для публикации", проверяем, являетсяли он таковым
				categoryTree.removeCategory(rule.getCategoryId());
			} else if (rule.getProOrApprovedPhys() != null) {
				// Если условием является характеристики пользователя, проверяем их
				boolean proOrApprovedPhys = proUser || approvedPhysForPublishToDisabledCats;
				if (rule.getProOrApprovedPhys().equals(proOrApprovedPhys)) {
					categoryTree.removeCategory(rule.getCategoryId());
				}
			}
		}
	}

	/**
	 * Заполнение DTO товара в части полей о состоянии товара перед модерацией
	 *
	 * @param productDTO DTO товара
	 */
	private void fillModerationConsistencyFields(ProductDTO productDTO) {
		List<String> result = new ArrayList<>();
		Map<LackingFieldType, List<String>> warnings = new HashMap<>();

		Category productCategory = categoryService.getCategory(productDTO.getCategoryId());
		if (productCategory == null || !productCategory.isLeaf()) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.Category");
			result.add(message);
			addWarning(warnings, LackingFieldType.INFO, message);
		}
		if (productDTO.getBrand() == null) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.Brand");
			result.add(message);
			addWarning(warnings, LackingFieldType.INFO, message);
		}

		if (productDTO.getCategoryId() != null) {
			// проверка того, что указаны все обязательные атрибуты
			Map<Long, Attribute> availableAttributes =
					categoryService.getAllAttributes(productDTO.getCategoryId())
								   .stream().map(CatalogAttribute::getAttribute)
								   .collect(Collectors.toMap(Attribute::getId, Function.identity()));

			List<Long> productAttributeValueIds = Optional.ofNullable(productDTO.getAttributeValueIds())
														  .orElse(Collections.emptyList());
			Set<Long> filledAttributes =
					productAttributeValueIds.stream().map(attributeService::getAttributeValue)
											.map(AttributeValue::getAttributeId)
											.filter(availableAttributes::containsKey)
											.collect(Collectors.toSet());
			availableAttributes.values().stream()
							   .filter(attribute -> attribute.getIsRequired()
									   && !filledAttributes.contains(attribute.getId()))
							   .map(Attribute::getName)
							   .forEach(attr -> {
								   result.add(attr);
								   addWarning(warnings, LackingFieldType.INFO, attr);
							   });
		}

		if (productDTO.getSizeType() == null) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.Size");
			result.add(message);
			addWarning(warnings, LackingFieldType.INFO, message);
		}

		try {
			validateSizes(productDTO);
		} catch (ProductPublicationException e) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.Size");
			result.add(message);
			addWarning(warnings, LackingFieldType.INFO, message);
		}

		if (productDTO.getImages() == null
				|| productDTO.getImages().stream().filter(i -> i.getPath() != null).count() < 2) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.MandatoryImage");
			result.add(message);
			addWarning(warnings, LackingFieldType.IMAGES, message);
		}

		if (isNullOrEmpty(productDTO.getDescription())) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.ItemDescription");
			result.add(message);
			addWarning(warnings, LackingFieldType.DESCRIPTION, message);
		}
		if (productService.getProductCondition(productDTO.getConditionId()) == null) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.ProductCondition");
			result.add(message);
			addWarning(warnings, LackingFieldType.PRICE_AND_CONDITION, message);
		}

		if (productDTO.getPrice() == null || productDTO.getPrice().intValue() <= 0) {
			String message = messageSourceAccessor.getMessage(
					"service.DefaultProductPublicationService.Property.Price");
			result.add(message);
			addWarning(warnings, LackingFieldType.PRICE_AND_CONDITION, message);
		}

		if (productDTO.getPickupAddressEndpointId() == null
				&& productDTO.getPickupAddressEndpointAggregationId() == null) {
			addWarning(warnings, LackingFieldType.ADDRESS,
					messageSourceAccessor.getMessage(
							"service.DefaultProductPublicationService.Property.Address"));
		}

		productDTO.setIsReadyForModeration(result.isEmpty());
		productDTO.setFieldsLackingForModeration(result);
		productDTO.setSectionsLackingForModeration(warnings);
	}

	private void addWarning(Map<LackingFieldType, List<String>> warnings, LackingFieldType key, String message) {
		warnings.merge(key, Collections.singletonList(message), ListUtils::union);
	}

	/**
	 * Валидация размеров, указанных в карточке товара
	 * В случае, если есть какая-то проблема, то выкидывается соответствующее исключение
	 *
	 * @param productListDTO модель товара
	 */
	private void validateSizes(@NonNull ProductDTO productListDTO) {
		if (Optional.ofNullable(productListDTO.getSizeType())
				.map(SizeTypeLocalized::getSizeType)
				.map(it -> it == SizeType.NO_SIZE)
				.orElse(false)) {
			return;
		}
		if (productListDTO.getSizes() == null || productListDTO.getSizes().isEmpty()) {
			throw new ProductPublicationException(messageSourceAccessor.getMessage("service.DefaultProductPublicationService.SizesNotFound"));
		}
		for (SizeValueDTO sizeValue : productListDTO.getSizes()) {
			assertCountPositive(sizeValue.getId(), sizeValue.getCount());
			validateAdditionalSizes(sizeValue.getAdditionalSizeValues(),
					categoryService.getCategory(productListDTO.getCategoryId()));
		}
	}

	@Override
	public void validateSalesChannel(SalesChannel salesChannel, Long productId) {
		if (salesChannel == SalesChannel.WEBSITE) {
			List<Long> boutiqueOrderIds = orderRepository.findActiveBoutiqueOrderIdsByProductId(productId);
			if (!boutiqueOrderIds.isEmpty()) {
				String boutiqueOrderIdsString = boutiqueOrderIds.stream()
						.map(String::valueOf)
						.collect(Collectors.joining(", "));
				String error = messageSourceAccessor.getMessage(
						"service.DefaultAdminProductService.WebsiteSalesChannelForBoutiqueProductError",
						new Object[] {productId, boutiqueOrderIdsString});
				log.error(error);
				throw new OskellyException(error);
			}
		}
	}

	private boolean isSalesChannelValid(SalesChannel salesChannel, Long productId) {
		try {
			validateSalesChannel(salesChannel, productId);
			return true;
		} catch (OskellyException e) {
			return false;
		}
	}

	@Override
	public void changeProductSalesChannelIfPossible(Product product, SalesChannel salesChannel) {

		if (!isSalesChannelValid(salesChannel, product.getId())
				|| product.isCustomCommission()
				|| product.getProductItems().stream().anyMatch(ProductItem::isCustomCommission)) {
			return;
		}

		product.setSalesChannel(salesChannel);

		if (product.getCurrentPrice() != null) {
			Conversion conversion = getConversionValue(
					null,
					product.getCurrentPrice(),
					salesChannel,
					product.getSeller().getId(),
					null
			);
			product.setCurrentPriceWithoutCommission(conversion.getPriceWithoutCommission());
			product.setCurrentPrice(conversion.getPriceWithCommission());
			product.setCommission(conversion.getCommissionId() != null ?
					commissionRepository.getOne(conversion.getCommissionId()) : null);
		}

		product.getProductItems().forEach(productItem -> {
			if (productItem.getCurrentPrice() != null) {
				Conversion conversion = getConversionValue(
						null,
						productItem.getCurrentPrice(),
						salesChannel,
						product.getSeller().getId(),
						null
				);
				productItem.setCurrentPriceWithoutCommission(conversion.getPriceWithoutCommission());
				productItem.setCurrentPrice(conversion.getPriceWithCommission());
				productItem.setCommission(conversion.getCommissionId() != null ?
						commissionRepository.getOne(conversion.getCommissionId()) : null);
			}
		});
	}
}
