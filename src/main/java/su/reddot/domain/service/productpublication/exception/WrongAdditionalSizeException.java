package su.reddot.domain.service.productpublication.exception;

public class WrongAdditionalSizeException extends ProductPublicationException {
    private Long additionalSizeId;
    public WrongAdditionalSizeException(Long additionalSizeId, String reason){
        super("Некорректный дополнительный размер " + additionalSizeId + ": " + reason);
        this.additionalSizeId = additionalSizeId;
    }
    @Override
    public Object getData(){
        return additionalSizeId;
    }
}
