package su.reddot.domain.service.productpublication.exception;

public class WrongAttributeValueException extends ProductPublicationException{
    private Long attributeValueId;
    public WrongAttributeValueException(Long attributeValueId, String reason){
        super("Некорректное значение атрибута " + attributeValueId + ": " + reason);
        this.attributeValueId = attributeValueId;
    }
    @Override
    public Object getData(){
        return attributeValueId;
    }
}
