package su.reddot.domain.service.commission;

import lombok.Getter;
import lombok.NonNull;

import java.math.BigDecimal;

public class FixCommissionAmountContainer {
    private BigDecimal amountWithoutFixedCommission;
    @Getter
    private BigDecimal fixedCommission;
    private BigDecimal amountWithFixedCommission;

    private FixCommissionAmountContainer(BigDecimal amountWithoutFixedCommission, BigDecimal amountWithFixedCommission) {
        this.amountWithoutFixedCommission = amountWithoutFixedCommission;
        this.amountWithFixedCommission = amountWithFixedCommission;
    }

    public static FixCommissionAmountContainer byAmountWithFixedCommission(@NonNull BigDecimal amountWithFixedCommission) {
        return new FixCommissionAmountContainer(null, amountWithFixedCommission);
    }

    public static FixCommissionAmountContainer byAmountWithoutFixedCommission(@NonNull BigDecimal amountWithoutFixedCommission) {
        return new FixCommissionAmountContainer(amountWithoutFixedCommission, null);
    }

    public boolean isInitialized() {
        return fixedCommission != null;
    }

    //если кто-то захочет использовать этот класс в нескольких потоках, к этому методу надо добавить synchronized
    public void initialize(BigDecimal commission) {

        if (isInitialized()) {
            throw new IllegalArgumentException("FixCommissionAmountContainer already initialized");
        }

        fixedCommission = commission != null ? commission : BigDecimal.ZERO;

        if (amountWithFixedCommission != null) {
            amountWithoutFixedCommission = amountWithFixedCommission.subtract(fixedCommission);

            //если фикса окажется больше стоимости
            if (amountWithoutFixedCommission.compareTo(BigDecimal.ZERO) < 0) {
                amountWithoutFixedCommission = BigDecimal.ZERO;
            }
        } else if (amountWithoutFixedCommission != null) {
            amountWithFixedCommission = amountWithoutFixedCommission.add(fixedCommission);

            //todo возможно, когда-то будет отрицательная комиссия :)
            if (amountWithFixedCommission.compareTo(BigDecimal.ZERO) < 0) {
                amountWithFixedCommission = BigDecimal.ZERO;
            }
        }
    }

    public BigDecimal getInitialAmount() {
        return amountWithFixedCommission != null ? amountWithFixedCommission : amountWithoutFixedCommission;
    }

    public BigDecimal getAmountWithoutFixedCommission() {
        if (amountWithoutFixedCommission == null) {
            throw new NullPointerException("amountWithoutFixedCommission is null");
        }
        return amountWithoutFixedCommission;
    }

    public BigDecimal getAmountWithFixedCommission() {
        if (amountWithFixedCommission == null) {
            throw new NullPointerException("amountWithFixedCommission is null");
        }
        return amountWithFixedCommission;
    }
}
