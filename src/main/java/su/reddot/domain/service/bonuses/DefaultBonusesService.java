package su.reddot.domain.service.bonuses;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.NoSuchMessageException;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.bonuses.OrderBonusesTransactionRepository;
import su.reddot.domain.dao.bonuses.UserBonusesAccountRepository;
import su.reddot.domain.dao.bonuses.UserLoyaltyExtendedInfoRepository;
import su.reddot.domain.dao.order.OrderDateForBonusesView;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.exception.bonuses.BonusesDistributionException;
import su.reddot.domain.exception.bonuses.BonusesTransactionAlreadyExistsExeption;
import su.reddot.domain.exception.bonuses.BonusesTransactionNotFoundExeption;
import su.reddot.domain.exception.bonuses.BonusesTransactionParamsException;
import su.reddot.domain.model.bonuses.OrderBonusesTransaction;
import su.reddot.domain.model.bonuses.OrderBonusesTransactionParams;
import su.reddot.domain.model.bonuses.OrderBonusesTransactionParamsOrderId;
import su.reddot.domain.model.bonuses.UserBonusesAccount;
import su.reddot.domain.model.bonuses.UserLoyaltyExtendedInfo;
import su.reddot.domain.model.notification.bonuses.BonusesNotification;
import su.reddot.domain.model.notification.bonuses.BonusesWelcomeNotification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.bonuses.model.BucketDTO;
import su.reddot.domain.service.bonuses.model.NotificationStage;
import su.reddot.domain.service.bonuses.model.ResponseBodyBalanceDTO;
import su.reddot.domain.service.bonuses.model.ResponseBodyListBucketDTO;
import su.reddot.domain.service.bonuses.model.ResponseBodyTransactionWithBalanceDTO;
import su.reddot.domain.service.bonuses.model.TransactionBriefDTO;
import su.reddot.domain.service.bonuses.model.TransactionDTO;
import su.reddot.domain.service.bonuses.model.TransactionWithBalanceDTO;
import su.reddot.domain.service.bonuses.model.TransferTemplateDTO;
import su.reddot.domain.service.bonuses.transferprograms.BirthdayBonusesTransferProgram;
import su.reddot.domain.service.bonuses.transferprograms.DefaultBonusesTransferProgram;
import su.reddot.domain.service.bonuses.transferprograms.WelcomeBonusesTransferProgram;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.bonuses.BonusesAmountDTO;
import su.reddot.domain.service.dto.bonuses.BonusesBalanceDTO;
import su.reddot.domain.service.dto.bonuses.BonusesBurningScheduleItemDTO;
import su.reddot.domain.service.dto.bonuses.BonusesInfoDTO;
import su.reddot.domain.service.dto.bonuses.BonusesInfoWithBurningDTO;
import su.reddot.domain.service.dto.bonuses.BonusesPaymentOptionType;
import su.reddot.domain.service.dto.bonuses.BonusesPaymentOptionsDTO;
import su.reddot.domain.service.dto.bonuses.BonusesTransactionDTO;
import su.reddot.domain.service.dto.bonuses.BonusesTransferTemplateDTO;
import su.reddot.domain.service.dto.bonuses.BonusesType;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.presentation.api.v2.bonuses.dto.offline.BonusesOfflineSplitItem;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static java.lang.Boolean.TRUE;
import static java.math.BigDecimal.ZERO;
import static java.util.concurrent.TimeUnit.SECONDS;
import static su.reddot.domain.model.order.OrderState.CREATED;
import static su.reddot.domain.service.util.DateTimeUtils.convertLocalDateToZonedDateTime;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty("bonuses-service.enabled")
public class DefaultBonusesService extends AbstractServiceWithHttpAndCompletableFuture implements BonusesService {

    private static final int DEFAULT_PAGE_SIZE = 20;
    private static final int DEFAULT_PAGE_NUMBER = 1;
    private static final BigDecimal DEFAULT_WITHDRAW_PERCENT = new BigDecimal(100);

    private final BonusesControllerAPI controller;
    private final MessageSourceAccessor messageSourceAccessor;
    private final Executor bonusesExecutor;
    private final OrderBonusesTransactionRepository orderBonusesTransactionRepository;
    private final UserBonusesAccountRepository userBonusesAccountRepository;
    private final UserLoyaltyExtendedInfoRepository userLoyaltyExtendedInfoRepository;
    private final CallInTransaction callInTransaction;
    private final OrderRepository orderRepository;
    private final UserRepository userRepository;
    private final SecurityService securityService;
    private final DefaultBonusesTransferProgram defaultBonusesTransferProgram;
    private final WelcomeBonusesTransferProgram welcomeBonusesTransferProgram;
    private final BirthdayBonusesTransferProgram birthdayBonusesTransferProgram;

    @Autowired
    @Lazy
    private BonusesService self;

    @Autowired
    @Lazy
    private NotificationService notificationService;

    @Autowired
    @Lazy
    private ConfigParamService configParamService;

    @Value("${bonuses-service.socket-timeout}")
    private Duration socketTimeout;

    @Value("${bonuses-service.not-lower-rest}")
    private BigDecimal notLowerRest;

    @Value("${bonuses-service.transfer.percent}")
    private BigDecimal configTransferPercent;

    @Value("${bonuses-service.transfer.limit}")
    private BigDecimal transferLimit;

    @Value("${bonuses-service.transfer.bonuses-limit}")
    private BigDecimal transferBonusesLimit;

    @Value("${bonuses-service.scheduler.error-income-transactions-processing.portion-size}")
    private Long processingPortionSize;

    @Value("${bonuses-service.scheduler.error-income-transactions-processing.max-attempts}")
    private Integer processingMaxAttempts;

    @Value("${bonuses-service.scheduler.old-hold-transactions-processing.oldness-in-seconds}")
    private Long oldnessOfHoldTransaction;

    @Value("${boutique.boutique-buyers-ids}")
    Collection<Long> boutiqueBuyersIds;

    public BonusesTransferTemplateDTO getBonusesTransferTemplate(String code) {
        return callWithExceptionWrapping(() ->
            CompletableFuture
                    .supplyAsync(
                            () -> controller.getTransferBonusesTemplate(code),
                            bonusesExecutor
                    )
                    .thenApplyAsync(
                            response -> {
                                if (response != null && response.getData() != null) {
                                    return convertTransferTemplateDTOTOBonusesTransferTemplateDTO(response.getData());
                                }
                                throw new OskellyException(messageSourceAccessor.getMessage(DEFAULT_UNEXPECTED_EXCEPTION));
                            },
                            bonusesExecutor
                    )
                    .exceptionally(this::exceptionallyTimeoutTemplate)
                    .get(socketTimeout.getSeconds() * 2, SECONDS)
        );
    }

    @Cacheable("DefaultBonusesService.getBonusesTransferTemplateCached")
    public BonusesTransferTemplateDTO getBonusesTransferTemplateCached(String code) {
        return self.getBonusesTransferTemplate(code);
    }

    public List<BonusesTransferTemplateDTO> getBonusesTransferTemplateList() {
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                controller::getTransferBonusesTemplateList,
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                response -> {
                                    if (response != null && response.getData() != null) {
                                        return response.getData()
                                                .stream()
                                                .map(this::convertTransferTemplateDTOTOBonusesTransferTemplateDTO)
                                                .collect(Collectors.toList());
                                    }
                                    return new ArrayList<BonusesTransferTemplateDTO>();
                                },
                                bonusesExecutor
                        )
                        .exceptionally(ex -> {
                            log.warn("Unexpected error", ex);
                            throw new OskellyException(ex);
                        })
                        .get(socketTimeout.getSeconds() * 2, SECONDS)
        );
    }

    @Cacheable("DefaultBonusesService.getBonusesTransferTemplateListCached")
    public List<BonusesTransferTemplateDTO> getBonusesTransferTemplateListCached() {
        return self.getBonusesTransferTemplateList();
    }

    private BonusesTransferTemplateDTO convertTransferTemplateDTOTOBonusesTransferTemplateDTO(TransferTemplateDTO source) {
        BonusesTransferTemplateDTO result = new BonusesTransferTemplateDTO();
        result.setBonusesType(BonusesType.valueOf(source.getType().getValue()));
        result.setName(source.getName());
        result.setCode(source.getCode());
        result.setReason(source.getReason());
        result.setAmount(source.getAmount());
        result.setDateEnd(source.getDateEnd());
        return result;
    }

    @Override
    public void transferBonusesManual(
            Long userId, BigDecimal bonusesAmount, BigDecimal moneyAmount, String description, String reason, LocalDate dateEnd
    ) {
        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                securityService::getCurrentAuthorizedUserId,
                                bonusesExecutor
                        )
                        .thenCombineAsync(
                                CompletableFuture
                                        .supplyAsync(
                                                () -> {
                                                    Optional<User> userOpt = userRepository.findById(userId);
                                                    if (userOpt.isPresent())  {
                                                        return self.getOrCreateUserBonusesAccount(userId);
                                                    }
                                                    return null;
                                                },
                                                bonusesExecutor
                                        )
                                        .exceptionally(
                                                ex -> {
                                                    log.warn("Unexpected error", ex);
                                                    throw new OskellyException(ex);
                                                }
                                        ),
                                (authorId, userBonusesAccount) -> {
                                    if (userBonusesAccount == null) {
                                        return null;
                                    }
                                    BigDecimal bonuses = (bonusesAmount == null) ? ZERO : bonusesAmount;
                                    BigDecimal money = (moneyAmount == null) ? ZERO : moneyAmount;
                                    return controller.transferBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(null, bonuses, money, bonuses.add(money)),
                                            authorId,
                                            dateEnd,
                                            description,
                                            reason,
                                            defaultBonusesTransferProgram.getStagesByDateEnd(dateEnd),
                                            generateIdempotencyKeyWithAccountIdAndRandom(userBonusesAccount.getBonusesAccountId()),
                                            false
                                    );
                                },
                                bonusesExecutor
                        )
                        .exceptionally(this::exceptionallyTimeoutTemplate)
                        .get(socketTimeout.getSeconds() * 2, SECONDS)
        );
    }

    @Override
    public void transferBonusesWelcome(Long userId) {
        String errPartText = "Welcome bonuses are not transferred to user: {}. ";

        if (!welcomeBonusesTransferProgram.isEnabled()) {
            log.warn(errPartText + "Welcome bonuses program is disabled.", userId);
            return;
        }

        BigDecimal bonusesAmount = welcomeBonusesTransferProgram.getSum();
        LocalDate dateEnd = welcomeBonusesTransferProgram.getDateEnd();
        List<NotificationStage> stages = welcomeBonusesTransferProgram.getStages();
        String[] idempotencyKey = new String[1];
        User[] user = new User[1];

        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    Optional<User> userOpt = userRepository.findById(userId);
                                    if (userOpt.isPresent())  {
                                        user[0] = userOpt.get();

                                        //Начисление можно выполнить только тем кто ранее не участвовал в программе лояльности
                                        if (Boolean.TRUE.equals(user[0].getIsLoyaltyProgramAccepted())) {
                                            log.warn(errPartText + "User already accepted old bonuses program.", userId);
                                            return null;
                                        }

                                        return self.getOrCreateUserBonusesAccount(userId);
                                    }
                                    return null;
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    if (userBonusesAccount == null) {
                                        return null;
                                    }
                                    idempotencyKey[0] = generateIdempotencyKeyWithAccountIdAndString(
                                            userBonusesAccount.getBonusesAccountId(),
                                            welcomeBonusesTransferProgram.getProgramName()
                                    );

                                    return controller.transferBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(
                                                    null,
                                                    bonusesAmount,
                                                    ZERO,
                                                    bonusesAmount
                                            ),
                                            null,
                                            dateEnd,
                                            "",
                                            welcomeBonusesTransferProgram.getProgramName(),
                                            stages,
                                            idempotencyKey[0],
                                            false
                                    );
                                },
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    log.warn("Unexpected error.", ex);

                                    if (checkIfExceptionPreventsToResendTransactionLater(ex)) {
                                        throw (OskellyException) ex.getCause();
                                    }

                                    orderBonusesTransactionRepository.save(
                                            convertErrorToOrderBonusesTransaction(
                                                    ex,
                                                    TransactionDTO.TrnTypeEnum.TRANSFER,
                                                    userId,
                                                    bonusesAmount,
                                                    ZERO,
                                                    null,
                                                    "",
                                                    welcomeBonusesTransferProgram.getProgramName(),
                                                    null,
                                                    dateEnd,
                                                    new OrderBonusesTransactionParams(stages, true, false),
                                                    idempotencyKey[0]
                                            )
                                    );
                                    return null;
                                }
                        )
                        //сама транзакция по начислению welcome бонусов при удачном выполнении не сохраняется в БД
                        .thenApplyAsync(
                                trn -> {
                                    if (trn !=null && user[0] != null) {
                                        createWelcomeNotification(user[0], bonusesAmount);
                                    }
                                    return null;
                                }
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS)
        );
    }

    @Override
    public void transferBonusesBirthdayToAllPossibleUsers() {
        if (!birthdayBonusesTransferProgram.isEnabled()) {
            log.warn("Birthday bonuses are not transferred by schedule. Birthday bonuses program is Disabled.");
            return;
        }

        LocalDate now = LocalDate.now();
        ZonedDateTime nowZ = ZonedDateTime.now();

        List<String> birthdays = getBirthdays(now, birthdayBonusesTransferProgram.getDaysBeforeBirthday());

        //TODO добавить внутрь findUserIdsToTransferBirthdayBonuses проверку принятия программы лояльности V2
        List<Long> userIds = userLoyaltyExtendedInfoRepository.findUserIdsToTransferBirthdayBonuses(
                birthdays, nowZ.minusDays(birthdayBonusesTransferProgram.getCheckPeriodInDays())
        );

        transferBonusesBirthday(userIds);
    }
    
    private List<String> getBirthdays(LocalDate date, int range) {
        return IntStream
                .rangeClosed(0, range)
                .mapToObj(i -> {
                    LocalDate d = date.plusDays(i);
                    String dMonthStr = d.getMonthValue() < 10 ? "0" + d.getMonthValue() : "" + d.getMonthValue();
                    String dDayStr = d.getDayOfMonth() < 10 ? "0" + d.getDayOfMonth() : "" + d.getDayOfMonth();
                    return dMonthStr + dDayStr;
                })
                .collect(Collectors.toList());
    }

    @Override
    public void transferBonusesBirthday(List<Long> userIds) {
        if (!birthdayBonusesTransferProgram.isEnabled()) {
            log.warn("Birthday bonuses are not transferred to users: {}. Birthday bonuses program is Disabled.", userIds);
            return;
        }

        //TODO определить нужную сумму по списку пользователей
        BigDecimal bonusesAmount = new BigDecimal(1000);
        //определить нужную сумму по списку пользователей

        for (Long userId: userIds) {
            try {
                transferBonusesBirthdayInternal(userId, bonusesAmount);
            } catch (Exception e) {
                log.error("Error transferring birthday bonuses to user: {}", userId, e);
            }
        }
    }

    private void transferBonusesBirthdayInternal(Long userId, BigDecimal bonusesAmount) {
        String errPartText = "Birthday bonuses are not transferred to user: {}. ";

        if (!birthdayBonusesTransferProgram.isEnabled()) {
            log.warn(errPartText + "Birthday bonuses program is Disabled.", userId);
            return;
        }

        List<NotificationStage>[] stages = new List[1];
        String[] idempotencyKey = new String[1];
        User[] user = new User[1];
        LocalDate[] dateEnd = new LocalDate[1];

        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    Optional<User> userOpt = userRepository.findById(userId);
                                    if (userOpt.isPresent())  {
                                        user[0] = userOpt.get();

                                        //TODO !!!!!!Проверка возможности начисления конкретному пользователю
                                        //Это дубль проверки в sql-запросе
                                        //!!!!!!Проверка возможности начисления конкретному пользователю

                                        return self.getOrCreateUserBonusesAccount(userId);
                                    }
                                    return null;
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    if (userBonusesAccount == null || user[0] == null) {
                                        log.warn(errPartText + "User not found.", userId);
                                        return null;
                                    }

                                    if (user[0].getBirthDate() == null) {
                                        log.warn(errPartText + "User doesn't have birth date.", userId);
                                        return null;
                                    }

                                    idempotencyKey[0] = generateIdempotencyKeyWithAccountIdAndString(
                                            userBonusesAccount.getBonusesAccountId(),
                                            birthdayBonusesTransferProgram.getProgramName()
                                                    + "_" + user[0].getBirthDate().getYear()
                                    );

                                    LocalDate birthday = user[0].getBirthDate().toLocalDate();
                                    dateEnd[0] = birthday.plusDays(birthdayBonusesTransferProgram.getDaysAfterBirthday());
                                    stages[0] = birthdayBonusesTransferProgram.getStages(birthday);

                                    return controller.transferBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(
                                                    null,
                                                    bonusesAmount,
                                                    ZERO,
                                                    bonusesAmount
                                            ),
                                            null,
                                            dateEnd[0],
                                            "",
                                            birthdayBonusesTransferProgram.getProgramName(),
                                            stages[0],
                                            idempotencyKey[0],
                                            true
                                    );
                                },
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    log.warn("Unexpected error.", ex);

                                    if (checkIfExceptionPreventsToResendTransactionLater(ex)) {
                                        throw (OskellyException) ex.getCause();
                                    }

                                    orderBonusesTransactionRepository.save(
                                            convertErrorToOrderBonusesTransaction(
                                                    ex,
                                                    TransactionDTO.TrnTypeEnum.TRANSFER,
                                                    userId,
                                                    bonusesAmount,
                                                    ZERO,
                                                    null,
                                                    "",
                                                    birthdayBonusesTransferProgram.getProgramName(),
                                                    null,
                                                    dateEnd[0],
                                                    new OrderBonusesTransactionParams(stages[0], false, true),
                                                    idempotencyKey[0]
                                            )
                                    );
                                    return null;
                                }
                        )
                        //сама транзакция по начислению бонусов на ДР при удачном выполнении не сохраняется в БД,
                        //но сохраняется время начисления бонусов пользователю
                        .thenApplyAsync(
                                trn -> {
                                    if (trn !=null && user[0] != null) {
                                        UserLoyaltyExtendedInfo n = userLoyaltyExtendedInfoRepository.findById(user[0].getId()).orElse(new UserLoyaltyExtendedInfo());
                                        n.setUserId(user[0].getId());
                                        n.setBirthdayBonusesTransferredAt(ZonedDateTime.now());
                                        userLoyaltyExtendedInfoRepository.save(n);
                                    }
                                    return null;
                                }
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS)
        );
    }

    @Override
    public OrderBonusesTransaction transferBonusesAfterError(Long resendId) {
        OrderBonusesTransaction[] existedTrn = new OrderBonusesTransaction[1];
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    Optional<OrderBonusesTransaction> existedTrnOpt = orderBonusesTransactionRepository.findById(resendId);
                                    if (existedTrnOpt.isPresent()) {
                                        existedTrn[0] = existedTrnOpt.get();
                                        return self.getOrCreateUserBonusesAccount(existedTrn[0].getUserId());
                                    } else {
                                        throw new BonusesTransactionNotFoundExeption("Bonuses transaction with id: " + resendId + " not found");
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    BigDecimal bonuses1 = getBigDecimal(existedTrn[0].getBonusAmount());
                                    BigDecimal money1 = getBigDecimal(existedTrn[0].getMoneyAmount());

                                    return controller.transferBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(existedTrn[0].getOrderId(), bonuses1, money1, bonuses1.add(money1)),
                                            existedTrn[0].getAuthorId(),
                                            existedTrn[0].getDateEnd(),
                                            existedTrn[0].getDescription(),
                                            existedTrn[0].getReason(),
                                            existedTrn[0].getParams() == null
                                                    ? defaultBonusesTransferProgram.getStagesByDateStartAndDuration()
                                                    : existedTrn[0].getParams().getNotificationStages(),
                                            existedTrn[0].getIdempotencyKey(),
                                            existedTrn[0].getParams() == null ? null : existedTrn[0].getParams().getIsSilentMode()
                                    );
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(existedTrn[0].getUserId(), trn),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    if (ex.getCause() instanceof BonusesTransactionAlreadyExistsExeption) {
                                        return ((BonusesTransactionAlreadyExistsExeption) ex.getCause()).getOrderBonusesTransaction();
                                    }

                                    //Если после успешной переотправки, со стороны бонусного сервиса прилетело бизнес-исключение,
                                    //то нет смысла дальше делать переотправки
                                    if (checkIfExceptionPreventsToResendTransactionLater(ex)) {
                                        existedTrn[0].setAttempts(processingMaxAttempts);
                                    }

                                    log.warn("Unexpected error.", ex.getCause());
                                    return convertErrorToOrderBonusesTransaction(
                                            ex,
                                            TransactionDTO.TrnTypeEnum.TRANSFER,
                                            existedTrn[0].getUserId(),
                                            existedTrn[0].getBonusAmount(),
                                            existedTrn[0].getMoneyAmount(),
                                            existedTrn[0].getOrderId(),
                                            existedTrn[0].getDescription(),
                                            existedTrn[0].getReason(),
                                            existedTrn[0].getAuthorId(),
                                            existedTrn[0].getDateEnd(),
                                            existedTrn[0].getParams(),
                                            existedTrn[0].getIdempotencyKey()
                                    );
                                }
                        )
                        .thenApplyAsync(
                                (trn) -> {
                                    if (trn.getTrnState() == null) {
                                        //если вдруг текущая транзакция ошибочная и ранее уже была ошибочная транзакция, то нужно увеличить счетчик
                                        existedTrn[0].setAttempts(existedTrn[0].getAttempts() + 1);
                                        existedTrn[0].setErrorText(trn.getErrorText());
                                        return orderBonusesTransactionRepository.save(existedTrn[0]);
                                    } else {
                                        //если ранее уже была ошибочная транзакция, но текущая транзакция нормальная, то нужно сделать апдейт существующей транзакции
                                        trn.setId(existedTrn[0].getId());
                                        //и если транзакция была welcome, то отправить нотификацию
                                        if (existedTrn[0].getParams() != null && existedTrn[0].getParams().getIsWelcome()) {
                                            Optional<User> userOpt = userRepository.findById(existedTrn[0].getUserId());
                                            if (userOpt.isPresent())  {
                                                createWelcomeNotification(userOpt.get(), existedTrn[0].getBonusAmount());
                                            } else {
                                                log.warn("User with id: {} not found. Error creating welcome notification.", trn.getUserId());
                                            }
                                        }
                                        return orderBonusesTransactionRepository.save(trn);
                                    }
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    private OrderBonusesTransaction transferBonusesOrder(User user, BigDecimal bonusesAmount, Long orderId) {
        return transferBonusesOrderInternal(user, bonusesAmount, null, "", "", null, orderId, null);
    }

    @Override
    public OrderBonusesTransaction transferBonusesOrder(
            User user, BigDecimal bonusesAmount, BigDecimal moneyAmount, String description, String reason, Long authorId, Long orderId, LocalDate dateEnd
    ) {
        return transferBonusesOrderInternal(user, bonusesAmount, moneyAmount, description, reason, authorId, orderId, dateEnd);
    }

    private OrderBonusesTransaction transferBonusesOrderInternal(
            User user, BigDecimal bonusesAmount, BigDecimal moneyAmount, String description, String reason, Long authorId, Long orderId, LocalDate dateEnd
    ) {
        if (boutiqueBuyersIds.contains(user.getId())) {
            log.debug("User with id: {} is boutique user. Ignore bonuses transfer.", user.getId());
            return null;
        }

        if ((bonusesAmount == null ? ZERO : bonusesAmount).compareTo(ZERO) == 0 && (moneyAmount == null ? ZERO : moneyAmount).compareTo(ZERO) == 0) {
            log.info("Ignoring bonuses transferring for user: {}. Both bonuses and money are null or ZERO.", user.getId());
            return null;
        }

        List<NotificationStage> stages = defaultBonusesTransferProgram.getStagesByDateStartAndDuration();
        String[] idempotencyKey = new String[1];

        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    List<OrderBonusesTransaction> existedTrnList = orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, user.getId(), TransactionDTO.TrnTypeEnum.TRANSFER);
                                    if (existedTrnList.isEmpty()) {
                                        return self.getOrCreateUserBonusesAccount(user.getId());
                                    } else {
                                        throw new BonusesTransactionAlreadyExistsExeption(existedTrnList.get(0));
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    BigDecimal bonuses1 = (bonusesAmount == null) ? ZERO : bonusesAmount;
                                    BigDecimal money1 = (moneyAmount == null) ? ZERO : moneyAmount;
                                    idempotencyKey[0] = generateIdempotencyKeyTransfer(userBonusesAccount.getBonusesAccountId(), orderId);

                                    return controller.transferBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(orderId, bonuses1, money1, bonuses1.add(money1)),
                                            authorId,
                                            dateEnd,
                                            description,
                                            reason,
                                            stages,
                                            idempotencyKey[0],
                                            false
                                    );
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(user.getId(), trn),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    if (ex.getCause() instanceof BonusesTransactionAlreadyExistsExeption) {
                                        OrderBonusesTransaction trn = ((BonusesTransactionAlreadyExistsExeption) ex.getCause()).getOrderBonusesTransaction();
                                        log.info("Bonuses transfer transaction with id: " + trn.getBonusTransactionId() + " already exists");
                                        return trn;
                                    }
                                    log.warn("Unexpected error.", ex.getCause());

                                    if (checkIfExceptionPreventsToResendTransactionLater(ex)) {
                                        throw (OskellyException) ex.getCause();
                                    }

                                    return convertErrorToOrderBonusesTransaction(
                                            ex, TransactionDTO.TrnTypeEnum.TRANSFER, user.getId(), bonusesAmount, moneyAmount, orderId, description, reason, authorId, dateEnd, new OrderBonusesTransactionParams(stages, false, false), idempotencyKey[0]
                                    );
                                }
                        )
                        .thenApplyAsync(
                                (trn) -> {
                                    //Если идентификатор не null, то это уже успешно выполненная ранее транзакция
                                    if (trn.getId() != null) {
                                        return trn;
                                    }

                                    //сохранение обычного результата
                                    return orderBonusesTransactionRepository.save(trn);
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public OrderBonusesTransaction withdrawBonusesHold(
            Long userId,
            Boolean isLoyaltyProgramAccepted,
            BigDecimal bonusesAmount,
            BigDecimal moneyAmount,
            String description,
            String reason,
            Long authorId,
            Long orderId,
            OrderBonusesTransactionParams params //признак того, что вызов идет из BonusesOfflineService и здесь содержится перечень товаров
    ) {
        if (!TRUE.equals(isLoyaltyProgramAccepted)) {
            logUserNotAcceptedLoyalityProgram(userId);
            return null;
        }

        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnTypeAndOrderIdIsNotNull(
                                        orderId, userId, TransactionDTO.TrnTypeEnum.WITHDRAW
                                ),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                existedTrnList -> {
                                    if (existedTrnList.isEmpty()) {
                                        return self.getOrCreateUserBonusesAccount(userId);
                                    } else {
                                        throw new BonusesTransactionAlreadyExistsExeption(existedTrnList.get(0));
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    BigDecimal bonuses = (bonusesAmount != null && bonusesAmount.signum() == 0) ? null : bonusesAmount;
                                    BigDecimal money = (moneyAmount != null && moneyAmount.signum() == 0) ? null : moneyAmount;
                                    return controller.withdrawBonusesHold(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(orderId, bonuses, money, null),
                                            authorId,
                                            description,
                                            reason,
                                            params == null
                                                    ? generateIdempotencyKeyWithAccountIdAndRandom(userBonusesAccount.getBonusesAccountId())
                                                    : generateIdempotencyKeyWithdraw(userBonusesAccount.getBonusesAccountId(), orderId)
                                    );
                                },
                                bonusesExecutor)
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(userId, trn),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    if (ex.getCause() instanceof BonusesTransactionAlreadyExistsExeption) {
                                        OrderBonusesTransaction trn = ((BonusesTransactionAlreadyExistsExeption) ex.getCause()).getOrderBonusesTransaction();
                                        log.info("Bonuses transaction with id: " + trn.getBonusTransactionId() + " in HOLD state already exists");
                                        return trn;
                                    }
                                    exceptionallyTimeoutTemplate(ex);
                                    return null;
                                }
                        )
                        .thenApplyAsync(
                                (trn) -> {
                                    if (trn.getId() != null) {
                                        return trn;
                                    }
                                    trn.setParams(params);
                                    return orderBonusesTransactionRepository.save(trn);
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public OrderBonusesTransaction splitBonuses(Long bonusTransactionId, List<BonusesOfflineSplitItem> productItemsAndOrders) {
        OrderBonusesTransaction[] transaction = new OrderBonusesTransaction[1];
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    Optional<OrderBonusesTransaction> trnOpt = orderBonusesTransactionRepository.findByBonusTransactionId(bonusTransactionId);
                                    if (!trnOpt.isPresent()) {
                                        throw new BonusesTransactionNotFoundExeption("Bonuses transaction with id: " + bonusTransactionId + " not found");
                                    }
                                    transaction[0] = trnOpt.get();
                                    if (TransactionDTO.StateEnum.CANCELED.equals(trnOpt.get().getTrnState())) {
                                        throw new BonusesTransactionAlreadyExistsExeption(transaction[0]);
                                    }
                                    return trnOpt.get();
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                trn -> self.getOrCreateUserBonusesAccount(trn.getUserId()),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                account -> {
                                    Map<Long, List<Long>> productItemsAndOrdersMap = productItemsAndOrders
                                            .stream()
                                            .collect(Collectors.toMap(
                                                    BonusesOfflineSplitItem::getProductItemId,
                                                    splitItem -> new ArrayList<>(splitItem.getOrderIds()))
                                            );
                                    //orders содержит все позиции чека, даже те, на которые списсание бонусов не производилось
                                    List<BonusesControllerAPI.BonusesOrderAmountSplit> orders = transaction[0].getParams().getOfflineItems()
                                            .stream()
                                            .map(productItem -> {
                                                List<Long> productItemOrders = productItemsAndOrdersMap.get(productItem.getProductItemId());
                                                if (productItemOrders == null || productItemOrders.isEmpty()) {
                                                    throw new BonusesTransactionParamsException(
                                                            "Error combining transaction with id: " + transaction[0].getBonusTransactionId() + " with params: " + productItemsAndOrders
                                                    );
                                                }
                                                Long orderId = productItemOrders.remove(productItemOrders.size() - 1);
                                                //В бонусный сервис нужно передавать null значения, а не 0
                                                return new BonusesControllerAPI.BonusesOrderAmountSplit(
                                                        new BonusesControllerAPI.BonusesOrderAmount(
                                                                orderId,
                                                                productItem.getBonuses() == null || ZERO.compareTo(productItem.getBonuses()) == 0
                                                                        ? null
                                                                        : productItem.getBonuses(),
                                                                productItem.getMoney() == null || ZERO.compareTo(productItem.getMoney()) == 0
                                                                        ? null
                                                                        : productItem.getMoney(),
                                                                null
                                                        ),
                                                        generateIdempotencyKeyWithdraw(account.getBonusesAccountId(), orderId)
                                                );
                                            })
                                            .collect(Collectors.toList());
                                    transaction[0].getParams().setOrders(
                                            orders.stream()
                                                    .map(order ->
                                                            //Вместо null нужно вернуть 0 для дальнейшего сохранения в заказе
                                                            new OrderBonusesTransactionParamsOrderId(
                                                                    order.getAmount().getOrderId(),
                                                                    order.getAmount().getBonuses() == null ? ZERO : order.getAmount().getBonuses(),
                                                                    order.getAmount().getMoney() == null ? ZERO : order.getAmount().getMoney()
                                                            )
                                                    )
                                                    .collect(Collectors.toList())
                                    );
                                    return controller.withdrawBonusesSplit(
                                            account.getBonusesAccountId(),
                                            transaction[0].getBonusTransactionId(),
                                            //тут нужно исключить позиции, на которые бонусы не списывались
                                            orders.stream().filter(o -> o.getAmount().getBonuses() != null || o.getAmount().getMoney() != null).collect(Collectors.toList())
                                    );
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                newTransactionsResponseBody -> {
                                    List<TransactionWithBalanceDTO> newTransactionsAfterSplit = newTransactionsResponseBody.getData();
                                    if (newTransactionsAfterSplit == null) {
                                        throw new OskellyException(
                                                "Unknown exception splitting transaction " + bonusTransactionId + "for user " + transaction[0].getUserId() + ". Response from bonuses service is null."
                                        );
                                    }
                                    return newTransactionsAfterSplit
                                            .stream()
                                            .map(trn -> convertTransactionDTOToOrderBonusesTransaction(transaction[0].getUserId(), trn.getTransaction()))
                                            .collect(Collectors.toList());
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                newTransactions -> {
                                    callInTransaction.runInNewTransaction(() -> {
                                        newTransactions.forEach(orderBonusesTransactionRepository::save);
                                        transaction[0].setTrnState(TransactionDTO.StateEnum.CANCELED);
                                        orderBonusesTransactionRepository.save(transaction[0]);
                                    });
                                    //возвращается транзакция, в которой сохранено распределение бонусов по заказам
                                    return transaction[0];
                                },
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    if (ex.getCause() instanceof BonusesTransactionAlreadyExistsExeption) {
                                        OrderBonusesTransaction trn = ((BonusesTransactionAlreadyExistsExeption) ex.getCause()).getOrderBonusesTransaction();
                                        log.info("Bonuses transaction with id: " + trn.getBonusTransactionId() + " already in CANCELED state");
                                        return trn;
                                    }
                                    exceptionallyTimeoutTemplate(ex);
                                    return null;
                                }
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public OrderBonusesTransaction withdrawBonusesCancel(Long userId, OrderBonusesTransaction transaction) {
        return withdrawBonusesCancelInternal(userId, transaction.getOrderId(), transaction);
    }

    @Override
    public OrderBonusesTransaction withdrawBonusesCancel(Long userId, Long orderId) {
        return withdrawBonusesCancelInternal(userId, orderId, null);
    }

    private OrderBonusesTransaction withdrawBonusesCancelInternal(Long userId, Long orderId, OrderBonusesTransaction transaction) {
        OrderBonusesTransaction[] localTransaction = new OrderBonusesTransaction[1];
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .runAsync(
                                () -> {
                                    if (transaction == null) {
                                        localTransaction[0] = getWithdrawTransactionByUserAndOrderInHoldState(userId, orderId);
                                    } else {
                                        localTransaction[0] = transaction;
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                (o) -> self.getOrCreateUserBonusesAccount(userId),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> controller.withdrawBonusesCancel(
                                        userBonusesAccount.getBonusesAccountId(),
                                        localTransaction[0].getBonusTransactionId()
                                ),
                                bonusesExecutor)
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(userId, trn),
                                bonusesExecutor
                        )
                        .exceptionally(this::exceptionallyTimeoutTemplate)
                        .thenApplyAsync(
                                (trn) -> {
                                    trn.setId(localTransaction[0].getId());
                                    trn.setParams(localTransaction[0].getParams());
                                    return orderBonusesTransactionRepository.save(trn);
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public OrderBonusesTransaction withdrawBonusesCommit(Long userId, Long orderId) {
        OrderBonusesTransaction[] localTransaction = new OrderBonusesTransaction[1];
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> self.getOrCreateUserBonusesAccount(userId),
                                bonusesExecutor
                        )
                        .thenCombineAsync(
                                CompletableFuture
                                        .supplyAsync(
                                                () -> {
                                                    OrderBonusesTransaction trn = getWithdrawTransactionByUserAndOrderInHoldState(userId, orderId);
                                                    localTransaction[0] = trn;
                                                    return trn;
                                                },
                                                bonusesExecutor
                                        )
                                        .exceptionally(ex -> {
                                            log.warn("Unexpected error", ex);
                                            throw new OskellyException(ex);
                                        }),
                                (userBonusesAccount, transaction) -> controller.withdrawBonusesCommit(
                                                userBonusesAccount.getBonusesAccountId(),
                                                transaction.getBonusTransactionId(),
                                                orderId
                                ),
                                bonusesExecutor)
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(userId, trn),
                                bonusesExecutor
                        )
                        .exceptionally(this::exceptionallyTimeoutTemplate)
                        .thenApplyAsync(
                                (trn) -> {
                                    trn.setId(localTransaction[0].getId());
                                    return orderBonusesTransactionRepository.save(trn);
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public void withdrawBonusesManual(
            Long userId, BigDecimal bonusesAmount, BigDecimal moneyAmount, String description, String reason
    ) {
        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                securityService::getCurrentAuthorizedUserId,
                                bonusesExecutor
                        )
                        .thenCombineAsync(
                                CompletableFuture
                                        .supplyAsync(
                                                () -> {
                                                    Optional<User> userOpt = userRepository.findById(userId);
                                                    if (userOpt.isPresent())  {
                                                        return self.getOrCreateUserBonusesAccount(userId);
                                                    }
                                                    return null;
                                                },
                                                bonusesExecutor
                                        )
                                        .exceptionally(
                                                ex -> {
                                                    log.warn("Unexpected error", ex);
                                                    throw new OskellyException(ex);
                                                }
                                        ),
                                (authorId, userBonusesAccount) -> {
                                    if (userBonusesAccount == null) {
                                        return null;
                                    }
                                    BigDecimal bonuses = (bonusesAmount == null) ? ZERO : bonusesAmount;
                                    BigDecimal money = (moneyAmount == null) ? ZERO : moneyAmount;
                                    return controller.withdrawBonusesManual(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(null, bonuses, money, null),
                                            authorId,
                                            description,
                                            reason,
                                            generateIdempotencyKeyWithAccountIdAndRandom(userBonusesAccount.getBonusesAccountId())
                                    );
                                },
                                bonusesExecutor
                        )
                        .exceptionally(this::exceptionallyTimeoutTemplate)
                        .get(socketTimeout.getSeconds() * 2, SECONDS)
        );
    }

    @Override
    public OrderBonusesTransaction returnBonusesOnlyIfInCommittedState(User user, Long orderId) {
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(
                                        orderId, user.getId(), TransactionDTO.TrnTypeEnum.WITHDRAW
                                )
                        )
                        .thenApplyAsync(
                                existedTrnList -> {
                                    if (existedTrnList.isEmpty()) {
                                        throw new BonusesTransactionNotFoundExeption("Bonuses transaction for orderId:" + orderId + " and userId: " + user.getId() + " not found");
                                    }
                                    if (!TransactionDTO.StateEnum.COMMITTED.equals(existedTrnList.get(0).getTrnState())) {
                                        return null;
                                    }
                                    return existedTrnList.get(0);
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                withdrawTransaction -> {
                                    if (withdrawTransaction == null) {
                                        log.warn("Skip returning bonuses for orderId: {} because state of the withdraw transaction is not COMMITTED", orderId);
                                        return null;
                                    }
                                    return returnBonusesInternal(user, withdrawTransaction.getBonusAmount(), withdrawTransaction.getMoneyAmount(), orderId);
                                },
                                bonusesExecutor)
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public OrderBonusesTransaction returnBonusesAfterError(Long resendId) {
        OrderBonusesTransaction[] existedTrn = new OrderBonusesTransaction[1];
        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    Optional<OrderBonusesTransaction> existedTrnOpt = orderBonusesTransactionRepository.findById(resendId);
                                    if (existedTrnOpt.isPresent()) {
                                        existedTrn[0] = existedTrnOpt.get();
                                        return self.getOrCreateUserBonusesAccount(existedTrn[0].getUserId());
                                    } else {
                                        throw new BonusesTransactionNotFoundExeption("Bonuses transaction with id: " + resendId + " not found");
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    BigDecimal bonuses1 = getBigDecimal(existedTrn[0].getBonusAmount());
                                    BigDecimal money1 = getBigDecimal(existedTrn[0].getMoneyAmount());

                                    return controller.returnBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(
                                                    existedTrn[0].getOrderId(), bonuses1, money1, bonuses1.add(money1)
                                            ),
                                            existedTrn[0].getIdempotencyKey()
                                    );
                                },
                                bonusesExecutor)
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(null, trn),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    if (ex.getCause() instanceof BonusesTransactionAlreadyExistsExeption) {
                                        return ((BonusesTransactionAlreadyExistsExeption) ex.getCause()).getOrderBonusesTransaction();
                                    }

                                    log.warn("Unexpected error.", ex);

                                    //Если после успешной переотправки, со стороны бонусного сервиса прилетело бизнес-исключение,
                                    //то нет смысла дальше делать переотправки
                                    if (checkIfExceptionPreventsToResendTransactionLater(ex)) {
                                        existedTrn[0].setAttempts(processingMaxAttempts);
                                    }

                                    if (existedTrn[0] != null) {
                                        return convertErrorToOrderBonusesTransaction(
                                                ex,
                                                TransactionDTO.TrnTypeEnum.RETURN,
                                                existedTrn[0].getUserId(),
                                                existedTrn[0].getBonusAmount(),
                                                existedTrn[0].getMoneyAmount(),
                                                existedTrn[0].getOrderId(),
                                                existedTrn[0].getDescription(),
                                                existedTrn[0].getReason(),
                                                existedTrn[0].getAuthorId(),
                                                existedTrn[0].getDateEnd(),
                                                existedTrn[0].getParams(),
                                                existedTrn[0].getIdempotencyKey()
                                        );
                                    }
                                    exceptionallyTimeoutTemplate(ex);
                                    return null;
                                }
                        )
                        .thenApplyAsync(
                                (trn) -> {
                                    if (trn.getTrnState() == null) {
                                        //если вдруг текущая транзакция ошибочная и ранее уже была ошибочная транзакция, то нужно увеличить счетчик
                                        existedTrn[0].setAttempts(existedTrn[0].getAttempts() + 1);
                                        existedTrn[0].setErrorText(trn.getErrorText());
                                        return orderBonusesTransactionRepository.save(existedTrn[0]);
                                    } else {
                                        //если ранее уже была ошибочная транзакция, но текущая транзакция нормальная, то нужно сделать апдейт существующей транзакции
                                        trn.setId(existedTrn[0].getId());
                                        trn.setUserId(existedTrn[0].getUserId());
                                        return orderBonusesTransactionRepository.save(trn);
                                    }
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public OrderBonusesTransaction returnBonuses(User user, BigDecimal bonusesAmount, BigDecimal moneyAmount, Long orderId) {
        return returnBonusesInternal(user, bonusesAmount, moneyAmount, orderId);
    }

    private OrderBonusesTransaction returnBonusesInternal(User user, BigDecimal bonusesAmount, BigDecimal moneyAmount, Long orderId) {
        if (!TRUE.equals(user.getIsLoyaltyProgramAccepted())) {
            logUserNotAcceptedLoyalityProgram(user.getId());
            return null;
        }

        BigDecimal bonuses = (bonusesAmount == null) ? ZERO : bonusesAmount;
        BigDecimal money = (moneyAmount == null) ? ZERO : moneyAmount;
        String[] idempotencyKey = new String[1];

        return callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> {
                                    List<OrderBonusesTransaction> existedTrnList = orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(orderId, user.getId(), TransactionDTO.TrnTypeEnum.RETURN);
                                    if (existedTrnList.isEmpty()) {
                                        return self.getOrCreateUserBonusesAccount(user.getId());
                                    } else {
                                        OrderBonusesTransaction existedTrnWithSameAmount = existedTrnList
                                                .stream()
                                                .filter(trn ->
                                                                bonuses.compareTo(getBigDecimal(trn.getBonusAmount())) == 0
                                                                        && money.compareTo(getBigDecimal(trn.getMoneyAmount())) == 0
                                                )
                                                .findAny()
                                                .orElse(null);
                                        if (existedTrnWithSameAmount == null) {
                                            return self.getOrCreateUserBonusesAccount(user.getId());
                                        }
                                        throw new BonusesTransactionAlreadyExistsExeption(existedTrnList.get(0));
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    BigDecimal bonuses1 = (bonusesAmount == null) ? ZERO : bonusesAmount;
                                    BigDecimal money1 = (moneyAmount == null) ? ZERO : moneyAmount;

                                    idempotencyKey[0] = generateIdempotencyKeyWithAccountIdAndOrderIdAndSums(
                                            userBonusesAccount.getBonusesAccountId(), orderId, bonuses1, money1
                                    );

                                    return controller.returnBonuses(
                                            userBonusesAccount.getBonusesAccountId(),
                                            new BonusesControllerAPI.BonusesOrderAmount(
                                                    orderId,
                                                    bonuses1,
                                                    money1,
                                                    bonuses1.add(money1)
                                            ),
                                            idempotencyKey[0]
                                    );
                                },
                                bonusesExecutor)
                        .thenApplyAsync(
                                trn -> convertTransactionDTOToOrderBonusesTransaction(user.getId(), trn),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    if (ex.getCause() instanceof BonusesTransactionAlreadyExistsExeption) {
                                        OrderBonusesTransaction trn = ((BonusesTransactionAlreadyExistsExeption) ex.getCause()).getOrderBonusesTransaction();
                                        log.info("Bonuses return transaction with id: " + trn.getBonusTransactionId() + " already exists");
                                        return trn;
                                    }
                                    log.warn("Unexpected error.", ex);

                                    if (checkIfExceptionPreventsToResendTransactionLater(ex)) {
                                        throw (OskellyException) ex.getCause();
                                    }

                                    return convertErrorToOrderBonusesTransaction(
                                            ex, TransactionDTO.TrnTypeEnum.RETURN, user.getId(), bonusesAmount, moneyAmount, orderId, "", "", null, null, null, idempotencyKey[0]
                                    );
                                }
                        )
                        .thenApplyAsync(
                                (trn) -> {
                                    //Если идентификатор не null, то это уже успешно выполненная ранее транзакция
                                    if (trn.getId() != null) {
                                        return trn;
                                    }

                                    //сохранение обычного результата
                                    return orderBonusesTransactionRepository.save(trn);
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS));
    }

    @Override
    public BonusesBalanceDTO getBalanceForCurrentUser() {
        return callSilentlyWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                securityService::getCurrentAuthorizedUserId,
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                this::getBalance,
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    log.warn("Unexpected error.", ex);
                                    throw new OskellyException(ex);
                                }
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS),
                BonusesBalanceDTO.createZeroBalance()
        );
    }

    @Override
    public BonusesBalanceDTO getBalance(Long userId) {
        if (userId == null) {
            return BonusesBalanceDTO.createZeroBalance();
        }
        return callSilentlyWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> userBonusesAccountRepository.findByUserId(userId),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccountOptional ->
                                        userBonusesAccountOptional.map(userBonusesAccount ->
                                                        controller.getAccountCurrentBonusesBalance(userBonusesAccount.getBonusesAccountId())
                                                )
                                                .map(this::convertBonusesBalance)
                                                .orElseGet(BonusesBalanceDTO::createZeroBalance),
                                bonusesExecutor)
                        .exceptionally(this::exceptionallyTimeoutTemplate)
                        .get(socketTimeout.getSeconds() * 2, SECONDS),
                BonusesBalanceDTO.createZeroBalance()
        );
    }

    @Override
    public List<BonusesBurningScheduleItemDTO> getBurningScheduleForCurrentUserInUserMode() {
        return callSilentlyWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                securityService::getCurrentAuthorizedUserId,
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userId -> getBurningScheduleForUser(userId, false),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    log.warn("Unexpected error.", ex);
                                    throw new OskellyException(ex);
                                }
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS),
                Collections.emptyList()
        );
    }

    @Override
    public List<BonusesBurningScheduleItemDTO> getBurningScheduleForUser(Long userId, boolean adminMode) {
        return callSilentlyWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> Optional.ofNullable(userId)
                                              .flatMap(userBonusesAccountRepository::findByUserId)
                                              .orElse(null),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    if (userBonusesAccount == null
                                            || userBonusesAccount.getBonusesAccountId() == null) {
                                        return null;
                                    } else {
                                        return controller.getAccountBucketsWithTransactions(
                                                userBonusesAccount.getBonusesAccountId(),
                                                BucketDTO.TypeEnum.BONUS.getValue()
                                        );
                                    }
                                },
                                bonusesExecutor)
                        .thenApplyAsync(
                                s -> convertBucketsToBurningSchedule(s, adminMode),
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS),
                Collections.emptyList()
        );
    }

    @Override
    public Page<BonusesTransactionDTO> getTransactionsForCurrentUserInUserMode(Integer pageNumber, Integer pageSize) {
        return callSilentlyWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                securityService::getCurrentAuthorizedUserId,
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                (userId) -> getTransactionsForUser(userId, null, pageNumber, pageSize, false),
                                bonusesExecutor
                        )
                        .exceptionally(
                                ex -> {
                                    log.warn("Unexpected error.", ex);
                                    throw new OskellyException(ex);
                                }
                        )
                        .get(socketTimeout.getSeconds() * 2, SECONDS),
                Page.emptyPage()
        );
    }

    @Override
    public Page<BonusesTransactionDTO> getTransactionsForUser(Long userId, BonusesType type, Integer pageNumber, Integer pageSize, boolean adminMode) {
        return callSilentlyWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> Optional.ofNullable(userId)
                                              .flatMap(userBonusesAccountRepository::findByUserId)
                                              .orElse(null),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                userBonusesAccount -> {
                                    if (userBonusesAccount == null || userBonusesAccount.getBonusesAccountId() == null) {
                                        return null;
                                    } else {
                                        return controller.getTransactionsForAccount(
                                                userBonusesAccount.getBonusesAccountId(),
                                                pageNumber == null ? DEFAULT_PAGE_NUMBER : pageNumber,
                                                pageSize == null ? DEFAULT_PAGE_SIZE : pageSize
                                        );
                                    }
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                response -> {
                                    if (response == null || response.getData() == null) {
                                        return Page.<BonusesTransactionDTO>emptyPage();
                                    }

                                    Page<BonusesTransactionDTO> page = new Page<>();
                                    page.setTotalPages(response.getData().getTotalPages());
                                    page.setTotalAmount(response.getData().getTotalAmount());
                                    page.setItems(
                                            response.getData().getItems()
                                                    .stream()
                                                    .filter(t -> {
                                                        if (BonusesType.BONUS.equals(type)) {
                                                            return ZERO.compareTo(getBigDecimal(t.getBonusesAmount())) != 0;
                                                        }
                                                        if (BonusesType.MONEY.equals(type)) {
                                                            return ZERO.compareTo(getBigDecimal(t.getMoneyAmount())) != 0;
                                                        }
                                                        return true;
                                                    })
                                                    .map(t -> {
                                                        if (adminMode) {
                                                            return convertTransactionDTOToBonusesTransactionDTOForAdmin(t);
                                                        } else {
                                                            return convertTransactionDTOToBonusesTransactionDTOForUser(t);
                                                        }
                                                    })
                                                    .sorted((t1, t2) -> (-1) * t1.getTrnDate().compareTo(t2.getTrnDate()))
                                                    .collect(Collectors.toList())
                                    );

                                    return page;
                                },
                                bonusesExecutor
                        )
                        .exceptionally(this::exceptionallyTimeoutTemplate)
                        .get(socketTimeout.getSeconds() * 2, SECONDS),
                Page.emptyPage()
        );
    }

    private OrderBonusesTransaction getWithdrawTransactionByUserAndOrderInHoldState(Long userId, Long orderId) {
        List<OrderBonusesTransaction> trns = orderBonusesTransactionRepository.findByOrderIdAndUserIdAndTrnType(
                orderId, userId, TransactionDTO.TrnTypeEnum.WITHDRAW
        );
        if (trns.isEmpty()) {
            throw new BonusesTransactionNotFoundExeption(
                    "Bonuses transaction for orderId:" + orderId + " and userId: " + userId + " not found"
            );
        } else if (trns.size() != 1) {
            throw new OskellyException(
                    "There are " + trns.size() + " transactions for orderId:" + orderId + " and userId: " + userId
            );
        }
        if (!TransactionDTO.StateEnum.HOLD.equals(trns.get(0).getTrnState())) {
            throw new OskellyException(
                    "Wrong state " + trns.get(0).getTrnState() + " for transactionId: " + trns.get(0).getBonusTransactionId() + ", orderId:" + orderId + " and userId: " + userId + "."
            );
        }
        return trns.get(0);
    }

    private List<BonusesBurningScheduleItemDTO> convertBucketsToBurningSchedule(ResponseBodyListBucketDTO source, boolean adminMode) {
        if (source == null || source.getData() == null) {
            return new ArrayList<>();
        }

        return source.getData()
                .stream()
                //группировка бакетов по дате сгорания
                .collect(
                        Collectors.groupingBy(
                                BucketDTO::getDateEnd,
                                Collectors.toList())
                )
                .entrySet()
                .stream()
                //сортировка entrySet по дате группировки
                .sorted(Map.Entry.comparingByKey())
                //конвертация бакета и его тразакций в BonusesBurningScheduleItemDTO
                .map(bEnt -> new BonusesBurningScheduleItemDTO(
                        //подсчет общей суммы по всем транзакциям
                        bEnt.getValue().stream().map(BucketDTO::getAmount).reduce(ZERO, BigDecimal::add),
                        //дата сгорания
                        convertLocalDateToZonedDateTime(bEnt.getKey()),
                        //список транзакций, которые будут отображаться в UI
                        bEnt.getValue()
                                .stream()
                                //выделение из каждого бакета всех его транзакций
                                .flatMap(bucketDTO -> {
                                    if (bucketDTO.getTransactions() == null) {
                                        return Stream.empty();
                                    }
                                    return bucketDTO.getTransactions().stream();
                                })
                                //фильтр на всякий случай. По идее из бонусного сервиса только такие транзакции и должны вернуться.
                                .filter(t ->
                                        TransactionDTO.TrnTypeEnum.RETURN.equals(t.getTrnType())
                                                || TransactionDTO.TrnTypeEnum.TRANSFER.equals(t.getTrnType())
                                )
                                //группировка транзакций по их идентификатору и вычисление общей суммы
                                .collect(
                                       Collectors.groupingBy(
                                               TransactionDTO::getId,
                                               Collectors.toList()
                                       )
                                )
                                .entrySet()
                                .stream()
                                //агрегирование нескольких транзакций с общим идентификатором в одну
                                .map(tEnt -> {
                                    //Т.к. записи отличаются только суммой, то все нечисловые поля берем из первого элемента
                                    TransactionDTO src0 = tEnt.getValue().get(0);
                                    TransactionDTO agr = new TransactionDTO();
                                    agr.setId(tEnt.getKey());
                                    agr.setState(src0.getState());
                                    agr.setReason(src0.getReason());
                                    agr.setAccountId(src0.getAccountId());
                                    agr.setDescription(src0.getDescription());
                                    agr.setTrnType(src0.getTrnType());
                                    agr.setTrnDate(src0.getTrnDate());
                                    agr.setParentId(src0.getParentId());
                                    agr.setOrderId(src0.getOrderId());
                                    agr.setAuthorId(src0.getAuthorId());
                                    agr.setUpdateDate(src0.getUpdateDate());
                                    //Считаем числовые поля
                                    agr.setBonusesAmount(
                                            tEnt.getValue()
                                                    .stream()
                                                    .map(TransactionDTO::getBonusesAmount)
                                                    .reduce(ZERO, BigDecimal::add)
                                    );
                                    agr.setMoneyAmount(
                                            tEnt.getValue()
                                                    .stream()
                                                    .map(TransactionDTO::getMoneyAmount)
                                                    .reduce(ZERO, BigDecimal::add)
                                    );
                                    return agr;
                                })
                                //сортировка транзакций по дате
                                .sorted(Comparator.comparing(TransactionDTO::getTrnDate))
                                //маппировка вида транзакции из той что пришла от бонусного сервиса в тот, который нужен в UI
                                .map(t -> {
                                    if (adminMode) {
                                        return convertTransactionDTOToBonusesTransactionDTOForAdmin(t);
                                    } else {
                                        return convertTransactionDTOToBonusesTransactionDTOForUser(t);
                                    }
                                })
                                .collect(Collectors.toList())
                ))
                .collect(Collectors.toList());
    }

    private BonusesTransactionDTO convertTransactionDTOToBonusesTransactionDTOForAdmin(TransactionDTO source) {
        String typeStr = null;
        String description = source.getDescription();
        switch (source.getTrnType()) {
            case RETURN: {
                typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.return");
                break;
            }
            case TRANSFER: {
                if (source.getAuthorId() == null) {
                    typeStr = getTypeStrForNonManualTransfer(source);
                } else {
                    if (source.getReason() != null && !"".equals(source.getReason())) {
                        typeStr = source.getReason();
                    } else {
                        typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.income.other");
                    }
                }
                break;
            }
            case WITHDRAW: {
                if (source.getAuthorId() == null) {
                    typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.withdraw");
                } else {
                    if (source.getReason() != null && !"".equals(source.getReason())) {
                        typeStr = source.getReason();
                    } else {
                        typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.outcome.other");
                    }
                }
                break;
            }
            case EXPIRE: {
                typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.expire");
                description = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.expire.description");
                break;
            }
        }
        ZonedDateTime orderDate = getOrderDate(source.getOrderId());

        return new BonusesTransactionDTO(
                createBonusesAmountDTO(source.getBonusesAmount(), source.getMoneyAmount()),
                convertLocalDateToZonedDateTime(source.getTrnDate().toLocalDate()),
                source.getTrnType(),
                typeStr,
                source.getReason(),
                description,
                source.getOrderId(),
                orderDate
        );
    }

    private BonusesTransactionDTO convertTransactionDTOToBonusesTransactionDTOForUser(TransactionDTO source) {
        String typeStr = null;
        String description = "";
        switch (source.getTrnType()) {
            case RETURN: {
                typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.return");
                break;
            }
            case TRANSFER: {
                if (source.getAuthorId() == null) {
                    typeStr = getTypeStrForNonManualTransfer(source);
                } else {
                    typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.income.other");
                    if (source.getReason() == null) {
                        description = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.income.description");
                    } else {
                        description = source.getReason();
                    }
                }
                break;
            }
            case WITHDRAW: {
                if (source.getAuthorId() == null) {
                    typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.withdraw");
                } else {
                    typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.outcome.other");
                    if (source.getReason() == null) {
                        description = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.outcome.description");
                    } else {
                        description = source.getReason();
                    }
                }
                break;
            }
            case EXPIRE: {
                typeStr = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.expire");
                description = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.expire.description");
                break;
            }
        }
        ZonedDateTime orderDate = getOrderDate(source.getOrderId());

        return new BonusesTransactionDTO(
                createBonusesAmountDTO(source.getBonusesAmount(), source.getMoneyAmount()),
                convertLocalDateToZonedDateTime(source.getTrnDate().toLocalDate()),
                source.getTrnType(),
                typeStr,
                source.getReason(),
                description,
                source.getOrderId(),
                orderDate
        );
    }

    private String getTypeStrForNonManualTransfer(TransactionDTO source) {
        String result;

        if (source.getReason() != null && !"".equals(source.getReason())) {
            try {
                result = messageSourceAccessor.getMessage("bonuses.transactions.item.title." + source.getReason() + ".transfer");
            } catch (NoSuchMessageException e) {
                result = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.income.other");
            }
        } else {
            result = messageSourceAccessor.getMessage("bonuses.transactions.item.title.order.transfer");
        }

        return result;
    }

    private ZonedDateTime getOrderDate(Long orderId) {
        ZonedDateTime orderDate = null;
        if (orderId != null) {
            OrderDateForBonusesView order = orderRepository.getOrderForBonusesView(orderId);
            if (order != null) {
                if (order.getHoldTime() != null) {
                    orderDate = convertLocalDateToZonedDateTime(order.getHoldTime().toLocalDateTime().toLocalDate());
                } else if (order.getCreateTime() != null) {
                    orderDate = convertLocalDateToZonedDateTime(order.getCreateTime().toLocalDateTime().toLocalDate());
                }
            }
        }
        return orderDate;
    }

    private BonusesBalanceDTO convertBonusesBalance(ResponseBodyBalanceDTO source) {
        if (source.getData() == null) {
            throw new OskellyException("Unexpected error. Data is null. Error converting response: {}", source);
        }

        BonusesBalanceDTO result = new BonusesBalanceDTO();

        BonusesAmountDTO amount = new BonusesAmountDTO();
        amount.setBonuses(source.getData().getAmount().getBonuses());
        amount.setMoney(source.getData().getAmount().getMoney());
        amount.setTotal(source.getData().getAmount().getTotalAmount());

        result.setAmount(amount);
        result.setBurningAmount(source.getData().getBurningAmount());

        return result;
    }

    @Override
    public BigDecimal getValueConsideringTransferPercentAndTransferLimit(BigDecimal value) {
        return getValueConsideringTransferPercentAndTransferLimit(value, configTransferPercent);
    }

    private BigDecimal getValueConsideringTransferPercentAndTransferLimit(BigDecimal value, BigDecimal transferPercent) {
        BigDecimal base;
        if (isTransferLimitReached(value)) {
            base = transferLimit;
        } else {
            base = value;
        }
        BigDecimal preResult = base.multiply(transferPercent).divide(new BigDecimal(100), 0, RoundingMode.HALF_UP);
        return (preResult.compareTo(transferBonusesLimit) > 0) ? transferBonusesLimit : preResult;
    }

    private boolean isTransferLimitReached(BigDecimal value) {
        return value.compareTo(transferLimit) > 0;
    }

    @Override
    public BonusesPaymentOptionsDTO prepareBonusesPaymentOptions(OrderDTO order, String currencyCode) {
        applyBonusesTo1OrderDTO(order, false);
        Set<BonusesPaymentOptionType> options = new HashSet<>();
        long prohibitedPositionsCount = order.getItems()
                .stream()
                .filter(OrderPositionDTO::isBonusesProhibited)
                .count();
        BigDecimal withdrawAmount = order.getBonusesInfo() == null
                || order.getBonusesInfo().getWithdrawBonusesAmount() == null
                || order.getBonusesInfo().getWithdrawBonusesAmount().getTotal() == null
                ? ZERO
                : order.getBonusesInfo().getWithdrawBonusesAmount().getTotal();
        BigDecimal transferAmount = order.getBonusesInfo() == null
                || order.getBonusesInfo().getTransferBonusesAmount() == null
                || order.getBonusesInfo().getTransferBonusesAmount().getTotal() == null
                ? ZERO
                : order.getBonusesInfo().getTransferBonusesAmount().getTotal();

        if (prohibitedPositionsCount == order.getItems().size()) {
            if (ZERO.compareTo(withdrawAmount) == 0) {
                options.add(BonusesPaymentOptionType.WITHDRAW_PROHIBITED);
            } else {
                options.add(BonusesPaymentOptionType.WITHDRAW_LIMITED);
            }
        }

        if (prohibitedPositionsCount > 0 && prohibitedPositionsCount != order.getItems().size()) {
            options.add(BonusesPaymentOptionType.WITHDRAW_LIMITED);
        }

        if (TRUE.equals(order.getBonusesLimitReached())) {
            options.add(BonusesPaymentOptionType.AMOUNT_LIMIT_REACHED);
        }

        if (TRUE.equals(order.getBonusesWithdrawLimitReached())) {
            options.add(BonusesPaymentOptionType.WITHDRAW_BY_PERCENT_LIMITED);
        }

        return new BonusesPaymentOptionsDTO(
                transferAmount,
                withdrawAmount,
                options,
                options.contains(BonusesPaymentOptionType.AMOUNT_LIMIT_REACHED) ? transferLimit : null,
                options.contains(BonusesPaymentOptionType.WITHDRAW_BY_PERCENT_LIMITED) ? getConfigParamWithdrawPercent() : null
        );
    }

    @Override
    public BonusesPaymentOptionsDTO prepareBonusesPaymentOptionsEmpty() {
        return new BonusesPaymentOptionsDTO(ZERO, ZERO, new HashSet<>(), transferLimit, getConfigParamWithdrawPercent());
    }

    @Override
    public void applyBonusesToCart(List<OrderDTO> cartGroups, boolean applyWithdrawBonuses) {
        for (OrderDTO group: cartGroups) {
            applyBonusesTo1OrderDTO(group, applyWithdrawBonuses);
        }
    }

    private void applyBonusesTo1OrderDTO(OrderDTO group, boolean applyWithdrawBonuses) {
        applyBonusesTo1OrderDTO(group, null, applyWithdrawBonuses);
    }

    @Override
    public void applyBonusesTo1OrderDTO(OrderDTO group, BonusesBalanceDTO balance, boolean applyWithdrawBonuses) {
        BigDecimal limitToWithdraw;
        //Чистая сумма заказа по всем позициям не подходит. Нужно использовать сумму с учетом торгов. -- BigDecimal groupAmount = group.getClearAmount();
        //Берем значение finalAmountWithoutDeliveryCost из группы. По идее это finalAmount без стоимости доставки.

        // но если окажется, что getFinalAmount нулевой,
        // то смысла что либо считать нет (скорее всего товар просто продан)
        if (group.getFinalAmount() == null || ZERO.compareTo(group.getFinalAmount()) >= 0) {
            return;
        }

        BigDecimal groupAmount =
                (getBigDecimal(group.getFinalAmount()))
                        .subtract(getBigDecimal(group.getDeliveryCost()));
        //Если был применен промокод, то дополнительно вычитаем значение промокода
        //В реальности, одновременно применить промокод и списание бонусов нельзя,
        // но начисленные баллы должны быть меньше, чем если бы промокод не был применен
        if (group.getDiscount() != null && group.getDiscount().discountValue != null) {
            groupAmount = groupAmount.subtract(group.getDiscount().discountValue).setScale(0, RoundingMode.HALF_UP);
        }
        //Все позиции
        List<Position> positions = new ArrayList<>();
        //Сумма позиций, на которые запрещено списывать сгораемые бонусы
        BigDecimal amountProhibitedToBonusesWithdraw = ZERO;
        //Сумма позиций, на которые можно списывать сгораемые бонусы
        BigDecimal amountAllowedToBonusesWithdraw = ZERO;
        boolean bonusesWithdrawLimitReached = false;
        //В расчет берутся только позиции доступные к покупке. В теории, в корзине могут присутствовать товары как доступные, так и уже проданные (для того, что бы пользователь мог подобрать похожие)
        for (OrderPositionDTO orderPosition: group.getItems().stream().filter(OrderPositionDTO::isAvailable).collect(Collectors.toList())) {
            Position position = Position.fromOrderPositionDTO(orderPosition);
            positions.add(position);
            AmountSubtractPercentAndNLRResult res = getAmountSubtractPercentAndNLR(position.getAmount(), false);
            if (checkIfBonusesApplicableToOrderPosition(position, false)) {
                //Если хотя бы по одной позиции на резрешенном товаре достигнут лимит списания, то выставляем этот признак для всей корзины.
                if (res.isLimitReached()) {
                    bonusesWithdrawLimitReached = true;
                }
                amountAllowedToBonusesWithdraw = amountAllowedToBonusesWithdraw.add(res.getVal());
            } else {
                amountProhibitedToBonusesWithdraw = amountProhibitedToBonusesWithdraw.add(res.getVal());
            }
        }

        group.setBonusesLimitReached(isTransferLimitReached(groupAmount));
        group.setBonusesWithdrawLimitReached(bonusesWithdrawLimitReached);

        BigDecimal amountToTransfer = getValueConsideringTransferPercentAndTransferLimit(groupAmount);
        BonusesBalanceDTO bonusesBalanceForTransfer = createBalanceWithValue(groupAmount);

        //Общая сумма бонусов, которую можно списать
        BigDecimal amountToBonusesWithdraw = amountProhibitedToBonusesWithdraw.add(amountAllowedToBonusesWithdraw);

        BonusesBalanceDTO bonusesBalanceForWithdraw;
        if (group.getBuyer() == null || group.getBuyer().getId() == null) {
            //Если пользователь НЕ авторизован, то списание не рассчитываем
            bonusesBalanceForWithdraw = null;
            limitToWithdraw = null;
        } else {
            //Если пользователь авторизован
            //Определяем сколько может пользователь списать сгораемых бонусов. Что меньше доступная для списания сумма или кол-во сгораемых бонусов у пользователя?
            bonusesBalanceForWithdraw = (balance == null) ? getBalance(group.getBuyer().getId()) : balance;
            BigDecimal limitToBonusesWithdraw = amountAllowedToBonusesWithdraw.compareTo(bonusesBalanceForWithdraw.getAmount().getBonuses()) > 0
                    ? bonusesBalanceForWithdraw.getAmount().getBonuses()
                    : amountAllowedToBonusesWithdraw;
            //Остаток от общий суммы заказа заказа за вычетом неснижаемого остатка и получившегося лимита сгораемых бонусов
            BigDecimal rest = amountToBonusesWithdraw.subtract(limitToBonusesWithdraw);
            //Смотрим, что меньше остаток доступный для списания, или количество НЕсгораемых бонусов на балансе пользователя
            BigDecimal limitToMoneyWithdraw = rest.compareTo(bonusesBalanceForWithdraw.getAmount().getMoney()) > 0
                    ? bonusesBalanceForWithdraw.getAmount().getMoney()
                    : rest;
            limitToWithdraw = limitToBonusesWithdraw.add(limitToMoneyWithdraw);
        }

        DistributeBonusesResult disTransfer = distributeBonuses(
                getLogIdAsSellerId(group),
                amountToTransfer,
                amountToTransfer,
                bonusesBalanceForTransfer,
                positions,
                true
        );

        DistributeBonusesResult disWithdraw;
        if (limitToWithdraw == null) {
            disWithdraw = null;
        } else {
            disWithdraw = distributeBonuses(
                    getLogIdAsSellerId(group),
                    groupAmount,
                    limitToWithdraw,
                    bonusesBalanceForWithdraw,
                    positions,
                    false
            );
        }

        group.getItems().forEach(op -> op.setBonusesInfo(
                new BonusesInfoDTO(
                        (disWithdraw == null) ? createBonusesAmountDTOWithZero() : disWithdraw.getDistribution().getOrDefault(op.getId(), createBonusesAmountDTOWithZero()),
                        disTransfer.getDistribution().getOrDefault(op.getId(), createBonusesAmountDTOWithZero())
                )
        ));

        //По позициям finalAmount c учетом бонусов не проставляется, т.к. нигде не используется
        //finalAmount проставляется у заказа целиком
        group.setBonusesInfo(
                new BonusesInfoDTO(
                        (disWithdraw == null)
                                ? createBonusesAmountDTOWithZero()
                                : new BonusesAmountDTO(
                                        disWithdraw.getBonusBonuses(),
                                        disWithdraw.getMoneyBonuses(),
                                        disWithdraw.getBonusBonuses().add(disWithdraw.getMoneyBonuses())
                        ),
                        new BonusesAmountDTO(
                                disTransfer.getBonusBonuses(),
                                ZERO,
                                disTransfer.getBonusBonuses()
                        )
                )
        );

        if (applyWithdrawBonuses) {
            if (group.getFinalAmount() != null) {
                group.setFinalAmount(group.getFinalAmount().subtract(group.getBonusesInfo().getWithdrawBonusesAmount().getTotal()));
            }

            if (group.getFinalAmountWithoutDeliveryCost() != null) {
                group.setFinalAmountWithoutDeliveryCost(group.getFinalAmountWithoutDeliveryCost().subtract(group.getBonusesInfo().getWithdrawBonusesAmount().getTotal()));
            }

            //TODO base currency
        }
    }

    @Override
    public void applyWithdrawBonusesToOrder(Order order, BigDecimal bonusesToWithdraw) {
        if (order == null || order.getBuyer() == null || order.getBuyer().getId() == null) {
            throw new BonusesDistributionException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-unexpected", new Object[]{(order == null) ? null : order.getId()}));
        }
        if (!Boolean.TRUE.equals(order.getBuyer().getIsLoyaltyProgramAccepted())) {
            //не применяем списание бонусов, если покупатель не принял программу лояльности
            return;
        }

        if (bonusesToWithdraw != null && bonusesToWithdraw.compareTo(ZERO) > 0) {
            log.debug("Bonuses. Order: {}. Calculate bonuses to withdraw.", order.getId());
            BonusesBalanceDTO bonusesBalance = getBalance(order.getBuyer().getId());

            List<OrderPosition> applyToOrderPositions = ((order.getState() == CREATED) ? order.getOrderPositions() : order.getParticipatedInPaymentOrderPositions())
                    .stream()
                    .filter(OrderPosition::isAvailableToBuy)
                    .collect(Collectors.toList());

            BigDecimal startCleanAmount = order.getStartCleanAmount();

            log.debug(
                    "Bonuses. Order: {}. Distribution. orderAmount: {}, bonusesToWithdraw: {}, bonuses: {}, money: {}, positions: {}",
                    order.getId(), startCleanAmount, bonusesToWithdraw, bonusesBalance.getAmount().getBonuses(), bonusesBalance.getAmount().getMoney(), applyToOrderPositions.size()
            );

            DistributeBonusesResult disResult = distributeBonuses(
                    getLogIdAsOrderId(order),
                    startCleanAmount,
                    bonusesToWithdraw,
                    bonusesBalance,
                    applyToOrderPositions.stream().map(op -> Position.fromOrderPosition(op, isBonusesProhibited(op))).collect(Collectors.toList()),
                    false
            );

            applyToOrderPositions.forEach(op -> {
                BonusesAmountDTO bonusesAmount = disResult.getDistribution().getOrDefault(op.getId(), createBonusesAmountDTOWithZero());
                op.setWithdrawBonusBonuses(bonusesAmount.getBonuses());
                op.setWithdrawMoneyBonuses(bonusesAmount.getMoney());
            });

            log.debug("Bonuses. Order: {}. Distribution. Withdraw. Bonuses: {}. Money: {}", order.getId(), disResult.getBonusBonuses(), disResult.getMoneyBonuses());

            order.setWithdrawBonusBonuses(disResult.getBonusBonuses());
            order.setWithdrawMoneyBonuses(disResult.getMoneyBonuses());
        }
    }

    @Override
    public void applyTransferBonusesToOrder(Order order) {
        if (order == null || order.getBuyer() == null || order.getBuyer().getId() == null) {
            throw new BonusesDistributionException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-unexpected", new Object[]{(order == null) ? null : order.getId()}));
        }
        if (!Boolean.TRUE.equals(order.getBuyer().getIsLoyaltyProgramAccepted())) {
            //не применяем начисление бонусов, если покупатель не принял программу лояльности
            return;
        }

        log.debug("Bonuses. Order: {}. Calculate bonuses to transfer.", order.getId());
        //Из заказа берется сумма с учетом примененного промокода
        BigDecimal orderAmount = order.getPriceWithDiscount(order.getStartCleanAmount());
        BigDecimal amountToTransfer = getValueConsideringTransferPercentAndTransferLimit(orderAmount);
        BonusesBalanceDTO bonusesBalance = createBalanceWithValue(orderAmount);

        List<OrderPosition> applyToOrderPositions = ((order.getState() == CREATED) ? order.getOrderPositions() : order.getParticipatedInPaymentOrderPositions())
                .stream()
                .filter(OrderPosition::isAvailableToBuy)
                .collect(Collectors.toList());

        log.debug(
                "Bonuses. Order: {}. Distribution. orderAmount: {}, amountToTransfer: {}, bonuses: {}, positions: {}",
                order.getId(), orderAmount, amountToTransfer, bonusesBalance.getAmount().getBonuses(), applyToOrderPositions.size()
        );

        DistributeBonusesResult disResult = distributeBonuses(
                getLogIdAsOrderId(order),
                orderAmount,
                amountToTransfer,
                bonusesBalance,
                applyToOrderPositions.stream().map(op -> Position.fromOrderPosition(op, isBonusesProhibited(op))).collect(Collectors.toList()),
                true
        );

        applyToOrderPositions.forEach(op -> {
            BonusesAmountDTO bonusesAmount = disResult.getDistribution().getOrDefault(op.getId(), createBonusesAmountDTOWithZero());
            op.setTransferredBonuses(bonusesAmount.getBonuses());
        });

        log.debug("Bonuses. Order: {}. Distribution. TransferredBonuses: {}", order.getId(), disResult.getBonusBonuses());

        order.setTransferredBonuses(disResult.getBonusBonuses());
        order.setTransferredBonusesPercent(configTransferPercent);
    }

    @Override
    public void applyEffectiveTransferBonusesToOrder(Order order, BigDecimal overallCleanAmount) {
        DistributeBonusesResult dis = applyEffectiveTransferBonusesToOrderInternal(
                order.getId(),
                order.getParticipatedInPaymentOrderPositions(),
                overallCleanAmount,
                order.getTransferredBonusesPercent(),
                order.getBuyer() != null && order.getBuyer().getIsLoyaltyProgramAccepted() != null
                        ? order.getBuyer().getIsLoyaltyProgramAccepted()
                        : false
        );

        if (dis != null) {
            order.getParticipatedInPaymentOrderPositions().forEach(op -> {
                BonusesAmountDTO bonusesAmount = dis.getDistribution().getOrDefault(op.getId(), createBonusesAmountDTOWithZero());
                op.setEffectiveTransferredBonuses(bonusesAmount.getBonuses());
            });
            order.setEffectiveTransferredBonuses(dis.getBonusBonuses());
        }
    }

    @Override
    public void applyEffectiveTransferBonusesToOfflineOrder(Order order) {
        DistributeBonusesResult dis = applyEffectiveTransferBonusesToOrderInternal(
                order.getId(),
                order.getEffectiveOrderPositions(),
                order.getEffectiveOrderPositions().stream().map(OrderPosition::getEffectiveAmount).reduce(ZERO, BigDecimal::add),
                null,
                order.getEffectiveBuyer().getIsLoyaltyProgramAccepted()
        );

        if (dis != null) {
            order.getEffectiveOrderPositions().forEach(op -> {
                BonusesAmountDTO bonusesAmount = dis.getDistribution().getOrDefault(op.getId(), createBonusesAmountDTOWithZero());
                op.setTransferredBonuses(bonusesAmount.getBonuses());
                op.setEffectiveTransferredBonuses(bonusesAmount.getBonuses());
            });

            order.setTransferredBonusesPercent(configTransferPercent);
            order.setTransferredBonuses(dis.getBonusBonuses());
            order.setEffectiveTransferredBonuses(dis.getBonusBonuses());
        }
    }

    private DistributeBonusesResult applyEffectiveTransferBonusesToOrderInternal(
            Long orderId, List<OrderPosition> effectivePositions, BigDecimal effectiveOrderAmount, BigDecimal transferPercent, boolean isLoyaltyProgramAccepted
    ) {
        //effectivePositions могут содержать позиции на которые по итогу нельзя проводить распределение бонусов (например, по результатам экспертизы).
        //такие позиции помечаются признаком isEffective = false
        //поэтому заполняем такие позиции значением 0
        if (!Boolean.TRUE.equals(isLoyaltyProgramAccepted)) {
            //не применяем начисление бонусов, если покупатель не принял программу лояльности
            return null;
        }

        log.debug("Bonuses. Order: {}. Calculate effective bonuses to transfer.", orderId);
        //Из заказа берется сумма с учетом примененного промокода
        //При начислении бонусов берется тот процент, который был при оформлении заказа
        BigDecimal amountToTransfer = getValueConsideringTransferPercentAndTransferLimit(
                effectiveOrderAmount, transferPercent == null ? configTransferPercent : transferPercent
        );
        BonusesBalanceDTO bonusesBalance = createBalanceWithValue(effectiveOrderAmount);

        log.debug(
                "Bonuses. Order: {}. Distribution. orderAmount: {}, amountToTransfer: {}, bonuses: {}, positions: {}",
                orderId, effectiveOrderAmount, amountToTransfer, bonusesBalance.getAmount().getBonuses(), effectivePositions.size()
        );

        DistributeBonusesResult disResult = distributeBonuses(
                String.valueOf(orderId),
                effectiveOrderAmount,
                amountToTransfer,
                bonusesBalance,
                effectivePositions.stream().filter(OrderPosition::getIsEffective).map(op -> Position.fromOrderPosition(op, isBonusesProhibited(op))).collect(Collectors.toList()),
                true
        );

        log.debug("Bonuses. Order: {}. Distribution. TransferredBonuses: {}", orderId, disResult.getBonusBonuses());

        return disResult;
    }

    @Override
    public BigDecimal applyWithdrawExpertiseToOrderPosition(OrderPosition orderPosition) {
        //сколько сгораемых бонусов по итогу спишется за данную позицию
        BigDecimal effectiveWithdrawBonuses;
        //сколько НЕсгораемых бонусов по итогу спишется за данную позицию
        BigDecimal effectiveWithdrawMoney;
        //сколько денег по итогу спишется за данную позицию
        BigDecimal positionEffectiveAmount;

        //сколько сгораемых бонусов предполагалось к списанию до инициирования возврата
        BigDecimal withdrawBonuses = getBigDecimal(orderPosition.getWithdrawBonusBonuses());
        //сколько НЕсгораемых бонусов предполагалось к списанию до инициирования возврата
        BigDecimal withdrawMoney = getBigDecimal(orderPosition.getWithdrawMoneyBonuses());
        //сколько денег предполагалось к списанию до инициирования возврата
        BigDecimal withdrawAmount = orderPosition.getAmount().subtract(withdrawBonuses).subtract(withdrawMoney);

        BigDecimal expertiseDiscountPrice =
                orderPosition.getLastExpertise() == null || orderPosition.getLastExpertise().getDefectDiscountPrice() == null
                        ? ZERO
                        : orderPosition.getLastExpertise().getDefectDiscountPrice();

        BonusesService.DistributeBonusesReturnResult dis = distributeBonusesReturn(
                expertiseDiscountPrice,
                withdrawAmount,
                withdrawBonuses,
                withdrawMoney,
                false
        );
        positionEffectiveAmount = dis.getPositionEffectiveAmount();
        effectiveWithdrawBonuses = dis.getEffectiveBonusBonuses();
        effectiveWithdrawMoney = dis.getEffectiveMoneyBonuses();

        orderPosition.setEffectiveWithdrawBonusBonuses(effectiveWithdrawBonuses);
        orderPosition.setEffectiveWithdrawMoneyBonuses(effectiveWithdrawMoney);

        return positionEffectiveAmount;
    }

    @Override
    public BonusesInfoWithBurningDTO calculateBonusesOnProductWithBurningAmount(BigDecimal currentPrice, Boolean isBonusesProhibitedFlag, Long brandId, Long userId) {
        BigDecimal burningAmount = ZERO;
        BigDecimal limitToWithdraw = ZERO;
        BigDecimal limitToWithdrawMoney = ZERO;
        BigDecimal price = currentPrice == null ? ZERO : getAmountSubtractPercentAndNLR(currentPrice, false).getVal();
        if (userId != null) {
            BonusesBalanceDTO balance = getBalance(userId);
            if (balance != null) {
                if (balance.getBurningAmount() != null) {
                    burningAmount = balance.getBurningAmount();
                }
                if (balance.getAmount() != null && balance.getAmount().getTotal() != null) {
                    limitToWithdraw = balance.getAmount().getTotal();
                }
                if (balance.getAmount() != null && balance.getAmount().getMoney() != null) {
                    limitToWithdrawMoney = balance.getAmount().getMoney();
                }
            }
        }

        BonusesAmountDTO withdraw = new BonusesAmountDTO(
                ZERO,
                ZERO,
                isBonusesProhibited(isBonusesProhibitedFlag, brandId)
                        ? (
                                limitToWithdrawMoney.compareTo(price) > 0
                                        ? price
                                        : limitToWithdrawMoney
                        )
                        : (
                                limitToWithdraw.compareTo(price) > 0
                                        ? price
                                        : limitToWithdraw
                        )
        );

        BonusesAmountDTO transfer = new BonusesAmountDTO(
                ZERO,
                ZERO,
                getValueConsideringTransferPercentAndTransferLimit(price)
        );

        return new BonusesInfoWithBurningDTO(
                new BonusesInfoDTO(withdraw, transfer),
                burningAmount
        );
    }

    @Override
    public DistributeBonusesResult distributeBonuses(
            String logId,
            BigDecimal startCleanAmountOriginal,
            BigDecimal limitToDistribute,
            BonusesBalanceDTO bonusesBalance,
            List<Position> positions,
            boolean transferMode
    ) {
        //техническая проверка, что positions не null
        if (positions == null) {
            log.error("Unexpected error while distributing bonuses for logId: {}. Positions == null.", logId);
            throw new BonusesDistributionException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-unexpected", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmountOriginal}));
        }

        //Уменьшаем сумму заказа на значение неснижаемого денежного остатка (настраивается в пропертях) по каждой позиции заказа,
        BigDecimal startCleanAmount = startCleanAmountOriginal.subtract(
                positions
                        .stream()
                        .map(op -> op.getAmount().subtract(getAmountSubtractNLR(op.getAmount(), transferMode)))
                        .reduce(ZERO, BigDecimal::add)
        );

        //Техническая проверка, что суммы не отрицательные.
        if (startCleanAmount.signum() < 0 || limitToDistribute.signum() < 0) {
            log.error("Values of startCleanAmount ({}) and limitToDistribute ({}) should be greater or equals 0. OrderID: {}. Balance: {}", startCleanAmount, limitToDistribute, logId, bonusesBalance.getAmount().getTotal());
            throw new OskellyException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-incorrect-sum", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}));
        }

        Map<Long, BonusesAmountDTO> distribution = new HashMap<>();

        //Проверка условия: Общий баланс должен быть больше, чем нужно списать.
        //Предполагается что кол-во бонусов к списанию ранее было вычислено с учетом баланса.
        //Но вдруг к данному моменту баланс изменился
        if (bonusesBalance.getAmount().getTotal().subtract(limitToDistribute).signum() < 0) {
            log.error("Total balance ({}) should be greater than limitToDistribute ({}). OrderId: {}", bonusesBalance.getAmount().getTotal(), limitToDistribute, logId);
            throw new OskellyException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-incorrect-sum", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}));
        }

        //Проверка условия: Стоимость заказа (с учетом неснижаемого остатка) должна быть больше или равна тому, что нужно списать.
        //Предполагается что кол-во бонусов к списанию ранее было вычислено с учетом баланса.
        //Но вдруг окажется, что стоимость заказа по какой-то странной причине будет меньше, чем планируется к списанию.
        if (startCleanAmount.subtract(limitToDistribute).signum() < 0) {
            log.warn("StartCleanAmount ({}) should be greater or equal then limitToDistribute ({}). OrderId: {}", startCleanAmount, limitToDistribute, logId);
            throw new OskellyException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-incorrect-sum", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}));
        }

        //В случае если проводится списание, дополнительная проверка, что нельзя списать более процента настроенного в конфиге
        if (!transferMode) {
            if (startCleanAmountOriginal.multiply(self.getConfigParamWithdrawRateCached()).setScale(0, RoundingMode.DOWN).compareTo(limitToDistribute) < 0) {
                log.warn("StartCleanAmountOriginal ({}) * configParamWithdrawRateCached ({}) should be greater or equal then limitToDistribute ({}). OrderId: {}", startCleanAmountOriginal, self.getConfigParamWithdrawRateCached(), limitToDistribute, logId);
                throw new OskellyException(
                        messageSourceAccessor.getMessage(
                                "exception.oskelly.pay-bonuses-incorrect-sum", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}
                        )
                );
            }
        }

        //
        //I. Нужно сначала взять список обычных товаров и между ними пропорционально распределить все сгораемые баллы
        //

        //1. Берем все обычные товары и считаем их сумму
        List<Position> notProhibitedBonusesPositions = positions.stream().filter(op -> checkIfBonusesApplicableToOrderPosition(op, transferMode)).collect(Collectors.toList());
        BigDecimal notProhibitedTotal = notProhibitedBonusesPositions.stream().map(op -> getAmountSubtractPercentAndNLR(op.getAmount(), transferMode).getVal()).reduce(ZERO, BigDecimal::add);

        //2. Выбираем наименьшее между доступными к списанию сгораемыми бонусами и лимитом на списание
        BigDecimal value1 = (limitToDistribute.subtract(bonusesBalance.getAmount().getBonuses()).signum() < 0)
                ? limitToDistribute
                : bonusesBalance.getAmount().getBonuses();

        //Если количество сгораемых бонусов меньше, чем сумма всех позиций заказа за исключением запрещенных
        //Полученное значение позволяет понять, можно ли списывать всю сумму позиции или же нужно применить пропорциональное списание
        boolean value1LessThanNotProhibitedTotal = value1.subtract(notProhibitedTotal).signum() < 0;

        //3. Раскидываем минимум value1 по всем обычным товарам
        int i = 1; //используется для корректного вычисления суммы последней позиции, что бы избежать проблем округления
        BigDecimal bonusesAmount = ZERO;
        for (Position position: notProhibitedBonusesPositions) {
            BigDecimal positionBonuses;
            BonusesAmountDTO existingPositionAmount = distribution.getOrDefault(position.getId(), createBonusesAmountDTOWithZero());

            if (value1LessThanNotProhibitedTotal) {
                BigDecimal lastPositionBonusesAmount = value1.subtract(bonusesAmount);
                BigDecimal positionRate = getAmountSubtractPercentAndNLR(position.getAmount(), transferMode).getVal().divide(notProhibitedTotal, 3, RoundingMode.DOWN);
                positionBonuses = (i == notProhibitedBonusesPositions.size()) ? lastPositionBonusesAmount : positionRate.multiply(value1).setScale(0, RoundingMode.DOWN);
            } else {
                positionBonuses = getAmountSubtractPercentAndNLR(position.getAmount(), transferMode).getVal();
            }

            existingPositionAmount.setBonuses(positionBonuses);
            existingPositionAmount.setTotal(positionBonuses);

            distribution.put(position.getId(), existingPositionAmount);

            i++;
            bonusesAmount = bonusesAmount.add(positionBonuses);
        }

        //Проверка на всякий случай. Не должна возникнуть никогда. Сумма bonusesAmount всегда будет <= value1.
        if (bonusesAmount.subtract(value1).signum() > 0) {
            log.error(
                    "Unexpected error while calculating bonuses (bonuses) distribution. value1 != bonusesAmount. OrderId: {}, limitToDistribute: {}, bonusesBalance: {}, orderAmount: {}",
                    logId,
                    limitToDistribute,
                    bonusesBalance.getAmount().getBonuses(),
                    startCleanAmount
            );
            throw new BonusesDistributionException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-unexpected", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}));
        }

        //
        //II. Потом нужно взять все запрещенные товары и остатки от обычных товаров и пропорционально между ними распределить негораемые баллы
        //

        //4. Вычисляем остаток, который осталось списать в рамках лимита
        BigDecimal rest = limitToDistribute.subtract(bonusesAmount);

        BigDecimal moneyAmount = ZERO;

        //5. Если остаток больше чем количество доступных НЕсгораемых бонусов, то бросаем ошибку, т.к. у нас не получится списать необходимую сумму
        //Теоретически это возможно, если вдруг меняется пропорция между обычнми товарами и товарами запрещенными к покупке через бонусы
        if (rest.subtract(bonusesBalance.getAmount().getMoney()).signum() > 0) {
            log.error(
                    "Unexpected error while calculating bonuses distribution. rest > bonusesBalance.getAmount().getMoney(). OrderId: {}, limitToDistribute: {}, moneyBalance: {}, rest: {}, orderAmount: {}",
                    logId,
                    limitToDistribute,
                    bonusesBalance.getAmount().getMoney(),
                    rest,
                    startCleanAmount
            );
            throw new BonusesDistributionException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-unexpected", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}));
        }

        //6. В случае если осталось что-то еще раскидать (rest > 0),
        //то раскидываем оставшуюся сумму rest по всем остаткам (имеется ввиду остаток от суммы каждой позиции, который не компенсирован сгорамыми бонусами) от обычных товаров и по всем запрещенным товарам

        if (rest.signum() > 0) {
            //7.Считаем оставшуюсь по всем позициям сумму, т.е. берем значение из OrderPosition и вычитаем из него уже компенсированную сгораемыми бонусами часть
            BigDecimal allRest = positions.stream().map(op -> getAmountSubtractPercentAndNLR(op.getAmount(), transferMode).getVal().subtract(distribution.getOrDefault(op.getId(), createBonusesAmountDTOWithZero()).getBonuses())).reduce(ZERO, BigDecimal::add);

            //Проверка на всякий случай. По идее не должна возникнуть никогда.
            //Суммы allRest обязательно должна быть >= rest, иначе непонятно на что раскидывать остаток
            if (allRest.subtract(rest).signum() < 0) {
                log.error(
                        "Unexpected error while calculating bonuses (money) distribution. allRest < rest. OrderId: {}, limitToDistribute: {}, bonusesBalance: {}, orderAmount: {}, allRest: {}, rest: {}",
                        logId,
                        limitToDistribute,
                        bonusesBalance.getAmount().getBonuses(),
                        startCleanAmount,
                        allRest,
                        rest
                );
                throw new BonusesDistributionException(messageSourceAccessor.getMessage("exception.oskelly.pay-bonuses-unexpected", new Object[]{logId, limitToDistribute, bonusesBalance.getAmount().getTotal(), startCleanAmount}));
            }

            //Если количество того, что еще нужно списать меньше остаточной стоимости всех позиций (больше не может быть, но может быть равно)
            //Полученное значение позволяет понять, можно ли списывать всю оставшуюся сумму позиции или же нужно применить пропорциональное списание
            boolean restLessThanAllRest = rest.subtract(allRest).signum() < 0;

            i = 1; //используется для корректного вычисления суммы последней позиции, что бы избежать проблем округления

            for (Position position: positions) {
                BigDecimal positionMoney;
                BonusesAmountDTO existingPositionAmount = distribution.getOrDefault(position.getId(), createBonusesAmountDTOWithZero());

                //Эффективная стоимость позиции с учетом бонусов, начисленных на предыдущем шаге
                BigDecimal effectivePositionAmount = getAmountSubtractPercentAndNLR(position.getAmount(), transferMode).getVal().subtract(existingPositionAmount.getBonuses());

                if (restLessThanAllRest) {
                    BigDecimal lastPositionMoneyAmount = rest.subtract(moneyAmount);
                    BigDecimal positionRate = effectivePositionAmount.divide(allRest, 3, RoundingMode.DOWN);
                    positionMoney = (i == positions.size()) ? lastPositionMoneyAmount : positionRate.multiply(rest).setScale(0, RoundingMode.DOWN);
                } else {
                    positionMoney = effectivePositionAmount;
                }

                existingPositionAmount.setMoney(positionMoney);
                existingPositionAmount.setTotal(existingPositionAmount.getBonuses().add(positionMoney));

                distribution.put(position.getId(), existingPositionAmount);

                i++;
                moneyAmount = moneyAmount.add(positionMoney);
            }
        }

        //Дополняем незатронутые позиции нулями
        fillDistributionWithZero(distribution, positions);

        return new DistributeBonusesResult(distribution, bonusesAmount, moneyAmount);
    }

    @Override
    public DistributeBonusesReturnResult distributeBonusesReturn(
            BigDecimal expertiseDefectDiscountPrice, BigDecimal amount, BigDecimal bonusBonuses, BigDecimal moneyBonuses, boolean ignoreNotLowerRest
    ) {
        BigDecimal positionEffectiveAmount;
        BigDecimal effectiveMoney;
        BigDecimal effectiveBonuses;
        if (expertiseDefectDiscountPrice.subtract(amount).signum() >= 0) {
            //Если оказалось что сумма к возврату больше, чем реальные деньги по данной позиции
            //Нужно оставить 1 рубль
            BigDecimal amount1 = ignoreNotLowerRest ? ZERO : notLowerRest;
            //сумму к возврату уменьшаем на всю денежную сумму за исключением 1 рубля
            BigDecimal defectDiscountPrice2 = expertiseDefectDiscountPrice.subtract(amount).add(amount1);
            //эффективная сумма позиции получается 1 рубль
            positionEffectiveAmount = amount1;
            //следующим шагом считаем НЕсгораемые бонусы
            if (defectDiscountPrice2.subtract(moneyBonuses).signum() > 0) {
                //Если оказалось, что оставшаяся сумма к возврату больше чем сумма списаных НЕсгораемых бонусов и надо будет вернуть и сгораемые бонусы тоже
                //сумму к возврату уменьшаем на сумму НЕсгораемых бонусов
                BigDecimal defectDiscountPrice3 = defectDiscountPrice2.subtract(moneyBonuses);
                //сумму НЕсгораемых бонусов приравниваем к 0
                effectiveMoney = ZERO;
                //сумму сгораемых бонусов уменьшаем на величину, которая осталась после возврата НЕсгораемых бонусов
                effectiveBonuses = bonusBonuses.subtract(defectDiscountPrice3);
            } else {
                //Если оказалось, что оставшаяся сумма к возврату меньше чем сумма списаных НЕсгораемых бонусов
                //сумму НЕсгораемых бонусов уменьшаем на оставшуюся к возврату сумму
                effectiveMoney = moneyBonuses.subtract(defectDiscountPrice2);
                //сгораемые бонусы не трогаем
                effectiveBonuses = bonusBonuses;
            }
        } else {
            //Если оказалось что сумма к возврату меньше чем реальные деньги по данной позиции
            positionEffectiveAmount = amount.subtract(expertiseDefectDiscountPrice);
            //НЕсгораемые бонусы не трогаем
            effectiveMoney = moneyBonuses;
            //Сгораемые бонусы не трогаем
            effectiveBonuses = bonusBonuses;
        }
        return new DistributeBonusesReturnResult(
                positionEffectiveAmount,
                effectiveBonuses,
                effectiveMoney
        );
    }

    private BonusesBalanceDTO createBalanceWithValue(BigDecimal value) {
        return new BonusesBalanceDTO(
                new BonusesAmountDTO(
                        value,
                        ZERO,
                        value.add(ZERO)
                ),
                ZERO
        );
    }

    private void fillDistributionWithZero(Map<Long, BonusesAmountDTO> distribution, List<Position> positions) {
        for(Position position: positions) {
            if (!distribution.containsKey(position.getId())) {
                distribution.put(position.getId(), createBonusesAmountDTOWithZero());
            }
        }
    }

    private BonusesAmountDTO createBonusesAmountDTOWithZero() {
        return new BonusesAmountDTO(ZERO, ZERO, ZERO);
    }

    private void createWelcomeNotification(User user, BigDecimal bonuses) {
        ImmutableMap.Builder<String, Object> paramsBuilder = ImmutableMap.<String, Object>builder()
                .put(BonusesNotification.PARAM_BONUSES_AMOUNT, bonuses)
                .put(BonusesNotification.PARAM_BONUSES_TARGET_OBJECT_URL, configParamService.getValueAsStringCached(ConfigParamService.CONFIG_PARAM_BONUSES_WELCOME_BANNER));

        notificationService.createSync(
                new BonusesWelcomeNotification()
                        .setUser(user)
                        .setParamsFromMap(paramsBuilder.build()));
    }

    private BonusesAmountDTO createBonusesAmountDTO(BigDecimal bonuses, BigDecimal money) {
        return new BonusesAmountDTO(
                getBigDecimal(bonuses),
                getBigDecimal(money),
                getBigDecimal(bonuses).add(getBigDecimal(money))
        );
    }

    private boolean checkIfBonusesApplicableToOrderPosition(Position p, boolean ignoreProhibited) {
        if (ignoreProhibited) {
            return true;
        }

        if (p != null && p.isBonusesProhibited()) {
            return false;
        }
        return true;
    }

    /**
     * Возвращает бонусный счет пользователя с заданным userId.
     * Если счета еще нет, то создает его.
     * При создании используется блокировка на всей таблице таблице user_bonuses_account для исключения попытки заведения
     * бонусного счета двумя параллельными процессами.
     * ** В качестве альтернативного решения можно использовать фейковую запись в данной таблице и вешать блокировку на неё.
     */
    @Transactional
    public UserBonusesAccount getOrCreateUserBonusesAccount(Long userId) {
        Optional<UserBonusesAccount> accountOpt = userBonusesAccountRepository.findByUserId(userId);
        UserBonusesAccount account;
        if (!accountOpt.isPresent()) {
            try {
                account = callInTransaction.runInNewTransaction(() -> {
                    userBonusesAccountRepository.lockTable();
                    Optional<UserBonusesAccount> account1Opt = userBonusesAccountRepository.findByUserId(userId);
                    if (!account1Opt.isPresent()) {
                        UserBonusesAccount newAccount = new UserBonusesAccount();
                        newAccount.setUserId(userId);
                        newAccount.setBonusesAccountId(UUID.randomUUID().toString());
                        return userBonusesAccountRepository.save(newAccount);
                    } else {
                        return account1Opt.get();
                    }
                });
            } catch (Exception e) {
                accountOpt = userBonusesAccountRepository.findByUserId(userId);
                if (!accountOpt.isPresent()) {
                    throw new OskellyException("Unexpected error with lock. Can't create userId: " + userId, e);
                }
                account = accountOpt.get();
            }
        } else {
            account = accountOpt.get();
        }
        return account;
    }

    @Override
    public void processErrorIncomeTransactions() {
        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> orderBonusesTransactionRepository.findErrorTransactionIds(processingMaxAttempts, TransactionDTO.TrnTypeEnum.TRANSFER.toString()),
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                trnIds -> {
                                    for (Long id: trnIds) {
                                        try {
                                            self.transferBonusesAfterError(id);
                                        } catch (Exception e) {
                                            log.warn("Error processing error bonuses transaction with id: {}", id, e);
                                        }
                                    }
                                    log.info("Error transaction processing count: {}", trnIds.size());
                                    return null;
                                },
                                bonusesExecutor
                        )
                        .thenApplyAsync(
                                (o) -> orderBonusesTransactionRepository.findErrorTransactionIds(processingMaxAttempts, TransactionDTO.TrnTypeEnum.RETURN.toString()),
                                bonusesExecutor
                        )
                        .thenAcceptAsync(
                                trnIds -> {
                                    for (Long id: trnIds) {
                                        try {
                                            self.returnBonusesAfterError(id);
                                        } catch (Exception e) {
                                            log.warn("Error processing error bonuses transaction with id: {}", id, e);
                                        }
                                    }
                                    log.info("Error transaction processing count: {}", trnIds.size());
                                },
                                bonusesExecutor
                        )
                        .exceptionally(ex -> {
                            log.warn("Unexpected error", ex);
                            throw new OskellyException(ex);
                        })
                        .get(socketTimeout.getSeconds() * 1000, SECONDS)
        );
    }

    @Override
    public void processOldHoldTransactions() {
        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> controller.getTransactions(TransactionDTO.StateEnum.HOLD.toString(), oldnessOfHoldTransaction)
                        )
                        .thenAcceptAsync(
                                response -> {
                                    if (response == null || response.getData() == null) {
                                        log.warn("Error processing old bonuses transaction in HOLD because of null response from bonuses service");
                                        return;
                                    }

                                    for (TransactionBriefDTO trn: response.getData()) {
                                        try {
                                            processOldHoldTransaction(trn);
                                        } catch (Exception e) {
                                            log.warn("Error processing old bonuses transaction in HOLD state with id: {}", trn.getId(), e);
                                        }
                                    }

                                    log.info("Processed {} transactions in HOLD state", response.getData().size());
                                },
                                bonusesExecutor
                        )
                        .exceptionally(ex -> {
                            log.warn("Unexpected error", ex);
                            throw new OskellyException(ex);
                        })
                        .get(socketTimeout.getSeconds() * 1000, SECONDS)
        );
    }

    @Override
    public void transferEffectiveBonusesToBuyer(Order order) {
        User buyer = order.getBuyer() != null ? order.getBuyer() : null;
        if (buyer == null) {
            log.warn("Can't determine buyer for the order: {}", order.getId());
            return;
        }
        transferEffectiveBonusesToUserInternal(order, buyer);
    }

    @Override
    public void transferEffectiveBonusesToEffectiveBuyer(Order order) {
        User effectiveBuyer = order.getEffectiveBuyer() != null ? order.getEffectiveBuyer() : null;
        if (effectiveBuyer == null) {
            log.warn("Can't determine effective buyer for the order: {}", order.getId());
            return;
        }
        transferEffectiveBonusesToUserInternal(order, effectiveBuyer);
    }

    private void transferEffectiveBonusesToUserInternal(Order order, User user) {
        if (order.getEffectiveOrderPositions() != null) {
            //Начисление бонусов effectiveBuyer'у осуществляется не здесь, а шедулером с учетом срока в 14 дней на возможный возврат покупки
            BigDecimal amountToTransfer = BigDecimal.ZERO;
            for (OrderPosition position: order.getEffectiveOrderPositions()) {
                if (position.getEffectiveTransferredBonuses() != null) {
                    amountToTransfer = amountToTransfer.add(position.getEffectiveTransferredBonuses());
                }
            }

            if (amountToTransfer.signum() > 0) {
                OrderBonusesTransaction trn = transferBonusesOrder(user, amountToTransfer, order.getId());
                log.info("BonusesTransaction: " + (trn == null ? "" : trn.getId()));
            }
        } else {
            log.warn("Can't transfer bonuses because order with id: {} has no positions", order.getId());
        }
    }

    private void processOldHoldTransaction(TransactionBriefDTO trn) {
        if (trn.getOrderId() == null) {
            log.warn("Bonuses transaction with id: {} has orderId is null", trn.getId());
            return;
        }

        callWithExceptionWrapping(() ->
                CompletableFuture
                        .supplyAsync(
                                () -> self.getOrderBrief(trn.getOrderId())
                        )
                        .thenCombineAsync(
                                CompletableFuture
                                        .supplyAsync(
                                                () -> userBonusesAccountRepository.findByBonusesAccountId(trn.getAccountId()).orElse(null),
                                                bonusesExecutor
                                        ),
                                (order, account) -> {
                                    if (order == null || account == null) {
                                        log.warn(
                                                "Bonuses transaction with id: {}, orderId: {} and accountId: {} has order is null: {} and account is null: {}",
                                                trn.getId(), trn.getOrderId(), trn.getAccountId(), (order == null), (account == null)
                                        );
                                        return null;
                                    }

                                    if (CREATED.equals(order.getState()) || OrderState.DELETED.equals(order.getState()) || OrderState.REFUND.equals(order.getState())) {
                                        withdrawBonusesCancel(account.getUserId(), trn.getOrderId());
                                        return null;
                                    } else {
                                        if (order.getPayments() != null) {
                                            //TODO выбрать нужный OrderPayment. По словам Алана OrderPayment сейча может быть только один. Но на бдущее нужно иметь это место ввиду.
                                            OrderPaymentBrief payment = order.getPayments().stream().findFirst().orElse(null);
                                            if (payment != null) {
                                                //Признак того, что платеж был реально выполнен
                                                if (payment.getAuthorizeTime() != null) {
                                                    withdrawBonusesCommit(account.getUserId(), trn.getOrderId());
                                                    return null;
                                                }
                                            }
                                        }
                                    }

                                    log.info("Bonuses transaction with id: {}, orderId: {} and accountId: {} is skipped", trn.getId(), trn.getOrderId(), trn.getAccountId());

                                    return null;
                                },
                                bonusesExecutor
                        )
                        .get(socketTimeout.getSeconds() * 1000, SECONDS)
        );
    }

    @Override
    @Transactional
    public OrderBrief getOrderBrief(Long orderId) {
        Order order = orderRepository.findById(orderId).orElse(null);
        if (order == null) {
            return null;
        }

        List<OrderPaymentBrief> payments;

        if (order.getOrderPayments() != null) {
            payments = order.getOrderPayments()
                    .stream()
                    .map(op -> new OrderPaymentBrief(op.getId(), op.getState(), op.getAuthorizeTime()))
                    .collect(Collectors.toList());
        } else {
            payments = new ArrayList<>();
        }

        return new OrderBrief(order.getState(), payments);
    }

    @Override
    public boolean isBonusesProhibited(OrderPosition op) {
        if (op != null && op.getProductItem() != null && op.getProductItem().getProduct() != null) {
            return isBonusesProhibited(op.getProductItem().getProduct());
        }

        return false;
    }

    @Override
    public boolean isBonusesProhibited(Product p) {
        if (p != null) {
            return isBonusesProhibited(
                    p.isBonusesProhibited(),
                    p.getBrand() != null ? p.getBrand().getId() : null
            );
        }
        return false;
    }

    @Override
    public boolean isBonusesProhibited(boolean isBonusesProhibitedFlag, Long brandId) {
        if (isBonusesProhibitedFlag) {
            return isBonusesProhibitedFlag;
        }

        Set<Long> prohibitedBrands = (configParamService == null)
                ? new HashSet<>()
                : configParamService.getValueAsSetOfLongCached(ConfigParamService.CONFIG_PARAM_BONUSES_PROHIBITED_BRANDS);

        return brandId != null && prohibitedBrands.contains(brandId);
    }

    @Override
    @Cacheable("DefaultBonusesService.getConfigParamWithdrawRateCached")
    public BigDecimal getConfigParamWithdrawRateCached() {
        BigDecimal configPercent = getConfigParamWithdrawPercent();
        return (configPercent == null ? DEFAULT_WITHDRAW_PERCENT : configPercent).divide(new BigDecimal(100), 3, RoundingMode.DOWN);
    }

    private BigDecimal getConfigParamWithdrawPercent() {
        return configParamService.getValueAsBigDecimalNullableCached(ConfigParamService.CONFIG_PARAM_BONUSES_WITHDRAW_PERCENT);
    }

    private OrderBonusesTransaction convertTransactionDTOToOrderBonusesTransaction(Long userId, ResponseBodyTransactionWithBalanceDTO res) {
        if (res.getData() == null) {
            throw new OskellyException("Unexpected error. Data is null. Error converting response: {}", res);
        }

        return convertTransactionDTOToOrderBonusesTransaction(userId, res.getData().getTransaction());
    }

    private OrderBonusesTransaction convertTransactionDTOToOrderBonusesTransaction(Long userId, TransactionDTO trnSrc) {
        OrderBonusesTransaction trn = new OrderBonusesTransaction();
        trn.setAttempts(0);
        trn.setBonusAmount(trnSrc.getBonusesAmount());
        trn.setMoneyAmount(trnSrc.getMoneyAmount());
        trn.setBonusTransactionId(trnSrc.getId());
        trn.setTrnDate(trnSrc.getTrnDate().toZonedDateTime());
        trn.setTrnType(trnSrc.getTrnType());
        trn.setTrnState(trnSrc.getState());
        trn.setOrderId(trnSrc.getOrderId());
        trn.setUserId(userId);
        trn.setProcessedDate(ZonedDateTime.now());
        trn.setDescription(trnSrc.getDescription());
        trn.setReason(trnSrc.getReason());
        trn.setAuthorId(trnSrc.getAuthorId());
        return trn;
    }

    private OrderBonusesTransaction convertErrorToOrderBonusesTransaction(
            Throwable t, TransactionDTO.TrnTypeEnum trnType, Long userId, BigDecimal bonusesAmount, BigDecimal moneyAmount, Long orderId, String description, String reason, Long authorId, LocalDate dateEnd, OrderBonusesTransactionParams params, String idempotencyKey
    ) {
        OrderBonusesTransaction trn = new OrderBonusesTransaction();
        trn.setAttempts(0);
        trn.setBonusAmount(bonusesAmount);
        trn.setMoneyAmount(moneyAmount);
        trn.setBonusTransactionId(null);
        trn.setTrnDate(null);
        trn.setTrnType(trnType);
        trn.setOrderId(orderId);
        trn.setUserId(userId);
        trn.setProcessedDate(null);
        trn.setErrorText(t.getCause() != null ? t.getCause().getMessage() : t.getMessage());
        trn.setTrnState(null);
        trn.setAuthorId(authorId);
        trn.setDescription(description);
        trn.setReason(reason);
        trn.setDateEnd(dateEnd);
        trn.setParams(params);
        trn.setIdempotencyKey(idempotencyKey);
        return trn;
    }

    private String generateIdempotencyKeyWithdraw(String accountId, Long orderId) {
        return generateIdempotencyKeyWithAccountIdAndOrderId(accountId, orderId, TransactionDTO.TrnTypeEnum.WITHDRAW);
    }

    private String generateIdempotencyKeyTransfer(String accountId, Long orderId) {
        return generateIdempotencyKeyWithAccountIdAndOrderId(accountId, orderId, TransactionDTO.TrnTypeEnum.TRANSFER);
    }

    private String generateIdempotencyKeyWithAccountIdAndOrderId(String accountId, Long orderId, TransactionDTO.TrnTypeEnum type) {
        return accountId + "_" + orderId + "_" + type;
    }

    private String generateIdempotencyKeyWithAccountIdAndOrderIdAndSums(String accountId, Long orderId, BigDecimal bonuses, BigDecimal money) {
        return accountId + "_" + orderId + "_" + bonuses.toPlainString() + "_" + money.toPlainString();
    }

    private String generateIdempotencyKeyWithAccountIdAndRandom(String accountId) {
        return accountId + "_" + UUID.randomUUID();
    }

    private String generateIdempotencyKeyWithAccountIdAndString(String accountId, String value) {
        return accountId + "_" + value;
    }

    private boolean checkIfExceptionPreventsToResendTransactionLater(Throwable t) {
        //Тут нужно убедиться, что t именно потомок OskellyException (бизнес-исключение), а не сам OskellyException
        //По идее, какие-то ошибки типа NotFoundException нет смысла конвертировать в ошибочную транзакцию
        //А вот технические ошибки при обращении к бонусному сервису (например таймаут) вполне могут быть переотправлены позже.
        if (t != null && t.getCause() instanceof OskellyException) {
            if (!t.getCause().getClass().equals(OskellyException.class)) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected String getErrorGeneralText() {
        return messageSourceAccessor.getMessage("BonusesService.error");
    }

    @Override
    protected String getErrorOveralTimeoutText() {
        return messageSourceAccessor.getMessage("BonusesService.timeout.overall");
    }

    @Override
    protected String getErrorRequestTimeoutText() {
        return messageSourceAccessor.getMessage("BonusesService.timeout.request");
    }

    private BigDecimal getAmountSubtractNLR(BigDecimal amount, boolean ignoreNotLowerRest) {
        if (ignoreNotLowerRest) {
            return amount;
        }
        return amount.subtract(notLowerRest).signum() > 0 ? amount.subtract(notLowerRest) : ZERO;
    }

    private AmountSubtractPercentAndNLRResult getAmountSubtractPercentAndNLR(BigDecimal amount, boolean ignoreNotLowerRest) {
        if (ignoreNotLowerRest) {
            return new AmountSubtractPercentAndNLRResult(amount, false);
        }
        //сколько нужно списать с учетом максимально возможного процента списания
        BigDecimal amount2 = amount.multiply(self.getConfigParamWithdrawRateCached()).setScale(0, RoundingMode.DOWN);
        if (amount.subtract(amount2).subtract(notLowerRest).compareTo(ZERO) > 0) {
            //если amount2 и notLowerRest в сумме меньше чем общая сумма amount, то можем списать максимально возможный процент
            return new AmountSubtractPercentAndNLRResult(amount2, true);
        } else {
            //иначе, допустимое значение вычисляется по старому, т.е. просто за вычетом неснижаемого остатка
            return new AmountSubtractPercentAndNLRResult(amount.subtract(notLowerRest).signum() > 0 ? amount.subtract(notLowerRest) : ZERO, false);
        }
    }

    private void logUserNotAcceptedLoyalityProgram(Long userId) {
        log.debug("User with id: {} didn't accept loyalty program", userId);
    }

    private String getLogIdAsSellerId(OrderDTO order) {
        return (order.getSeller() != null && order.getSeller().getId() != null) ? order.getSeller().getId().toString() : null;
    }

    private String getLogIdAsOrderId(Order order) {
        return (order.getId() != null) ? order.getId().toString() : null;
    }

    private BigDecimal getBigDecimal(BigDecimal value) {
        return value == null ? ZERO : value;
    }

    @RequiredArgsConstructor
    @Getter
    private static class AmountSubtractPercentAndNLRResult {
        private final BigDecimal val;
        private final boolean limitReached;
    }
}