package su.reddot.domain.service.account.bubble.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.account.bubble.BubblesCategory;
import su.reddot.domain.service.account.bubble.BubblesFiller;
import su.reddot.domain.service.dto.BubblesDTO;
import su.reddot.domain.service.salerequest.SaleRequestService;

@Component
@Slf4j
@RequiredArgsConstructor
public class SaleRequestsFiller implements BubblesFiller {

    private final SaleRequestService saleRequestService;

    @Override
    public void fill(<PERSON><PERSON><PERSON><PERSON>TO bubblesContainer, User user, boolean supportBoutique) {
        long saleRequestsCount = 0;
        try {
            saleRequestsCount = saleRequestService.getCurrentUserRequestsCount();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        bubblesContainer.setSaleRequestsTotal(saleRequestsCount);
    }

    @Override
    public String getCategory() {
        return BubblesCategory.SALE_REQUESTS.name();
    }
}
