package su.reddot.domain.service.promocode.sort;

import com.querydsl.core.types.OrderSpecifier;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.exception.PromoCodeRequestException;

import java.util.List;

@Component
@RequiredArgsConstructor
public class PromoCodeSortProvider {

    private final List<PromoCodeSort> sortings;

    private final MessageSourceAccessor messageSourceAccessor;

    public List<OrderSpecifier<?>> getSort(String sortCode) {
        return findSortOrThrow(sortCode).getSort();
    }

    public PromoCodeSort findSortOrThrow(String sortCode) {
        return sortings.stream()
                       .filter(processor -> StringUtils.equalsIgnoreCase(sortCode, processor.getType()))
                       .findFirst()
                       .orElseThrow(() -> new PromoCodeRequestException(
                               messageSourceAccessor.getMessage(
                                       "promocode.sort.PromoCodeSortProvider.unknownSorting",
                                       new Object[] {sortCode})));
    }
}
