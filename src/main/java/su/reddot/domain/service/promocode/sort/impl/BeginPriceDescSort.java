package su.reddot.domain.service.promocode.sort.impl;

import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.NumberPath;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.discount.QPromoCode;

import java.math.BigDecimal;

@Component
public class BeginPriceDescSort extends BasicSort {

    @Override
    protected String getColumnName() {
        return QPromoCode.promoCode.beginPrice.getMetadata().getName();
    }

    @Override
    protected NumberPath<BigDecimal> getExpression() {
        return QPromoCode.promoCode.beginPrice;
    }

    @Override
    protected Order getSortOrder() {
        return Order.DESC;
    }

    @Override
    protected OrderSpecifier.NullHandling getNullHandling() {
        return OrderSpecifier.NullHandling.NullsLast;
    }
}
