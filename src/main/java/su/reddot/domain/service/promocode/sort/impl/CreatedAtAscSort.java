package su.reddot.domain.service.promocode.sort.impl;

import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.DateTimePath;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.discount.QPromoCode;

import java.time.ZonedDateTime;

@Component
public class CreatedAtAscSort extends BasicSort {

    @Override
    protected String getColumnName() {
        return QPromoCode.promoCode.createdAt.getMetadata().getName();
    }

    @Override
    protected DateTimePath<ZonedDateTime> getExpression() {
        return QPromoCode.promoCode.createdAt;
    }

    @Override
    protected Order getSortOrder() {
        return Order.ASC;
    }

    @Override
    protected OrderSpecifier.NullHandling getNullHandling() {
        return OrderSpecifier.NullHandling.NullsLast;
    }
}
