package su.reddot.domain.service.instagramfeed;

import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;

import java.util.List;

public interface InstagramFeedRetriever {
    List<InstagramFeedItem> retrieveFeed(int maxItemsCount, Integer minLikesCount, Integer minCommentsCount);
    ResponseEntity<Resource> retrieveFeedItemContent(String mediaId, String variantType);
}