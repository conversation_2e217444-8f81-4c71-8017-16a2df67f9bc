package su.reddot.domain.service.instagramfeed;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Accessors(chain = true)
@ToString
@Setter
@Getter
public class InstagramFeedItem {
    private String id;
    private String mediaUrl;
    private String thumbnailUrl;
    private String caption;
    private long likesCount;
    private long commentsCount;
    private String timestamp;
    private String userName;
    private String url;
}