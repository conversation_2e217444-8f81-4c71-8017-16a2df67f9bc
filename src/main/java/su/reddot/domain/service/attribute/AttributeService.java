package su.reddot.domain.service.attribute;

import su.reddot.domain.model.attribute.Attribute;
import su.reddot.domain.model.attribute.AttributeValue;
import su.reddot.domain.service.dto.attribute.AttributeDTO;
import su.reddot.domain.service.dto.attribute.AttributeValueDTO;
import su.reddot.domain.service.dto.attribute.AttributeWithValueDTO;
import su.reddot.infrastructure.util.CollectionDistinctSortedCacheKeyWrapper;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AttributeService {

	void updateCache();

	/**
     * @return список всех атрибутов, упорядоченных в алфавитном порядке по возрастанию
     */
	List<Attribute> getAllAttributes();

	List<AttributeDTO> getAllAttributeDTOs();

	List<AttributeDTO> getAllAttributeDTOsCached();

	//Атрибут цвета (часто требуется в разных местах)
	Attribute getColorAttribute();

	//Кэшируемая версия
	Attribute getColorAttributeCached();

	List<AttributeValue> getAllAttributeValues();

	Attribute getAttribute(Long attributeId);

	AttributeValue getAttributeValue(Long attributeValueId);

	List<Attribute> getAttributes(List<Long> attributeIds);

	List<AttributeValue> getAttributeValues(Collection<Long> attributeValueIds);

	AttributeDTO getAttributeDTO(Long attributeId);

	List<AttributeDTO> getAttributeDTOs(List<Long> attributeIds);

	AttributeValueDTO getAttributeValueDTO(Long attributeValueId);

	List<AttributeValueDTO> getAttributeValueDTOsByIds(List<Long> attributeValueIds);

	List<AttributeValueDTO> getAttributeValueDTOs(List<AttributeValue> attributeValues);

	AttributeWithValueDTO getAttributeWithValueDTO(Long attributeValueId);

	List<AttributeWithValueDTO> getAttributeWithValueDTOs(List<Long> attributeValueIds);

	Map<Attribute, List<AttributeValue>> getAttributeValueMap(Collection<Long> attributeValueIds);

	Map<Attribute, List<AttributeValue>> getAttributeValueMapCached(Collection<Long> attributeValueIds);

	Map<Attribute, List<AttributeValue>> getAttributeValueMap(Collection<Long> attributeValueIds, Boolean showFilter);

	/**
	 * @return только те значения атрибутов, которые присущи товарам в указанной ветке категорий, по атрибутам, входящим в текущую и родительскую категории.
	 * если includingSubtreeAttributes,  то в выборку включатся значения по атрибутам, определенных в подветке
	 */
	Map<Attribute, List<AttributeValue>> getActualAttributeValues(Long categoryId, boolean includingSubtreeAttributes);

	/**
	 * Кэшируемая версия
	 */
	Map<Attribute, List<AttributeValue>> getActualAttributeValuesCached(Long categoryId, boolean includingSubtreeAttributes);

	/**
	 * @return только те значения атрибутов, которые присущи товарам в указанных категориях. Применяются атрибуты категорий и вышестоящих категорий.
	 * если includingSubtreeAttributes, то применяюся так же атрибуты, определенные в подветках
	 */
	Map<Attribute, List<AttributeValue>> getActualAttributeValues(Collection<Long> categoryIds, boolean includingSubtreeAttributes);

	/**
	 * Кэшируемая версия
	 */
	Map<Attribute, List<AttributeValue>> getActualAttributeValuesCached(
			CollectionDistinctSortedCacheKeyWrapper<Long> categoryIds, boolean includingSubtreeAttributes);

	Map<Attribute, List<AttributeValue>> getActualAttributeValuesCachedOneByOne(Collection<Long> categoryIds, boolean includingSubtreeAttributes);

	Set<Long> getAttributeIdsByCategories(Collection<Long> categoryIds, boolean includeSubtreeAttributes, boolean showFilterOnly);

	Set<Long> getAttributeIdsByCategoriesCached(Collection<Long> categoryIds, boolean includeSubtreeAttributes, boolean showFilterOnly);

	Set<Long> getAttributeIdsByCategory(Long categoryId, boolean includingSubtreeAttributes, boolean showFilterOnly);

	Set<Long> getAttributeIdsByCategoryCached(Long categoryId, boolean includingSubtreeAttributes, boolean showFilterOnly);
}
