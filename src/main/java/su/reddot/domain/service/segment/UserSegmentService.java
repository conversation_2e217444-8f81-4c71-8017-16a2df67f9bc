package su.reddot.domain.service.segment;

import su.reddot.domain.service.dto.segment.SegmentDTO;
import su.reddot.domain.service.dto.segment.UploadUserIdsForSegmentDTO;

import java.util.List;
import java.util.Optional;

public interface UserSegmentService {

    long ALL_USERS_SEGMENT_ID_DATA = 1L;

    List<SegmentDTO> findAll();
    List<SegmentDTO> findAllCached();

    Optional<SegmentDTO> findById(Long segmentId);

    List<SegmentDTO> findAllByUserIdOrGuestToken(Long userId, String guestToken, Long countryId);
    List<SegmentDTO> findAllByUserIdOrGuestToken(Long countryId);

    SegmentDTO update(SegmentDTO segmentDTO);

    SegmentDTO deleteUserSegment(Long userSegmentId);

    UploadUserIdsForSegmentDTO addSegmentForUsers(Long segmentId, List<Long> userIds);
}
