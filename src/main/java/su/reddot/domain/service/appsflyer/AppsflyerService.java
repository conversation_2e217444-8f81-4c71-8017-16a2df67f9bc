package su.reddot.domain.service.appsflyer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.model.activity.Activity;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.service.dto.AppsflyerResultDTO;
import su.reddot.infrastructure.util.ProductionEnvironment;

import java.util.List;

public interface AppsflyerService {

	/**
	 * Получить активности для отправки в Appsflyer
	 * @param limit
	 * @return
	 */
	List<Activity> getActivitiesToSendToAppsflyer(int limit);

	/**
	 * Получить объект для значения eventValue для запроса к Appsflyer
	 * @param activity
	 * @return
	 */
	AFEventValue getAFEventValueForActivity(Activity activity);

	/**
     * Получить зарос к Appsflyer
     *
     * @param activity
     * @param dtype
     * @return
     */
	AFRequest getAFRequestForActivity(Activity activity, String appsflyerId, DeviceDtype dtype);

	/**
	 * Создает запрос согласно активности и направляет запрос на AppsFlyer, возвращает true в случае успеха
	 *
	 * @param activity
	 * @return
	 */
	AppsflyerResultDTO sendToAppsflyer(Activity activity);

	/**
	 * Отправляет активности в AppsFlyer
	 *
	 * @param activities
	 */
	List<AppsflyerResultDTO> sendToAppsflyer(List<Activity> activities);

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@Getter
	@Setter
	@Accessors(chain = true)
	class AFEventValue{

		@JsonProperty("af_revenue")
		private Double revenue;

		@JsonProperty("af_price")
		private Double price;

		@JsonProperty("af_content_type")
		private String contentType;

		@JsonProperty("af_content_id")
		private Long contentId;

		@JsonProperty("af_content_list")
		private List<Long> contentList;

		@JsonProperty("af_order_id")
		private Long orderId;

		@JsonProperty("af_quantity")
		private Integer quantity;

		@JsonProperty("amount")
		private Double amount;

		@JsonProperty("af_currency")
		private String currency;

		@JsonProperty("order_position")
		private AFEventOrderPosition orderPosition;

		@JsonProperty("order_positions")
		private List<AFEventOrderPosition> orderPositions;

		@JsonProperty("product")
		private AFEventProduct product;

		@JsonProperty("brand")
		private AEEventBrand brand;

		@JsonProperty("following")
		private AFEventFollowing following;

		@JsonProperty("product_like_id")
		private Long productLikeId;

		@JsonProperty("brand_like_id")
		private Long brandLikeId;

		@JsonProperty("bargain_id")
		private Long bargainId;

		@JsonProperty("comment_id")
		private Long commentId;

		@JsonProperty("concierge_form_id")
		private Long conciergeFormId;

		@JsonProperty("payment_type")
		private String paymentType;

		@JsonProperty("environment")
		private ProductionEnvironment environment;
	}

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@Getter
	@Setter
	@Accessors(chain = true)
	class AFEventProduct{

		private Long id;

		private Double price;

		@JsonProperty("category_id")
		private Long categoryId;

		@JsonProperty("category")
		private String categoryName;

		@JsonProperty("brand_id")
		private Long brandId;

		@JsonProperty("brand")
		private String brandName;
	}

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@Getter
	@Setter
	@Accessors(chain = true)
	class AFEventOrderPosition{

		private Long id;

		private AFEventProduct product;

		@JsonProperty("size_id")
		private Long sizeId;

		private int quantity = 1;

		@JsonProperty("amount")
		private Double amount;
	}


	@JsonInclude(JsonInclude.Include.NON_NULL)
	@Getter
	@Setter
	@Accessors(chain = true)
	@AllArgsConstructor
	class AFEventFollowing {
		@JsonProperty("user_id")
		private Long userId;
		@JsonProperty("subscriber_id")
		private Long subscriber_id;
	}

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@Getter
	@Setter
	@Accessors(chain = true)
	@AllArgsConstructor
	class AEEventBrand {
		@JsonProperty("brand_id")
		private Long brandId;
	}

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@Getter
	@Setter
	@Accessors(chain = true)
	class AFRequest{

		@JsonProperty("appsflyer_id")
		private String appsflyerId;

		@JsonProperty("idfa")
		private String idfa;

		@JsonProperty("idfv")
		private String idfv;

		@JsonProperty("advertising_id")
		private String advertisingId;

		@JsonProperty("oaid")
		private String oaid;

		@JsonProperty("customer_user_id")
		private String customerUserId;

		@JsonProperty("guest_token")
		private String guestToken;

		@JsonProperty("app_version_name")
		private String appVersionName;

		@JsonProperty("eventName")
		private String eventName;

		@JsonProperty("eventValue")
		private String eventValue;

		@JsonProperty("eventCurrency")
		private String eventCurrency;

		@JsonProperty("ip")
		private String ip;

		@JsonProperty("eventTime")
		private String eventTime;

		@JsonProperty("bundleIdentifier")
		private String bundleIdentifier;

	}
}
