package su.reddot.domain.service.task;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.profile.welcome.*;
import su.reddot.domain.service.master.MasterServiceRequest;
import su.reddot.domain.service.social.SocialService;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledWelcomeNotificationRunner {
    private final NotificationRepository<Notification> notificationRepository;
    private final ApplicationEventPublisher publisher;
    private final SocialService socialService;

    @Value("${notifications.welcome.initialDate}")
    private String initialDateInString;

    public synchronized void createUsePromocodeSecondDayNotification(int count){
        generateUsePromocodeNotification(1, UsePromocodeSecondDayNotification.class.getSimpleName(), count);
    }

    public synchronized void createUsePromocodeSixthDayNotification(int count){
        generateUsePromocodeNotification(5, UsePromocodeSixthDayNotification.class.getSimpleName(), count);
    }

    public synchronized void createUsePromocodeNinthDayNotification(int count){
        generateUsePromocodeNotification(8, UsePromocodeNinthDayNotification.class.getSimpleName(), count);
    }

    public synchronized void createWhyNeedLikeNotification(int count){
        generateWelcomeNotification(2, WhyNeedLikeNotification.class.getSimpleName(), count);
    }

    public synchronized void createSubscribeCelebritiesNotification(int count){
        //Соц. уведомления можно создавать только когда база к этому готова (есть необходимые показатели)
        if (!socialService.isReadyForSocialNotifications()) {
            log.info("База данных соц. аккаунтов не готова к рассылке соц. уведомлений");
            return;
        }
        generateWelcomeNotification(2, SubscribeCelebritiesNotification.class.getSimpleName(), count);
    }

    public synchronized void createHowToTakePhotoNotification(int count){
        generateWelcomeNotification(6, HowToTakePhotoNotification.class.getSimpleName(), count);
    }

    public synchronized void createHowToUseBargainNotification(int count){
        generateWelcomeNotification(4, HowToUseBargainNotification.class.getSimpleName(), count);
    }

    public synchronized void createWhatIsBeegzNotification(int count){
        generateWelcomeNotification(7, WhatIsBeegzNotification.class.getSimpleName(), count);
    }

    public synchronized void createWhatIsConciergeNotification(int count){
        generateWelcomeNotification(6, WhatIsConciergeNotification.class.getSimpleName(), count);
    }

    public synchronized void createWhyNeedAuthenticationNotification(int count){
        generateWelcomeNotification(3, WhyNeedAuthenticationNotification.class.getSimpleName(), count);
    }

    private void generateWelcomeNotification(int daysCount, @NonNull String welcomeNotificationDtype, int limit){
        ZonedDateTime initialTime = ZonedDateTime.parse(initialDateInString);
        List<Long> userIds = notificationRepository.findUserIdsWhoNeverGotNotificationTypeAndLastSomeDaysAfterInstall(welcomeNotificationDtype, ZonedDateTime.now().minusDays(daysCount), initialTime, limit);
        List<String> guestTokens = notificationRepository.findGuestTokensWhoNeverGotNotificationTypeBefore(welcomeNotificationDtype, ZonedDateTime.now().minusDays(daysCount), initialTime, limit);
        createWelcomeNotifications(welcomeNotificationDtype, userIds, guestTokens);
    }

    private void generateUsePromocodeNotification(int daysCount, @NonNull String promocodeNotificationDtype, int limit){
        ZonedDateTime initialTime = ZonedDateTime.parse(initialDateInString);
        List<Long> userIds = new ArrayList<>(notificationRepository.findUsersWhoRegiseredAndDidntHaveOrders(promocodeNotificationDtype, ZonedDateTime.now().minusDays(daysCount), initialTime, limit));
        List<String> guestTokens = notificationRepository.findGuestTokensWhoNeverGotNotificationTypeAndNoOrderBefore(promocodeNotificationDtype, ZonedDateTime.now().minusDays(daysCount), initialTime, limit);
        createWelcomeNotifications(promocodeNotificationDtype, userIds, guestTokens);
    }

    private void createWelcomeNotifications(String notification, List<Long> userIds, List<String> guestTokens) {
        if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(guestTokens)) return;
        MasterServiceRequest request = new MasterServiceRequest()
                .setUrl("/api/v2/master/notification/createWelcomeNotifications");
        request.addHttpParam("userIds", userIds);
        request.addHttpParam("guestTokens", guestTokens);
        request.addHttpParam("notification", notification);
        publisher.publishEvent(request);
    }
}
