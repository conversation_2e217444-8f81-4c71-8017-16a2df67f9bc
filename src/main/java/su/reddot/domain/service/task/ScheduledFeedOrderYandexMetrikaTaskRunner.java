package su.reddot.domain.service.task;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import su.reddot.domain.service.feed.order.FeedOrderYandexMetrikaCsvExportProcessor;

import java.util.UUID;

@Component
@ConditionalOnExpression("${app.feed-db.enabled} and ${app.feed-db.export.enabled} and ${app.feed-db.export.order.csv.yandex-metrika.enabled}")
@RequiredArgsConstructor
@Slf4j
public class ScheduledFeedOrderYandexMetrikaTaskRunner {

    private final FeedOrderYandexMetrikaCsvExportProcessor feedOrderYandexMetrikaCsvExportProcessor;

    @Scheduled(cron = "${app.feed-db.export.order.csv.yandex-metrika.cron}")
    public void runExportFeedOrderCsvExport() {
        UUID uuid = UUID.randomUUID();
        log.info("task:runExportFeedOrderCsvExport({})", uuid);
        feedOrderYandexMetrikaCsvExportProcessor.processCsvGeneration(uuid);
        log.info("end task:runExportFeedOrderCsvExport({})", uuid);

    }
}
