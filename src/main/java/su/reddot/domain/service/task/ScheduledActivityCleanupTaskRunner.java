package su.reddot.domain.service.task;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.activity.ActivityRepository;
import su.reddot.domain.model.activity.product.ViewProductActivity;

import java.util.UUID;

@Component
@Slf4j
@RequiredArgsConstructor
public class ScheduledActivityCleanupTaskRunner {

    private final ActivityRepository<ViewProductActivity> activityRepository;

    @Value("${app.activity-table-cleanup.view-product-activity.limit-per-user}")
    private Integer viewActivityLimitPerUser;

    @Value("${app.activity-table-cleanup.view-product-activity.task.clean-by-guest-token.guest-token-page-size}")
    private Integer guestTokenPageSize;
    @Value("${app.activity-table-cleanup.view-product-activity.task.clean-by-user-id.user-id-page-size}")
    private Integer userIdPageSize;

    @Scheduled(cron = "${app.activity-table-cleanup.view-product-activity.task.clean-by-guest-token.cron}")
    @Timed(value = "ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByGuestToken")
    public void cleanupViewProductActivityByGuestToken() {
        String uuid = UUID.randomUUID().toString();
        log.info("task:ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByGuestToken({})", uuid);
        Page<String> guestTokenPage = activityRepository.getGuestTokenPage(PageRequest.of(0, guestTokenPageSize));
        Long totalDeletedRows = 0L;
        Long pageNumber = 0L;
        while (pageNumber < guestTokenPage.getTotalPages()) {
            Integer deletedRows = activityRepository
                    .deleteViewProductByGuestTokenByPage(pageNumber, guestTokenPageSize, viewActivityLimitPerUser);
            totalDeletedRows += deletedRows;
            pageNumber++;
            log.info("task:ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByGuestToken({}) page {}/{}",
                    uuid,
                    pageNumber,
                    guestTokenPage.getTotalPages());
        }
        log.info("end task:ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByGuestToken({}) total deleted rows {}", uuid, totalDeletedRows);
    }

    @Scheduled(cron = "${app.activity-table-cleanup.view-product-activity.task.clean-by-user-id.cron}")
    @Timed(value = "ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByUserId")
    public void cleanupViewProductActivityByUserId() {
        String uuid = UUID.randomUUID().toString();
        log.info("task:ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByUserId({})", uuid);
        Page<Long> userIdPage = null;
        Long totalDeletedRows = 0L;
        while (userIdPage == null || userIdPage.hasNext()) {
            Pageable pageRequest = userIdPage != null ?
                    userIdPage.nextPageable() :
                    PageRequest.of(0, userIdPageSize);

            userIdPage = activityRepository.getUserIdPage(pageRequest);
            Integer deletedRows = activityRepository
                    .deleteViewProductsByUserIdWhereGuestTokenIsNull(userIdPage.getContent(), viewActivityLimitPerUser);
            totalDeletedRows += deletedRows;
            log.info("task:ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByUserId({}) page {}/{}",
                    uuid,
                    userIdPage.getNumber(),
                    userIdPage.getTotalPages());
        }
        log.info("end task:ScheduledActivityCleanupTaskRunner.cleanupViewProductActivityByUserId({}) total deleted rows {}", uuid, totalDeletedRows);
    }

}
