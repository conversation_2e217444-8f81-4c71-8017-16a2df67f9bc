package su.reddot.domain.service.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import su.reddot.infrastructure.logistic.LogisticsInfoService;

@Component
@RequiredArgsConstructor
@Slf4j
public class ScheduledAutoPickupRunner {

    private final LogisticsInfoService logisticsInfoService;

    @Scheduled(cron = "${logistics.job.auto-pickup-runner.cron}", zone = "Europe/Moscow")
    public void processOrdersPickup() {
        log.info("task:ScheduledAutoPickupRunner.processOrdersPickup");
        long processed = logisticsInfoService.processOrdersPickup().size();
        log.info("end task:ScheduledAutoPickupRunner.processOrdersPickup ({} orders pickup processed)", processed);
    }

}
