package su.reddot.domain.service.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@ConditionalOnProperty("app.bargain.24h-buyer-notifications-task.enabled")
@RequiredArgsConstructor
@Slf4j
public class ScheduledBargain24hBuyerNotificationsTaskRunner {
    private final ScheduledBargainRunner scheduledBargainRunner;

    @Value("${app.bargain.24h-buyer-notifications-task.count}")
    private int count;

    @Scheduled(fixedDelayString = "${app.bargain.24h-buyer-notifications-task.delay-millis}")
    public void run24hBuyerNotificationsTask() {
        String uuid = UUID.randomUUID().toString();
        log.info("task:run24hBuyerNotificationsTask({})", uuid);
        scheduledBargainRunner.create24hNewBuyerOfferNotifications(count);
        log.info("end task:run24hBuyerNotificationsTask({})", uuid);
    }
}
