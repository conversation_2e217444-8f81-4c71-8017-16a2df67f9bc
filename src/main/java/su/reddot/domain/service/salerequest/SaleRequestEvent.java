package su.reddot.domain.service.salerequest;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.event.OskellyEvent;
import su.reddot.domain.service.salerequest.model.SaleRequestState;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ToString(callSuper = true)
public class SaleRequestEvent extends OskellyEvent {

    private long saleRequestId;
    private long initiatorUserId;
    private long ownerUserId;
    private SaleRequestState state;
    private String contactName;
    private String contactPhone;
    private long productsCount;
    private List<String> imageUrls;
    private String comments;
    private SaleRequestSource source;
    private Long orderId;
    private Long bitrixDealId;

    @Override
    public String getUniqueEntityId() {
        return getClass().getSimpleName() + "_" + saleRequestId + "_" + state;
    }
}