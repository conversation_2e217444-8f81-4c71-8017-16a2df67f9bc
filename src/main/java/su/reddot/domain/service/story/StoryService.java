package su.reddot.domain.service.story;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.notification.story.CartProductStoryNotification;
import su.reddot.domain.model.notification.story.LikedProductStoryNotification;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.notification.amqp.NotificationEvent;
import su.reddot.domain.service.notification.amqp.NotificationEventService;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

import static su.reddot.domain.model.notification.story.StoryNotification.IMAGE_PARAM;
import static su.reddot.domain.model.notification.story.StoryNotification.SLIDE_ID_PARAM;
import static su.reddot.domain.service.notification.amqp.NotificationEventConverter.GUEST_TOKEN_PARAM;
import static su.reddot.domain.service.notification.amqp.NotificationEventConverter.PRODUCT_ID_PARAM;

@Service
@Slf4j
@RequiredArgsConstructor
public class StoryService {

    private final LikeService likeService;

    private final NotificationEventService notificationEventService;

    private final ProductRepository productRepository;

    private final OrderRepository orderRepository;

    private final ObjectMapper objectMapper;

    @Value("${spring.rabbitmq.enabled}")
    private boolean rabbitmqEnabled;

    public void process(StoryPublishedEvent event) {
        // cгенерированные оповещения отправляются в очередь оповещений.
        // Если очередь выключена, то и обрабатывать событие нет смысла
        if (!rabbitmqEnabled) {
            log.warn("RabbitMQ disabled! Unable to publish notifications!");
            return;
        }
        if (StoryStatus.ACTIVE.name().equals(event.getStatus())) {
            if (CollectionUtils.isNotEmpty(event.getProductIds())) {
                event.getProductIds().forEach(productId -> {
                    Optional<Product> foundProduct = findProduct(productId);
                    if (foundProduct.isPresent()) {
                        findLikesAndSendNotificationEvents(foundProduct.get(), event);
                        findCartAndCreateNotificationEvents(foundProduct.get(), event);
                    } else {
                        log.debug("Empty product id given. Skipping...");
                    }
                });
            } else {
                log.debug("Published story with id {} has no products. Skipping...", event.getSlideId());
            }
        } else {
            log.debug("Published story with id {} and status {} is not in active status. Skipping...",
                    event.getSlideId(), event.getStatus());
        }
    }

    private Optional<Product> findProduct(Long productId) {
        if (productId != null) {
            Optional<Product> foundProduct = productRepository.findById(productId);
            if (!foundProduct.isPresent()) {
                log.debug("Failed to find product with id {}", productId);
                return Optional.empty();
            }
            if (!ProductState.PUBLISHED.equals(foundProduct.get().getProductState())) {
                log.debug("Product with id {} is not in PUBLISHED state. Actual state is {}. Skipping...",
                        productId, foundProduct.get().getProductState());
                return Optional.empty();
            }
            return foundProduct;
        }
        return Optional.empty();
    }

    private void findLikesAndSendNotificationEvents(Product product, StoryPublishedEvent event) {
        log.debug("Finding likes for product with id {}...", product.getId());
        List<User> usersWhoLikeProduct = likeService.getUsersWhoLikeProduct(product);
        if (CollectionUtils.isEmpty(usersWhoLikeProduct)) {
            log.debug("There are no users liked product with id {}. Skipping...", product.getId());
            return;
        }
        usersWhoLikeProduct.stream()
                           .map(user -> createLikedProductStoryNotificationEvent(user, product, event))
                           .forEach(notificationEventService::publish);
    }

    private void findCartAndCreateNotificationEvents(Product product, StoryPublishedEvent event) {
        log.debug("Finding carts for product with id {}...", product.getId());
        List<Order> carts = orderRepository.findCartsWithProductWithAvailablePositionsUpdatedLaterThan(
                product.getId(), ZonedDateTime.now().minusDays(10));
        if (CollectionUtils.isEmpty(carts)) {
            log.debug("There are no recently updated carts with product with id {}. Skipping...", product.getId());
        }

        carts.stream()
             .map(cart ->
                     createCartProductStoryNotificationEvent(cart.getBuyer(), cart.getGuestToken(), product, event))
             .forEach(notificationEventService::publish);
    }

    private NotificationEvent createLikedProductStoryNotificationEvent(User user, Product product,
                                                                       StoryPublishedEvent event) {
        NotificationEvent notificationEvent = new NotificationEvent();
        notificationEvent.setType(LikedProductStoryNotification.class.getSimpleName());

        if (user != null) {
            notificationEvent.setUserId(user.getId());
        }
        ObjectNode node = objectMapper.createObjectNode();
        if (StringUtils.isNotBlank(event.getSlideId())) {
            node.put(SLIDE_ID_PARAM, event.getSlideId());
        }
        if (StringUtils.isNotBlank(event.getImage())) {
            node.put(IMAGE_PARAM, event.getImage());
        }
        if (product != null && product.getId() != null) {
            node.put(PRODUCT_ID_PARAM, product.getId());
        }
        notificationEvent.setParams(node);

        return notificationEvent;
    }

    private NotificationEvent createCartProductStoryNotificationEvent(User user, String guestToken, Product product,
                                                                      StoryPublishedEvent event) {
        NotificationEvent notificationEvent = new NotificationEvent();
        notificationEvent.setType(CartProductStoryNotification.class.getSimpleName());

        if (user != null) {
            notificationEvent.setUserId(user.getId());
        }

        ObjectNode node = objectMapper.createObjectNode();
        if (StringUtils.isNotBlank(event.getSlideId())) {
            node.put(SLIDE_ID_PARAM, event.getSlideId());
        }
        if (StringUtils.isNotBlank(event.getImage())) {
            node.put(IMAGE_PARAM, event.getImage());
        }
        if (StringUtils.isNotBlank(guestToken)) {
            node.put(GUEST_TOKEN_PARAM, guestToken);
        }
        if (product != null && product.getId() != null) {
            node.put(PRODUCT_ID_PARAM, product.getId());
        }
        notificationEvent.setParams(node);

        return notificationEvent;
    }

}
