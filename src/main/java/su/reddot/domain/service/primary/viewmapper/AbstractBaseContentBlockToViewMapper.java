package su.reddot.domain.service.primary.viewmapper;

import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.service.metric.MicrometerService;

public abstract class AbstractBaseContentBlockToViewMapper extends AbstractContentBlockToViewMapper<BaseContentBlock> {

    public AbstractBaseContentBlockToViewMapper(MicrometerService micrometerService) {
        super(micrometerService);
    }

    @Override
    protected BaseContentBlock castToTargetClass(BaseContentBlock fromBlock) {
        return fromBlock;
    }
}
