package su.reddot.domain.service.primary.viewmapper;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.ContentBlockType;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageProductDTO;
import su.reddot.domain.service.dto.primary.converter.PrimaryPageProductDtoConverter;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.primary.interfaces.PrimaryServiceReactor;
import su.reddot.domain.service.topn.TopNService;

import java.util.Collections;
import java.util.List;

@Component
public class TopNProductsBlockToViewMapper extends AbstractBaseContentBlockToViewMapper {

    private final TopNService topNService;
    private final PrimaryPageProductDtoConverter primaryPageProductDtoConverter;
    private final MessageSourceAccessor messageSourceAccessor;

    @Value("${app.top-n-product.enabled:false}")
    private boolean topNProductEnabled;

    public TopNProductsBlockToViewMapper(MicrometerService micrometerService,
                                         TopNService topNService,
                                         PrimaryPageProductDtoConverter primaryPageProductDtoConverter,
                                         MessageSourceAccessor messageSourceAccessor) {
        super(micrometerService);
        this.topNService = topNService;
        this.primaryPageProductDtoConverter = primaryPageProductDtoConverter;
        this.messageSourceAccessor = messageSourceAccessor;
    }

    @Override
    public PrimaryServiceReactor.GetPrimaryPageDataContext specificSubcontext(PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        return context.clone();
    }

    @Override
    public boolean isApplicable(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        return ContentBlockType.TOPN_PRODUCTS.equals(fromBlock.getType()) && topNProductEnabled;
    }

    @Override
    protected List<PrimaryContentDTO<?>> mapBlockToView(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context, PrimaryServiceReactor.GetPrimaryPageDataState state) {
        List<ProductDTO> recommendedProducts = topNService.getTopNProducts(
                context.getType().getConfig().getCategory(),
                context.getType().getConfig().getNewResaleValue()
        );
        List<PrimaryPageProductDTO> primaryPageProductDTOList = primaryPageProductDtoConverter.convertFrom(recommendedProducts);

        PrimaryContentDTO<PrimaryPageProductDTO> result = new PrimaryContentDTO<PrimaryPageProductDTO>()
                .setTitle(messageSourceAccessor.getMessage("service.PrimaryServiceImpl.TopNProducts"))
                .setContentType(PrimaryContentType.H_SELECTION)
                .setId("topn_products")
                .setContentList(primaryPageProductDTOList);
        return Collections.singletonList(result);
    }
}
