package su.reddot.domain.service.primary.viewmapper.bannersetting.filtration;

import org.springframework.stereotype.Component;
import su.reddot.domain.model.adminpanel.v2.common.BannerSettingFiltrationContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.ContentBlockType;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.primary.interfaces.PrimaryServiceReactor;
import su.reddot.presentation.adminpanel.v2.dto.converter.FilterContentDtoConverter;

import java.util.List;

@Component
public class ProductRequestFiltrationBlockToViewMapper extends AbstractFiltreationBlockToViewMapper {

    public ProductRequestFiltrationBlockToViewMapper(MicrometerService micrometerService, FilterContentDtoConverter filterContentDtoConverter) {
        super(micrometerService, filterContentDtoConverter);
    }

    @Override
    public boolean isApplicable(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        return ContentBlockType.PRODUCT_REQUEST_FILTRATION.equals(fromBlock.getType());
    }

    @Override
    protected List<PrimaryContentDTO<?>> mapBlockToView(BannerSettingFiltrationContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context, PrimaryServiceReactor.GetPrimaryPageDataState state) {
        return getFiltrationViewBlock(PrimaryContentType.PRODUCT_REQUEST_FILTERABLE_ITEMS, fromBlock, context);
    }
}
