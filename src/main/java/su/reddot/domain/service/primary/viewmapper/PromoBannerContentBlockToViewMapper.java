package su.reddot.domain.service.primary.viewmapper;

import org.springframework.stereotype.Component;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageSettingContainer;
import su.reddot.domain.model.adminpanel.v2.PromoBanner;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.ContentBlockType;
import su.reddot.domain.model.adminpanel.v2.common.PromoBannerContentBlock;
import su.reddot.domain.service.adminpanel.v2.PrimaryPageSettingService;
import su.reddot.domain.service.adminpanel.v2.PromoBannerService;
import su.reddot.domain.service.dto.primary.AdditionalBannerDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.primary.interfaces.PrimaryServiceReactor;
import su.reddot.presentation.adminpanel.v2.dto.segment.BaseContentBlockSegmentDto;
import su.reddot.presentation.adminpanel.v2.dto.segment.PromoBannerContentBlockSegmentDto;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
public class PromoBannerContentBlockToViewMapper extends AbstractBaseContentBlockToViewMapper {

    private final PrimaryPageSettingService settingService;
    private final PromoBannerService promoBannerService;

    public PromoBannerContentBlockToViewMapper(MicrometerService micrometerService,
                                               PrimaryPageSettingService settingService,
                                               PromoBannerService promoBannerService) {
        super(micrometerService);
        this.settingService = settingService;
        this.promoBannerService = promoBannerService;
    }

    @Override
    public boolean isApplicable(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        return ContentBlockType.PROMO_BANNER.equals(fromBlock.getType()) && context.isPromoBannerEnable() && !context.isNewHomeDesignEnabled();
    }

    @Override
    protected List<PrimaryContentDTO<?>> mapBlockToView(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context, PrimaryServiceReactor.GetPrimaryPageDataState state) {
        PrimaryPageSettingContainer primaryPageSettingContainer = settingService.getMainSettingContainer();
        PromoBanner promoBanner = promoBannerService.getPromoBanner(primaryPageSettingContainer.getPromoBannerId());

        PromoBannerContentBlock promoBannerContentBlock = new PromoBannerContentBlock();
        promoBannerContentBlock.setSegments(promoBanner.getSegments());
        Optional<? extends PromoBannerContentBlockSegmentDto> targetSegment =
                promoBannerContentBlock.getSegmentByDTOs(context.getSegmentDtoList());

        if (!targetSegment.isPresent() && promoBannerContentBlock.getSegments() != null) {
            return Collections.emptyList();
        }

        String description = targetSegment.map(BaseContentBlockSegmentDto::getDescription).orElse(promoBanner.getDescription());
        String promoCode = targetSegment.map(PromoBannerContentBlockSegmentDto::getPromoCode).orElse(null);

        return Collections.singletonList(new PrimaryContentDTO<AdditionalBannerDTO>()
                .setContentType(PrimaryContentType.ADV_BANNER)
                .setId(PrimaryContentType.ADV_BANNER.name())
                .setContent(new AdditionalBannerDTO(description, promoCode)));
    }
}
