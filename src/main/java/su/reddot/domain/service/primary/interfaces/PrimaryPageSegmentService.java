package su.reddot.domain.service.primary.interfaces;

import su.reddot.domain.model.adminpanel.v2.common.ContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.TitledDisabledContentBlock;
import su.reddot.domain.service.dto.segment.SegmentDTO;
import su.reddot.presentation.adminpanel.v2.dto.segment.property.BaseSegmentPropertiesDto;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface PrimaryPageSegmentService {
    Collection<String> getContentIds(List<SegmentDTO> userSegments, ContentBlock<?> contentBlock);
    String getTitle(List<SegmentDTO> userSegments, TitledDisabledContentBlock<BaseSegmentPropertiesDto> contentBlock);
}
