package su.reddot.domain.service.util.qdsl;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.NumberPath;

import java.util.Collection;

@BBAppender(forClass = NumberPath.class)
public class NumericAppender implements BooleanBuilderPredicate {
    @Override
    public Predicate predicate(Path pathFieldValue, Object queryFieldValue, CompareType compareType) {
        NumberPath numberPath = (NumberPath) pathFieldValue;

        if (CompareType.NOT_NULL == compareType) {
            if ((Boolean) queryFieldValue) {
                return numberPath.isNotNull();
            } else {
                return numberPath.isNull();
            }
        }

        if (queryFieldValue instanceof Collection) {
            if (CompareType.NOT_CONTAINS == compareType) {
                BooleanExpression notIn = numberPath.notIn((Collection) queryFieldValue);
                BooleanExpression isNull = numberPath.isNull();

                return notIn.or(isNull);
            }

            return numberPath.in((Collection) queryFieldValue);
        }

        if (compareType != null) {
            switch (compareType) {
                case FROM:
                    return numberPath.goe((Number) queryFieldValue);
                case TO:
                    return numberPath.loe((Number) queryFieldValue);
                //строковое вхождение
                case CONTAINS:
                    return numberPath.stringValue().contains(String.valueOf(queryFieldValue));
            }
            return numberPath.eq(queryFieldValue);
        }

        return numberPath.eq(queryFieldValue);
    }
}
