package su.reddot.domain.service.util;

import lombok.Getter;
import lombok.Setter;
import su.reddot.domain.service.adminpanel.domain.PredicateType;
import su.reddot.domain.service.util.qdsl.CompareType;

import java.lang.reflect.Field;

@Getter
@Setter
public class EntityFieldPredicate extends FieldPredicate {
    //EntityPathBase
    private Object object;
    private Field field;
    private CompareType compareType;

    public EntityFieldPredicate(Object object, Field field, CompareType compareType, PredicateType predicateType) {
        super(predicateType);
        this.object = object;
        this.field = field;
        this.compareType = compareType;
    }
}
