package su.reddot.domain.service.opencv;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import ru.oskelly.common.messaging.messages.opencv.SaveOpenCvProductCommand;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.service.dto.opencv.ProductToOpenCvTopicDTO;
import su.reddot.domain.service.externalcatalog.AbstractProductToKafkaCompletelySender;
import su.reddot.domain.service.externalcatalog.ProductFiltersToExternalCatalogManager;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogExecutionIdGenerator;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogRevisionGenerator;
import su.reddot.domain.service.integration.opencv.ProductToOpenCvMapper;
import su.reddot.domain.service.kafka.KafkaSenderService;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.util.Collections;
import java.util.List;

@Slf4j
public abstract class ProductsToOpenCvCompletelySender
        extends AbstractProductToKafkaCompletelySender<ProductToOpenCvTopicDTO, ProductToOpenCvMapper.OpenCvListInfo, SaveOpenCvProductCommand> {
    protected final ProductToOpenCvMapper productMapper;

    public ProductsToOpenCvCompletelySender(
            ProductToOpenCvMapper productMapper,
            MicrometerService micrometerService,
            CallInTransaction callInTransaction,
            KafkaSenderService kafkaSenderService,
            ProductFiltersToExternalCatalogManager filtersManager,
            ProductsToExternalCatalogRevisionGenerator revisionGenerator,
            ProductsToExternalCatalogExecutionIdGenerator executionIdGenerator,
            JPAQueryFactory jpaQueryFactory,
            @Value("${app.kafka.updating-opencv-commands-topic.name}") String completelyUpdatingTopicName,
            @Value("${app.products-to-external-catalog-sender.batch-size:1000}") int batchSize,
            @Value("${app.products-to-external-catalog-sender.cancel-deletion-errors-count-threshold:0}") long cancelDeletionErrorsCountThreshold,
            @Value("${app.products-to-external-catalog-sender.abort-sending-errors-count-threshold:10}") long abortSendingErrorsCountThreshold
    ) {
        super(
                callInTransaction, micrometerService, kafkaSenderService, filtersManager, revisionGenerator,
                executionIdGenerator, jpaQueryFactory, completelyUpdatingTopicName,
                batchSize, cancelDeletionErrorsCountThreshold, abortSendingErrorsCountThreshold
        );
        this.productMapper = productMapper;
    }

    @Override
    @Async("productsSenderTaskExecutor")
    @Timed("ProductsToOpenCvCompletelySender.sendProducts")
    public void sendProductsCompletelyAsync(List<Long> productIds) {
        sendProductsCompletely(productIds);
    }

    @Override
    protected List<Product> getProducts(long totalCount, BooleanBuilder predicate, boolean noLimit) {
        QProduct qProduct = QProduct.product;
        List<Product> products;

        long startedAt = System.currentTimeMillis();

        log.debug("{}: Fetch count took {} ms", getMessagePrefix(), System.currentTimeMillis() - startedAt);

        long limit = noLimit ? totalCount : batchSize;
        if (totalCount > 0) {
            startedAt = System.currentTimeMillis();

            products = jpaQueryFactory
                    .from(qProduct)
                    .where(predicate)
                    .innerJoin(qProduct.brand).fetchJoin()
                    .leftJoin(qProduct.productModel).fetchJoin()
                    .offset(0)
                    .limit(limit)
                    .orderBy(qProduct.id.asc())
                    .select(qProduct)
                    .distinct()
                    .fetch();

            log.debug("{}: Fetch products took {} ms", getMessagePrefix(), System.currentTimeMillis() - startedAt);
        } else {
            products = Collections.emptyList();
        }

        return products;
    }

    @Override
    protected SaveOpenCvProductCommand createSaveCommand(ProductToOpenCvTopicDTO dto, long executionId) {
        return new SaveOpenCvProductCommand(dto, executionId);
    }

    @Override
    protected void executeAfterSending(long errorsCount, long initialRevisionId, long executionId) {
    }

    @Override
    protected ProductToOpenCvTopicDTO getProductDto(Product p, long revisionId,
                                                    ProductToOpenCvMapper.OpenCvListInfo listInfo) {
        return productMapper.toProductToOpenCvTopicDTO(p, revisionId, listInfo);
    }
}
