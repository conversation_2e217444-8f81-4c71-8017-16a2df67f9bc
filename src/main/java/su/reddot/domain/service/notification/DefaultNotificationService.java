package su.reddot.domain.service.notification;

import com.google.common.base.Strings;
import io.micrometer.core.annotation.Timed;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reflections.Reflections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.JpaSort;
import org.springframework.data.jpa.repository.JpaContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.activity.ActivityRepository;
import su.reddot.domain.dao.bargain.BargainRecordRepository;
import su.reddot.domain.dao.bargain.BargainRepository;
import su.reddot.domain.dao.notification.NotificationGroupRepository;
import su.reddot.domain.dao.notification.NotificationGroupUserBindingRepository;
import su.reddot.domain.dao.notification.NotificationRepository;
import su.reddot.domain.dao.notificationDelivery.NotificationDeliveryRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.ForbiddenException;
import su.reddot.domain.exception.NotFoundException;
import su.reddot.domain.exception.NotificationException;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.exception.UserNotFoundException;
import su.reddot.domain.model.adminalert.AdminAlert;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.notification.Notification;
import su.reddot.domain.model.notification.NotificationGroup;
import su.reddot.domain.model.notification.NotificationGroupDtypeBinding;
import su.reddot.domain.model.notification.NotificationGroupUserBinding;
import su.reddot.domain.model.notification.UserSubscriptionType;
import su.reddot.domain.model.notification.bargain.BargainNotification;
import su.reddot.domain.model.notification.bargain.BargainNotificationDTO;
import su.reddot.domain.model.notification.catalog.LastChance1000Notification;
import su.reddot.domain.model.notification.catalog.NewArrivalsNotification;
import su.reddot.domain.model.notification.catalog.Promo24HoursLeftNotification;
import su.reddot.domain.model.notification.catalog.YouWillLikeItNotification;
import su.reddot.domain.model.notification.comment.CommentNotification;
import su.reddot.domain.model.notification.comment.NewCommentNotification;
import su.reddot.domain.model.notification.content.ContentNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToBuyerNeedAgentReportNotification;
import su.reddot.domain.model.notification.order.OrderDeliveredToBuyerRequestConfirmationNotification;
import su.reddot.domain.model.notification.order.OrderNeedConfirmationNotification;
import su.reddot.domain.model.notification.order.OrderNotification;
import su.reddot.domain.model.notification.product.PrivateSellerProductPublishedNotification;
import su.reddot.domain.model.notification.product.PrivateSellerProductsPublishedNotification;
import su.reddot.domain.model.notification.product.ProductNotification;
import su.reddot.domain.model.notification.product.SetLowerPriceForSeveralProductsNotification;
import su.reddot.domain.model.notification.product.SetLowerPriceNotification;
import su.reddot.domain.model.notification.product.wishlist.CartItemInBoutiqueNotification;
import su.reddot.domain.model.notification.product.wishlist.WishlistItemInBoutiqueNotification;
import su.reddot.domain.model.notification.productRequest.CreatePublicationNotification;
import su.reddot.domain.model.notification.productRequest.InformAboutCountOfUserWhoCreateProductRequestNotification;
import su.reddot.domain.model.notification.productRequest.InformAboutProductRequestServiceNotification;
import su.reddot.domain.model.notification.productRequest.NeedPublishBuyerProductRequestNotification;
import su.reddot.domain.model.notification.productRequest.NoResponseBuyerProductRequestNotification;
import su.reddot.domain.model.notification.productResponse.MotivateCreateProductResponseNotification;
import su.reddot.domain.model.notification.profile.AddBirthdateAndAvatarNotification;
import su.reddot.domain.model.notification.profile.AddBrandLikeNotification;
import su.reddot.domain.model.notification.profile.NoActivityNotification;
import su.reddot.domain.model.notification.profile.ProfileBirthdateNotification;
import su.reddot.domain.model.notification.profile.RegisterNotification;
import su.reddot.domain.model.notification.profile.welcome.HowItWorksNotification;
import su.reddot.domain.model.notification.profile.welcome.HowToTakePhotoNotification;
import su.reddot.domain.model.notification.profile.welcome.HowToUseBargainNotification;
import su.reddot.domain.model.notification.profile.welcome.SubscribeCelebritiesNotification;
import su.reddot.domain.model.notification.profile.welcome.UsePromocodeNinthDayNotification;
import su.reddot.domain.model.notification.profile.welcome.UsePromocodeSecondDayNotification;
import su.reddot.domain.model.notification.profile.welcome.UsePromocodeSixthDayNotification;
import su.reddot.domain.model.notification.profile.welcome.WhatIsBeegzNotification;
import su.reddot.domain.model.notification.profile.welcome.WhatIsConciergeNotification;
import su.reddot.domain.model.notification.profile.welcome.WhyNeedAuthenticationNotification;
import su.reddot.domain.model.notification.profile.welcome.WhyNeedLikeNotification;
import su.reddot.domain.model.notification.streamsale.StreamNotification;
import su.reddot.domain.model.notificationDelivery.NotificationDelivery;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItemLocation;
import su.reddot.domain.model.product.publication.CompletePublicationNotification;
import su.reddot.domain.model.product.publication.CompletePublicationNotificationRangeDay;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminalert.AdminAlertService;
import su.reddot.domain.service.boutique.BoutiqueService;
import su.reddot.domain.service.device.DeviceService;
import su.reddot.domain.service.dto.NotificationDeliveryResult;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.dto.notification.CompletePublicationNotificationDTO;
import su.reddot.domain.service.dto.notification.MindboxNotificationDTO;
import su.reddot.domain.service.dto.notification.NotificationDTO;
import su.reddot.domain.service.dto.notification.NotificationGroupDTO;
import su.reddot.domain.service.dto.notification.TextNotificationDTO;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.like.LikeService;
import su.reddot.domain.service.notification.converter.NotificationConverter;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.delivery.DeliveryService;
import su.reddot.infrastructure.notificationDelivery.NotificationTransport;
import su.reddot.infrastructure.security.SecurityService;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAmount;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static su.reddot.infrastructure.util.Utils.isTrue;
import static su.reddot.infrastructure.util.Utils.notNullOrEmpty;
import static su.reddot.infrastructure.util.Utils.notNullOrZero;

@Service
@RequiredArgsConstructor
@Slf4j
public class DefaultNotificationService implements NotificationService{

	@Value("${promocode.oskelly5.enabled}")
	private boolean promocodeOskelly5Enabled;

	@Value("${app.product-publication-notification-create.interval.default}")
	private String productPublicationSaveDefaultInterval;

	@Value("${app.product-publication-notification-create.interval.select}")
	private String productPublicationSaveSelectInterval;

	@Value("${app.publication.min-price}")
	private int minPrice;

	private final NotificationRepository<Notification> notificationRepository;

	private final NotificationGroupRepository notificationGroupRepository;

	private final NotificationGroupUserBindingRepository notificationGroupUserBindingRepository;

	private final UserRepository userRepository;

	private final ActivityRepository activityRepository;

	private final NotificationDeliveryRepository notificationDeliveryRepository;
	private final ProductRepository productRepository;
	private final UserService userService;
	private final SecurityService securityService;
	private final JpaContext jpaContext;
	private final FollowingService followingService;
	private final Timer notificationDeltaTimer;
	private final BargainRepository bargainRepository;
	private final BargainRecordRepository bargainRecordRepository;
	private final MessageSourceAccessor messageSourceAccessor;
	private final NotificationProperties notificationProperties;
	private final AdminAlertService<AdminAlert> adminAlertService;
	private final DeviceService deviceService;
	private final DeliveryService deliveryService;
	private final OrderService orderService;
	private final LikeService likeService;

	@Setter
	@Autowired
	@Lazy
	private BoutiqueService boutiqueService;

	//Для запросов пользователей-инициаторов с доп. параметрами
	private UserService.UserRequest initiatorRequestWithIsFollowed = new UserService.UserRequest().setWithIsFollowed(true);

	//Если тип уведомления прописан в данной группе, то пуш-уведомления отправлять не следует
	private static Long NO_PUSH_NOTIFICATION_GROUP_ID = 4L;

	//Если пользователь выключил данную группу, то он выключил все пуши вообще, отправлять не следует
	private Map<UserSubscriptionType, Long> generalNotificationsGroups = new HashMap<>();

	@Setter
	@Autowired
	@Lazy
	private NotificationService self;

	@Setter
	@Autowired
	private List<NotificationProcessor> notificationProcessors;

	//Процессоры по типу уведомления. Для одного типа может быть несколько разных процессоров и один и тот же процессор может использоваться для разных типов.
	private Map<String, List<NotificationProcessor>> notificationProcessorsByTypes = new HashMap<>();

	@Autowired @Setter
	private List<NotificationConverter> notificationConverters;

	//Конвертеры по типу связанного объекта
	private Map<String, NotificationConverter> convertersByTargetObjectTypes = new HashMap<>();

	@Autowired @Setter
	private List<NotificationTransport> notificationTransports;

	//Доставщики уведомлений по каналу
	private Map<String, NotificationTransport> notificationTransportsByChannel = new HashMap<>();

	//Не используется
	//private List<NotificationProcessor> processors = new ArrayList<>();
	//Конвертеры по типу уведомления
	private Map<String, NotificationConverter> convertersByTypes = new HashMap<>();

	private NotificationConverter defaultConverter;

	//Группы уведомлений
	private List<NotificationGroup> notificationGroupsCache;

	//Уведомления, которые могут доставляться повторно, если действия по ним не выполнены
	private static final List<String> MULTIPLE_DELIVERY_NOTIFICATION_DTYPES = Arrays.asList(
			OrderNeedConfirmationNotification.class.getSimpleName(),
			OrderDeliveredToBuyerNeedAgentReportNotification.class.getSimpleName(),
			NewCommentNotification.class.getSimpleName(),
			CartItemInBoutiqueNotification.class.getSimpleName(),
			WishlistItemInBoutiqueNotification.class.getSimpleName(),
			CreatePublicationNotification.class.getSimpleName());

	//Уведомления, которые создаются в первую очередь после появления нового пользователя/гостя
	Class<? extends Notification>[] defaultNotificationTypes = new Class[] {HowItWorksNotification.class};

	//Уведомления, по которым не должно быть дубликатов для одного пользователя/гостя
	List<String> noDuplicatesNotificationTypes = Arrays.asList(
			AddBirthdateAndAvatarNotification.class.getSimpleName(),
			ProfileBirthdateNotification.class.getSimpleName(),
			AddBrandLikeNotification.class.getSimpleName(),
			HowItWorksNotification.class.getSimpleName(),
			RegisterNotification.class.getSimpleName(),
			SubscribeCelebritiesNotification.class.getSimpleName(),
			YouWillLikeItNotification.class.getSimpleName(),
			NewArrivalsNotification.class.getSimpleName(),
			Promo24HoursLeftNotification.class.getSimpleName(),
			LastChance1000Notification.class.getSimpleName(),
			HowToTakePhotoNotification.class.getSimpleName(),
			HowToUseBargainNotification.class.getSimpleName(),
			WhatIsBeegzNotification.class.getSimpleName(),
			WhatIsConciergeNotification.class.getSimpleName(),
			WhyNeedAuthenticationNotification.class.getSimpleName(),
			WhyNeedLikeNotification.class.getSimpleName(),
			UsePromocodeSecondDayNotification.class.getSimpleName(),
			UsePromocodeSixthDayNotification.class.getSimpleName(),
			UsePromocodeNinthDayNotification.class.getSimpleName()
	);

	List<String> noDuplicatesWithTransactionalIdNotificationTypes = Arrays.asList(
			ContentNotification.class.getSimpleName()
	);

	private final HashMap<String, Class> welcomeNotificationClasses = new HashMap<String, Class>(){{
		put(HowItWorksNotification.class.getSimpleName(), HowItWorksNotification.class);
		put(HowToTakePhotoNotification.class.getSimpleName(), HowToTakePhotoNotification.class);
		put(HowToUseBargainNotification.class.getSimpleName(), HowToUseBargainNotification.class);
		put(WhatIsBeegzNotification.class.getSimpleName(), WhatIsBeegzNotification.class);
		put(WhatIsConciergeNotification.class.getSimpleName(), WhatIsConciergeNotification.class);
		put(WhyNeedAuthenticationNotification.class.getSimpleName(), WhyNeedAuthenticationNotification.class);
		put(WhyNeedLikeNotification.class.getSimpleName(), WhyNeedLikeNotification.class);
		put(UsePromocodeSecondDayNotification.class.getSimpleName(), UsePromocodeSecondDayNotification.class);
		put(UsePromocodeSixthDayNotification.class.getSimpleName(), UsePromocodeSixthDayNotification.class);
		put(UsePromocodeNinthDayNotification.class.getSimpleName(), UsePromocodeNinthDayNotification.class);
		put(SubscribeCelebritiesNotification.class.getSimpleName(), SubscribeCelebritiesNotification.class);
	}};

	private Reflections notificationReflections;
	private Sort defaultSort;

	@Value("${app.notificationDelivery.threadPool}")
	private int notificationDeliveryThreadPool;
	@Value("${app.notificationDelivery.maxRetry}")
	private int maxRetry;

	private ExecutorService sendNotificationExecutorService;

	@PostConstruct
	public void init(){
		//Индексируем процессоры для более быстрого поиска
		reindexNotificationProcessors();

		//Индексируем конвертеры для более быстрого поиска
		reindexNotificationConverters();

		//Индексируем доставщиков уведомлений для более быстрого поиска
		reindexNotificationTransports();

		notificationReflections = new Reflections("su.reddot.domain.model.notification");

		sendNotificationExecutorService = Executors.newFixedThreadPool(notificationDeliveryThreadPool);


		List<NotificationGroup> notificationGroup = notificationGroupRepository.findGeneralGroupsForSubscriptionTypes(Arrays.asList(UserSubscriptionType.values()));
		notificationGroup.forEach(ng -> generalNotificationsGroups.put(ng.getSubscriptionType(), ng.getId()));
	}

	@PreDestroy
	public void shutdown() {
		sendNotificationExecutorService.shutdown();
	}

	//Индексирует процессоры для более быстрого поиска
	private void reindexNotificationProcessors(){
		Map<String, List<NotificationProcessor>> newNotificationProcessorsByTypes = new HashMap<>();
		for(NotificationProcessor notificationProcessor : notificationProcessors){
			Class<? extends Notification>[] supportedTypes = notificationProcessor.getSupportedTypes();
			if(supportedTypes == null) continue;
			for(Class<? extends Notification> supportedType : supportedTypes){
				String simpleName = supportedType.getSimpleName();
				if(!newNotificationProcessorsByTypes.containsKey(simpleName)) newNotificationProcessorsByTypes.put(simpleName, new ArrayList<>());
				newNotificationProcessorsByTypes.get(simpleName).add(notificationProcessor);
			}
		}
		notificationProcessorsByTypes = newNotificationProcessorsByTypes;
	}

	//Индексирует конвертеры для более быстрого поиска
	private void reindexNotificationConverters(){
		Map<String, NotificationConverter> newConvertersByTargetObjectTypes = new HashMap<>();
		Map<String, NotificationConverter> newConvertersByTypes = new HashMap<>();
		NotificationConverter newDefaultConverter = defaultConverter;

		for(NotificationConverter converter : notificationConverters){
			String[] targetObjectTypes = converter.getSupportedTargetObjectTypes();
			String[] types = converter.getSupportedTypes();
			if(targetObjectTypes == null && types == null){
				newDefaultConverter = converter;
				continue;
			}
			if(targetObjectTypes != null){
				for(String targetObjectType : targetObjectTypes){
					newConvertersByTargetObjectTypes.put(targetObjectType, converter);
				}
			}
			if(types != null){
				for(String type : types){
					newConvertersByTypes.put(type, converter);
				}
			}
		}
		convertersByTargetObjectTypes = newConvertersByTargetObjectTypes;
		convertersByTypes = newConvertersByTypes;
		defaultConverter = newDefaultConverter;
	}

	//Индексирует доставщиков уведомлений для более быстрого поиска
	private void reindexNotificationTransports(){
		Map<String, NotificationTransport> newNotificationTransportsByChannel = new HashMap<>();

		for(NotificationTransport transport : notificationTransports){
			newNotificationTransportsByChannel.put(transport.getChannel(), transport);
		}
		notificationTransportsByChannel = newNotificationTransportsByChannel;
	}

	@Override
	public Map<Long, Boolean> getDeliveryToBuyerConfirmByOrderIdsAsMap(List<Long> orderIds) {
		List<Notification> notifications = getNotificationsByOrderIds(orderIds, OrderDeliveredToBuyerRequestConfirmationNotification.class);
		Map<Long, Boolean> map = new HashMap<>();
		for (Notification notification : notifications) {
			map.computeIfAbsent(notification.getTargetObjectId(), b -> notification.isActionCompleted());
		}
		return map;
	}

	@Override
	public List<Notification> getNotificationsByOrderIds(List<Long> orderIds, Class<? extends Notification> notificationClass) {
		if (orderIds == null || orderIds.isEmpty()) return Collections.emptyList();
		return notificationRepository.findAllByOrdersAndDtype(orderIds, notificationClass.getSimpleName());
	}

	@Override
	public Long countUnreadNotificationsByUser(Long userId) {
		User user = userService.getUserById(userId).orElseThrow(() -> new IllegalArgumentException(messageSourceAccessor.getMessage("service.DefaultNotificationService.UserNotFound", new Object[]{userId})));
		try {
			List<Notification> notifications = notificationRepository.findTop100ByUserAndReadTimeIsNullOrderByCreateTimeDesc(user);
			Predicate<Notification> notificationPredicate = n -> (n.getType().equals(Notification.NotificationType.NOTIFICATION));
			return notifications.stream().filter(notificationPredicate).count();
		} catch (Exception e) {
			log.error("Notification failed: {}", e.getMessage());
		}

		return null;
	}

	@Override
	public Notification getOne(Long id){
		return notificationRepository.getOne(id);
	}

	@Override
	public Notification getNotification(Long id){
		return getNotification(id, true);
	}

	@Override
	public Notification getNotification(Long id, boolean checkNotifications){
		if(id == null) return null;

		User user = securityService.getCurrentAuthorizedUser();
		String guestToken = securityService.getGuestToken();
		if(checkNotifications) checkUserNotifications(user, guestToken, null);

		if(user == null && Strings.isNullOrEmpty(guestToken)) return null;
		Notification notification = notificationRepository.findById(id)
				.orElseThrow(() -> new NotFoundException(messageSourceAccessor.getMessage("exception.not-found.notification")));
		if(notification.getDeleteTime() != null) throw new NotFoundException(messageSourceAccessor.getMessage("exception.not-found.notification"));
		checkOwner(notification, user, guestToken);
		return notification;
	}

	@Override
	public NotificationDTO getNotificationDTO(Notification notification, NotificationListInfo notificationListInfo) {
		return getNotificationConverter(notification).getNotificationDTO(notification, notificationListInfo);
	}

	@Override
	public NotificationDTO getNotificationDTO(Notification notification) {
		return getNotificationDTO(notification, null) ;
	}

	protected NotificationConverter getNotificationConverter(Notification notification){
		//Пытаемся найти конвертер по типу уведомления
		if(convertersByTypes.containsKey(notification.getDtype())) return convertersByTypes.get(notification.getDtype());

		//Пытаемся найти конвертер по типу связанного объекта
		if(convertersByTargetObjectTypes.containsKey(notification.getTargetObjectType())) return convertersByTargetObjectTypes.get(notification.getTargetObjectType());

		//Если ничего не нашли, то возвращаем конвертер по умолчанию
		return defaultConverter;
	}

	@Override
	public NotificationDTO getNotificationDTO(Long id){
		return getNotificationDTO(getNotification(id), null);
	}

	@Override
	public NotificationDTO getNotificationDTO(Long id, boolean checkNotifications, NotificationListInfo notificationListInfo){
		return getNotificationDTO(getNotification(id, checkNotifications), notificationListInfo);
	}

	@Override
	public NotificationDTO readNotification(Long id){
		read(id);
		return getNotificationDTO(id);
	}

	@Override
	public NotificationDTO completeActionAndRead(Long id){
		Notification notification = getNotification(id);
		if(notification != null){
			if(notification.getActionCompletedTime() == null)
				notification.setActionCompletedTime(ZonedDateTime.now());
			if(notification.getReadTime() == null)
				notification.setReadTime(ZonedDateTime.now());
			notificationRepository.save(notification);
		}
		NotificationDTO dto = getNotificationDTO(notification, null);
		if (OrderDeliveredToBuyerRequestConfirmationNotification.class.getSimpleName().equals(dto.getType())) {
			adminAlertService.processDeliveryFromBuyerConfirmationResult(dto);
			deliveryService.setRecipientConfirmedTime(dto.getTargetObjectId(), null);
		}
		return dto;
	}

	@Override
	public Page<NotificationDTO> getNotifications(NotificationRequest request){
		//Прямая выборка бех кэширования
		org.springframework.data.domain.Page<Notification> page = getRawNotifications(request);
		return new Page<>(getNotificationDTOs(page.getContent()), page.getTotalPages(), page.getTotalElements());

		//Выборка с кэшированием (могут быть проблеммы с изменяемыми полями)
		//org.springframework.data.domain.Page<Long> page = getNotificationIdsPage(request);
		//return new Page<>(getNotificationDTOsByIds(page.getContent()), page.getTotalPages(), page.getTotalElements());
	}

	@Override
	public org.springframework.data.domain.Page<Notification> getRawNotifications(NotificationRequest request){
		return getRawNotifications(request, securityService.getCurrentAuthorizedUser());
	}

	@Override
	public List<Notification> getRawNotifications(int count, User user){
		return getRawNotifications(new NotificationRequest().setPageSize(count), user).getContent();
	}

	@Override
	public List<Notification> getRawNotifications(int count, User user, String guestToken){
		return self.getRawNotifications(new NotificationRequest().setPageSize(count), user, guestToken).getContent();
	}

	@Override
	public org.springframework.data.domain.Page<Notification> getRawNotifications(NotificationRequest request, User user){
		return self.getRawNotifications(request, user, securityService.getGuestToken());
	}

	@Override
	@Transactional
	public org.springframework.data.domain.Page<Notification> getRawNotifications(NotificationRequest request, User user, String guestToken){
		if(user == null && Strings.isNullOrEmpty(guestToken)) return org.springframework.data.domain.Page.empty();
		checkUserNotifications(user, guestToken, request);
		PageRequest pageRequest = getPageRequest(request);
		List<String> dtypeIn = Collections.emptyList();
		if (StringUtils.isNotBlank(request.getOnlyType())) {
			dtypeIn = getSubTypes(request.getOnlyType());
		}

		List<String> dtypeExclude = Collections.emptyList();
		if (StringUtils.isNotBlank(request.getExceptType())) {
			dtypeExclude = getSubTypes(request.getExceptType());
		}
		return notificationRepository.find(user != null ? user.getId() : 0,
				user == null ? guestToken : "",
				dtypeIn,
				dtypeExclude,
				request.getNeedAction() != null, isTrue(request.getNeedAction()),
				request.getIsRead() != null, isTrue(request.getIsRead()),
				request.getIsActionCompleted() != null, isTrue(request.getIsActionCompleted()),
				pageRequest);
	}

	@Override
	public long countNotifications(NotificationRequest request){
		return countNotifications(request, securityService.getCurrentAuthorizedUser(), securityService.getGuestToken());
	}

	@Override
	public long countNotifications(NotificationRequest request, User user, String guestToken){
		if(user == null && Strings.isNullOrEmpty(guestToken)) return 0L;

		// Так как dtype у нас по факту равен имени класса, то просто выгрузим все дочерние классы Notification
		List<String> dtypeIn = Collections.emptyList();
		if (StringUtils.isNotBlank(request.getOnlyType())) {
			dtypeIn = getSubTypes(request.getOnlyType());
		}

		List<String> dtypeExclude = Collections.emptyList();
		if (StringUtils.isNotBlank(request.getExceptType())) {
			dtypeExclude = getSubTypes(request.getExceptType());
		}

		return notificationRepository.count(user != null ? user.getId() : null,
				user == null ? guestToken : "",
				dtypeIn,
				dtypeExclude,
				request.getNeedAction() != null, isTrue(request.getNeedAction()),
				request.getIsRead() != null, isTrue(request.getIsRead()),
				request.getIsActionCompleted() != null, isTrue(request.getIsActionCompleted()));
	}

	private List<String> getSubTypes(String request) {
		Set<Class<? extends Notification>> notificationSubTypes = notificationReflections.getSubTypesOf(Notification.class);
		return notificationSubTypes
				.stream()
				.map(Class::getSimpleName)
				.filter(className -> className.endsWith(request))
				.collect(Collectors.toList());
	}

	@Override
	public NotificationBubbles getBubbles(){
		return getBubbles(securityService.getCurrentAuthorizedUser(), securityService.getGuestToken());
	}

	@Override
	public NotificationBubbles getBubbles(User user, String guestToken){
		return getBubbles(user, guestToken, true);
	}

	@Override
	public NotificationBubbles getBubbles(User user, String guestToken, boolean checkUserNotifications){
		if(checkUserNotifications) checkUserNotifications(user, guestToken, null);

		NotificationRequest commentsRequest = new NotificationRequest().setOnlyType(CommentNotification.class.getSimpleName());
		NotificationRequest noCommentsRequest = new NotificationRequest().setExceptType(CommentNotification.class.getSimpleName());
		NotificationBubbles bubbles = new NotificationBubbles()

				.setCommentsTotal(countNotifications(commentsRequest, user, guestToken))
				.setNoCommentsTotal(countNotifications(noCommentsRequest, user, guestToken))

				.setCommentsNotRead(countNotifications(commentsRequest.setIsRead(Boolean.FALSE), user, guestToken))
				.setNoCommentsNotRead(countNotifications(noCommentsRequest.setIsRead(Boolean.FALSE), user, guestToken))

				.setNoCommentsNeedActionNotCompleted(countNotifications(noCommentsRequest.setIsRead(null).setNeedAction(Boolean.TRUE).setIsActionCompleted(Boolean.FALSE), user, guestToken))

				.setNoCommentsNeedNoActionNotRead(countNotifications(noCommentsRequest.setIsRead(Boolean.FALSE).setNeedAction(Boolean.FALSE).setIsActionCompleted(null), user, guestToken))
				;
		return bubbles;
	}

	private NotificationListInfo getNotificationListInfo(List<Notification> notifications){
		if(notifications == null || notifications.isEmpty()) return null;
		Set<User> users = new HashSet<>();
		for(Notification notification : notifications){
			if(notification.getTargetUser().isPresent()) users.add(notification.getTargetUser().get());
			if(notification.getInitiator().isPresent()) users.add(notification.getInitiator().get());
		}
		if(users.isEmpty()) return null;
		List<UserDTO> userDTOs = userService.getUserDTOs(new ArrayList<>(users), initiatorRequestWithIsFollowed, null);
		Map<Long, UserDTO> usersMap = userDTOs.stream().collect(Collectors.toMap(u -> u.getId(), u -> u));
		return new NotificationListInfo().setUsersMap(usersMap);
	}

	private List<NotificationDTO> getNotificationDTOs(List<Notification> notifications){
		if(notifications == null) return null;
		if(notifications.isEmpty()) return Collections.emptyList();
		NotificationListInfo notificationListInfo = getNotificationListInfo(notifications);
		return notifications.stream().map(n -> getNotificationDTO(n, notificationListInfo)).collect(Collectors.toList());
	}

	private PageRequest getPageRequest(NotificationRequest notificationRequest){
		if(notificationRequest == null) notificationRequest = new NotificationRequest();
		int page = notificationRequest.getPage() - 1;
		if(page < 0) page = 0;
		return PageRequest.of(page, notificationRequest.getPageSize(), getSort(notificationRequest));
	}

	private Sort getSort(NotificationRequest notificationRequest){
		//В перспективе могут потребоваться другие виду сортировки в зависимости от запроса
		return getDefaultSort();
	}

	//Способ сортировки, при котором непрочитанные сообщения выводятся на равных с прочитанными, более свежие наверху
	private Sort getDefaultSort(){
		if(defaultSort == null) defaultSort = JpaSort.unsafe(Sort.Direction.DESC, "need_action AND action_completed_time IS NULL", "create_time", "id");
		return defaultSort;
	}

	private void checkOwner(Notification notification, User user, String guestToken){
		if(user != null && (notification.getUser() == null || !notification.getUser().getId().equals(user.getId()))
				|| user == null && (Strings.isNullOrEmpty(guestToken) || !guestToken.equals(notification.getGuestToken())))
			throw new ForbiddenException(messageSourceAccessor.getMessage("exception.forbidden.notification-access-denied"));
	}

	@Override
	public boolean read(Long notificationId) {
		Notification notification = getNotification(notificationId);
		if(notification.isRead()) return false;
		notification.setReadTime(ZonedDateTime.now());
		notificationRepository.save(notification);
		return true;
	}

	@Override
	@Async
	public void create(Notification notification) {
		self.createSync(notification);
	}

	/**
	 * Создает список уведомлений
	 * @param notificationList
	 */
	@Override
	@Async
	public void create(NotificationList<Notification> notificationList){
		self.createSync(notificationList);
	}

	@Override
	public void createSync(NotificationList<Notification> notificationList){
		createSync(notificationList.getNotifications());
	}

	@Override
	public void createSync(List<Notification> notifications){
		if(notifications == null || notifications.isEmpty()) return;
		for(Notification notification : notifications){
			self.createSync(notification);
		}
	}

	@Override
	@Timed(value = "notification.deliver.batch", description = "Deliver notifications batch", percentiles = {0.99, 0.95, 0.9})
	public List<NotificationDeliveryResult> deliverNotifications(String channel, List<Long> notificationIds) {

		NotificationTransport transport = notificationTransportsByChannel.get(channel);
		if(transport == null) throw new OskellyException(messageSourceAccessor.getMessage("exception.oskelly.unavailable-channel-notification", new Object[]{channel}));

		if(notificationIds.isEmpty()) return Collections.emptyList();

		//Возможно с момента выборки id уведомлений на отправку в базе уже произошли изменения.
		//Часть уведомлений могло быть удалено как дубликаты, другая часть прочитана или выполнена
		//Фильтруем только актуальные уведомдения
		List<Notification> notifications = notificationRepository.findAllByIdsNotDeleted(notificationIds).stream()
				.filter(n -> !n.isActionCompleted() && !n.isRead()).collect(Collectors.toList());
		List<CompletableFuture<NotificationDeliveryResult>> taskPool = new ArrayList<>();
		for (Notification notification : notifications) {
			taskPool.add(CompletableFuture.supplyAsync(() -> deliverNotification(channel, transport, notification), sendNotificationExecutorService));
		}
		return taskPool.stream().map(CompletableFuture::join).collect(Collectors.toList());
	}

	private NotificationDeliveryResult deliverNotification(String channel, NotificationTransport transport, Notification notification) {

		//На данный момент проверка необходимости доставки уведомления пользователю общая для всех типов доставки
		try {
			if (!checkSchedule(notification.getDtype())) //check that notification has a schedule and schedule is active
				return new NotificationDeliveryResult(notification.getId(), channel,
						messageSourceAccessor.getMessage("service.DefaultNotificationService.HasSchedule"),
						NotificationDeliveryResult.ResultType.SKIPPED);
			return self.shouldSendNotification(notification, transport.getSupportedSubscriptionType())
					? !isDuplicate(notification) ? transport.deliverNotification(notification.getId())
					: new NotificationDeliveryResult(notification.getId(), channel, messageSourceAccessor.getMessage("service.DefaultNotificationService.Duplicate"), NotificationDeliveryResult.ResultType.SKIPPED)
					: new NotificationDeliveryResult(notification.getId(), channel, messageSourceAccessor.getMessage("service.DefaultNotificationService.NotificationDisabled"), NotificationDeliveryResult.ResultType.SKIPPED);
		} catch (Exception e) {
			log.error("Failed to send notification with id {}", notification.getId(), e);
			throw e;
		}
	}

	private boolean checkSchedule(String dtype){
		if (StringUtils.isBlank(dtype))
			return true;

		Map<String, String> schedule = notificationProperties.getSchedule().get(dtype);
		if (CollectionUtils.isEmpty(schedule))
			return true;

		if (StringUtils.isBlank(schedule.get("startTime")) || StringUtils.isBlank(schedule.get("endTime")) || StringUtils.isBlank(schedule.get("timezone")))
			return true;

		DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_TIME;
		LocalTime now = LocalTime.now(ZoneId.of(schedule.get("timezone")));
		LocalTime start = LocalTime.parse(schedule.get("startTime"), formatter);
		LocalTime end = LocalTime.parse(schedule.get("endTime"), formatter);
		return now.isAfter(start) && now.isBefore(end);
	}

	@Transactional
	@Override
	public void markNotificationDelivery(NotificationDeliveryResult notificationDeliveryResult) {
		log.info("markNotificationDelivery");

		List<NotificationDelivery> notificationDeliveryListByNotification = notificationDeliveryRepository.findDeliveries(notificationDeliveryResult.getChannel(), notificationDeliveryResult.getNotificationId());
		if (notificationDeliveryListByNotification.size() > 1) {
			log.warn("Для notification {} найденно более 1 записи о доставке по каналу " + notificationDeliveryResult.getChannel(), notificationDeliveryResult.getNotificationId());
			return;
		}

		NotificationDelivery notificationDelivery = notificationDeliveryListByNotification.isEmpty()
				? new NotificationDelivery() //Создаем новую запись
				.setNotificationId(notificationDeliveryResult.getNotificationId())
				.setCreateTime(ZonedDateTime.now())
				.setChannel(notificationDeliveryResult.getChannel())
				: notificationDeliveryListByNotification.get(0); //Обновляем имеющуюся запись

		Optional<Notification> notification = notificationRepository.findById(notificationDelivery.getNotificationId());

		switch (notificationDeliveryResult.getType()) {
			case SUCCESSFUL:
				notificationDelivery.markAsSent(notificationDeliveryResult);
				notification.ifPresent(n -> notificationDeltaTimer.record(Duration.between(n.getCreateTime(), notificationDelivery.getCreateTime())));
				break;
			case SKIPPED:
				notificationDelivery.markAsSkipped(notificationDeliveryResult);
				break;
			case FAILED:
				notificationDelivery.markAsFailed(notificationDeliveryResult);
				break;
			case TERMINAL_FAILED:
				notificationDelivery.markAsFailed(notificationDeliveryResult);
				notificationDelivery.setCountErrorSent(maxRetry);
				break;
		}

		notificationDeliveryRepository.save(notificationDelivery);
	}

	@Override
	public void markNotificationDeliveries(List<NotificationDeliveryResult> notificationDeliveryResults){
		for(NotificationDeliveryResult notificationDeliveryResult : notificationDeliveryResults){
			try {
				self.markNotificationDelivery(notificationDeliveryResult);
			} catch (Exception e) {
				log.error("Unable to save notification {} info, error: {}", notificationDeliveryResult.getNotificationId(), e.getMessage(), e);
			}
		}
	}

	@Async
	@Override
	public void markNotificationDeliveriesAsync(List<NotificationDeliveryResult> notificationDeliveryResults){
		markNotificationDeliveries(notificationDeliveryResults);
	}

	private List<NotificationWithBubbles> getNotificationsWithBubblesByNotificationsList(List<Notification> notifications) {
		List<NotificationWithBubbles> notificationsWithBubbles = new ArrayList<>();
		for (Notification notification : notifications) {
			notificationsWithBubbles.add(getNotificationWithBubbles(notification));
		}
		return notificationsWithBubbles;
	}

	@Override
	public NotificationWithBubbles getNotificationWithBubbles(Notification notification) {
		NotificationService.NotificationBubbles notificationBubbles = self.getBubbles(notification.getUser(), notification.getGuestToken(), false);
		NotificationDTO notificationDTO = self.getNotificationDTO(notification, null);
		return new NotificationWithBubbles(notificationDTO, notificationBubbles.getCommentsNotReadNoCommentsNeedActionNotCompleted());
	}

	@Transactional
	@Override
	public List<NotificationWithBubbles> getNotificationsWithBubbles(List<Long> notificationIds) {
		if(notificationIds.isEmpty()) return Collections.emptyList();
		List<Notification> notifications = notificationRepository.findAllById(notificationIds);
		return getNotificationsWithBubblesByNotificationsList(notifications);
	}

	@Transactional
	@Override
	public NotificationWithBubbles getNotificationWithBubbles(Long notificationId){
		return getNotificationWithBubbles(notificationRepository.getOne(notificationId));
	}

	@Transactional
	@Override
	public void closeNotifications(List<Long> notificationIds){
		if(notificationIds == null || notificationIds.isEmpty()) return;
		notificationRepository.readAll(notificationIds);
		notificationRepository.setActionCompleted(notificationIds);
	}

	@Async
	@Override
	public void closeNotificationsAsync(List<Long> notificationIds){
		self.closeNotifications(notificationIds);
	}

	@Override
	public NotificationDTO getLastNotificationDTOByUserAndOrderAndType(Long userId, Long orderId, Class<? extends OrderNotification> type){
		List<Notification> notifications = notificationRepository.findAllByUserAndOrderAndDtype(userId, orderId, type.getSimpleName());
		if(!notifications.isEmpty()) return defaultConverter.getNotificationDTO(notifications.get(notifications.size() - 1), null);
		return null;
	}

	@Override
	public List<Notification> getNotificationIdsByUserIdAdnStreamIdAndDataType(Long userId, Long streamId, Class<? extends StreamNotification> dType) {
		return notificationRepository.getNotificationIdsByUserIdAndStreamIdAndDataType(userId, streamId, dType.getSimpleName());
	}

    @Override
	@Transactional
	@Timed(value = "notification.db.getIds", description = "Get notification Ids to deliver", percentiles = {0.99, 0.95, 0.9})
	public List<Long> getNotificationIdsToDeliver(String channel, List<String> dtypes, ZonedDateTime startDate, int limit, List<Long> excludeNotificationIds) {
		List<Long> excludeIds = excludeNotificationIds.isEmpty() ? Collections.singletonList(0L) : excludeNotificationIds;
		return notificationRepository.findNotificationIdsToDeliver(
				channel,
				dtypes,
				startDate,
				limit,
				excludeIds,
				MULTIPLE_DELIVERY_NOTIFICATION_DTYPES,
				maxRetry
		);
	}

	@Override
	@Timed(value = "notification.db.getDeliveredIds", description = "Get delivered notification Ids to deliver", percentiles = {0.99, 0.95, 0.9})
	public List<Long> getDeliveredNotificationIds(String channel, Collection<Long> notificationIds) {
		return notificationRepository.findNotificationIdsWithHandledDelivery(channel, notificationIds);
	}

	@Override
	public Collection<String> getSupportedNotificationDeliveryChannels() {
		return notificationTransportsByChannel.keySet();
	}

	@Override
	public boolean isDuplicate(Notification notification) {
		//Сначала проверяем, является ли тип данного уведомления недоблируемым
		if(noDuplicatesNotificationTypes.contains(notification.getDtype())){
			Long userId = notification.getUser() != null ? notification.getUser().getId() : null;
			//Если уведомление уже сохранено в базе (содержит ID), то оно будет считаться дубликатом, если в базе есть как минимум 2 таких уведомления.
			//Если же уведомление не сохранено в бзе (не содержит ID), то оно будет считаться дубликатом, если в базе есть хотя бы одно такое уведомление.
			int isDubCount = notification.getId() != null ? 1 : 0;
			return notificationRepository.countByUserOrGuestTokenAndDtypeIncludingDeleted(notNullOrZero(userId), notNullOrEmpty(notification.getGuestToken()), notNullOrEmpty(notification.getDtype())) > isDubCount;
		}

		if (noDuplicatesWithTransactionalIdNotificationTypes.contains(notification.getDtype())) {
			Long userId = notification.getUser() != null ? notification.getUser().getId() : null;
			String guestToken = notification.getGuestToken();
			String transactionalId = ((ContentNotification)notification).getTransactionalId();

			if (Strings.isNullOrEmpty(transactionalId)) {
				return false;
			}

			Long duplicateCount = 0L;

			if (userId != null) {
				duplicateCount = notificationRepository.countByUserIdAndTransactionalId(userId, transactionalId);
			} else if (!Strings.isNullOrEmpty(guestToken)) {
				duplicateCount = notificationRepository.countByGuestTokenAndTransactionalId(guestToken, transactionalId);
			}

			return duplicateCount > 0;
		}

		return false;
	}

	private boolean wasUserInCurrentEnvironment(Notification notification) {
		if (notification == null) {
			return false;
		}

		// Если в нотификации нет пользователя, значит мы что-то знаем про девайс,
		// значит девайс в этом окружении был
		if (notification.getUser() == null) {
			return true;
		}

        return notification.getUser().getSyncOnlyTime() == null;
    }

	@Override
	public void createSync(Notification notification) {
		//Сначала проверяем, не является ли данное уведомление дубликатом.
		//Если оно дублирует другое уведомление, то мы его не создаем
		if(isDuplicate(notification)) return;

		//Тянем пользователя из базы, чтобы не было проблем с lazy-полями, т.к. пользователь в уведомлении был вытянут в транзакции, которая скорее всего уже закрыта
		if(notification.getUser() != null) {
			User user = userService.getUserById(notification.getUser().getId()).orElseThrow(() -> new UserNotFoundException(messageSourceAccessor.getMessage("exception.cart.user-not-found", new Object[]{notification.getUser().getId()})));
			notification.setUser(user);
		}

		// Проверяем, должны ли мы создавать нотификации. Может быть пользователь
		// ниразу не был на этом окружении – тогда нотификации не создаём.
		if(!wasUserInCurrentEnvironment(notification)) return;

		notification.setCreateTime(ZonedDateTime.now());
		save(notification);
	}

	@Override
	public boolean notificationExistsForUser(Long userId, String dtype){
		return notificationRepository.countByUserAndDtype(notNullOrZero(userId), notNullOrEmpty(dtype)) > 0;
	}

	@Override
	public boolean checkNotificationNotExistsForUserAndProduct(Long userId, Long productId, String dtype) {
		int count = notificationRepository.countByUserAndProductAndDtype(notNullOrZero(userId), notNullOrZero(productId), notNullOrEmpty(dtype));
		return count == 0;
	}

	@Override
	public void createCompletePublicationNotification(List<CompletePublicationNotificationDTO> notificationsDtoList) {
        log.info("create complete publication notification: start {}", notificationsDtoList.toString());

		if (CollectionUtils.isEmpty(notificationsDtoList)) {
			log.info("create complete publication notification: notification list is empty of null");
			return;
		}

		Set<Long> distinctUserIds = notificationsDtoList.stream()
				.map(CompletePublicationNotificationDTO::getUserId)
				.collect(Collectors.toSet());

		Map<Long, User> userMap = userService.findRawUsersByUserIds(distinctUserIds).stream()
				.collect(Collectors.toMap(User::getId, user -> user));
		log.info("create complete publication notification: distinct user ids = {}, found user ids = {}", distinctUserIds, userMap.keySet());

		Set<Long> distinctProductIds = notificationsDtoList.stream()
				.map(CompletePublicationNotificationDTO::getProductId)
				.collect(Collectors.toSet());

		Map<Long, Product> productMap = productRepository.findAllById(distinctProductIds).stream()
				.collect(Collectors.toMap(Product::getId, product -> product));
		log.info("create complete publication notification: distinct product ids = {}, found product ids = {}", distinctProductIds, productMap.keySet());

		List<Notification> notifications = notificationsDtoList.stream()
				.filter(notificationDto -> userMap.containsKey(notificationDto.getUserId())
						&& productMap.containsKey(notificationDto.getProductId()))
				.map(notificationDto -> new CompletePublicationNotification()
						.setProduct(productMap.get(notificationDto.getProductId()))
						.setUser(userMap.get(notificationDto.getUserId())))
				.collect(Collectors.toList());

		List<CompletePublicationNotificationDTO> missedNotifications = notificationsDtoList.stream()
				.filter(notificationDto -> notifications.stream()
						.noneMatch(notification -> notification.getUser().getId().equals(notificationDto.getUserId())
								&& ((CompletePublicationNotification) notification).getProduct().getId().equals(notificationDto.getProductId())))
				.collect(Collectors.toList());

		if (!missedNotifications.isEmpty()) {
			log.warn("create complete publication notification: some notifications were not created: notifications = {}", missedNotifications);
		}

		createSync(notifications);
        log.info("create complete publication notification: done {}", notifications.size());
	}

	@Override
	public void createCompletePublicationNotificationRangeDay(List<CompletePublicationNotificationDTO> notificationsDtoList) {
        log.info("create complete publication notification range day: start {}", notificationsDtoList.toString());

		if (CollectionUtils.isEmpty(notificationsDtoList)) {
			log.info("create complete publication notification range day: notification list is empty of null");
			return;
		}

		Set<Long> distinctUserIds = notificationsDtoList.stream()
				.map(CompletePublicationNotificationDTO::getUserId)
				.collect(Collectors.toSet());

		Map<Long, User> userMap = userService.findRawUsersByUserIds(distinctUserIds).stream()
				.collect(Collectors.toMap(User::getId, user -> user));
		log.info("create complete publication notification range day: distinct user ids = {}, found user ids = {}", distinctUserIds, userMap.keySet());

		Set<Long> distinctProductIds = notificationsDtoList.stream()
				.map(CompletePublicationNotificationDTO::getProductId)
				.collect(Collectors.toSet());

		Map<Long, Product> productMap = productRepository.findAllById(distinctProductIds).stream()
				.collect(Collectors.toMap(Product::getId, product -> product));
		log.info("create complete publication notification range day: distinct product ids = {}, found product ids = {}", distinctProductIds, productMap.keySet());

		List<Notification> notifications = notificationsDtoList.stream()
				.filter(notificationDto -> userMap.containsKey(notificationDto.getUserId())
						&& productMap.containsKey(notificationDto.getProductId()))
				.map(notificationDto -> new CompletePublicationNotificationRangeDay()
						.setProduct(productMap.get(notificationDto.getProductId()))
						.setUser(userMap.get(notificationDto.getUserId())))
				.collect(Collectors.toList());

		List<CompletePublicationNotificationDTO> missedNotifications = notificationsDtoList.stream()
				.filter(notificationDto -> notifications.stream()
						.noneMatch(notification -> notification.getUser().getId().equals(notificationDto.getUserId())
								&& ((CompletePublicationNotificationRangeDay) notification).getProduct().getId().equals(notificationDto.getProductId())))
				.collect(Collectors.toList());

		if (!missedNotifications.isEmpty()) {
			log.warn("create complete publication notification range day: some notifications were not created: notifications = {}", missedNotifications);
		}

		createSync(notifications);
		log.info("create complete publication notification range day: done {}", notifications.size());
	}

	@Override
	@Async
	@TransactionalEventListener(fallbackExecution = true)
	public void create(NotificationPackage<? extends Notification> notificationPackage) {
		List<Notification> notifications = new ArrayList<>();
		for(User follower : followingService.getFollowers(notificationPackage.getUser(), null)){
			Notification notification = notificationPackage.getTemplateNotification();
			if(notification.getId() != null) {
				jpaContext.getEntityManagerByManagedType(notification.getClass()).detach(notification);
				notification.setId(null);
			}
			notification.setUser(follower);
			self.createSync(notification);
			notifications.add(notification);
		}
	}

	@Override
	public void save(Notification notification){
		notificationRepository.saveAndFlush(notification);
	}

	@Override
	public void delete(Notification notification){
		notification.setDeleteTime(ZonedDateTime.now());
		save(notification);
	}

	@Override
	public void addNotificationProcessor(NotificationProcessor notificationProcessor){
		if(notificationProcessors.contains(notificationProcessor)) return;
		notificationProcessors.add(notificationProcessor);
		reindexNotificationProcessors();
	}

	@Override
	public void removeNotificationProcessor(NotificationProcessor notificationProcessor){
		notificationProcessors.remove(notificationProcessor);
		reindexNotificationProcessors();
	}

	@Override
	@Transactional
    public void readAll(User u) { notificationRepository.readAll(u); }

	@Override
	public List<NotificationGroup> getNotificationGroups(){
		if(notificationGroupsCache == null) notificationGroupsCache = notificationGroupRepository.findAll().stream()
				.sorted(Comparator.comparing(NotificationGroup::getSortOrder)).collect(Collectors.toList());
		return notificationGroupsCache;
	}

	@Override
	public List<NotificationGroup> getNotHiddenNotificationGroupsForSubscriptionType(UserSubscriptionType subscriptionType){
		return getNotificationGroups().stream().filter(ng -> !ng.isHidden() && ng.getSubscriptionType().equals(subscriptionType))
				.collect(Collectors.toList());
	}

	@Override
	public List<Long> getNotHiddenNotificationGroupIds(){
		return getNotificationGroups().stream().filter(ng -> !ng.isHidden()).map(ng -> ng.getId()).collect(Collectors.toList());
	}

	@Override
	public NotificationGroup getNotificationGroup(Long notificationGroupId){
		return self.getNotificationGroups().stream().filter(ng -> ng.getId().equals(notificationGroupId)).findFirst().orElse(null);
	}

	@Override
	public boolean notificationGroupContainsDtype(Long notificationGroupId, String dtype){
		NotificationGroup group = self.getNotificationGroup(notificationGroupId);
		return group != null && group.containsNotificationDtype(dtype);
	}

	@Override
	public List<NotificationGroup> getUserNotificationGroups(User user, UserSubscriptionType subscriptionType){
		if(user == null) return null;
		if(user.getNotificationGroupsChangeTime() == null) //Пользователь еще не редактировал группы подписок пуш-уведомлений
			return getNotHiddenNotificationGroupsForSubscriptionType(subscriptionType); //По умолчанию у него будут включены все уведомления
		List<NotificationGroup> groups = notificationGroupRepository.findByUser(user.getId(), subscriptionType);
		Map<UserSubscriptionType, List<NotificationGroup>> groupMap = groups.stream().collect(Collectors.groupingBy(NotificationGroup::getSubscriptionType, Collectors.toList()));

		List<NotificationGroup> result = new ArrayList<>();
		for (UserSubscriptionType type : groupMap.keySet()){
			List<NotificationGroup> notificationGroups = groupMap.get(type);
			boolean isMainGroupActive = notificationGroups.stream().anyMatch(NotificationGroup::isGeneral);
			if (isMainGroupActive)
				result.addAll(notificationGroups);
		}
		return result;
	}

	@Override
	public List<Long> getUserNotificationGroupIds(User user){
		if(user == null) return null;
		if(user.getNotificationGroupsChangeTime() == null) //Пользователь еще не редактировал группы подписок пуш-уведомлений
			return getNotHiddenNotificationGroupIds(); //По умолчанию у него будут включены все уведомления
		return notificationGroupRepository.findIdsByUser(user.getId());
	}

	@Override
	public List<Long> getUserNotificationGroupIds(Long userId, Boolean notificationGroupsWasChanged) {
		// Если пользователь ещё не редактировал группы подписок пуш-уведомлений
		if(notificationGroupsWasChanged == null || !notificationGroupsWasChanged)
			// То по умолчанию у него будут включены все уведомления (хотя в базе будет пусто)
			return getNotHiddenNotificationGroupIds();
		// Иначе берём подписки из базы
		return notificationGroupRepository.findIdsByUser(userId);
	}

	@Override
	public boolean isGeneralPushGroupSwitchedOnForUser(User user, UserSubscriptionType userSubscriptionType){
		if(user == null) return false;
		return getUserNotificationGroupIds(user).contains(generalNotificationsGroups.get(userSubscriptionType));
	}

	@Override
	@Transactional
	public void setUserNotificationGroups(User user, List<Long> notificationGroupIds, UserSubscriptionType subscriptionType){
		if(user == null) return;
		short count = notificationGroupUserBindingRepository.deleteAllByUserIdAndNotificationGroupSubscriptionType(user.getId(), subscriptionType);
		log.debug("delete all notification group, count = {}", count);
		if(notificationGroupIds != null && !notificationGroupIds.isEmpty()) {
			List<NotificationGroupUserBinding> userBindingList = new ArrayList<>(5);
			for (Long notificationGroupId : notificationGroupIds) {
				NotificationGroup group = getNotificationGroup(notificationGroupId);
				if (group == null) continue;
				userBindingList.add(new NotificationGroupUserBinding().setUser(user).setNotificationGroup(group));
			}
			// Если пользователь ранее не обновлял группы надо включить все кроме настраиваемых
			if (user.getNotificationGroupsChangeTime() == null) {
				for (UserSubscriptionType value : UserSubscriptionType.values()) {
					if (value == subscriptionType) continue;
					List<NotificationGroup> groupsForSubscriptionType = getNotHiddenNotificationGroupsForSubscriptionType(value);
					for (NotificationGroup notificationGroup : groupsForSubscriptionType) {
						userBindingList.add(new NotificationGroupUserBinding().setUser(user).setNotificationGroup(notificationGroup));
					}
				}
			}
			notificationGroupUserBindingRepository.saveAll(userBindingList);
		}

		user.setNotificationGroupsChangeTime(ZonedDateTime.now());
		userService.save(user, false);
	}

	@Override
	@Transactional
	public void setUserNotificationGroupsForAuthorizedUser(User currentAuthorizedUser, List<Long> notificationGroupIds, UserSubscriptionType subscriptionType){
		setUserNotificationGroups(currentAuthorizedUser, notificationGroupIds, subscriptionType);
	}

	@Override
	@Transactional
	public void enableAllNonEmailNotifications(User user) {
		// Если пользователь ранее не обновлял группы надо включить все кроме настраиваемых
		List<NotificationGroupUserBinding> userBindings = new ArrayList<>();
		for (UserSubscriptionType value : UserSubscriptionType.values()) {
			if (value == UserSubscriptionType.EMAIL) continue;
			List<NotificationGroup> groupsForSubscriptionType = getNotHiddenNotificationGroupsForSubscriptionType(value);
			for (NotificationGroup notificationGroup : groupsForSubscriptionType) {
				userBindings.add(new NotificationGroupUserBinding().setUser(user).setNotificationGroup(notificationGroup));
			}
		}
		notificationGroupUserBindingRepository.saveAll(userBindings);

		user.setNotificationGroupsChangeTime(ZonedDateTime.now());
		userService.save(user);
	}

	@Override
	@Transactional
	public void enableAllEmailNotifications(User user) {
		// Если пользователь ранее не обновлял группы надо включить все кроме настраиваемых
		if (user.getNotificationGroupsChangeTime() == null) {
			enableAllNonEmailNotifications(user);
		}

		List<NotificationGroupUserBinding> userBindings = getNotHiddenNotificationGroupsForSubscriptionType(UserSubscriptionType.EMAIL)
				.stream()
				.map(it -> new NotificationGroupUserBinding()
						.setUser(user)
						.setNotificationGroup(it))
				.collect(Collectors.toList());
		notificationGroupUserBindingRepository.saveAll(userBindings);

		user.setNotificationGroupsChangeTime(ZonedDateTime.now());
		userService.save(user);
	}

	@Override
	@Transactional
	public boolean shouldSendNotification(User user, String notificationDtype, UserSubscriptionType subscriptionType) {
		if (Strings.isNullOrEmpty(notificationDtype) || notificationGroupContainsDtype(NO_PUSH_NOTIFICATION_GROUP_ID, notificationDtype)) return false;
		//Гости получают все уведомления без фильтрации
		if (user == null) return true;

		//К нам зачастую приходит Lazy user, и при попытке работать с ним с параллельных сессиях происхоит сбой illegally attempted to associate a proxy with two open Sessions
		//Поэтому приходится тянуть пользователя заново в своей сессии, чтобы не мешать другим параллельным процессам
		Long userId = user.getId();
		user = userRepository.findById(userId).orElseThrow(() -> new UserNotFoundException(String.valueOf(userId)));
		//На какие группы подписан пользователь
		List<NotificationGroup> userNotificationGroups = getUserNotificationGroups(user, subscriptionType);
		//Проверка, включена ли главная группа по subscriptionType (общий выключатель для всей группы уведомлений)
		if (userNotificationGroups.stream().noneMatch(ng -> ng.getId().equals(generalNotificationsGroups.get(subscriptionType)))) return false;
		for (NotificationGroup userNotificationGroup : userNotificationGroups) {
			if (notificationGroupContainsDtype(userNotificationGroup.getId(), notificationDtype)) return true;
		}
		return false;
	}

	@Override
	@Transactional
	public boolean shouldSendNotification(Notification notification, UserSubscriptionType supportedSubscriptionType){
		User user = notification.getUser();
		notification = notificationRepository.getOne(notification.getId()); // прикрепляемся к сессии снова в новом потоке
		return (user == null || self.shouldSendNotification(user, notification.getDtype(), supportedSubscriptionType)) && notification.shouldBeSendForSubscriptionType(supportedSubscriptionType);
	}

	@Override
	public List<NotificationGroupDTO> getNotificationGroupsForSubscriptionType(User user, UserSubscriptionType subscriptionType){
		return getAllNotHiddenNotificationGroupsDTO(getUserNotificationGroupIds(user), subscriptionType);
	}

	@Override
	public List<NotificationGroupDTO> getNotificationGroupsForAuthorizedUser(UserSubscriptionType userSubscriptionType){
		return getNotificationGroupsForSubscriptionType(securityService.getCurrentAuthorizedUser(), userSubscriptionType);
	}

	@Override
	public void createAddBrandLikeNotificationsByUserIds(List<Long> userIds){
		if(userIds == null || userIds.isEmpty()) return;
		createAddBrandLikeNotifications(userRepository.findAllById(userIds));
	}

	@Async
	@Override
	public void createAddBrandLikeNotificationsByUserIdsAsync(List<Long> userIds){
		self.createAddBrandLikeNotificationsByUserIds(userIds);
	}

	@Override
	public void createNotifications(String type, List<String> ukeys){
	    log.info("Создание уведомлений типа " + type + " для " + ukeys.size() + " ukeys");
		if(Strings.isNullOrEmpty(type) || ukeys == null || ukeys.isEmpty()) return;
		try {
			Class<? extends Notification> clazz = (Class<? extends Notification>) Class.forName(type);
			for(String ukey : ukeys){
				createNotification(clazz, ukey);
			}

		} catch (ClassNotFoundException e) {
			log.error("Notification class not found: " + type);
		}
	}

	@Override
	public void createNotification(Class<? extends Notification> clazz, String ukey){
		if(Strings.isNullOrEmpty(ukey)) return;
        log.info("Создание уведомлений типа " + clazz.getSimpleName() + " для " + ukey);
		String[] parts = ukey.split(":");
		User user = null;
		String guestToken = null;
		if(parts.length > 0 && !parts[0].isEmpty()){
			Long userId = Long.parseLong(parts[0]);
			user = userRepository.getOne(userId);
		}
		if(parts.length > 1 && !parts[1].isEmpty()){
			guestToken = parts[1];
		}
		//Если нет никаких следов о пользователе или госте, то уведомление не создаем
		if(user == null && guestToken == null) return;
		Notification notification = constructNotification(clazz, user, guestToken);
		createSync(notification);
	}

	@Async
	@Override
	public void createNotificationsAsync(String type, List<String> ukeys){
		self.createNotifications(type, ukeys);
	}

	@Transactional
	public void createSetLowerPriceNotifications(MultiValueMap<Long, Long> usersToProducts) {
		List<Notification> notifications = new ArrayList<>(usersToProducts.size());

		Set<Long> productIdUniques = new HashSet<>();
		for (Long userId : usersToProducts.keySet()){
			List<Long> productIds = usersToProducts.get(userId);
			productIdUniques.addAll(productIds);
		}
		Map<Long, Product> productMap = productRepository.findAllById(productIdUniques).stream().collect(Collectors.toMap(Product::getId, p -> p));
		for (Long userId : usersToProducts.keySet()) {
			List<Long> productIds = usersToProducts.get(userId);
			User user = userRepository.getOne(userId);
			if (productIds.size() == 1) {
				Long productId = productIds.get(0);

				if (productId == null) continue;

				Product product = productMap.get(productId);
				boolean isPriceBigEnough = product.getCurrentPrice().compareTo(BigDecimal.valueOf(minPrice * 1.1)) > -1;
				if (!isPriceBigEnough)
					continue;

				Notification setLowerPriceNotification = new SetLowerPriceNotification()
						.setProduct(product)
						.setUser(user)
						.setNeedAction(false);
				notifications.add(setLowerPriceNotification);
			}
			else {
				boolean isPriceBigEnough = false;
				for (Long productId : productIds) {
					Product product = productMap.get(productId);
					if (product.getCurrentPrice().compareTo(BigDecimal.valueOf(minPrice * 1.1)) > -1){
						isPriceBigEnough = true;
						break;
					}
				}
				if (!isPriceBigEnough)
					continue;

				Notification setLowerPrice = new SetLowerPriceForSeveralProductsNotification()
						.setUser(user)
						.setNeedAction(false);

				notifications.add(setLowerPrice);
			}
		}
		createSync(notifications);
	}

    @Override
	@Transactional
    public void markNotificationsAsNotNeedAction(List<Long> notificationIds) {
        notificationRepository.markNotificationsAsNotNeedAction(notificationIds);
    }

	@Override
	public List<String> getDtypesForSubscriptionType(UserSubscriptionType subscriptionType) {
		List<NotificationGroup> notificationGroups = getNotificationGroups().stream().filter(ng -> ng.getSubscriptionType().equals(subscriptionType)).collect(Collectors.toList());
		return notificationGroups.stream().flatMap(group -> group.getNotificationGroupDtypeBindings().stream()).map(NotificationGroupDtypeBinding::getNotificationDtype).collect(Collectors.toList());
	}

	protected Notification prepareTextNotificationForCreation(TextNotificationDTO textNotificationDTO){
		if(textNotificationDTO == null) throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.NotificationNotSent"));
		String className = textNotificationDTO.getClassName();
		if(Strings.isNullOrEmpty(className)) throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.ClassNameNotSent"));
		if(!className.startsWith("su.reddot.domain.model.notification")) throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.WrongPackage"));
		if(textNotificationDTO.getUserId() == null) throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.UserIdNotSent"));
		Notification notification;
		try {
			Class<? extends Notification> clazz = (Class<? extends Notification>) Class.forName(className);
			notification = clazz.newInstance();
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
			throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.ClassNotFound", new Object[]{className}));
		} catch (IllegalAccessException e) {
			e.printStackTrace();
			throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.AccessErrorWhenCreateInstance", new Object[]{className, e.getMessage()}));
		} catch (InstantiationException e) {
			e.printStackTrace();
			throw new NotificationException(messageSourceAccessor.getMessage("service.DefaultNotificationService.ErrorWhenCreateInstance", new Object[]{className, e.getMessage()}));
		}

		User user = userService.getUserById(textNotificationDTO.getUserId()).orElseThrow(() -> new UserNotFoundException(messageSourceAccessor.getMessage("exception.cart.user-not-found", new Object[]{notification.getUser().getId()})));
		notification.setUser(user)
			.setGuestToken(textNotificationDTO.getGuestToken())
			.setCreateTime(ZonedDateTime.now())
			.setNeedAction(textNotificationDTO.isNeedAction())
			.setTxt(textNotificationDTO.getTxt());
		return notification;
	}

	@Override
	public void createTextNotifications(List<TextNotificationDTO> textNotificationDTOs){
		if(textNotificationDTOs == null || textNotificationDTOs.isEmpty()) return;
		List<Notification> notificationsToCreate = textNotificationDTOs.parallelStream()
				.map(this::prepareTextNotificationForCreation)
				.filter(this::wasUserInCurrentEnvironment)
				.collect(Collectors.toList());
		notificationRepository.saveAll(notificationsToCreate);
	}

	@Override
	public void createTextNotificationsAsync(List<TextNotificationDTO> textNotificationDTOs){
		if(textNotificationDTOs == null || textNotificationDTOs.isEmpty()) return;
		self.createTextNotifications(textNotificationDTOs);
	}

	@Override
	synchronized public void createDefaultNotifications(List<Long> userIds, List<String> guestTokens){
		//Сначала обходим гостей. Не исключено, что с такими гостевыми токенами существуют активности и у пользователей
		//Для этого ищем таких пользователей и прикрепляем их к уведомлению, если нашли
		if(guestTokens != null && !guestTokens.isEmpty()){
			for(String guestToken : guestTokens){
				if(Strings.isNullOrEmpty(guestToken)) continue;
				Long userId = activityRepository.findLastUserIdWithGuestToken(guestToken);
				self.createDefaultNotifications(userId, guestToken);
			}
		}

		//После этого обходим пользователей. Вероятно для некоторых из них уведомление уже было создано шагом ранее
		//и здесь оно не должно быть создано повторно, т.к. все дефолтные уведомления создаются с использованием createNotificationOnce
		if(userIds != null && !userIds.isEmpty()){
			for(Long userId : userIds){
				if(userId == null) continue;
				self.createDefaultNotifications(userId, null);
			}
		}
	}

	@Transactional
	@Override
	public void createDefaultNotifications(Long userId, String guestToken){
		if(userId == null && guestToken == null) return;
		User user = userService.getOne(userId);
		for(Class<? extends Notification> clazz : defaultNotificationTypes){
			if (clazz == RegisterNotification.class && (!promocodeOskelly5Enabled || user != null)) {
				//Имеем ситуацию: пользователь уже зарегистрирован, а мы пытаемся ему отправить новое уведомление "Зарегистрируйся"
				//Поэтому пропускаем создание этого уведомления
				continue;
			}
			Notification notification = constructNotification(clazz, user, guestToken);
			createSync(notification);
		}
	}

	//Создает обхект уведомления заданного типа и присваивает пользователя и/или гостевой токен
	private Notification constructNotification(Class<? extends Notification> clazz, User user, String guestToken){
		Notification notification = null;
		try {
			notification = clazz.newInstance();
			notification.setUser(user);
			notification.setGuestToken(guestToken);
		} catch (InstantiationException | IllegalAccessException e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}
		return notification;
	}

	@Async
	@Override
	public void createDefaultNotificationsAsync(List<Long> userIds, List<String> guestTokens){
		self.createDefaultNotifications(userIds, guestTokens);
	}

	@Async
	@Override
	public void createWelcomeNotificationAsync(String notification, List<Long> userIds, List<String> guestTokens) {
		//Сначала обходим гостей. Не исключено, что с такими гостевыми токенами существуют активности и у пользователей
		//Для этого ищем таких пользователей и прикрепляем их к уведомлению, если нашли
		if (!welcomeNotificationClasses.containsKey(notification)){
			log.error("Welcome notifications don't have a notification with name: " + notification);
			return;
		}

		Class notificationClass = welcomeNotificationClasses.get(notification);
		if(guestTokens != null && !guestTokens.isEmpty()){
			for(String guestToken : guestTokens){
				if(Strings.isNullOrEmpty(guestToken)) continue;
				try {
					Long userId = activityRepository.findLastUserIdWithGuestToken(guestToken);
					User user = userService.getOne(userId);
					self.createSync(constructNotification(notificationClass, user, guestToken));
				} catch (Exception e){
					log.error("error while creating welcome notification", e);
				}
			}
		}

		//После этого обходим пользователей. Вероятно для некоторых из них уведомление уже было создано шагом ранее
		//и здесь оно не должно быть создано повторно, т.к. все дефолтные уведомления создаются с использованием createNotificationOnce
		if(userIds != null && !userIds.isEmpty()){
			for(Long userId : userIds){
				if(userId == null) continue;
				try {
					User user = userService.getOne(userId);
					self.createSync(constructNotification(notificationClass, user, null));
				} catch (Exception e){
					log.error("error while creating welcome notification", e);
				}
			}
		}
	}

	@Override
	synchronized public void createAddBrandLikeNotifications(List<User> users){
		if(users == null || users.isEmpty()) return;
		List<Notification> notifications = new ArrayList<>();
		for(User user : users){
			if(user.getAddBrandLikeNotificationTime() != null) continue;
			notifications.add(new AddBrandLikeNotification().setUser(user));
			user.setAddBrandLikeNotificationTime(ZonedDateTime.now());
		}
		createSync(notifications);
		userRepository.saveAll(users);
	}

	@Override
	public void createBargainNotifications(List<BargainNotificationDTO> bargainNotificationDtoList) {
		for (BargainNotificationDTO notificationDto : bargainNotificationDtoList) {
			try {
				Class<? extends BargainNotification> clazz = (Class<? extends BargainNotification>) Class.forName(notificationDto.getType());
				BargainNotification notification = clazz.newInstance();
				notification.setUser(userService.getOne(notificationDto.getUserId()));
				notification.setBargain(bargainRepository.getOne(notificationDto.getBargainId()));
				notification.setBargainRecord(bargainRecordRepository.getOne(notificationDto.getBargainRecordId()));
				self.createSync(notification);
			} catch (ClassNotFoundException | InstantiationException | IllegalAccessException e) {
				log.error("Couldn't create bargain notification for userId: " + notificationDto.getUserId() + "and type: " + notificationDto.getType(), e);
			}
		}
	}

	@Override
	public void createPrivateSellerProductPublishedNotification(List<NotificationDTO> notificationDTOList) {
		for (NotificationDTO notificationDTO : notificationDTOList) {
			ProductNotification productNotification =
					PrivateSellerProductPublishedNotification.class.getSimpleName().equals(notificationDTO.getType()) ?
							new PrivateSellerProductPublishedNotification() :
							PrivateSellerProductsPublishedNotification.class.getSimpleName().equals(notificationDTO.getType()) ?
									new PrivateSellerProductsPublishedNotification() : null;
			if (productNotification == null) {
				log.error("Couldn't create private seller product published notification for userId: " +
						Optional.ofNullable(notificationDTO.getTargetUser()).map(UserDTO::getId).orElse(null) +
						" and type: " + notificationDTO.getType());
				return;
			}
			productNotification
					.setProduct(productRepository.getOne(notificationDTO.getTargetObjectId()))
					.setUser(userRepository.getOne(notificationDTO.getTargetUserId()))
					.setCounter(notificationDTO.getCounter());

			self.createSync(productNotification);
		}
	}

	@Override
	public void createNoActivityNotificationsByUserIdsAndGuestTokens(List<Long> userIds, List<String> guestTokens){
		if((userIds == null || userIds.isEmpty()) && (guestTokens == null || guestTokens.isEmpty())) return;
		List<Notification> notifications = new ArrayList<>();
		if(userIds != null){
			for(Long userId : userIds){
				User user = userService.getUserById(userId).orElse(null);
				if(user == null) continue;
				notifications.add(new NoActivityNotification().setUser(user));
			}
		}
		if(guestTokens != null){
			for(String guestToken : guestTokens){
				if(Strings.isNullOrEmpty(guestToken)) continue;
				notifications.add(new NoActivityNotification().setGuestToken(guestToken));
			}
		}
		createSync(notifications);
	}

	@Async
	@Override
	public void createNoActivityNotificationsByUserIdsAndGuestTokensAsync(List<Long> userIds, List<String> guestTokens){
		self.createNoActivityNotificationsByUserIdsAndGuestTokens(userIds, guestTokens);
	}

	@Override
	public void createAddBirthdateAndAvatarNotificationsByUserIds(List<Long> userIds){
		if(userIds == null || userIds.isEmpty()) return;
		List<Notification> notifications = new ArrayList<>();
		for(Long userId : userIds){
			User user = userService.getUserById(userId).orElse(null);
			if(user == null || user.getBirthDate() != null || user.getAvatarPath() != null) continue;
			notifications.add(new AddBirthdateAndAvatarNotification().setUser(user));
		}
		createSync(notifications);
	}

	@Async
	@Override
	public void createAddBirthdateAndAvatarNotificationsByUserIdsAsync(List<Long> userIds) {
		self.createAddBirthdateAndAvatarNotificationsByUserIds(userIds);
	}

	@Override
	public void createProductRequestNotification(List<NotificationDTO> notificationDTOList) {
		Map<Long, User> userMap = userRepository.findAllById(notificationDTOList
				.stream()
				.map(NotificationDTO::getTargetUser)
				.filter(Objects::nonNull)
				.map(UserDTO::getId)
				.collect(Collectors.toList())
		).stream().collect(Collectors.toMap(User::getId, Function.identity()));

		List<Notification> notificationsForCreate = notificationDTOList.stream()
				.map(it -> mapDTOToProductRequestNotification(it, userMap))
				.collect(Collectors.toList());

		self.createSync(notificationsForCreate);
	}

	private Notification mapDTOToProductRequestNotification(NotificationDTO dto, Map<Long, User> userMap) {
		Optional<UserDTO> targetUserDto = Optional.ofNullable(dto.getTargetUser());
		Notification result;
		if (InformAboutProductRequestServiceNotification.class.getSimpleName().equals(dto.getType())) {
			result = new InformAboutProductRequestServiceNotification();
		} else if (NeedPublishBuyerProductRequestNotification.class.getSimpleName().equals(dto.getType())) {
			result = new NeedPublishBuyerProductRequestNotification();
		} else if (NoResponseBuyerProductRequestNotification.class.getSimpleName().equals(dto.getType())) {
			result = new NoResponseBuyerProductRequestNotification()
					.setProductRequestId(dto.getTargetObjectId());
		} else if (InformAboutCountOfUserWhoCreateProductRequestNotification.class.getSimpleName().equals(dto.getType())) {
			result = new InformAboutCountOfUserWhoCreateProductRequestNotification();
		} else if (MotivateCreateProductResponseNotification.class.getSimpleName().equals(dto.getType())) {
			result = new MotivateCreateProductResponseNotification();
		} else {
			return null;
		}

		result.setGuestToken(dto.getGuestToken())
				.setUser(targetUserDto.map(UserDTO::getId).map(userMap::get).orElse(null))
				.setCounter(dto.getCounter());
		return result;
	}

	@Override
	public List<Long> findUserIdsWithUncompletedNotifications() {
		return notificationRepository.findUserIdsWithUncompletedNotifications();
	}

	@Override
	public List<String> findGuestTokensWithUncompletedNotifications() {
		return notificationRepository.findGuestTokensWithUncompletedNotifications();
	}

	@Override
	public boolean checkActionCompleted(User user) {
		return self.checkActionCompleted(user, true);
	}

	@Override
	public boolean checkActionCompleted(User user, NotificationRequest request) {
		return self.checkActionCompleted(user, true, request);
	}

	@Override
	public boolean checkActionCompleted(User user, boolean modifyNotifications){
		return self.checkActionCompleted(user, modifyNotifications, null);
	}

	@Transactional
	@Override
	public boolean checkActionCompleted(User user, boolean modifyNotifications, NotificationRequest request) {
		List<Notification> notifications = findActionNotCompletedNotifications(user);
		return checkNotificationCompletions(modifyNotifications, notifications, request);
	}

	private boolean checkNotificationCompletions(boolean modifyNotifications, List<Notification> notifications) {
		return checkNotificationCompletions(modifyNotifications, notifications, null);
	}

	private boolean checkNotificationCompletions(boolean modifyNotifications, List<Notification> notifications, NotificationRequest notificationRequest) {
		boolean hasChanges = false;
		//PUR-992: Добавили этот костыль для оптимизации. Некоторые пользователи имеют много (>50) нотификаций, которые требуют действий
		//В таких случаях данный метод работает долго.
		//Как только насчитаем pageSize нотификаций, которые требуют действий то остановимся далее проверять нотификации.
		int notCompletedNotificationCounter = 0;
		for(Notification notification : notifications){
			List<NotificationProcessor> suitableProcessors = getSuitableProcessors(notification);
			if(suitableProcessors == null || suitableProcessors.isEmpty()) continue;
			boolean isNotificationCompleted = false;
			for(NotificationProcessor processor : suitableProcessors) {
				ZonedDateTime actionCompletedTime = processor.getActionCompletedTime(notification);
				if(actionCompletedTime != null) {
					if (modifyNotifications) notification.setActionCompletedTime(actionCompletedTime);
					hasChanges = true;
					isNotificationCompleted = true;
				}
			}
			if (!isNotificationCompleted) {
				notCompletedNotificationCounter++;
			}
			if (notificationRequest != null && notCompletedNotificationCounter >= notificationRequest.getPageSize()) {
				return hasChanges;
			}
		}
		return hasChanges;
	}

	@Override
	@Transactional
	public void completeActions(String dtype, TemporalAmount period, int pageSize) {
		if (!notificationProcessorsByTypes.containsKey(dtype)) {
			log.warn("Not found notification processor for dtype {}", dtype);
			return;
		}
		PageRequest pageRequest = PageRequest.of(0, pageSize);
		org.springframework.data.domain.Page<Notification> notificationsPage = notificationRepository.findAllByDtypeAndCreateTimeIsAfterAndActionCompletedTimeIsNull(dtype, ZonedDateTime.now().minus(period), pageRequest);
		checkNotificationCompletions(true, notificationsPage.getContent());
		for (int i = 0; i < notificationsPage.getTotalPages(); i++) {
			notificationsPage = notificationRepository.findAllByDtypeAndCreateTimeIsAfterAndActionCompletedTimeIsNull(dtype, ZonedDateTime.now().minus(period), pageRequest.next());
			checkNotificationCompletions(true, notificationsPage.getContent());
		}
	}

	private List<NotificationProcessor> getSuitableProcessors(Notification notification){
		return notificationProcessorsByTypes.get(notification.getDtype());
	}

	@Override
	public boolean checkActionCompleted(List<Long> userIds){
		if(userIds == null || userIds.isEmpty()) return false;
		boolean result = false;
		List<User> users = userRepository.findAllById(userIds);
		for(User user : users){
			result |= checkActionCompleted(user);
		}
		return result;
	}

	@Async
	@Override
	public void checkActionCompletedAsync(List<Long> userIds){
		self.checkActionCompleted(userIds);
	}

	@Override
	@Transactional(readOnly = true)
	public List<String> findUserIdsWithActionCompletedNotificatins(List<Long> userIdsToCheck){
		List<String> userIdsWithChangedBubbles = new ArrayList<>();
		List<User> users = userRepository.findAllById(userIdsToCheck);
		for(User user : users){
			if(checkActionCompleted(user, false)) userIdsWithChangedBubbles.add(user.getId().toString());
		}
		return userIdsWithChangedBubbles;
	}

	@Override
	public List<String> findUkeysForNotification(Class<? extends Notification> type, int hoursLimitFrom, int hoursLimitTo, int count){
		//Ищем гостей и пользователей, активных в период hoursLimitFrom - hoursLimitTo часов назад, никогда не получавших уведомления type
		ZonedDateTime toTime = ZonedDateTime.now().minusHours(hoursLimitTo);
		ZonedDateTime fromTime = ZonedDateTime.now().minusHours(hoursLimitFrom);
		return notificationRepository.findUkeysActiveInPeriodWhoNeverGotNotificationType(fromTime, toTime, type.getSimpleName(), count);
	}

	@Override
	public List<String> findUkeysWithNoOrdersForNotification(Class<? extends Notification> type, int hoursLimitFrom, int hoursLimitTo, int count){
		//Ищем гостей и пользователей, активных в период hoursLimitFrom - hoursLimitTo часов назад, никогда не получавших уведомления type
		//и не совершавших покупок
		ZonedDateTime toTime = ZonedDateTime.now().minusHours(hoursLimitTo);
		ZonedDateTime fromTime = ZonedDateTime.now().minusHours(hoursLimitFrom);
		return notificationRepository.findUkeysActiveInPeriodWhoNeverGotNotificationTypeWithNoOrders(fromTime, toTime, type.getSimpleName(), count);
	}

	@Override
	public List<String> findUkeysForNotification(Class<? extends Notification> type, int hoursLimitTo, int count){
		//Начинаем искать по активностям 100-летней давности
		return findUkeysForNotification(type, 100 * 365 * 24, hoursLimitTo, count);
	}

	@Override
	public List<Long> findNotificationsToClose(int count){
		List<Long> result = new ArrayList<>();
		//Выключаем старые офферы
		//result.addAll(notificationRepository.getOfferNotificationIdsToClose(count));
		result.addAll(notificationRepository.getProductNotificationIdsToClose(count));
		result.addAll(notificationRepository.getCartNotificationIdsToClose(count));
		result = result.stream().distinct().collect(Collectors.toList());
		if(result.size() > count) result = result.subList(0, count);
		return result;
	}

	@Override
	public List<Long> findUserIdsByUserAndDTypesAndCreateTimeAfter(List<Long> userIds, List<String> dtypes, ZonedDateTime time) {
		return notificationRepository.findUserIdsByUserAndDTypesAndCreateTimeAfter(userIds, dtypes, time);
	}

	@Override
	public void maybeLinkNotificationsToUser(User user, String guestToken){
		if(user == null || Strings.isNullOrEmpty(guestToken)) return;
		List<Long> notificationIdsToBeLinked = notificationRepository.getNotificationIdsWhichShouldBeLinkedToUser(guestToken);
		if(!notificationIdsToBeLinked.isEmpty()) {
			self.linkNotificationsToUser(user, guestToken);
		}
	}

	@Override
	@Transactional
	public void linkNotificationsToUser(User user, String guestToken){
		notificationRepository.linkToUser(user, guestToken);
		//Новая версия очистки дубликатов базовых уведомлений
		List<Long> duplicateIds = deleteNotificationDuplicates(user);

		//Если были дубликаты, то обновляем гостевой токен всех старых уведомлений до текущего. Это нужно чтобы при выходе для текущего токена не создаваись новые уведомления, не допускающие дубликатов
		//Решено не делать этого больше, т.к. пользователи ходят с разных устройств с разными токенами
		//и приведение к единому токену вносит путаницу в происходящее
		//Пока остается здесь для истории. После проверки корректности работы пушей это будет удалено
		//if(!duplicateIds.isEmpty()) notificationRepository.updateGuestTokenForUserNotifications(user, guestToken);
	}

	@Override
	public List<ProductNotification> getProductPublishedNotificationsForSaving() {
		List<NotificationRepository.ProductPublishedNotificationInfoRow> rows = notificationRepository
				.getInformationForProductPublishedNotification(productPublicationSaveDefaultInterval, productPublicationSaveSelectInterval);

		List<Long> productIds = rows.stream()
				.map(NotificationRepository.ProductPublishedNotificationInfoRow::getProductId)
				.collect(Collectors.toList());
		Map<Long, Product> idToProduct = productRepository.findAllById(productIds)
				.stream()
				.collect(Collectors.toMap(Product::getId, Function.identity()));

		List<Long> userIds = rows.stream()
				.map(NotificationRepository.ProductPublishedNotificationInfoRow::getUserId)
				.collect(Collectors.toList());
		Map<Long, User> idToUser = userRepository.findAllById(userIds)
				.stream()
				.collect(Collectors.toMap(User::getId, Function.identity()));

		return rows.stream()
				.map(it -> createPublicationNotification(
						idToProduct.get(it.getProductId()), idToUser.get(it.getUserId()), it.getProductCount()))
				.collect(Collectors.toList());
	}

	private ProductNotification createPublicationNotification(Product product, User user, int productCount) {
		if (productCount > 1) {
			return (PrivateSellerProductsPublishedNotification) new PrivateSellerProductsPublishedNotification()
					.setProduct(product)
					.setCounter(productCount)
					.setUser(user);
		}

		return (PrivateSellerProductPublishedNotification) new PrivateSellerProductPublishedNotification()
				.setProduct(product)
				.setUser(user);
	}

	@Override
	public void createMindboxNotification(List<MindboxNotificationDTO> notificationDTOList) {
		Set<Long> userIdSet = notificationDTOList
				.stream()
				.map(MindboxNotificationDTO::getUserId)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		Map<Long, User> userMap = userRepository.findAllById(userIdSet).stream().collect(Collectors.toMap(User::getId, Function.identity()));

		List<Device> deviceList = deviceService.getDevicesByMindboxIDs(notificationDTOList
				.stream()
				.filter(it -> it.getUserId() == null)
				.map(MindboxNotificationDTO::getMindboxID)
				.filter(Objects::nonNull)
				.collect(Collectors.toList())
		);

		Map<Long, Device> deviceMap = deviceList.stream()
				.filter(it -> it.getUserId() == null)
				.collect(Collectors.toMap(Device::getMindboxId, Function.identity(), (d1, d2) -> d1));

		List<Notification> notificationToCreate = new ArrayList<>();
		for (MindboxNotificationDTO notificationDTO : notificationDTOList) {
			User user = null;
			String guestToken = null;

			if (notificationDTO.getUserId() != null) {
				 user = userMap.get(notificationDTO.getUserId());
			} else if (notificationDTO.getMindboxID() != null) {
				Device device = deviceMap.get(notificationDTO.getMindboxID());
				if (device != null) {
					guestToken = device.getGuestToken();
				}
			}

			if (user == null && Strings.isNullOrEmpty(guestToken)) {
				continue;
			}

			ContentNotification notification = new ContentNotification();
			notification.setUser(user);
			notification.setGuestToken(guestToken);
			notification.setTitle(notificationDTO.getTitle());
			notification.setTxt(notificationDTO.getBody());
			notification.setUrl(notificationDTO.getAppUrl());
			notification.setImageUrl(notificationDTO.getImageUrl());
			notification.setTransactionalId(notificationDTO.getTransactionId());

			notificationToCreate.add(notification);
		}

		self.createSync(notificationToCreate);
	}

	/**
	 * Проверяет отсутствие уведомления определенного типа пользователю начиная с определенной даты.
	 * Что бы не отправлять уведомления чаще разрешенного
	 * @param userId
	 * @param dtype
	 * @param frequencyOfNotifications
	 */
	@Override
	public boolean checkNotificationNotExistsForBuyerThisPeriod(Long userId, String dtype, ZonedDateTime frequencyOfNotifications) {
		int count = notificationRepository.countByUserAndDtypeThisMonth(notNullOrZero(userId), notNullOrEmpty(dtype), frequencyOfNotifications);
		return count == 0;
	}

	/**
	 * Создает уведомления CreatePublicationNotification для указанных пользователей
	 * @param userIds
	 */
	public void createCreatePublicationNotifications(List<Long> userIds) {
		if (userIds == null || userIds.isEmpty()) {
			return;
		}

		Map<Long, User> userMap = userService.findRawUsersByUserIds(userIds).stream()
				.collect(Collectors.toMap(User::getId, user -> user));

		List<Notification> notifications = userIds.stream()
				.filter(userMap::containsKey)
				.map(userId -> new CreatePublicationNotification()
						.setUser(userMap.get(userId)))
				.collect(Collectors.toList());

		if (userMap.keySet().size() < userIds.size()) {
			List<Long> missingUserIds = userIds.stream()
					.filter(userMap::containsKey)
					.collect(Collectors.toList());

			log.warn("create create publication notification: some notifications were not created: missing user ids = {}", missingUserIds);
		}

		createSync(notifications);
	}


	/**
	 * Удаляет дубликаты уведомлений определенного типа пользователя и возвращает их идентификаторы.
	 * Если дубликатов не было - возвращает пустой список
	 * @param user
	 * @param dtype
	 * @return
	 */
	private List<Long> deleteNotificationDuplicates(User user, String dtype){
		List<Long> notificationIds = notificationRepository.findAllIdsByUserAndDtype(user.getId(), dtype);
		//Если не более одного уведомления, значит дубликатов нет
		if(notificationIds.size() <= 1) return Collections.emptyList();
		//Будем считать дубликатами все уведомления, кроме первого
		List<Long> duplicateIds = notificationIds.subList(1, notificationIds.size());
		//Удаляем дубликаты
		deleteNotificationsByIds(duplicateIds);
		return duplicateIds;
	}

	/**
	 * Удаляет дубликаты уведомлений пользователя и возвращает их идентификаторы.
	 * Если дубликатов не было - возвращает пустой список
	 * @param user
	 * @return
	 */
	private List<Long> deleteNotificationDuplicates(User user){
		List<Long> result = new ArrayList<>();
		for(String noDuplicatesNotificationType : noDuplicatesNotificationTypes){
			result.addAll(deleteNotificationDuplicates(user, noDuplicatesNotificationType));
		}
		return result;
	}

	private void deleteNotificationsByIds(Collection<Long> ids){
		notificationRepository.setDeleted(ids);
	}

	private NotificationGroupDTO getNotificationGroupDTO(NotificationGroup notificationGroup, boolean selected){
		if(notificationGroup == null) return null;
		return new NotificationGroupDTO().setId(notificationGroup.getId())
				.setName(notificationGroup.getName()).setDescription(notificationGroup.getDescription())
				.setHint(notificationGroup.getHint()).setSortOrder(notificationGroup.getSortOrder())
				.setIsGeneral(notificationGroup.isGeneral()).setIsSelected(selected);
	}

	private List<NotificationGroupDTO> getAllNotHiddenNotificationGroupsDTO(List<Long> selectedIds, UserSubscriptionType subscriptionType){
		List<NotificationGroupDTO> result = new ArrayList<>();
		for(NotificationGroup notificationGroup : getNotHiddenNotificationGroupsForSubscriptionType(subscriptionType)){
			result.add(getNotificationGroupDTO(notificationGroup, selectedIds != null && selectedIds.contains(notificationGroup.getId())));
		}
		return result;
	}

    private void checkUserNotifications(User user, String guestToken, NotificationRequest notificationRequest) {
	    maybeLinkNotificationsToUser(user, guestToken);
	    //SELF ASYNC???
	    if(user != null) checkActionCompleted(user, notificationRequest);
    }

	private <T extends Notification> List<T> findActionNotCompletedNotifications(User user, Class<? extends Notification>[] types){
		if(user == null || types == null || types.length == 0) return Collections.emptyList();
		List<String> typesStr = Arrays.stream(types).map(t -> t.getSimpleName()).collect(Collectors.toList());
		return (List<T>) notificationRepository.findAllUncompleted(user.getId(), typesStr);
	}

	private List<Notification> findActionNotCompletedNotifications(User user){
		if(user == null) return Collections.emptyList();
		return notificationRepository.findAllUncompleted(user.getId());
	}

	@Transactional
	public void createItemInBoutiqueNotificationsIfNeed(Long boutiqueId, List<Long> productIds) {
		Optional<ProductItemLocation> productItemLocation = boutiqueService.getProductItemLocationById(boutiqueId);
		if(productItemLocation.isPresent()) {
			Map<Product, List<Long>> productOrdersBuyerIds = orderService.getProductOrdersBuyerIdsMapByProductIdsAndState(productIds, OrderState.CREATED);
			createCartItemInBoutiqueNotifications(productOrdersBuyerIds, productItemLocation.get());
			Map<Product, List<Long>> productUserIds = likeService.getProductUserIdsMapFromMoscowCityByLikedProducts(productIds);
			createWishlistItemInBoutiqueNotifications(productUserIds, productItemLocation.get());
		}
	}

	private void createCartItemInBoutiqueNotifications(Map<Product, List<Long>> productOrdersBuyerIds, ProductItemLocation productItemLocation) {
		for (Map.Entry<Product, List<Long>> productOrderBuyerIds : productOrdersBuyerIds.entrySet()) {
			Product product = productOrderBuyerIds.getKey();
			List<Long> userIds = productOrderBuyerIds.getValue();
			Map<Long, User> userMap = userService.findRawUsersByUserIds(userIds).stream()
					.collect(Collectors.toMap(User::getId, user -> user));
			List<Notification> cartItemInBoutiqueNotifications = userIds.stream()
					.filter(userMap::containsKey)
					.map(userId -> new CartItemInBoutiqueNotification(productItemLocation)
							.setProduct(product)
							.setUser(userMap.get(userId)))
					.collect(Collectors.toList());
			if(userMap.size() < userIds.size()) {
				List<Long> missingUserIds = userIds.stream()
						.filter(userId -> !userMap.containsKey(userId))
						.collect(Collectors.toList());
				log.warn("create CartItemInBoutiqueNotifications: some notifications were not created for product {} : missing user ids = {}", product.getId(), missingUserIds);
			}
			createSync(cartItemInBoutiqueNotifications);
		}
	}

	private void createWishlistItemInBoutiqueNotifications(Map<Product, List<Long>> productUserIdsMap, ProductItemLocation productItemLocation) {
		for (Map.Entry<Product, List<Long>> productUserIds : productUserIdsMap.entrySet()) {
			Product product = productUserIds.getKey();
			List<Long> userIds = productUserIds.getValue();
			Map<Long, User> userMap = userService.findRawUsersByUserIds(userIds).stream()
					.collect(Collectors.toMap(User::getId, user -> user));
			List<Notification> wishlistItemInBoutiqueNotifications = userIds.stream()
					.filter(userMap::containsKey)
					.map(userId -> new WishlistItemInBoutiqueNotification(productItemLocation)
							.setProduct(product)
							.setUser(userMap.get(userId)))
					.collect(Collectors.toList());
			if (userMap.keySet().size() < userIds.size()) {
				List<Long> missingUserIds = userIds.stream()
						.filter(userId -> !userMap.containsKey(userId)).collect(Collectors.toList());
				log.warn("create WishlistItemInBoutiqueNotifications: some notifications were not created for product {} : missing user ids = {}", product.getId(), missingUserIds);
			}
			createSync(wishlistItemInBoutiqueNotifications);
		}
	}

	@Override
	public void completeOrderDeliveredToBuyerRequestConfirmationNotification(long orderId) {
		List<Notification> confirmationNotifications = getNotificationsByOrderIds(
				Collections.singletonList(orderId), OrderDeliveredToBuyerRequestConfirmationNotification.class);
		for (Notification notification : confirmationNotifications) {
			if (!notification.isActionCompleted()) {
				notification.setActionCompletedTime(ZonedDateTime.now());
				if (notification.getReadTime() == null) {
					notification.setReadTime(ZonedDateTime.now());
				}
				notificationRepository.save(notification);
			}
		}
	}
}
