package su.reddot.domain.service.notification.amqp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.notification.Notification;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class UnifiedNotificationEventConverter {

    private final List<NotificationEventConverter> converters;

    public Optional<Notification> convert(NotificationEvent event) {
        Optional<NotificationEventConverter> foundConverter = findConverter(event.getType());
        if (!foundConverter.isPresent()) {
            log.warn("No converter found for notification event type {}", event.getType());
        }
        return foundConverter.flatMap(converter -> converter.convert(event));
    }

    private Optional<NotificationEventConverter> findConverter(String type) {
        return converters.stream()
                         .filter(converter -> Objects.equals(type, converter.getType()))
                         .findFirst();
    }
}
