package su.reddot.presentation.api.v3.domain;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.product.domain.CommissionGridDTOV3;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
public class UserDTO {
    private Long id;
    private String email;
    private String phone;
    private String nickname;
    private OffsetDateTime registrationTime;
    private OffsetDateTime activationTime;
    private User.Sex sex;
    private OffsetDateTime birthDate;
    private Boolean isTrusted;
    private Long countryId;
    private User.UserType userType;
    private String position;
    private SellerType sellerType;
    private Long pickupCountryId;
    private Boolean celebrity;
    private Boolean bestFriend;
    private Boolean legalEntity;
    private Boolean vip;
    private Boolean pro;
    private Boolean banned;
    private String firstName;
    private String lastName;
    private Boolean admin;
    private Boolean agentSeller;
    private Boolean deleted;
    private Boolean moderator;
    private String avatarPath;
    private List<String> tags = new ArrayList<>();
    private CommissionGridDTOV3 commissionGrid;
    private Boolean acceptsReturns;
}
