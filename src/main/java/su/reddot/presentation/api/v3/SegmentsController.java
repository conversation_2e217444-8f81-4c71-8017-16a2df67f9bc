package su.reddot.presentation.api.v3;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.adminpanel.segment.domain.CreateSegmentRequest;
import su.reddot.domain.service.adminpanel.segment.domain.NewAdminSegmentDTO;
import su.reddot.domain.service.adminpanel.segment.domain.SegmentUserDTO;
import su.reddot.domain.service.adminpanel.segment.domain.UpdateSegmentsRequest;
import su.reddot.domain.service.adminpanel.segment.SegmentService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v3/admin/segments")
@Slf4j
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ADMIN','USER_MODERATION')")
public class SegmentsController {
    private final SegmentService segmentService;

    @ApiOperation("Get segments by user")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/user/{userId}")
    public Api2Response<List<NewAdminSegmentDTO>> getUserSegments(@PathVariable("userId") Long userId) {
        Api2Response<List<NewAdminSegmentDTO>> response = Api2Response.create();

        return response.success(segmentService.getSegmentsByUser(userId));
    }

    @ApiOperation("Get all segments")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/all")
    public Api2Response<List<NewAdminSegmentDTO>> getAllSegments() {
        Api2Response<List<NewAdminSegmentDTO>> response = Api2Response.create();

        return response.success(segmentService.getAllSegments());
    }

    @ApiOperation("Get all segments")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/{segmentId}/users")
    public Api2Response<List<SegmentUserDTO>> getSegmentUsers(@PathVariable("segmentId") Long segmentId) {
        Api2Response<List<SegmentUserDTO>> response = Api2Response.create();

        return response.success(segmentService.getUsersBySegment(segmentId));
    }

    @ApiOperation("Create segment")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "")
    public Api2Response<NewAdminSegmentDTO> createSegment(@RequestBody CreateSegmentRequest request) {
        Api2Response<NewAdminSegmentDTO> response = Api2Response.create();

        return response.success(segmentService.createSegment(request));
    }

    @ApiOperation("Bind tag to group")
    @ResponseStatus(HttpStatus.OK)
    @PostMapping(value = "/user")
    public Api2Response<List<NewAdminSegmentDTO>> updateUserSegments(@RequestBody UpdateSegmentsRequest request) {
        Api2Response<List<NewAdminSegmentDTO>> response = Api2Response.create();

        return response.success(segmentService.updateUserSegments(request));
    }

    @ApiOperation("Delete segment")
    @ResponseStatus(HttpStatus.OK)
    @DeleteMapping(value = "/{segmentId}")
    public Api2Response<Void> deleteSegment(@PathVariable("segmentId") Long segmentId) {
        Api2Response<Void> response = Api2Response.create();
        segmentService.deleteSegment(segmentId);
        return response.success("ok");
    }
}
