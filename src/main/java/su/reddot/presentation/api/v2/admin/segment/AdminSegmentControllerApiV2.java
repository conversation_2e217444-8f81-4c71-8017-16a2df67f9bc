package su.reddot.presentation.api.v2.admin.segment;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import su.reddot.domain.service.dto.segment.SegmentDTO;
import su.reddot.domain.service.dto.segment.UploadUserIdsForSegmentDTO;
import su.reddot.domain.service.segment.UserSegmentCsvParserService;
import su.reddot.domain.service.segment.UserSegmentService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v2/admin/segment")
@Slf4j
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ADMIN','CONTENT_MODERATION')")
public class AdminSegmentControllerApiV2 {

    private final UserSegmentService segmentService;
    private final UserSegmentCsvParserService segmentCsvParserService;

    @GetMapping("/allSegments")
    public Api2Response<List<SegmentDTO>> getAllSegments() {
        Api2Response<List<SegmentDTO>> response = Api2Response.create();
        return response.success(segmentService.findAll());
    }

    @PostMapping("/update")
    public Api2Response<SegmentDTO> updateUserSegment(@RequestBody SegmentDTO segmentDTO) {
        Api2Response<SegmentDTO> response = Api2Response.create();
        SegmentDTO update = segmentService.update(segmentDTO);
        return response.success(update);
    }

    @DeleteMapping("/{segmentId}")
    public Api2Response<SegmentDTO> deleteSegment(@PathVariable("segmentId") Long segmentId) {
        Api2Response<SegmentDTO> response = Api2Response.create();
        SegmentDTO deleteUserSegment = segmentService.deleteUserSegment(segmentId);
        return response.success(deleteUserSegment);
    }

    @SneakyThrows
    @PostMapping("/upload/csv/{segmentId}")
    public Api2Response<UploadUserIdsForSegmentDTO> uploadCsvFile(@PathVariable("segmentId") Long segmentId, @RequestParam("file") MultipartFile csvMultipartFile) {
        Api2Response<UploadUserIdsForSegmentDTO> response = Api2Response.create();
        List<Long> userIds = segmentCsvParserService.parseCsvWithProductIds(csvMultipartFile.getInputStream());
        return response.success(segmentService.addSegmentForUsers(segmentId, userIds));
    }
}
