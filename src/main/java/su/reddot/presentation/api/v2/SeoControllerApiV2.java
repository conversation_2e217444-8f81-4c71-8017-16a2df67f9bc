package su.reddot.presentation.api.v2;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import ru.oskelly.seo.model.SeoDataDTO;
import su.reddot.domain.service.seo.SeoService;

@RestController
@RequestMapping(value = "/api/v2/seo")
@Slf4j
@RequiredArgsConstructor
public class SeoControllerApiV2 {

    private final SeoService seoService;

    @ApiOperation("Get seo data")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value = "/getData", produces = MediaType.APPLICATION_JSON_VALUE)
    public Api2Response<SeoDataDTO> getSeoData(@RequestParam(value = "categoryId", required = false) Long categoryId,
                                               @RequestParam(value = "brandId", required = false) Long brandId,
                                               @RequestParam(value = "modelId", required = false) Long modelId) {
        Api2Response<SeoDataDTO> response = Api2Response.create();

        return response.success(seoService.getSeoDataCached(categoryId, brandId, modelId));
    }
}
