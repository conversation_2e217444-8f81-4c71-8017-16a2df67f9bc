package su.reddot.presentation.api.v2;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.dto.purchaser.AvatarPathDTO;
import su.reddot.domain.service.dto.purchaser.PurchaserSyncInfoDTO;
import su.reddot.domain.service.salesapp.model.PurchaserFullInfoDTO;
import su.reddot.domain.service.salesapp.model.PurchaserWithIdsDTO;
import su.reddot.domain.service.user.UserService;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v2/account/purchaser")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority(T(su.reddot.domain.model.enums.AuthorityName).MERCAUX_ADMIN)")
public class PurchaserAccountController {

    private final UserService userService;

    @PostMapping("/user-purchaser-sync")
    public ResponseEntity<PurchaserWithIdsDTO> syncWithPurchaser(@RequestBody PurchaserWithIdsDTO purchaserWithIds) {
        return ResponseEntity.ok(userService.syncPurchaserWithUser(purchaserWithIds));
    }

    @GetMapping("/user-field/avatar")
    public ResponseEntity<List<AvatarPathDTO>> getPurchaserAvatarPath(@RequestParam("userIds") List<Long> userIds) {
        List<UserDTO> userList = userService.findByUserIds(userIds);
        List<AvatarPathDTO> avatars = userList.stream()
                .map(user -> new AvatarPathDTO(user.getId(), user.getAvatarPath()))
                .collect(Collectors.toList());
        return ResponseEntity.ok(avatars);
    }

    @GetMapping("/purchaser-info-sync")
    ResponseEntity<PurchaserSyncInfoDTO> syncPurchaserInfo(@RequestParam("userId") Long userId) {
        return ResponseEntity.ok(userService.syncPurchaserInfo(userId));
    }

    @PostMapping("/batch-sync")
    ResponseEntity<List<PurchaserWithIdsDTO>> syncWithPurchaserBatch(@RequestBody List<PurchaserWithIdsDTO> request,
                                                                     @RequestParam Boolean withUserId) {
        return ResponseEntity.ok(userService.syncWithPurchaserBatch(request, withUserId));
    }

    @GetMapping("/info")
    public ResponseEntity<PurchaserFullInfoDTO> getPurchaserInfo(@RequestParam("id") Long userId) {
        return ResponseEntity.ok(userService.getPurchaserInfo(userId));
    }
}
