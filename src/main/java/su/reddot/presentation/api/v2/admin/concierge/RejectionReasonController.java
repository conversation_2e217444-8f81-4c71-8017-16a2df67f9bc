package su.reddot.presentation.api.v2.admin.concierge;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.model.RejectionReason;
import su.reddot.domain.service.concierge.oskellyconcierge.PurchaseOrderService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.List;

@Slf4j
@Validated
@PreAuthorize("hasAnyAuthority('CONCIERGE_SALES_ADMIN','CONCIERGE_SOURCERS_ADMIN', 'SALES', 'SOURCER')")
@RestController
@RequiredArgsConstructor
public class RejectionReasonController implements RejectionReasonControllerDelegate {
    private final PurchaseOrderService purchaseOrderService;

    @Override
    public Api2Response<List<RejectionReason>> getReasons(RejectionReason.ObjectTypeEnum objectType, RejectionReason.OrderStatusEnum status) {
        log.info("Getting rejection reasons for object type: {}, status: {}", objectType, status);
        return new Api2Response<List<RejectionReason>>()
                .success("Причины отклонения получены", purchaseOrderService.getRejectionReasons(objectType, status));
    }
}
