package su.reddot.presentation.api.v2.admin.bargain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import su.reddot.domain.exception.ParseException;
import su.reddot.domain.service.bargain.BargainConverter;
import su.reddot.domain.service.bargain.BargainService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.bargain.*;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

@RestController
@RequestMapping(value = "/api/v2/admin/bargains")
@Slf4j
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ADMIN','OFFER_MODERATION')")
public class AdminBargainsControllerApiV2 {

    private final BargainService bargainService;
    private final BargainConverter converter;
    private final MessageSourceAccessor messageSourceAccessor;

    @ApiOperation("Получить страницу контрторгов с фильтрацией по заданным параметрам")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping
    public Api2Response<Page<AdminBargainLiteDTO>> getBargains(AdminBargainRequest request){
        Api2Response<Page<AdminBargainLiteDTO>> response = Api2Response.create();
        return response.success(messageSourceAccessor.getMessage("controller.AdminBargainsControllerApiV2.Bargains"), bargainService.getRawBargains(getBargainRequest(request), false)
                .map(b -> converter.getAdminBargainLiteDTO(b)));
    }

    @ApiOperation("Получить детализированную информацию о контрторге по его ID")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(value="/{id}")
    public Api2Response<AdminBargainDetailedDTO> getDetailedBargain(@PathVariable @Min(1) Long id){
        Api2Response<AdminBargainDetailedDTO> response = Api2Response.create();
        return response.success(messageSourceAccessor.getMessage("controller.AdminBargainsControllerApiV2.DetailedBargain"), converter.getAdminBargainDetailedDTO(bargainService.getRawBargain(id)));
    }

    @ApiOperation("Оживить статус товара из EXPIRED (дать возможность подтвердить, отклонить, купить)")
    @ResponseStatus(HttpStatus.OK)
    @PatchMapping(value="/{id}/restore-expired")
    public Api2Response<AdminBargainDetailedDTO> refreshBargain(@PathVariable @Min(1) Long id){
        Api2Response<AdminBargainDetailedDTO> response = Api2Response.create();
        return response.success(messageSourceAccessor.getMessage("controller.AdminBargainsControllerApiV2.RefreshBargain"), converter.getAdminBargainDetailedDTO(bargainService.restoreExpiredBargain(id)));
    }

    //Формирует BargainRequest для получения страницы торгов из сервиса
    private BargainService.BargainRequest getBargainRequest(AdminBargainRequest request){
        return new BargainService.BargainRequest()
                .bargainIds(request.bargainIds)
                .productIds(request.productIds)
                .buyerIds(request.buyerIds)
                .sellerIds(request.sellerIds)
                .startTime(Utils.parseLocalDateTime(request.startTime))
                .endTime(Utils.parseLocalDateTime(request.endTime))
                .states(converter.getBargainStates(request.bargainStates))
                .pageRequest(converter.getPageRequest(request.page, request.pageSize))
                .bargainSort(converter.getBargainSort(request.sort))
                .filterType(request.filterType)
                ;
    }


    @ApiModel(value = "AdminBargainRequest", description = "Запрос на страницу контрторгов для админки")
    @NoArgsConstructor
    @Getter @Setter
    public class AdminBargainRequest{
        @ApiModelProperty(value = "Идентификаторы торгов")
        private List<Long> bargainIds;
        @ApiModelProperty(value = "Идентификаторы товаров")
        private List<Long> productIds;
        @ApiModelProperty(value = "Идентификаторы покупателей")
        private List<Long> buyerIds;
        @ApiModelProperty(value = "Идентификаторы продавцов")
        private List<Long> sellerIds;
        @ApiModelProperty(value = "Начальное время в формате YYYY-MM-dd'T'HH:mm:ss'Z'")
        private String startTime;
        @ApiModelProperty(value = "Конечное время в формате YYYY-MM-dd'T'HH:mm:ss'Z'")
        private String endTime;
        @ApiModelProperty(value = "Статусы торгов")
        private List<BargainStateDTO.Enum> bargainStates;

        @ApiModelProperty(value = "Номер страницы")
        private Integer page;
        @ApiModelProperty(value = "Размер страницы")
        private Integer pageSize;
        @ApiModelProperty(value = "Сортировка")
        private BargainSortDTO.Enum sort;

        @ApiModelProperty(value = "Тип фильтрации")
        private String filterType;
    }


}
