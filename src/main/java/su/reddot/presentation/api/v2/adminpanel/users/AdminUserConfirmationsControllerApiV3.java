package su.reddot.presentation.api.v2.adminpanel.users;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.adminpanel.user.ConfirmationService;
import su.reddot.domain.service.adminpanel.user.confirmation.ConfirmationDetails;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationConfirmedRequest;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationEditedRequest;
import su.reddot.presentation.api.v2.adminpanel.users.dto.ConfirmationTransferredToUserModerationRequest;


@RestController
@RequestMapping(value = "/api/v3/admin/users/confirmations")
@Slf4j
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ADMIN','USER_MODERATION')")
public class AdminUserConfirmationsControllerApiV3 {

    private final ConfirmationService confirmationService;

    @PostMapping("/confirmed")
    public Api2Response<Void> processConfirmed(
            @RequestBody ConfirmationConfirmedRequest<? extends ConfirmationDetails> request
    ) {
        confirmationService.processConfirmed(request);
        return Api2Response.<Void>create().success("OK");
    }

    @PostMapping("/transferred-to-user-moderation")
    public Api2Response<Void> transferredToUserModeration(
            @RequestBody ConfirmationTransferredToUserModerationRequest<? extends ConfirmationDetails> request
    ) {
        confirmationService.transferredToUserModeration(request);
        return Api2Response.<Void>create().success("OK");
    }

    @PostMapping("/edited")
    public Api2Response<Void> processEdited(
            @RequestBody ConfirmationEditedRequest<? extends ConfirmationDetails> request
    ) {
        confirmationService.processEdited(request);
        return Api2Response.<Void>create().success("OK");
    }

}
