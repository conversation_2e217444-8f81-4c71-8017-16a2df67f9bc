package su.reddot.presentation.api.v2.osocial;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.dto.TokenizedPage;
import su.reddot.domain.service.dto.TokenizedPageable;
import su.reddot.domain.service.dto.osocial.CoreOSocialDiscoveryItemDto;
import su.reddot.domain.service.osocial.OSocialDiscoveryService;
import su.reddot.presentation.api.v2.Api2Response;

@RestController
@RequestMapping(value = "/api/v2/social/discovery")
@Slf4j
@RequiredArgsConstructor
public class OSocialDiscoveryController {

    private final OSocialDiscoveryService osocialDiscoveryService;

    @GetMapping
    public Api2Response<TokenizedPage<CoreOSocialDiscoveryItemDto>> getDiscovery(
            TokenizedPageable pageable,
            @RequestParam(name = "sectionCode", required = false) String sectionCode
    ) {
        return Api2Response.<TokenizedPage<CoreOSocialDiscoveryItemDto>>create()
                .success(
                        "OK",
                        osocialDiscoveryService.getDiscovery(pageable, sectionCode));
    }

    @GetMapping("/posts/{postId}/similar/by-image")
    public Api2Response<TokenizedPage<CoreOSocialDiscoveryItemDto>> getSimilarItems(
            TokenizedPageable pageable,
            @PathVariable long postId,
            @RequestParam(required = false) Long mediaId
            ) {
        return Api2Response.<TokenizedPage<CoreOSocialDiscoveryItemDto>>create().success("OK",
                osocialDiscoveryService.getSimilarItems(pageable, postId, mediaId));
    }
}