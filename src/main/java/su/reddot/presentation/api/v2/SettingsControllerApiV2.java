package su.reddot.presentation.api.v2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.setting.SettingService;

/**
 * <AUTHOR> on 12.07.19.
 */
@RestController
@RequestMapping(value = "/api/v2/settings")
@Slf4j
@RequiredArgsConstructor
public class SettingsControllerApiV2 {

    private final SettingService settingService;

    /**
     * Возвращает системную информацию для моб. приложений и других клиентов (версия АПИ и т.д.)).
     *
     * @return
     */
    @GetMapping
    public Api2Response<SettingService.Settings> getSettings(@RequestParam(required = false) Long countryId) {
        Api2Response<SettingService.Settings> response = Api2Response.create();

        return response.success("Application Settings", settingService.getSettings(countryId));
    }

}
