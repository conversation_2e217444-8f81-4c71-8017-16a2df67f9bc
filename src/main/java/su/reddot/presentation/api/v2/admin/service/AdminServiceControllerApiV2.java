package su.reddot.presentation.api.v2.admin.service;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.externalcatalog.ProductsToExternalCatalogCompletelySender;
import su.reddot.domain.service.following.FollowingService;
import su.reddot.domain.service.opencv.ProductsNoImgCompletelySender;
import su.reddot.domain.service.opencv.ProductsWithImgCompletelySender;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.product.badge.ProductBadgesService;
import su.reddot.domain.service.product.hiding.HideUserProductsService;
import su.reddot.infrastructure.bank.BankAccountService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.admin.dto.ProductIdsDTO;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping(value = "/api/v2/admin/service")
@PreAuthorize("hasAnyAuthority('ADMIN')")
@RequiredArgsConstructor
public class AdminServiceControllerApiV2 {

    private final OrderService orderService;
    private final ProductsToExternalCatalogCompletelySender productsToExternalCatalogCompletelySender;
    private final ProductsWithImgCompletelySender productsWithImgCompletelySender;
    private final ProductsNoImgCompletelySender productsNoImgCompletelySender;
    private final FollowingService followingService;
    private final HideUserProductsService hideUserProductsService;
    private final ProductBadgesService productBadgesService;

    @ApiOperation("Validate operations with bank registry and store validation time in bankOperation")
    @ResponseStatus(HttpStatus.OK)
    @PutMapping("/validateBankOperations")
    public Api2Response<String> validateBankOperations(@RequestParam String date) {
        Api2Response<String> response = new Api2Response<>();
        LocalDate validateDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        BankAccountService bankAccountService = orderService.getBankAccountService(TcbBankService.SCHEMA);
        return response.success("Validation Done", bankAccountService.validateOperations(validateDate));
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/sendProducts")
    public Api2Response<String> sendProducts() {
        productsToExternalCatalogCompletelySender.sendProductsCompletelyAsync(Collections.emptyList());
        return new Api2Response<String>()
                .success("Sending started", null);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/fillFollowingProposals")
    public Api2Response<String> fillFollowingsProposals() {
        followingService.fillFollowingsProposals();
        return new Api2Response<String>()
                .success("Followings proposals filled", null);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/updateOpenCvProducts")
    public Api2Response<String> updateOpenCvProducts(
            @RequestParam(defaultValue = "true") boolean includeImages,
            @RequestBody(required = false) ProductIdsDTO productIdsDTO
    ) {
        if (includeImages) {
            productsWithImgCompletelySender.sendProductsCompletelyAsync(productIdsDTO.getProductIds());
        } else {
            productsNoImgCompletelySender.sendProductsCompletelyAsync(productIdsDTO.getProductIds());
        }

        return new Api2Response<String>().success("Updating OpenCV products started", null);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/productHiding/markUsers")
    public Api2Response<Integer> markUsersForProductHiding() {
        Api2Response<Integer> response = new Api2Response<>();
        Integer markedUsersAmount = hideUserProductsService.markUsersForProductHiding();
        return response.success("Users marked to hide products", markedUsersAmount);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/productHiding/sendWarningNotifications")
    public Api2Response<String> sendWarningNotifications(
            @RequestParam(required = false) Integer count,
            @RequestParam(required = false) List<Long> userIds
    ) {
        Api2Response<String> response = new Api2Response<>();
        hideUserProductsService.sendWarningNotifications(count, userIds);
        return response.success("Warning notifications have been sent", null);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/productHiding/hideProducts")
    public Api2Response<String> hideProducts(
            @RequestParam(required = false) Integer count,
            @RequestParam(required = false) List<Long> userIds
    ) {
        Api2Response<String> response = new Api2Response<>();
        hideUserProductsService.hideProducts(count, userIds);
        return response.success("Products have been hidden", null);
    }

    @ResponseStatus(HttpStatus.OK)
    @PostMapping("/recalcSeasonBadges")
    public Api2Response<String> recalcSeasonBadges() {
        Api2Response<String> response = new Api2Response<>();
        productBadgesService.recalculateSeasonBadges();
        return response.success("Season badges recalculated", null);
    }
}
