package su.reddot.presentation.api.v2.purchaser;

import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.admin.SalesAppApiService;
import su.reddot.domain.service.salesapp.model.SelectionPurchaserViewDTO;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v2.Api2Response;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v2/selections")
@PreAuthorize("isAuthenticated()")
public class PurchaserUserSelectionsController {

	private final SalesAppApiService salesAppApiService;
	private final SecurityService securityService;

	@GetMapping("/{id}/purchaser-view")
	public Api2Response<SelectionPurchaserViewDTO> getSelectionPurchaserView(@PathVariable("id") Long id) {
		Api2Response<SelectionPurchaserViewDTO> response = Api2Response.create();
		Long currentUserId = securityService.getCurrentAuthorizedUserId();
		return response.success(salesAppApiService.getSelectionPurchaserView(id, currentUserId));
	}
}
