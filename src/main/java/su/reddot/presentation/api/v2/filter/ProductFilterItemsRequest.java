package su.reddot.presentation.api.v2.filter;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductFilterItemsRequest extends ProductFilterInfoRequest {

    private String sorting;

    private Integer page;

    private Integer pageLength;

    ProductFilterItemsRequest(ProductFilterItemsRequest request) {
        super(request);
        this.sorting = request.getSorting();
        this.page = request.getPage();
        this.pageLength = request.getPageLength();
    }

}
