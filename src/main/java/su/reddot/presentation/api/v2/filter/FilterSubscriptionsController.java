package su.reddot.presentation.api.v2.filter;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.filter.FiltrationServiceFeaturesService;
import su.reddot.domain.service.filter.SearchProductFilterProcessorEngine;
import su.reddot.domain.service.filter.processor.filter.impl.BannerSettingIdFilterProcessor;
import su.reddot.domain.service.filtersubscriptions.api.FilterSubscriptionsControllerApi;
import su.reddot.domain.service.filtersubscriptions.model.FilterSubscriptionRestDto;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.api.v2.Api2Response;

@ConditionalOnProperty("filter-subscriptions-service.enabled")
@RestController
@RequestMapping(value = "/api/v2/filter-subscriptions")
@RequiredArgsConstructor
public class FilterSubscriptionsController {

    private final FilterSubscriptionsControllerApi filterSubscriptionsControllerApi;
    private final SecurityService securityService;
    private final SearchProductFilterProcessorEngine productFilterProcessorEngine;
    private final FiltrationServiceFeaturesService featuresService;


    @GetMapping
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<List<FilterSubscriptionResponse>> getMyFilterSubscriptions() {
        Long userId = securityService.getCurrentAuthorizedUserId();

        List<FilterSubscriptionResponse> filterSubscriptions =
            filterSubscriptionsControllerApi.findByUserId(userId).getData()
                .stream().map(FilterSubscriptionResponse::new).collect(Collectors.toList());

        Api2Response<List<FilterSubscriptionResponse>> response = Api2Response.create();
        return response.success(filterSubscriptions);
    }

    @PostMapping
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<FilterSubscriptionResponse> saveNew(@RequestBody CreateFilterSubscriptionRequest incomingRequest) {
        FilterSubscriptionOriginalFeRequest originalFeRequest =
            removeBannerSettingIdFilter(incomingRequest.getOriginalFeRequest());

        su.reddot.domain.service.filtersubscriptions.model.CreateFilterSubscriptionRequest outgoingRequest =
            new su.reddot.domain.service.filtersubscriptions.model.CreateFilterSubscriptionRequest();

        outgoingRequest.setUserId(securityService.getCurrentAuthorizedUserId());
        outgoingRequest.setName(incomingRequest.getName());
        outgoingRequest.setNotificationsEnabled(incomingRequest.getNotificationsEnabled());
        outgoingRequest.setOriginalFeRequest(originalFeRequest);

        outgoingRequest.setCatalogRequest(productFilterProcessorEngine.buildFilterSubscriptionCatalogRequest(
            originalFeRequest, featuresService.getFiltrationServiceFeatures()
        ));

        FilterSubscriptionRestDto filterSubscription =
            filterSubscriptionsControllerApi.saveNew(outgoingRequest).getData();
        return Api2Response.<FilterSubscriptionResponse>create()
            .success(new FilterSubscriptionResponse(filterSubscription));
    }

    @PutMapping("{subscriptionId}")
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<FilterSubscriptionResponse> update(
        @PathVariable UUID subscriptionId,
        @RequestParam String setName,
        @RequestParam Boolean setNotificationsEnabled
    ) {
        //TODO: проверить, что подписка принадлежит текущему юзеру
        FilterSubscriptionRestDto filterSubscription =
            filterSubscriptionsControllerApi.update(subscriptionId, setName, setNotificationsEnabled).getData();
        return Api2Response.<FilterSubscriptionResponse>create()
            .success(new FilterSubscriptionResponse(filterSubscription));
    }

    @DeleteMapping("{subscriptionId}")
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<Void> delete(@PathVariable UUID subscriptionId) {
        //TODO: проверить, что подписка принадлежит текущему юзеру
        filterSubscriptionsControllerApi.delete(subscriptionId);
        return Api2Response.<Void>create().success("OK");
    }

    //TODO: убрать этот метод, когда заработает поиск по вторым главным. https://jira.oskelly.ru/projects/GR/issues/GR-94
    //Временное решение для избегания ошибки 500, сделано для возможности разработки на ios
    private <Req extends ProductFilterInfoRequest> Req removeBannerSettingIdFilter(Req request) {
        if (request.getHiddenFilters() != null) {
            request.getHiddenFilters().remove(BannerSettingIdFilterProcessor.FILTER_CODE);
        }
        return request;
    }
}
