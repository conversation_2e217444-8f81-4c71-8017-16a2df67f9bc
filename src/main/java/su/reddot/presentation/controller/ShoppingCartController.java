package su.reddot.presentation.controller;

import com.google.common.base.Strings;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.HandlerMapping;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.cart.CartService;
import su.reddot.domain.service.cart.GetCartRequest;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.security.token.UserIdAuthenticationToken;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.QueryParam;
import java.util.List;

@Controller
@RequiredArgsConstructor
public class ShoppingCartController {
    private final CartService cartService;
    private final UserService userService;
    private final AddressEndpointService addressEndpointService;
    private final SecurityService securityService;
    private final CounterpartyService counterpartyService;
    private final OrderService orderService;

    @GetMapping({"/cart",})
    public String getCart(Model m, UserIdAuthenticationToken t, HttpServletRequest r) {

        //OrderDTO cart = cartService.getCart();

        //m.addAttribute("cart", cart);
        return ("order" + r.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE)).toLowerCase();
    }


    @GetMapping({"/checkout"})
    public String getCheckout(Model m,
                              UserIdAuthenticationToken t,
                              @QueryParam("sellerId") Long sellerId,
                              @QueryParam("orderId") Long orderId,
                              @QueryParam("email") String email,
                              HttpServletRequest r) {

        String result = ("order" + r.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE)).toLowerCase();

        OrderDTO orderDTO = null;

        if (orderId != null) {
            orderDTO = orderService.getOrderDTO(orderService.getOrder(orderId));
        }

        if (orderDTO == null) {
            GroupedCart groupedCart = cartService.getGroupedCart(GetCartRequest.builder().build());
            if (groupedCart != null) {
                orderDTO = groupedCart.getGroup(sellerId);
            }
        }


        m.addAttribute("order", orderDTO);


        if (t == null) {
            if (!Strings.isNullOrEmpty(email)) {
                m.addAttribute("email", email);
                return result;
            }
            return "redirect:/checkout-auth?sellerId=" + sellerId;
        }


        User user = securityService.getCurrentAuthorizedUser();

        List<AddressEndpoint> addressEndpoints = addressEndpointService.findAllUserActiveAddressEndpoints(user);


        m.addAttribute("addressEndpoints", addressEndpoints);


        return result;
    }

    @GetMapping({"/checkout-auth"})
    public String getCheckoutAuth(Model m, UserIdAuthenticationToken t, @QueryParam("sellerId") Long sellerId, HttpServletRequest r) {

        String result = ("order" + r.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE)).toLowerCase();
        return result;
    }


}
