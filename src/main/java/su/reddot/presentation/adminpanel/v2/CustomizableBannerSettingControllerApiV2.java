package su.reddot.presentation.adminpanel.v2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import su.reddot.domain.service.adminpanel.v2.BannerSettingService;
import su.reddot.presentation.adminpanel.v2.dto.bannersetting.CustomizableBannerSettingDto;
import su.reddot.presentation.api.v2.Api2Response;

@RestController
@RequestMapping("/api/v2/admin/banner/customizablepage")
@PreAuthorize("hasAnyAuthority('ADMIN','CONTENT_MODERATION')")
@RequiredArgsConstructor
@Slf4j
public class CustomizableBannerSettingControllerApiV2 {

    private final BannerSettingService<CustomizableBannerSettingDto> bannerSettingService;

    @PostMapping
    public Api2Response<CustomizableBannerSettingDto> createEmptyBanner() {
        Api2Response<CustomizableBannerSettingDto> response = new Api2Response<>();
        CustomizableBannerSettingDto result = bannerSettingService.createEmptyBannerSetting();
        return response.success(result);
    }

    @GetMapping("/{bannerId}")
    public Api2Response<CustomizableBannerSettingDto> getBannerSetting(@PathVariable("bannerId") String bannerId) {
        Api2Response<CustomizableBannerSettingDto> response = new Api2Response<>();
        CustomizableBannerSettingDto result = bannerSettingService
                .getBannerSetting(bannerId);
        return response.success(result);
    }

    @PostMapping("/update")
    public Api2Response<CustomizableBannerSettingDto> updateBannerSetting(@RequestBody CustomizableBannerSettingDto bannerSetting) {
        Api2Response<CustomizableBannerSettingDto> response = new Api2Response<>();
        CustomizableBannerSettingDto result = bannerSettingService
                .updateBannerSetting(bannerSetting);
        return response.success(result);
    }
}
