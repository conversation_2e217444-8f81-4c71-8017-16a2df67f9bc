package su.reddot.presentation.adminpanel.v2.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.InstagramFeedContentBlock;
import su.reddot.domain.service.util.ContentBlockUtils;
import su.reddot.presentation.adminpanel.v2.dto.segment.ContentBlockSegmentDto;
import su.reddot.presentation.adminpanel.v2.dto.segment.property.InstagramFeedSegmentPropertiesDto;

import java.util.function.Function;

@Getter
@Setter
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InstagramFeedContentBlockDto
        extends DisabledSegmentedContentBlockDto<String, ContentBlockSegmentDto<InstagramFeedSegmentPropertiesDto>, InstagramFeedSegmentPropertiesDto> {

    public InstagramFeedContentBlockDto() {
        super();
        ContentBlockUtils.setDefaultSegmentsIfNeed(segments, it -> this.setSegments(it));
    }

    public InstagramFeedContentBlockDto(InstagramFeedContentBlock domain) {
        super(domain, Function.identity());
    }

    @Override
    public BaseContentBlock mapToDomain() {
        return new InstagramFeedContentBlock(this);
    }
}
