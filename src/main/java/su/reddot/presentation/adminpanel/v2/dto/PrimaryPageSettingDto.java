package su.reddot.presentation.adminpanel.v2.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageSetting;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageType;
import su.reddot.presentation.adminpanel.v2.dto.common.ContentBlockDto;
import su.reddot.presentation.adminpanel.v2.dto.common.DisabledContentBlockDto;
import su.reddot.presentation.adminpanel.v2.dto.common.TitledDisabledContentBlockDto;

@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PrimaryPageSettingDto {

    private String id;
    private PrimaryPageType type;
    private ContentBlockDto bannerBlock;
    private ContentBlockDto bannerCollectionBlock;
    private TitledDisabledContentBlockDto additionalCollectionBlock1;
    private TitledDisabledContentBlockDto additionalCollectionBlock2;
    private DisabledContentBlockDto blogBlock;
    private DisabledContentBlockDto storiesBlock;

    public PrimaryPageSettingDto(PrimaryPageSetting domain) {
        id = domain.getId();
        type = domain.getType();
        bannerBlock = new ContentBlockDto(domain.getBannerBlock());
        bannerCollectionBlock = new ContentBlockDto(domain.getBannerCollectionBlock());
        additionalCollectionBlock1 = new TitledDisabledContentBlockDto(domain.getAdditionalCollectionBlock1());
        additionalCollectionBlock2 = new TitledDisabledContentBlockDto(domain.getAdditionalCollectionBlock2());
        blogBlock = new DisabledContentBlockDto(domain.getBlogBlock());
        storiesBlock = new DisabledContentBlockDto(domain.getStoriesBlock());
    }
}
