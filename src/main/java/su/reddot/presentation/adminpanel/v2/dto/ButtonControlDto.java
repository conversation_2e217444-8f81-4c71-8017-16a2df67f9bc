package su.reddot.presentation.adminpanel.v2.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ButtonControlDto {

    private String id;
    private String title;
    private String url;
    private String imageUrl;
    private String imageBase64;
}
