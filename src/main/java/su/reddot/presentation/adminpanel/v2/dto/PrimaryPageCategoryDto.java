package su.reddot.presentation.adminpanel.v2.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PrimaryPageCategoryDto {
    private PrimaryPageCategory primaryPageCategory;
    private List<PrimaryPageConditionDto> conditionSettings = new ArrayList<>();

    public PrimaryPageCategoryDto(PrimaryPageCategory primaryPageCategory) {
        this.primaryPageCategory = primaryPageCategory;
    }
}
