package su.reddot.presentation.adminpanel.v2.dto.converter;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.adminpanel.v2.filter.AbstractFilter;
import su.reddot.domain.service.dto.primary.FilterDTO;
import su.reddot.presentation.adminpanel.v2.dto.filter.CategoryAndUserFilterDto;
import su.reddot.presentation.adminpanel.v2.dto.filter.EmptyFilterDto;
import su.reddot.presentation.adminpanel.v2.dto.filter.FilterContentDto;
import su.reddot.presentation.adminpanel.v2.dto.filter.ProductListFilterDto;
import su.reddot.presentation.adminpanel.v2.dto.filter.ProductRequestCategoryAndUserFilterDto;
import su.reddot.presentation.adminpanel.v2.dto.filter.ProductRequestListFilterDto;
import su.reddot.presentation.adminpanel.v2.dto.filter.UserProfileFilterDto;

@Component
@RequiredArgsConstructor
public class FilterContentDtoConverter implements DtoConverter<FilterContentDto, AbstractFilter>{

    @SneakyThrows
    @Override
    public AbstractFilter convertFrom(FilterContentDto dto) {
        if (dto != null) {
            AbstractFilter filter = null;
            if (dto.getCategoryAndUserFilter() != null) {
                filter = dto.getCategoryAndUserFilter().toDomain();
            } else if (dto.getProductListFilter() != null) {
                filter = dto.getProductListFilter().toDomain();
            } else if (dto.getUserProfileFilter() != null) {
                filter = dto.getUserProfileFilter().toDomain();
            }
            else if (dto.getProductRequestCategoryAndUserFilter() != null) {
                filter = dto.getProductRequestCategoryAndUserFilter().toDomain();
            }
            else if (dto.getProductRequestListFilter() != null) {
                filter = dto.getProductRequestListFilter().toDomain();
            }
            else if (dto.getEmptyFilter() != null) {
                filter = dto.getEmptyFilter().toDomain();
            }
            if (filter != null) {
                filter.setTitle(dto.getTitle());
                filter.setEnableFiltration(BooleanUtils.isTrue(dto.getEnableFiltration()));
                return filter;
            }
        }
        return null;
    }

    @Override
    public FilterContentDto convertTo(AbstractFilter filter) {
        if (filter == null) {
            return null;
        }
        FilterContentDto filterDTO = null;
        switch (filter.getFilterType()) {
            case CATEGORY_AND_USER:
                filterDTO = new FilterContentDto().setCategoryAndUserFilter((CategoryAndUserFilterDto) filter.toDto());
                break;
            case PRODUCT_LIST:
                filterDTO = new FilterContentDto().setProductListFilter((ProductListFilterDto) filter.toDto());
                break;
            case USER_PROFILE:
                filterDTO = new FilterContentDto().setUserProfileFilter((UserProfileFilterDto) filter.toDto());
                break;
            case PRODUCT_REQUEST_CATEGORY_AND_USER:
                filterDTO = new FilterContentDto().setProductRequestCategoryAndUserFilter((ProductRequestCategoryAndUserFilterDto) filter.toDto());
                break;
            case PRODUCT_REQUEST_LIST:
                filterDTO = new FilterContentDto().setProductRequestListFilter((ProductRequestListFilterDto) filter.toDto());
                break;
            case EMPTY:
                filterDTO = new FilterContentDto().setEmptyFilter((EmptyFilterDto) filter.toDto());
                break;
        }
        if (filterDTO != null) {
            filterDTO.setTitle(filter.getTitle()).setEnableFiltration(filter.isEnableFiltration());
            return filterDTO;
        }
        return null;
    }

    public FilterDTO mapToFilterDTO(AbstractFilter filter) {
        FilterDTO filterDTO = new FilterDTO();
        if (filter != null) {
            filterDTO.setTitle(filter.getTitle());
            filter.fillFilter(filterDTO);
        }
        return filterDTO;
    }
}
