package su.reddot.presentation.adminpanel.v2.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageSetting;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageType;
import su.reddot.domain.service.util.ContentBlockUtils;
import su.reddot.presentation.adminpanel.v2.dto.common.BaseContentBlockDto;
import su.reddot.presentation.adminpanel.v2.mapping.ContentBlocksDtoDeserializer;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CustomizablePrimaryPageSettingDto {

    private String id;
    private PrimaryPageType type;

    @JsonDeserialize(using = ContentBlocksDtoDeserializer.class)
    private List<? extends BaseContentBlockDto> contentBlocks;

    @JsonDeserialize(using = ContentBlocksDtoDeserializer.class)
    private List<? extends BaseContentBlockDto> fixedContentBlocks;

    public CustomizablePrimaryPageSettingDto(PrimaryPageSetting domain) {
        id = domain.getId();
        type = domain.getType();
        if (domain.getContentBlocks() != null) {
            this.setContentBlocks(ContentBlockUtils.mapToContentBlockDTOs(domain.getContentBlocks()));
        }
        if (domain.getFixedContentBlocks() != null) {
            this.setFixedContentBlocks(ContentBlockUtils.mapToContentBlockDTOs(domain.getFixedContentBlocks()));
        }
    }
}
