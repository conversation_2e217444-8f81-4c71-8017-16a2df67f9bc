package su.reddot.presentation.adminpanel.v2.dto.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.StoryContentBlock;
import su.reddot.presentation.adminpanel.v2.dto.segment.property.BaseSegmentPropertiesDto;

@Getter
@Setter
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StoryContentBlockDto extends DisabledContentBlockDto<BaseSegmentPropertiesDto> {

    public StoryContentBlockDto(StoryContentBlock domain) {
        super(domain);
    }

    @Override
    public BaseContentBlock mapToDomain() {
        return new StoryContentBlock(this);
    }
}
