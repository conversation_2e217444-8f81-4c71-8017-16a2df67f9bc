package su.reddot.presentation.adminpanel.v2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import su.reddot.domain.service.adminpanel.v2.BlogService;
import su.reddot.domain.service.util.MongoUtils;
import su.reddot.presentation.adminpanel.v2.dto.BlogDto;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@PreAuthorize("hasAnyAuthority('ADMIN','CONTENT_MODERATION')")
@RestController
@RequestMapping("/api/v2/admin/blog")
public class BlogControllerApiV2 {

    private final BlogService blogService;

    @PostMapping
    public Api2Response<BlogDto> updateBlog(@RequestBody BlogDto dto){
        Api2Response<BlogDto> response = new Api2Response<>();
        BlogDto result = blogService.updateBlog(dto);
        return response.success(result);
    }

    @GetMapping
    public Api2Response<List<BlogDto>> getBlogsByIds(@RequestParam List<String> ids) {
        Api2Response<List<BlogDto>> response = new Api2Response<>();
        List<BlogDto> result = blogService.getBlogList(ids);
        result = MongoUtils.sortCollection(result, ids, BlogDto::getId);
        return response.success(result);
    }
}
