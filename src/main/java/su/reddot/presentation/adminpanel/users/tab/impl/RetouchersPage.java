package su.reddot.presentation.adminpanel.users.tab.impl;

import com.google.common.base.Preconditions;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import su.reddot.domain.dao.AuthorityRepository;
import su.reddot.domain.dao.UserAuthorityBindingRepository;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.model.Authority;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.user.QUser;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.user.DefaultAdminUserService;
import su.reddot.domain.service.adminpanel.user.item.RetoucherItemImpl;
import su.reddot.domain.service.adminpanel.user.item.itemparams.RetoucherItem;
import su.reddot.domain.service.adminpanel.view.AdminPage;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;
import su.reddot.presentation.adminpanel.users.UsersTab;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static su.reddot.domain.service.adminpanel.user.DefaultAdminUserService.PAGE_SIZE;

@Component
public class RetouchersPage extends AbstractUserTabPage {

    private final AuthorityRepository authorityRepository;

    private final UserAuthorityBindingRepository userAuthorityBindingRepository;

    public RetouchersPage(UserBanService userBanService,
                          UserRepository userRepository,
                          AuthorityRepository authorityRepository,
                          UserAuthorityBindingRepository userAuthorityBindingRepository) {
        super(userBanService, userRepository);
        this.authorityRepository = authorityRepository;
        this.userAuthorityBindingRepository = userAuthorityBindingRepository;
    }

    @Override
    public UsersTab getTab() {
        return UsersTab.RETOUCHER;
    }

    @Override
    public String getItemsKey() {
        return "retouchers";
    }

    @Override
    public AdminPage<?> getData(int page, DefaultAdminUserService.SortAttribute sortAttribute, List<Long> usersIds) {
        Sort.Direction d = Sort.Direction.DESC;
        if (sortAttribute == DefaultAdminUserService.SortAttribute.ID) {
            d = Sort.Direction.ASC;
        }
        Sort sort = Sort.by(d, "id");
        final PageRequest pageRequest = PageRequest.of(page - 1, PAGE_SIZE, sort);

        Authority authority = authorityRepository.findByName(AuthorityName.RETOUCHING_MODERATION);

        List<User> userList = new ArrayList<>();
        if (usersIds.size() > 0) {
            for (Long id : usersIds) {
                User user = userRepository.getOne(id);
                if (user.hasAuthority(authority.getName())) {
                    userList.add(user);
                }
            }
        } else {
            userList = userAuthorityBindingRepository.findByAuthority(authority);
        }

        final Page<RetoucherItem> result = userRepository.findAll(
                QUser.user.in(userList),
                pageRequest
        ).map(this::extractRetoucherItem);

        return new AdminPage<>(result);
    }

    private RetoucherItem extractRetoucherItem(@Nonnull User user) {
        Preconditions.checkNotNull(user);
        final String city = extractCity(user);

        final String role = "Ретушер";

        final Boolean trusted = user.getIsTrusted();

        return RetoucherItemImpl.of(
                Objects.requireNonNull(user.getId()),
                user.getFullName().orElse("-"),
                user.getNickname(),
                user.getEmail(),
                user.getPhone(),
                city,
                role,
                trusted != null ? trusted : Boolean.FALSE,
                userIsBanedById(user.getId())
        );
    }
}
