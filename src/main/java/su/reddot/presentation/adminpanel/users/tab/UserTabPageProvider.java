package su.reddot.presentation.adminpanel.users.tab;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import su.reddot.domain.exception.UserTabPageNotFoundException;
import su.reddot.domain.service.adminpanel.user.DefaultAdminUserService;
import su.reddot.domain.service.adminpanel.view.AdminPage;
import su.reddot.presentation.adminpanel.users.UsersTab;
import su.reddot.presentation.adminpanel.users.tab.impl.DefaultUsersPage;

import java.util.List;
import java.util.Objects;

/**
 * Провайдер данных для вкладки пользователей в админке
 */
@Component
@RequiredArgsConstructor
public class UserTabPageProvider {

    /** Список компонентов, ответственных за поставку данных для вкладки */
    private final List<UserTabPage> userTabPageList;

    /** Компонент по умолчанию */
    private final DefaultUsersPage defaultUsersPage;

    /**
     * Получение данных для вкладки заданного типа.
     * Если не найдеен компонент для типа вкладки, то используется компонент по умолчанию
     *
     * @param tab      тип вкладки
     * @param page     номер страницы
     * @param sort     атрибут сортировки
     * @param usersIds коллекция идентификаторов пользователей
     * @return данные для вкладки
     */
    public AdminPage<?> getData(UsersTab tab, int page, DefaultAdminUserService.SortAttribute sort,
                                List<Long> usersIds) {

        return findTabPageOrDefault(tab).getData(page, sort, usersIds);
    }

    /**
     * Получение компонента, ответственных за поставку данных для заданной вкладки
     *
     * @param tab вкладка
     * @return найденный компонент
     * @throws UserTabPageNotFoundException если не найден компонент для заданного типа вкладкит
     */
    public UserTabPage findTabPageOrThrow(UsersTab tab) {
        if (tab == null) {
            return defaultUsersPage;
        }
        return userTabPageList.stream().filter(tabPage -> Objects.equals(tabPage.getTab(), tab)).findFirst()
                              .orElseThrow(() -> new UserTabPageNotFoundException(tab.name()));
    }

    /**
     * Получение компонента, ответственных за поставку данных для заданной вкладки
     *
     * @param tab вкладка
     * @return найденный компонент либо компонент по умолчанию
     */
    public UserTabPage findTabPageOrDefault(UsersTab tab) {
        if (tab == null) {
            return defaultUsersPage;
        }
        return userTabPageList.stream().filter(tabPage -> Objects.equals(tabPage.getTab(), tab)).findFirst()
                              .orElse(defaultUsersPage);
    }
}
