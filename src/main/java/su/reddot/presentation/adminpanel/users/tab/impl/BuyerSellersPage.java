package su.reddot.presentation.adminpanel.users.tab.impl;

import org.springframework.stereotype.Component;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.dao.user.filter.UserFilterPredicate;
import su.reddot.domain.dao.user.filter.UserFilterPredicateBuilder;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;
import su.reddot.presentation.adminpanel.users.UsersTab;

import java.util.Collections;

@Component
public class BuyerSellersPage extends ProSellersPage {

    public BuyerSellersPage(UserBanService userBanService,
                            ProductRepository productRepository,
                            OrderRepository orderRepository,
                            UserRepository userRepository) {
        super(userBanService, productRepository, orderRepository, userRepository);
    }

    @Override
    public UsersTab getTab() {
        return UsersTab.PRO_SELLER_BUYER;
    }

    @Override
    public String getItemsKey() {
        return "proSellersBuyer";
    }

    @Override
    protected UserFilterPredicate filterPredicate() {
        return new UserFilterPredicateBuilder()
                .isProSeller()
                .withSellerTypeIn(Collections.singleton(SellerType.BUYER))
                .build();
    }

}
