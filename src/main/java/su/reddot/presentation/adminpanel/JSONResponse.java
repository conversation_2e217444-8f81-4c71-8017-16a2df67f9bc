package su.reddot.presentation.adminpanel;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter @Setter
@Accessors(chain = true)
public class JSONResponse {
    private String error;
    private Object data;
    public static JSONResponse ok(Object data){
        return new JSONResponse().setData(data);
    }
    public static JSONResponse error(String error){
        return new JSONResponse().setError(error);
    }
}
