package su.reddot.presentation.adminpanel;

import com.google.common.base.Strings;
import lombok.Data;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.model.address.Currency;
import su.reddot.domain.model.adminalert.product.AdminProductAlert;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.localization.SizeTypeLocalized;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.UserType;
import su.reddot.domain.service.adminalert.AdminAlertService;
import su.reddot.domain.service.adminpanel.category.AdminCategoryService;
import su.reddot.domain.service.adminpanel.exception.AdministrationException;
import su.reddot.domain.service.adminpanel.product.AdminProductService;
import su.reddot.domain.service.adminpanel.view.CategoryView;
import su.reddot.domain.service.adminpanel.view.PageNavigator;
import su.reddot.domain.service.adminpanel.view.ProductEditorView;
import su.reddot.domain.service.adminpanel.view.ProductStateView;
import su.reddot.domain.service.adminpanel.view.TableFilterView;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.BrandDTO;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.image.ProductImage;
import su.reddot.domain.service.product.ProductService;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/adminpanel/products")
@PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION','RETOUCHING_MODERATION')")
@RequiredArgsConstructor
@Slf4j
public class AdminProductsController {
    private final AdminProductService adminProductService;
    private final AdminCategoryService adminCategoryService;
    private final AdminAlertService<AdminProductAlert> adminAlertService;
    private final TemplateEngine templateEngine;
    private final SecurityService securityService;
    private final MessageSourceAccessor messageSource;
    private final CurrencyService currencyService;

    @RequestMapping(value="", method=RequestMethod.GET)
    public String getProductsTablePage(Model m, ProductTablePageRequest request) {
        ProductService.FilterSpecification filterSpecification = new ProductService.FilterSpecification();
        ProductState activeState = getStateByName(request.getState());
        ProductService.SortAttribute sortAttribute = request.getSort() == null ? ProductService.SortAttribute.ID_DESC : ProductService.SortAttribute.valueOf(request.getSort());
        applyStatesFilter(filterSpecification, activeState);
        applyFilters(filterSpecification, request);
        if(activeState == ProductState.NEED_MODERATION && filterSpecification.wasPublished() == null) filterSpecification.wasPublished(false);
        Page<ProductDTO> productsPage = adminProductService.getCatalogProductPage(filterSpecification, request.getPage(), sortAttribute);
        List<Long> productIds = productsPage.getItems().stream().map(ProductDTO::getProductId).collect(Collectors.toList());
        Map<Long, List<AdminProductAlert>> alertsMap = adminAlertService.getAdminAlertsByObjectIdsAsMap(productIds, AdminProductAlert.class);
        Map<String, TableFilterView> filtersMap = adminProductService.getTableFiltersCached(filterSpecification);
        Map<Long, List<CategoryView>> categoryBranches = adminCategoryService.getAllCategoryBranchesCached();
        List<ProductStateView> productStates = adminProductService.getProductStateViewsCached(filterSpecification);
        PageNavigator pageNavigator = new PageNavigator(productsPage.getTotalPages()).setCurrentPage(request.getPage());

        Page<ProductAdminView> p = adminProductService.convertPage(productsPage);
        Currency baseCurrency = currencyService.getBaseCurrencyCached();

        m.addAttribute("currentTab", request.getState());
        m.addAttribute("productsPage", p);
        m.addAttribute("categoryBranches", categoryBranches);
        m.addAttribute("productStates", productStates);
        m.addAttribute("sortAttribute", sortAttribute.name());
        m.addAttribute("alertsMap", alertsMap);
        m.addAttribute("filtersMap", filtersMap);
        m.addAttribute("pageNavigator", pageNavigator);
        m.addAttribute("sellerType", request.getSellerType());
        m.addAttribute("vendorCode", request.getVendorCodeContains());
        m.addAttribute("baseCurrency", baseCurrency);
        return "adminpanel/products/main";
    }

    private void applyFilters(ProductService.FilterSpecification filterSpecification, ProductTablePageRequest request){
        if(request.filterBrand != null && !request.filterBrand.isEmpty()){
            filterSpecification.interestingBrands(request.filterBrand);
        }
        if(request.filterCategory != null && !request.filterCategory.isEmpty()){
            filterSpecification.categoriesIds(request.filterCategory);
        }
        if(request.filterSize != null && !request.filterSize.isEmpty()){
            filterSpecification.interestingSizes(request.filterSize);
        }
        if(request.filterSizeType != null && !request.filterSizeType.isEmpty()){
            filterSpecification.interestingSizeTypes(request.filterSizeType);
        }
        if(request.filterSellerType != null && request.filterSellerType.size() == 1) {
            filterSpecification.isPro(request.filterSellerType.get(0) == UserType.PRO);
        }
        if(request.filterSeller != null && !request.filterSeller.isEmpty()){
            filterSpecification.sellerIds(request.filterSeller);
        }
        if(request.filterId != null && request.filterId.size() == 1) {
            if(request.filterId.contains(AdminProductService.IdFilterItemView.IdFilterParam.WITHOUT_RRP))
                filterSpecification.withRrp(false);
            if(request.filterId.contains(AdminProductService.IdFilterItemView.IdFilterParam.WITH_RRP))
                filterSpecification.withRrp(true);
        }
        if(request.filterDate != null && !request.filterDate.isEmpty()) {
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.ONE_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(1L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.TWO_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(2L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.THREE_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(3L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.SIX_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(6L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.NINE_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(9L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.TWELVE_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(12L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.EIGHTEEN_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(18L)));
            if(request.filterDate.contains(AdminProductService.DateFilterItemView.DateFilterParam.TWENTY_FOUR_MONTH))
                filterSpecification.isPublishedBeforeTimestamp(getEpochSecond(LocalDateTime.now().minusMonths(24L)));
        }
        if(request.wasPublished != null){
	        filterSpecification.wasPublished(request.wasPublished);
        }
        if(request.sellerType != null){
            switch (request.sellerType) {
                case BOUTIQUE:
                    filterSpecification.isPro(true);
                    break;
                case SIMPLE_USER:
                    filterSpecification.isPro(false);
            }
        }

        if(request.filterRetoucher != null && request.filterRetoucher.size() > 0){
            filterSpecification.retoucherIds(request.filterRetoucher);
        }

        if (!Strings.isNullOrEmpty(request.vendorCodeContains)) {
            filterSpecification.vendorCodeContains(request.vendorCodeContains);
        }

        if (!Strings.isNullOrEmpty(request.storeCodeContains)) {
            filterSpecification.storeCodeContains(request.storeCodeContains);
        }
    }

    /**
     * Добавление фильтра по состояниям, выбранным пользователем
     *
     * @param filterSpecification фильтр
     * @param activeState         выбранное состояние
     */
    private void applyStatesFilter(ProductService.FilterSpecification filterSpecification, ProductState activeState) {
        filterSpecification.state(activeState);
        if (activeState == null) {
            // значит на вкладке Все. Вот все доступные состояния и выбираем
            filterSpecification.states(
                    new HashSet<>(Arrays.asList(ProductState.NEED_MODERATION, ProductState.NEED_RETOUCH,
                            ProductState.RETOUCH_DONE, ProductState.SECOND_EDITION, ProductState.PUBLISHED,
                            ProductState.REJECTED, ProductState.HIDDEN, ProductState.SOLD, ProductState.DRAFT))
            );
        }
    }

    private Long getEpochSecond(LocalDateTime time){
        return time != null ? time.toEpochSecond(OffsetDateTime.now().getOffset()) : null;
    }

    @RequestMapping(value="/edit", method=RequestMethod.GET)
    public String getProduct(Model m, Long productId){
        ProductEditorView product = adminProductService.getProductEditorView(productId);
        Currency baseCurrency = currencyService.getBaseCurrencyCached();
        log.info("Edit product #{}, product: {}", productId, product);
        m.addAttribute("product", product);
        m.addAttribute("baseCurrency", baseCurrency);
        return "adminpanel/products/editor";
    }

    @RequestMapping(value="/saveProduct", method=RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<JSONResponse> saveProduct(@RequestBody AdminProductService.SaveProductRequest request) throws OskellyException {
        String resultMessage = adminProductService.saveProduct(request);
        return new ResponseEntity<>(JSONResponse.ok(resultMessage), HttpStatus.OK);
    }

    @RequestMapping(value="/patchProducts", method=RequestMethod.PATCH, consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<AdminProductService.PatchProductResponse>> patchProducts(@RequestBody List<AdminProductService.PatchProductRequest> request) throws OskellyException {
        List<AdminProductService.PatchProductResponse> result = adminProductService.patchProducts(request);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping(value="/recalcTags")
    public ResponseEntity<String> recalcTags(@RequestParam List<Long> productIds) {
        adminProductService.recalcTags(productIds);
        return new ResponseEntity<>("ok", HttpStatus.OK);
    }

    @RequestMapping(value="/publishProducts", method=RequestMethod.PATCH, consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<AdminProductService.PatchProductResponse>> publishProducts(@RequestBody List<Long> productIds) throws OskellyException {
        List<AdminProductService.PatchProductResponse> result = adminProductService.publishProducts(productIds);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @RequestMapping(value="/setNeedRetouchState", method=RequestMethod.PATCH, consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<AdminProductService.PatchProductResponse>> setNeedRetouchState(
            @RequestBody List<Long> productIds,
            @RequestParam long retoucherId) throws OskellyException {
        List<AdminProductService.PatchProductResponse> result = adminProductService.setNeedRetouchState(productIds, retoucherId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @RequestMapping(value="/deleteImages", method=RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<List<ProductImage>> deleteImages(@RequestBody DeleteImageRequest request) throws AdministrationException {
        List<ProductImage> images = adminProductService.deleteImages(request.getProductId(), request.getImageIds());
        return new ResponseEntity<>(images, HttpStatus.OK);
    }

    @RequestMapping(value="/changeImage", method=RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<UpdatedProductImageWrapper> changeImage(@ModelAttribute ChangeImageRequest request) throws AdministrationException {
        ProductImage newImage = adminProductService.changeImage(request.getProductId(), request.getOldImageId(), request.getImage());
        UpdatedProductImageWrapper updatedProductImageWrapper = new UpdatedProductImageWrapper(request.getOldImageId(), newImage);
        return new ResponseEntity<>(updatedProductImageWrapper, HttpStatus.OK);
    }

	@RequestMapping(value="/addImage", method=RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<ProductImage> addImage(@ModelAttribute AddImageRequest request) throws AdministrationException {
		ProductImage newImage = adminProductService.addImage(request.getProductId(), request.getImage());
		return new ResponseEntity<>(newImage, HttpStatus.OK);
	}

    @RequestMapping(value="/getImage", method=RequestMethod.GET)
    public ResponseEntity<ProductImage> getImage(@RequestParam Long imageId) throws AdministrationException {
        ProductImage image = adminProductService.getProductImage(imageId);
        return new ResponseEntity<>(image, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @GetMapping(value="/getSizeForm")
    @ResponseBody
    public HTMLBlock getSizeForm(@RequestParam Long productId, @RequestParam SizeType sizeType, @RequestParam Long activeSizeId) throws AdministrationException{
        adminProductService.setSizeType(productId, sizeType);
        return getSizeFormHTMLBlock(productId, activeSizeId);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @PostMapping(value="/addSize")
    @ResponseBody
    public HTMLBlock addSize(@RequestParam Long productId, @RequestParam Long sizeId, @RequestParam @NonNull Integer sizeCount) throws AdministrationException{
        adminProductService.addSize(productId, sizeId, sizeCount);
        return getSizeFormHTMLBlock(productId, sizeId);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @PostMapping(value="/getSubcategories")
    public ResponseEntity<List<CategoryView>> getSubcategories(@RequestParam Long productId, @RequestParam Long categoryId) throws AdministrationException{
        return new ResponseEntity<>(adminCategoryService.getSubcategories(productId, categoryId), HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @PostMapping(value="/setProductCategory")
    @ResponseBody
    public ResponseEntity<JSONResponse> setProductCategory(@RequestParam Long productId, @RequestParam Long categoryId) throws AdministrationException{
        adminProductService.setProductCategory(productId, categoryId);
        return new ResponseEntity<>(JSONResponse.ok(messageSource.getMessage("controller.AdminProductsController.ProductCategorySuccessfullySet")), HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @RequestMapping(value="/changeDefectImage", method=RequestMethod.POST, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<UpdatedProductImageWrapper> changeImage(@ModelAttribute ChangeDefectImageRequest request) throws AdministrationException {
        ProductImage newImage = adminProductService.changeDefectImage(request.getProductId(), request.getOldImageId(), request.getImageOrder(), request.getComment(), request.getImage());
        UpdatedProductImageWrapper updatedProductImageWrapper = new UpdatedProductImageWrapper(request.getOldImageId(), newImage);
        return new ResponseEntity<>(updatedProductImageWrapper, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @RequestMapping(value="/deleteDefect", method=RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces=MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Long> deleteDefect(@RequestBody DeleteImageRequest request) throws AdministrationException {
        Long defectId = adminProductService.deleteDefect(request.getProductId(), request.getImageIds() != null ? request.getImageIds().get(0) : null);
        return new ResponseEntity<>(defectId, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @RequestMapping(value="/getDefect", method=RequestMethod.GET)
    public ResponseEntity<ProductImage> getDefect(@RequestParam Long defectId) throws AdministrationException {
        ProductImage image = adminProductService.getDefectProductImage(defectId);
        return new ResponseEntity<>(image, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @RequestMapping(value="/defectComment", method=RequestMethod.PATCH)
    public ResponseEntity<ProductImage> updateDefectComment(@RequestParam Long productId, @RequestParam @NonNull Integer imageOrder, @RequestParam @NonNull String comment) throws AdministrationException {
        ProductImage image = adminProductService.updateDefectImageComment(productId, imageOrder, comment);
        return new ResponseEntity<>(image, HttpStatus.OK);
    }

    @PreAuthorize("hasAnyAuthority('ADMIN','PRODUCT_MODERATION')")
    @RequestMapping(value="/propagatePriceChange", method=RequestMethod.POST)
    public ResponseEntity<JSONResponse> propagatePriceChange(@RequestParam Long productId)
            throws AdministrationException {
        adminProductService.propagatePriceChange(productId);
        return new ResponseEntity<>(JSONResponse.ok("Цены в заказах бутика успешно обновлены"), HttpStatus.OK);
    }

    @ExceptionHandler({AdministrationException.class})
    public String administrationException(Model m, Exception e) {
        m.addAttribute("e", e);
        return "adminpanel/error/error";
    }

    private HTMLBlock getSizeFormHTMLBlock(Long productId, Long activeSizeId) throws AdministrationException{
        ProductEditorView product = adminProductService.getProductEditorView(productId);
        if(activeSizeId != null){
            product.getSizes().forEach(size -> {
                if(size.getId().equals(activeSizeId)){//active
                    size.setActive(true);
                }
            });
        }
        Context context = new Context();
        context.setVariable("product", product);
        String sizeFormCode = templateEngine.process(
                "adminpanel/products/size_form",
                new HashSet<>(Collections.singletonList("[th:fragment='size_form(product)']")),
                context);
        return new SizeFormBlock(sizeFormCode, activeSizeId);
    }

    public ProductState getStateByName(String name){
        if(name == null) return securityService.hasAnyAuthority(AuthorityName.ADMIN, AuthorityName.PRODUCT_MODERATION) ? ProductState.NEED_MODERATION : ProductState.NEED_RETOUCH; //default
        if(ProductStateView.getAllStateName().equals(name)) return null;
        return ProductState.valueOf(name);
    }

    @Data
    public static class ProductAdminView{
        private Long productId;
        private String primaryImageUrl;
        private UserDTO seller;
        private Long categoryId;
        private BrandDTO brand;
        private List<SizeValueAdminView> sizes;
        private SizeTypeLocalized sizeType;
        private BigDecimal price;
        private BigDecimal commissionProc;
        private BigDecimal priceWithoutCommission;
        private ZonedDateTime changeTime;
        private ZonedDateTime publishTime;
        private ZonedDateTime sendToModeratorTime;
        private ZonedDateTime productStateTime;
        private ZonedDateTime createTime;
        private ProductState productState;
        private UserDTO retoucher;
        private String vendorCode;
    }

    @Data
    public static class SizeValueAdminView {
        private Long id;
        private SizeTypeLocalized productSizeType;
        private String productSizeValue;
        private SizeTypeLocalized categorySizeType;
        private String categorySizeValue;
        private SizeTypeLocalized interestingSizeType;
        private String interestingSizeValue;
        private Integer count;
        private Map<Long, Integer> additionalSizeValues;
    }

    @Data
    private static class ProductTablePageRequest{
        private String state;
        private String sort;
        private int page = 1;
        private List<UserType> filterSellerType;
        private List<Long> filterSeller;
        private List<Long> filterBrand;
        private List<SizeType> filterSizeType;
        private List<Long> filterSize;
        private List<Long> filterCategory;
        private List<AdminProductService.IdFilterItemView.IdFilterParam> filterId;
        private List<AdminProductService.DateFilterItemView.DateFilterParam> filterDate;
        private Boolean wasPublished;
        private SellerTypeRequest sellerType;
        private List<Long> filterRetoucher;

        /** Подстрока для поиска по артикулу производителя */
        private String vendorCodeContains;

        /** Подстрока для поиска по артикулу бутика */
        private String storeCodeContains;
    }

    enum SellerTypeRequest{
        ALL,
        BOUTIQUE,
        SIMPLE_USER
    }

    @Data
    private static class DeleteImageRequest{
        private Long productId;
        private List<Long> imageIds;
    }
    @Data
    public static class ChangeImageRequest {
        private Long productId;
        private Long oldImageId;
        private MultipartFile image;
    }
    @Data
    public static class ChangeDefectImageRequest {
        private Long productId;
        private Long oldImageId;
        private MultipartFile image;
        private String comment;
        private Integer imageOrder;
    }
	@Data
	public static class AddImageRequest {
		private Long productId;
		private MultipartFile image;
	}
    @Data
    public static class UpdatedProductImageWrapper{
        private final Long oldId;
        private final ProductImage image;
    }
    @Data
    public static class HTMLBlock{
        private final String html;
    }
    @Getter
    public static class SizeFormBlock extends HTMLBlock{
        private final Long activeSizeId;
        public SizeFormBlock(String html, Long activeSizeId){
            super(html);
            this.activeSizeId = activeSizeId;
        }
    }

}
