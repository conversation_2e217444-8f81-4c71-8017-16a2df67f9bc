package su.reddot.presentation.adminpanel.productRequest;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/adminpanel/productRequest")
@PreAuthorize("hasAnyAuthority('ADMIN','CONTENT_MODERATION')")
@RequiredArgsConstructor
@Slf4j
public class AdminProductRequestController {

	@Value("${retool.host}")
	private String retoolHost;
	@GetMapping
	public String getProductRequestPage(Model model) {
		model.addAttribute("retoolHost", retoolHost);
		return "adminpanel/productRequest/main";
	}
}
