package su.reddot.presentation.validation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;
import org.springframework.validation.ValidationUtils;
import org.springframework.validation.Validator;
import su.reddot.domain.service.promo.selection.PromoSelectionRequest;

@Component
public class SelectionValidator implements Validator {
	@Autowired
	private MessageSourceAccessor messageSourceAccessor;

	private static final int PROMO_TEXT_LENGTH = 30;
	private static final long MAX_FILE_SIZE = 2 * 1024 * 1024;
	private DefaultImageValidator imageValidator;

	public SelectionValidator() {
		this.imageValidator = new DefaultImageValidator(MAX_FILE_SIZE);
	}

	@Override
	public boolean supports(Class<?> clazz) {
		return PromoSelectionRequest.class.isAssignableFrom(clazz);
	}

	@Override
	public void validate(Object target, Errors errors) {
		//фотки не проверяем, на стороне клиента она скукожится
		PromoSelectionRequest promoSelection = (PromoSelectionRequest) target;

		if (!(promoSelection.getImage() == null && promoSelection.getId() != null)) {
			imageValidator.validate(promoSelection.getImage(), errors);
		}
		ValidationUtils.rejectIfEmptyOrWhitespace(errors, "firstLine", "firstLine not found", messageSourceAccessor.getMessage("validator.SelectionValidator.FirstLineNotFound"));
		ValidationUtils.rejectIfEmptyOrWhitespace(errors, "secondLine", "secondLine not found", messageSourceAccessor.getMessage("validator.SelectionValidator.SecondLineNotFound"));
		ValidationUtils.rejectIfEmptyOrWhitespace(errors, "thirdLine", "thirdLine not found", messageSourceAccessor.getMessage("validator.SelectionValidator.ThirdLineNotFound"));

		if (promoSelection.getFirstLine() != null && promoSelection.getFirstLine().length() > PROMO_TEXT_LENGTH) {
			errors.rejectValue("firstLine", "firstLine is too long", messageSourceAccessor.getMessage("validator.SelectionValidator.FirstLineTooLong"));
		}
		if (promoSelection.getSecondLine() != null && promoSelection.getSecondLine().length() > PROMO_TEXT_LENGTH) {
			errors.rejectValue("secondLine", "secondLine is too long", messageSourceAccessor.getMessage("validator.SelectionValidator.SecondLineTooLong"));
		}
		if (promoSelection.getThirdLine() != null && promoSelection.getThirdLine().length() > PROMO_TEXT_LENGTH) {
			errors.rejectValue("thirdLine", "thirdLine is too long", messageSourceAccessor.getMessage("validator.SelectionValidator.ThirdLineTooLong"));
		}

		ValidationUtils.rejectIfEmptyOrWhitespace(errors, "url", "url not found", messageSourceAccessor.getMessage("validator.SelectionValidator.UrlNotFound"));

	}
}
