package su.reddot.presentation.pdf.logistic;

import java.time.ZonedDateTime;

import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;

@Builder
@Getter
public class LogisticActReportDataItem {

    @NonNull
    private final String waybillExternalSystemId;

    @NonNull
    private final String waybillOrderExternalSystemId;

    private final String waybillOrderClientSystemId;

    /**
     * Время создания накладной (CSE) или дата заказа (<PERSON><PERSON>).
     */
    @NonNull
    private final ZonedDateTime waybillDateTime;

    /**
     * Объявленная ценность в рублях.
     */
    private final double declaredValue;

    /**
     * Количество мест.
     */
    private final int quantity;
}
