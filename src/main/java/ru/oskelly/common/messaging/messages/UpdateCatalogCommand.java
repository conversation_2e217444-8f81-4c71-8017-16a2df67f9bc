package ru.oskelly.common.messaging.messages;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
public abstract class UpdateCatalogCommand extends Message {

    private long executionId;

    public UpdateCatalogCommand(String idempotencyKey, long executionId) {
        super(idempotencyKey);
        this.executionId = executionId;
    }
}