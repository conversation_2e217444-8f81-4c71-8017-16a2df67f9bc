package ru.oskelly.common.messaging.messages;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@ToString(callSuper = true)
public class SaveProductCommand extends UpdateCatalogCommand {

    private ProductToExternalCatalogDTO product;

    public SaveProductCommand(ProductToExternalCatalogDTO product, long executionId) {
        super("", executionId);
        this.product = product;
    }
}