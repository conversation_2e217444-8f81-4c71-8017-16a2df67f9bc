package ru.oskelly.common.messaging.messages.bonuses;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@ToString(callSuper = true)
public class BonusesReturnedEvent {

    private long id;
    private String accountId;
    private BigDecimal bonusesAmount;
    private BigDecimal moneyAmount;
    private String description;
    private String reason;
    private Long orderId;
}
