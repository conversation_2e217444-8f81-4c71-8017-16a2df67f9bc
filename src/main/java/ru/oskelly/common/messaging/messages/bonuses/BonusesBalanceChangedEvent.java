package ru.oskelly.common.messaging.messages.bonuses;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Setter
@Getter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@ToString(callSuper = true)
public class BonusesBalanceChangedEvent {
    private String accountId;
    private BigDecimal bonusesAmount;
    private BigDecimal moneyAmount;
}
