package ru.oskelly.tests.build_01.domain.service.feed;

import com.google.common.base.Strings;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.order.OrderPositionRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.feed.dao.FeedOrderPositionRepository;
import su.reddot.domain.feed.model.FeedOrderPosition;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.feed.FeedDbExporter;
import su.reddot.domain.service.feed.FeedDbSynchronizer;
import su.reddot.domain.service.feed.FeedService;
import su.reddot.domain.service.feed.config.MindboxCSVOrderOrderPositionFeedExportConfiguration;
import su.reddot.domain.service.feed.config.MindboxCSVSaleOrderPositionFeedExportConfiguration;
import su.reddot.domain.service.feed.orderPosition.mindbox.MindboxOrderState;
import su.reddot.domain.service.feed.orderPosition.mindbox.MindboxUserType;
import su.reddot.domain.service.order.OrderService;
import su.reddot.infrastructure.util.FileUtils;
import su.reddot.infrastructure.util.Utils;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class FeedDbOrderPositionSynchronizerAndExporterTest extends AbstractSpringTest {
    @Autowired
    private FeedOrderPositionRepository feedOrderPositionRepository;
    @Autowired
    private FeedDbSynchronizer feedDbSynchronizer;
    @Autowired
    private FeedDbExporter feedDbExporter;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private OrderPositionRepository orderPositionRepository;
    @Autowired
    private FeedService feedService;
    @Autowired
    private MindboxCSVOrderOrderPositionFeedExportConfiguration mindboxCSVOrderOrderPositionFeedExportConfiguration;
    @Autowired
    private MindboxCSVSaleOrderPositionFeedExportConfiguration mindboxCSVSaleOrderPositionFeedExportConfiguration;

    /**
     * Чистим все в фиде, обновляем любой заказ и он появляется в фиде
     *  После этого обновляем заказ снова и фид снова обновляется
     */
    @Transactional
    @Test
    public void _00_feedOrderPosition_orderChanged_OK(){
        int orderPositionCount = 3;

        feedOrderPositionRepository.deleteAll();
        feedOrderPositionRepository.flush();
        assertTrue(feedOrderPositionRepository.findAll().isEmpty());

        //Получаем любой заказ из базы
        Order order = findAnyOrder(OrderState.COMPLETED, orderPositionCount);
        Set<Long> orderPositionIds = order.getOrderPositions().stream().map(op -> op.getId()).collect(Collectors.toSet());

        //Старая сумма заказа
        BigDecimal oldAmount = order.getAmount();

        //Меняем сумму заказа
        BigDecimal newAmount = oldAmount.add(new BigDecimal(1000));
        order.setAmount(newAmount);
        orderService.saveOrder(order);

        commitAndStartNewTransaction();

        //Ждем пару сек (асинхронная синхронизация)
        TestUtils.sleep(3);

        //Тянем из базы
        List<FeedOrderPosition> feedOrderPositions = feedOrderPositionRepository.findAll();
        //Проверяем кол-во
        assertEquals(orderPositionCount, feedOrderPositions.size());

        //Проверяем содержимое
        assertEquals(orderPositionIds, feedOrderPositions.stream().map(fop -> fop.getId()).collect(Collectors.toSet()));
        feedOrderPositions.stream().forEach(feedOrderPosition -> {
            assertFeedOrderPositionIsFine(feedOrderPosition);
        });

        //Меняем сумму обратно
        order.setAmount(oldAmount);
        orderService.saveOrder(order);

        commitAndStartNewTransaction();

        //Ждем пару сек (асинхронная синхронизация)
        TestUtils.sleep(3);

        //Тянем из базы
        List<FeedOrderPosition> feedOrders2Position = feedOrderPositionRepository.findAll();
        //Проверяем кол-во
        assertEquals(orderPositionCount, feedOrderPositions.size());
    }

    /**
     * Удаляем все записи фидов заказов, синхронизируем и проверяем наличие новых записей в базе
     */
    @Transactional
    @Test
    public void _01_feedOrderPosition_sync_add_OK(){
        feedOrderPositionRepository.deleteAll();
        feedOrderPositionRepository.flush();
        assertTrue(feedOrderPositionRepository.findAll().isEmpty());

        commitAndStartNewTransaction();

        int count = 10;

        //Синхронизируем
        feedDbSynchronizer.runFeedOrderPositionSync(count);

        commitAndStartNewTransaction();

        //Тянем из базы
        List<FeedOrderPosition> feedOrderPositions = feedOrderPositionRepository.findAll();
        //Проверяем кол-во
        assertEquals(count, feedOrderPositions.size());

        //Проверяем содержимое
        for(FeedOrderPosition feedOrderPosition : feedOrderPositions){
            assertFeedOrderPositionIsFine(feedOrderPosition);
        }
    }

    /**
     * Обновляем записи фидов позиций заказов, синхронизируем и проверяем наличие изменения
     */
    @Test
    public void _02_feedOrderPosition_change_OK(){
        //Чтобы время после обновления отличалось
        TestUtils.sleep(1);

        //Тянем все ID заказов из фида
        List<Long> feedOrderPositionIds = feedOrderPositionRepository.getAllIds();

        //Список не пустой
        assertFalse(feedOrderPositionIds.isEmpty());

        //Сохраняем даты обновления фидов
        Map<Long, LocalDateTime> feedUpdateTimes = feedOrderPositionRepository.findAll().stream()
                .collect(Collectors.toMap(p -> p.getId(), p -> p.getFeedChangeTime()));

        //Проверяем, что список совпадает со списком id на обновление
        assertTrue(feedUpdateTimes.keySet().containsAll(feedOrderPositionIds));

        //Обновляем фиды по списку на обновление
        int updateResultCount = feedService.createOrUpdateFeedOrderPositions(feedOrderPositionIds);

        //Обновились все
        assertEquals(feedOrderPositionIds.size(), updateResultCount);

        //Тянем из базы
        List<FeedOrderPosition> feedOrderPositions = feedOrderPositionRepository.findAll();
        //Даты обновления изменились
        for(FeedOrderPosition feedOrderPosition : feedOrderPositions){
            assertTrue(feedOrderPosition.getFeedChangeTime().isAfter(feedUpdateTimes.get(feedOrderPosition.getId())));
        }
    }

    /**
     * Экспортируем Mindbox CSV-фид заказов
     */
    @Test
    public void _10_01_exportMindboxCsvFeed_orders_OK(){
        //Для того, чтобы выгрузка была не пустой, необходимо, чтобы в нее попали оформленные заказы, а не только корзины
        syncSomeRealOrders();

        String resultFilename = mindboxCSVOrderOrderPositionFeedExportConfiguration.getFilename();
        //Перед началом теста удаляем файл выгрузки
        FileUtils.deleteFile(resultFilename);
        File resultFile = new File(resultFilename);
        //Убеждаемся, что файла не существует
        assertFalse(resultFile.exists());
        //Выполняем выгрузку
        int count = feedDbExporter.runExportOrderOrderPositionCsvMindbox();
        //Что-то выгрузилось
        assertTrue(count > 0);
        //Проверяем наличие файла
        assertTrue(resultFile.exists());

        String resultFileContent = new String(TestUtils.loadFile(resultFile));
        int resultFileContentLinesCount = StringUtils.countMatches(resultFileContent, "\r\n");

        //Количество строк в файле на 1 больше, чем кол-во выгруженных заказов, т.к. в файле имеется заголовок
        assertEquals(resultFileContentLinesCount, count + 1);
    }

    /**
     * Экспортируем Mindbox CSV-фид продаж
     */
    @Test
    public void _10_02_exportMindboxCsvFeed_sales_OK(){
        //Для того, чтобы выгрузка была не пустой, необходимо, чтобы в нее попали оформленные заказы, а не только корзины
        syncSomeRealOrders();

        String resultFilename = mindboxCSVSaleOrderPositionFeedExportConfiguration.getFilename();
        //Перед началом теста удаляем файл выгрузки
        FileUtils.deleteFile(resultFilename);
        File resultFile = new File(resultFilename);
        //Убеждаемся, что файла не существует
        assertFalse(resultFile.exists());
        //Выполняем выгрузку
        int count = feedDbExporter.runExportSaleOrderPositionCsvMindbox();
        //Что-то выгрузилось
        assertTrue(count > 0);
        //Проверяем наличие файла
        assertTrue(resultFile.exists());

        String resultFileContent = new String(TestUtils.loadFile(resultFile));
        int resultFileContentLinesCount = StringUtils.countMatches(resultFileContent, "\r\n");

        //Количество строк в файле на 1 больше, чем кол-во выгруженных заказов, т.к. в файле имеется заголовок
        assertEquals(resultFileContentLinesCount, count + 1);
    }

    //Для того, чтобы выгрузка была не пустой, необходимо, чтобы в нее попали оформленные заказы, а не только корзины
    //Пытаемся добавить несколько заказов.
    protected void syncSomeRealOrders() {
        List<Order> orders = findSomeOrders(OrderState.HOLD_COMPLETED, 1, 5);
        List<Long> orderPositionIds = orders.stream().map(o -> orderPositionRepository.findIdsByOrderId(o.getId())).flatMap(o -> o.stream()).collect(Collectors.toList());
        feedService.createOrUpdateFeedOrderPositions(orderPositionIds);
    }

    protected void assertFeedOrderPositionIsFine(FeedOrderPosition feedOrderPosition){
        Set<String> allMindboxOrderStates = Arrays.stream(MindboxOrderState.values()).map(s -> s.name()).collect(Collectors.toSet());
        Set<String> allMindboxUserTypes = Arrays.stream(MindboxUserType.values()).map(s -> s.name()).collect(Collectors.toSet());
        OrderPosition orderPosition = orderPositionRepository.findById(feedOrderPosition.getId()).orElseThrow(() -> new OskellyException("Позиция заказа не найдена: " + feedOrderPosition.getId()));
        Order order = orderPosition.getOrder();
        User buyer = order.getBuyer();
        Long buyerId = buyer != null ? buyer.getId() : null;
        User seller = order.getSellerUser();
        Long sellerId = seller != null ? seller.getId() : null;
        String orderChangeTime = Utils.formatToUTCStr(order.getChangeTime()).replace("T",  " ")
                .replace("Z", "");
        Integer orderPositionAmountRounded = orderPosition.getAmount() != null ? Utils.round(orderPosition.getAmount()) : null;
        Integer orderAmountRounded = order.getAmount() != null ? Utils.round(order.getAmount()) : null;
        //BigDecimal orderTotalAmountWithoutDeliveryCost = order.getAmount();
        //if(order.getDeliveryCost() != null) orderTotalAmountWithoutDeliveryCost = orderTotalAmountWithoutDeliveryCost.subtract(order.getDeliveryCost());
        //Integer orderTotalAmountWithoutDeliveryCostRounded = orderTotalAmountWithoutDeliveryCost != null ? Utils.round(orderTotalAmountWithoutDeliveryCost) : null;
        PromoCode promoCode = order.getPromoCode();
        String promoCodeStr = promoCode != null ? promoCode.getCode() : null;
        AddressEndpoint deliveryAddressEndpoint = order.getDeliveryAddressEndpoint();
        String deliveryCity = deliveryAddressEndpoint != null ? deliveryAddressEndpoint.getAddress().getCity() : null;
        AddressEndpoint pickupAddressEndpoint = order.getPickupAddressEndpoint();
        String pickupCity = pickupAddressEndpoint != null ? pickupAddressEndpoint.getAddress().getCity() : null;
        BigDecimal commissionAmount = orderPosition.getOrder().calcOrderCommissionValue();
        Integer commissionAmountRounded = commissionAmount != null ? Utils.round(commissionAmount) : null;

        String orderLine = feedOrderPosition.getMindboxOrdersOrderPositionCsv();
        String saleLine = feedOrderPosition.getMindboxSalesOrderPositionCsv();

        //Строки не пустые
        assertFalse(Strings.isNullOrEmpty(orderLine));
        assertFalse(Strings.isNullOrEmpty(saleLine));

        //Проверка покупки (orderLine)
        //Строка начинается с ID покупателя на 0 позиции
        assertCsvLineContains(orderLine, buyerId, 0);
        //Строка содержит ID заказа на 1 позиции
        assertCsvLineContains(orderLine, order.getId(), 1);
        //Строка содержит дату изменения заказа на 2 позиции
        assertCsvLineContains(orderLine, orderChangeTime, 2);
        //Строка содержит productItemId на 3 позиции
        assertCsvLineContains(orderLine, orderPosition.getProductItem().getId(), 3);
        //Строка содержит кол-во товаров (всегда 1) на 4 позиции
        assertCsvLineContains(orderLine, "1", 4);
        //Строка содержит сумму по позиции на 5 позиции
        assertCsvLineContains(orderLine, orderPositionAmountRounded, 5);
        //Строка содержит итоговую сумму заказа включая сумму доставки на 6 позиции
        assertCsvLineContains(orderLine, orderAmountRounded, 6);
        //Строка содержит один из статусов заказа на 7 позиции
        assertCsvLineContainsAny(orderLine, allMindboxOrderStates, 7);
        //Строка содержит ID продавца на 8 позиции
        assertCsvLineContains(orderLine, sellerId, 8);
        //Строка содержит один из типов пользователя на 9 позиции
        assertCsvLineContainsAny(orderLine, allMindboxUserTypes, 9);
        //Строка содержит один из типов пользователя на 10 позиции
        assertCsvLineContainsAny(orderLine, allMindboxUserTypes, 10);
        //Строка содержит промокод на 11 позиции
        assertCsvLineContains(orderLine, promoCodeStr, 11);
        //Строка содержит город назначения на 12 позиции
        assertCsvLineContains(orderLine, deliveryCity, 12);
        //Строка содержит город забора на 13 позиции
        assertCsvLineContains(orderLine, pickupCity, 13);
        //Строка содержит сумму комиссии на 14 позиции
        assertCsvLineContains(orderLine, commissionAmountRounded, 14);


        //Проверка продажи (saleLine)
        //Строка начинается с ID продавца на 0 позиции
        assertCsvLineContains(saleLine, sellerId, 0);
        //Строка содержит ID заказа на 1 позиции
        assertCsvLineContains(saleLine, order.getId(), 1);
        //Строка содержит дату изменения заказа на 2 позиции
        assertCsvLineContains(saleLine, orderChangeTime, 2);
        //Строка содержит productItemId на 3 позиции
        assertCsvLineContains(saleLine, orderPosition.getProductItem().getId(), 3);
        //Строка содержит кол-во товаров (всегда 1) на 4 позиции
        assertCsvLineContains(saleLine, "1", 4);
        //Строка содержит сумму по позиции на 5 позиции
        assertCsvLineContains(saleLine, orderPositionAmountRounded, 5);
        //Строка содержит итоговую сумму заказа включая сумму доставки на 6 позиции
        assertCsvLineContains(saleLine, orderAmountRounded, 6);
        //Строка содержит один из статусов заказа на 7 позиции
        assertCsvLineContainsAny(saleLine, allMindboxOrderStates, 7);
        //Строка содержит ID покупателя на 8 позиции
        assertCsvLineContains(saleLine, buyerId, 8);
        //Строка содержит один из типов пользователя на 9 позиции
        assertCsvLineContainsAny(saleLine, allMindboxUserTypes, 9);
        //Строка содержит один из типов пользователя на 10 позиции
        assertCsvLineContainsAny(saleLine, allMindboxUserTypes, 10);
        //Строка содержит промокод на 11 позиции
        assertCsvLineContains(saleLine, promoCodeStr, 11);
        //Строка содержит город назначения на 12 позиции
        assertCsvLineContains(saleLine, deliveryCity, 12);
        //Строка содержит город забора на 13 позиции
        assertCsvLineContains(saleLine, pickupCity, 13);
        //Строка содержит сумму комиссии на 14 позиции
        assertCsvLineContains(saleLine, commissionAmountRounded, 14);
    }

    //CSV строка содержит указанное значение на конкретной позиции. 0-я позиция - первое значение в строке
    protected void assertCsvLineContains(@NonNull String line, Object value, int position){
        String delimiter = mindboxCSVOrderOrderPositionFeedExportConfiguration.getDelimiter();
        String[] lineValues = line.split(delimiter, 50);
        String valueAtPosition = lineValues[position];
        if(value != null) {
            assertEquals(value.toString(), valueAtPosition);
        }
        else {
            assertEquals("", valueAtPosition);
        }
    }

    //CSV строка содержит одно из значений на конкретной позиции
    protected void assertCsvLineContainsAny(@NonNull String line, @NonNull Collection<String> values, int position){
        String delimiter = mindboxCSVOrderOrderPositionFeedExportConfiguration.getDelimiter();
        String[] lineValues = line.split(delimiter);
        String valueAtPosition = lineValues[position];
        assertTrue(values.contains(valueAtPosition));
    }

    protected Order findAnyOrder(OrderState orderState, int orderPositionCount){
        String query = "SELECT o.id AS o_id FROM public.order o INNER JOIN order_position op ON op.order_id = o.id WHERE o.state='" + orderState.name() + "' GROUP BY o.id HAVING COUNT(op.*) = " + orderPositionCount + " ORDER BY o.promo_code_id IS NOT NULL DESC, o.change_time DESC LIMIT 1";
        Long orderId = jdbcTemplate.queryForObject(query, Long.class);
        return orderRepository.findById(orderId).orElse(null);
    }

    protected List<Order> findSomeOrders(OrderState orderState, int orderPositionCount, int ordersCount){
        String query = "SELECT o.id AS o_id FROM public.order o INNER JOIN order_position op ON op.order_id = o.id WHERE o.state='" + orderState.name() + "' GROUP BY o.id HAVING COUNT(op.*) = " + orderPositionCount + " ORDER BY o.promo_code_id IS NOT NULL DESC, o.change_time DESC LIMIT " + ordersCount;
        List<Long> orderIds = jdbcTemplate.queryForList(query, Long.class);
        List<Order> orders = orderRepository.findAllById(orderIds);
        return orders;
    }

}
