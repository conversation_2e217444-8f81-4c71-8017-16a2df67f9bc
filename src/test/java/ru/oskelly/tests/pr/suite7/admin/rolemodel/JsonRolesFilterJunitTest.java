package ru.oskelly.tests.pr.suite7.admin.rolemodel;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.infrastructure.configuration.jackson.RolesJsonFilter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static su.reddot.infrastructure.configuration.JacksonConfiguration.ROLES_FILTER;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class JsonRolesFilterJunitTest {

    private ObjectMapper getObjectMapper(List<AuthorityName> contextRoles) {
        ObjectMapper mapper = new ObjectMapper();

        //ролевая модель
        SimpleFilterProvider filtersProvider = new SimpleFilterProvider().addFilter(ROLES_FILTER,
                RolesJsonFilter.testInstance(contextRoles));
        filtersProvider.setFailOnUnknownId(false);

        mapper.setFilterProvider(filtersProvider);

        return mapper;
    }

    @Test
    public void testRoles() throws JsonProcessingException {
        
        TestClass tc = new TestClass();
        
        String strOutput = getObjectMapper(Arrays.asList(AuthorityName.BOUTIQUE_SALES)).writeValueAsString(tc);

        assertTrue(strOutput.contains("fieldSkip"));
        assertFalse(strOutput.contains("hideForAny"));
        assertFalse(strOutput.contains("hideWithSkip"));
        assertTrue(strOutput.contains("showForAny"));
        assertTrue(strOutput.contains("hideForAll"));
        assertFalse(strOutput.contains("showForAll"));
        assertTrue(strOutput.contains("objectFieldToSkip"));
        assertFalse(strOutput.contains("classFieldToHideForAny"));
        assertTrue(strOutput.contains("classFieldToShowForAny"));
        assertTrue(strOutput.contains("classFieldToHideForAll"));
        assertFalse(strOutput.contains("classFieldToShowForAll"));
        assertTrue(strOutput.contains("methodToSkip"));
        assertFalse(strOutput.contains("methodToHideForAny"));
        assertFalse(strOutput.contains("methodToShowForAny"));
        assertTrue(strOutput.contains("methodToHideForAll"));
        assertFalse(strOutput.contains("methodToShowForAll"));
        assertTrue(strOutput.contains("class2FieldToSkip"));
        assertFalse(strOutput.contains("class2FieldHideForAny"));
        assertTrue(strOutput.contains("classHideForAllField"));
        assertFalse(strOutput.contains("classHideForAnyField"));
        assertTrue(strOutput.contains("classShowForAnyField"));
        assertFalse(strOutput.contains("classShowForAllField"));
        assertTrue(strOutput.contains("brokenField"));
        assertTrue(strOutput.contains("justField"));

        strOutput = getObjectMapper(Arrays.asList(AuthorityName.BOUTIQUE_SALES,
                AuthorityName.ADMIN)).writeValueAsString(tc);

        System.out.println(strOutput);

        assertTrue(strOutput.contains("fieldSkip"));
        assertFalse(strOutput.contains("hideForAny"));
        assertTrue(strOutput.contains("hideWithSkip"));
        assertTrue(strOutput.contains("showForAny"));
        assertTrue(strOutput.contains("hideForAll"));
        assertTrue(strOutput.contains("showForAll"));
        assertTrue(strOutput.contains("objectFieldToSkip"));
        assertFalse(strOutput.contains("classFieldToHideForAny"));
        assertTrue(strOutput.contains("classFieldToShowForAny"));
        assertTrue(strOutput.contains("classFieldToHideForAll"));
        assertTrue(strOutput.contains("classFieldToShowForAll"));
        assertTrue(strOutput.contains("methodToSkip"));
        assertFalse(strOutput.contains("methodToHideForAny"));
        assertTrue(strOutput.contains("methodToShowForAny"));
        assertFalse(strOutput.contains("methodToHideForAll"));
        assertFalse(strOutput.contains("methodToShowForAll"));
        assertTrue(strOutput.contains("class2FieldToSkip"));
        assertFalse(strOutput.contains("class2FieldHideForAny"));
        assertFalse(strOutput.contains("classHideForAllField"));
        assertFalse(strOutput.contains("classHideForAnyField"));
        assertTrue(strOutput.contains("classShowForAnyField"));
        assertTrue(strOutput.contains("classShowForAllField"));

        strOutput = getObjectMapper(Arrays.asList(AuthorityName.MASTER_USER)).writeValueAsString(tc);

        System.out.println(strOutput);

        assertTrue(strOutput.contains("fieldSkip"));
        assertTrue(strOutput.contains("hideForAny"));
        assertTrue(strOutput.contains("hideWithSkip"));
        assertFalse(strOutput.contains("showForAny"));
        assertTrue(strOutput.contains("hideForAll"));
        assertFalse(strOutput.contains("showForAll"));
        assertTrue(strOutput.contains("objectFieldToSkip"));
        assertTrue(strOutput.contains("classFieldToHideForAny"));
        assertFalse(strOutput.contains("classFieldToShowForAny"));
        assertTrue(strOutput.contains("classFieldToHideForAll"));
        assertFalse(strOutput.contains("classFieldToShowForAll"));
        assertTrue(strOutput.contains("methodToSkip"));
        assertTrue(strOutput.contains("methodToHideForAny"));
        assertFalse(strOutput.contains("methodToShowForAny"));
        assertTrue(strOutput.contains("methodToHideForAll"));
        assertFalse(strOutput.contains("methodToShowForAll"));
        assertTrue(strOutput.contains("class2FieldToSkip"));
        assertTrue(strOutput.contains("class2FieldHideForAny"));
        assertTrue(strOutput.contains("classHideForAllField"));
        assertTrue(strOutput.contains("classHideForAnyField"));
        assertFalse(strOutput.contains("classShowForAnyField"));
        assertFalse(strOutput.contains("classShowForAllField"));

        strOutput = getObjectMapper(new ArrayList<>()).writeValueAsString(tc);

        System.out.println(strOutput);

        assertTrue(strOutput.contains("fieldSkip"));
        assertTrue(strOutput.contains("hideForAny"));
        assertTrue(strOutput.contains("hideWithSkip"));
        assertFalse(strOutput.contains("showForAny"));
        assertTrue(strOutput.contains("hideForAll"));
        assertFalse(strOutput.contains("showForAll"));
        assertTrue(strOutput.contains("objectFieldToSkip"));
        assertTrue(strOutput.contains("classFieldToHideForAny"));
        assertFalse(strOutput.contains("classFieldToShowForAny"));
        assertTrue(strOutput.contains("classFieldToHideForAll"));
        assertFalse(strOutput.contains("classFieldToShowForAll"));
        assertTrue(strOutput.contains("methodToSkip"));
        assertTrue(strOutput.contains("methodToHideForAny"));
        assertFalse(strOutput.contains("methodToShowForAny"));
        assertTrue(strOutput.contains("methodToHideForAll"));
        assertFalse(strOutput.contains("methodToShowForAll"));
        assertTrue(strOutput.contains("class2FieldToSkip"));
        assertTrue(strOutput.contains("class2FieldHideForAny"));
        assertTrue(strOutput.contains("classHideForAllField"));
        assertTrue(strOutput.contains("classHideForAnyField"));
        assertFalse(strOutput.contains("classShowForAnyField"));
        assertFalse(strOutput.contains("classShowForAllField"));
    }
}
