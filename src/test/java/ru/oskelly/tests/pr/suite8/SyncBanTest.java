package ru.oskelly.tests.pr.suite8;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.userban.UserBanRepository;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.userban.BanType;
import su.reddot.domain.model.user.userban.UserBan;
import su.reddot.domain.service.user.userban.interfaces.UserBanService;
import su.reddot.domain.service.usersync.dto.UserBanSyncDTO;
import su.reddot.domain.service.usersync.dto.UserBanSyncMessageDTO;
import su.reddot.domain.service.usersync.service.UserBanSyncServiceConsumer;
import su.reddot.domain.service.usersync.service.UserBanSyncServiceSender;
import su.reddot.infrastructure.util.ProductionEnvironment;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_08)
@TestPropertySource(properties = {
        "app.kafka.enabled=true",
        "user-sync-service.enabled.send=true",
        "user-sync-service.enabled.consume=false",
        "internationalVersion=false"
})
public class SyncBanTest extends AbstractSyncTest {

    @SpyBean
    private UserBanSyncServiceSender userBanSyncServiceSender;
    @SpyBean
    private UserBanSyncServiceConsumer userBanSyncServiceConsumer;
    @SpyBean
    private UserBanService userBanService;
    @Autowired
    private UserBanRepository userBanRepository;

    private static final Long TEST_OPERATOR_ID = 10L;
    private static final Long TEST_USER_ID = 11L;

    @BeforeEach
    @Transactional
    public void init() {
        setTestUserUUID();
    }

    private void setTestUserUUID() {
        userRepository.updateUserUidIfNull(TEST_OPERATOR_ID);
        userRepository.updateUserUidIfNull(TEST_USER_ID);
        commitAndStartNewTransaction();
    }

    @Test
    @Transactional
    public void testSendCreateBanMessage() {
        UserBanSyncDTO[] dto = new UserBanSyncDTO[1];

        doAnswer(invocation -> {
            dto[0] = invocation.getArgument(0);
            return null;
        }).when(userBanSyncServiceSender).sendUpdate(any(UserBanSyncDTO.class));

        User testUser = userRepository.getOne(TEST_USER_ID);
        User operator = userRepository.getOne(TEST_OPERATOR_ID);
        ZonedDateTime endDate = ZonedDateTime.now().plusDays(1);

        userBanService.saveUserBan(TEST_USER_ID, BanType.WARNING, endDate,
                "Some desc", operator, "Some comment");

        commitAndStartNewTransaction();

        verify(userBanSyncServiceSender).sendUpdate(any(UserBanSyncDTO.class));

        assertEquals(TEST_USER_ID, dto[0].getUserId());
        assertEquals(BanType.WARNING, dto[0].getBanType());
        assertNotNull(dto[0].getUid());
        assertEquals(testUser.getUid(), dto[0].getUserUid());
        assertEquals(TEST_OPERATOR_ID, dto[0].getStatusChangedUserId());
        assertEquals("Some comment", dto[0].getAdminComment());
        assertEquals("Some desc", dto[0].getDescription());
        assertEquals(operator.getEmail(), dto[0].getStatusChangedUserEmail());
        assertEquals(operator.getUid(), dto[0].getStatusChangedUserUid());
        assertEquals(endDate, dto[0].getEndDate());
        assertNotNull(dto[0].getTitle());
        assertTrue(dto[0].isBaned());
        assertFalse(dto[0].isDeleted());
    }

    @Test
    @Transactional
    public void testConsumeCreateBanMessage() {

        enableConsume();

        userBanRepository.deleteAllByUserId(TEST_USER_ID);
        commitAndStartNewTransaction();

        UserBanSyncMessageDTO userBanSyncMessageDTO = new UserBanSyncMessageDTO();
        userBanSyncMessageDTO.setCreatedAt(OffsetDateTime.now());
        userBanSyncMessageDTO.setSourceUserZone(ProductionEnvironment.INT);
        userBanSyncMessageDTO.setTraceId(UUID.randomUUID());

        User testUser = userRepository.getOne(TEST_USER_ID);
        User operator = userRepository.getOne(TEST_OPERATOR_ID);

        UUID baUid = UUID.randomUUID();
        ZonedDateTime endDate = ZonedDateTime.now().plusDays(1);

        //"нормальный" новый бан
        UserBanSyncDTO userBanSyncDTO = new UserBanSyncDTO();
        userBanSyncDTO.setUid(baUid);
        userBanSyncDTO.setUserUid(testUser.getUid());
        userBanSyncDTO.setBanType(BanType.WARNING);
        userBanSyncDTO.setBaned(true);
        userBanSyncDTO.setDeleted(false);
        userBanSyncDTO.setAdminComment("Admin comment");
        userBanSyncDTO.setDescription("Desc");
        userBanSyncDTO.setStatusChangedUserUid(operator.getUid());
        userBanSyncDTO.setEndDate(endDate);

        userBanSyncMessageDTO.setData(userBanSyncDTO);

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();
        //Бан должен быть создан

        UserBan userBan = userBanRepository.findByUid(baUid);

        assertNotNull(userBan);

        assertNotNull(userBan.getUid());
        assertEquals(testUser.getId(), userBan.getUserId());
        assertEquals(BanType.WARNING, userBan.getBanType());
        assertEquals(operator.getId(), userBan.getStatusChangedUserId());
        assertEquals("Admin comment", userBan.getAdminComment());
        assertEquals("Desc", userBan.getDescription());
        //assertEquals(endDate, userBan.getEndDate());
        assertNotNull(userBan.getTitle());
        assertTrue(userBan.isBaned());
        assertFalse(userBan.isDeleted());

        int banCount = userBanRepository.findAllByUserId(testUser.getId()).size();

        //----------------------------

        //повторная отправка сообщения, бан с таким же uid - ничего не должно произойти
        userBanSyncDTO.setAdminComment("New admin comment");

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();

        userBan = userBanRepository.findByUid(baUid);
        assertTrue(userBan.isBaned());
        assertFalse(userBan.isDeleted());
        assertEquals("Admin comment", userBan.getAdminComment());

        assertEquals(banCount, userBanRepository.findAllByUserId(testUser.getId()).size());

        //----------------------------

        //Закрытие несуществующего бана. Ничего не должно произойти
        userBanSyncDTO.setUid(UUID.randomUUID());
        userBanSyncDTO.setBaned(false);

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();

        userBan = userBanRepository.findByUid(baUid);
        assertTrue(userBan.isBaned());
        assertFalse(userBan.isDeleted());

        assertEquals(banCount, userBanRepository.findAllByUserId(testUser.getId()).size());

        //----------------------------

        //Закрытие несуществующего бана 2. Ничего не должно произойти
        userBanSyncDTO.setUid(UUID.randomUUID());
        userBanSyncDTO.setDeleted(true);

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();

        userBan = userBanRepository.findByUid(baUid);
        assertTrue(userBan.isBaned());
        assertFalse(userBan.isDeleted());

        assertEquals(banCount, userBanRepository.findAllByUserId(testUser.getId()).size());

        //------------------------------

        //Нормальное закрытие бана
        userBanSyncDTO.setUid(baUid);
        userBanSyncDTO.setBaned(false);
        userBanSyncDTO.setDeleted(false);

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();

        userBan = userBanRepository.findByUid(baUid);
        assertFalse(userBan.isBaned());
        assertFalse(userBan.isDeleted());

        assertEquals(banCount, userBanRepository.findAllByUserId(testUser.getId()).size());

        //------------------------------

        //Нормальное уделение бана
        UUID banToDeleteUid = UUID.randomUUID();
        userBanSyncDTO.setUid(banToDeleteUid);
        userBanSyncDTO.setBaned(true);
        userBanSyncDTO.setDeleted(false);

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();

        userBan = userBanRepository.findByUid(banToDeleteUid);
        assertTrue(userBan.isBaned());
        assertFalse(userBan.isDeleted());

        userBanSyncDTO.setBaned(false);
        userBanSyncDTO.setDeleted(true);

        userBanSyncServiceConsumer.consumeUpdate(userBanSyncMessageDTO);
        commitAndStartNewTransaction();

        userBan = userBanRepository.findByUid(banToDeleteUid);
        assertFalse(userBan.isBaned());
        assertTrue(userBan.isDeleted());

        disableConsume();
    }
}
