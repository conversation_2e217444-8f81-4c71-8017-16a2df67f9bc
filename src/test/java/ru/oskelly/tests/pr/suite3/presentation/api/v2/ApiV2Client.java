package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import ru.oskelly.tests.TestUtils;
import su.reddot.domain.model.device.DeviceDtype;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class ApiV2Client {
	public static final String USER_AGENT_HEADER_KEY = "user-agent";
	public static String HEADER_MINDBOX_CLIENT_UUID = "12341234-1234-1234-1234-123412341234";
	public static String HEADER_PUSH_TOKEN = "Test_pushtoken";
	public static String HEADER_IDFA = "Test_idfa";
	public static String HEADER_IDFV = "Test_idfv";
	public static String HEADER_ANDROID_ID = "Test_android_id";
	public static String HEADER_ADVERTISING_ID = "Test_advertising_id";
	public static String HEADER_APPSFLYER_ID = "Test_appsflyer_id";
	public static String HEADER_MAC = "Test_mac";

	@Getter @Setter
	private String email;
	@Getter @Setter
	private String password;

	//Устанавливать заголовки с токенами по умолчанию
	@Getter @Setter
	protected boolean setDefaultDeviceHeaders = true;

	public static final Map<String, String> defaultHeaders = Collections.unmodifiableMap(new HashMap<String, String>(){{
		put(USER_AGENT_HEADER_KEY, DeviceDtype.OskellyApiTestDevice.name());
		put("X-Osk-PushToken", HEADER_PUSH_TOKEN);
		put("X-Osk-Idfa", HEADER_IDFA);
		put("X-Osk-Idfv", HEADER_IDFV);
		put("X-Osk-AndroidId", HEADER_ANDROID_ID);
		put("X-Osk-AdvertisingId",  HEADER_ADVERTISING_ID);
		put("X-Osk-AppsflyerId", HEADER_APPSFLYER_ID);
		put("X-Osk-Mac", HEADER_MAC);
		put("X-Osk-MindboxClientUuid", HEADER_MINDBOX_CLIENT_UUID);
	}});

	private Map<String, String> headers = new HashMap<>();

	protected TestRestTemplate restTemplate = TestUtils.getTestRestTemplate();
	protected ObjectMapper objectMapper;
	protected List<String> cookie = new ArrayList<>();

	private void initObjectMapper(ObjectMapper objectMapper) {
		if (Objects.isNull(objectMapper)) {
			this.objectMapper = restTemplate.getRestTemplate().getMessageConverters().stream()
					.filter(MappingJackson2HttpMessageConverter.class::isInstance)
					.map(MappingJackson2HttpMessageConverter.class::cast)
					.findFirst()
					.map(AbstractJackson2HttpMessageConverter::getObjectMapper)
					.orElseThrow(() -> new IllegalArgumentException("Unable to find objectMapper"));
			return;
		}
		restTemplate.getRestTemplate().getMessageConverters().stream()
				.filter(MappingJackson2HttpMessageConverter.class::isInstance)
				.map(MappingJackson2HttpMessageConverter.class::cast)
				.forEach(converter -> converter.setObjectMapper(objectMapper));
	}

	public ApiV2Client() {
		initObjectMapper(null);
	}

	public ApiV2Client(String email, String password){
		this.email = email;
		this.password = password;
		this.restTemplate = TestUtils.createTestRestTemplate();
		initObjectMapper(null);
	}

	public ApiV2Client(String email, String password, ObjectMapper objectMapper) {
		this.email = email;
		this.password = password;
		this.objectMapper = objectMapper;
		this.restTemplate = TestUtils.createTestRestTemplate();
		initObjectMapper(objectMapper);
	}

	public void setHeader(@NonNull String key, @NonNull String value){
		headers.put(key, value);
	}

	public void removeHeader(@NonNull String key){
		headers.remove(key);
	}

	//Возвращает значение заголовак, которое фактически подставляется пир выполнении запроса
	public String getEffectiveHeader(@NonNull String key){
		List<String> list = getHeaders(null).get(key);
		return list != null && !list.isEmpty() ? list.get(0) : null;
	}

	public String getEffectiveUserAgent(){
		return getEffectiveHeader(USER_AGENT_HEADER_KEY);
	}

	public void removeAllHeaders(){
		headers.clear();
	}

	protected HttpHeaders getHeaders(MediaType mediaType){
		HttpHeaders result = new HttpHeaders();
		if(mediaType != null) result.setContentType(mediaType);
		if(setDefaultDeviceHeaders){
			defaultHeaders.forEach((k, v) -> result.set(k, v));
		}
		if(!headers.isEmpty()){
			headers.forEach((k, v) -> result.set(k, v));
		}
		cookie.forEach(s -> {
			result.add(HttpHeaders.COOKIE, s);
		});
		return result;
	}
	protected HttpEntity getHttpEntity(Object body, MediaType mediaType){
		return body == null ? new HttpEntity<String>(getHeaders(mediaType)) : new HttpEntity<Object>(body, getHeaders(mediaType));
	}
	protected HttpEntity getHttpEntity(Object body){
		return body == null ? new HttpEntity<String>(getHeaders(null)) : new HttpEntity<Object>(body, getHeaders(null));
	}
	private Map<String, String> getAuthorizeParams(){
		Map<String, String> result = new HashMap<>();
		result.put("email", email);
		result.put("password", TestUtils.getApiHashedPassword(password, email));
		return result;
	}
	@SneakyThrows
	public <T> ResponseEntity<T> request(String url, Map<String, String> getParams, HttpMethod method, MediaType mediaType, Object requestEntityObject, Class<T> responseType, boolean withAuthorizeParams){
		if (mediaType == MediaType.APPLICATION_JSON) {
			ObjectMapper mapper = new ObjectMapper();
			String request = mapper.writeValueAsString(requestEntityObject);
			log.info("Send {} request {} to ulr {}", method.name(), request, url);
		}
		ResponseEntity<T> response = restTemplate.exchange(getUrl(url, getParams, withAuthorizeParams), method, getHttpEntity(requestEntityObject, mediaType), responseType);
		processResponse(response);
		return response;
	}
	public <T> ResponseEntity<T> request(String url, Map<String, String> getParams, HttpMethod method, Object requestEntityObject, Class<T> responseType, boolean withAuthorizeParams){
		return request(url, getParams, method, null, requestEntityObject, responseType, withAuthorizeParams);
	}

	@SneakyThrows
	private <T> T readObjFromRawResponse(ResponseEntity<String> responseRaw, ParameterizedTypeReference<T> responseType) {
		if (Objects.isNull(responseRaw.getBody())) {
			return null;
		}
		if (responseType.getType() == String.class) {
			return (T)responseRaw.getBody();
		}
		try {
			return objectMapper.readValue(responseRaw.getBody(), new TypeReference<Object>() {
				public Type getType() {
					return responseType.getType();
				}
			});
		} catch (JsonProcessingException ex) {
			throw new ApiV2ParseException(ex.getMessage(), responseRaw.getBody(), ex);
		}
	}

	public <T> ResponseEntity<T> request(String url, Map<String, String> getParams, HttpMethod method, MediaType mediaType, Object requestEntityObject, ParameterizedTypeReference<T> responseType, boolean withAuthorizeParams){
		String urlWithParams = getUrl(url, getParams, withAuthorizeParams);
		HttpEntity httpEntity = getHttpEntity(requestEntityObject, mediaType);
		ResponseEntity<String> responseRaw = restTemplate.exchange(urlWithParams, method, httpEntity, String.class);
		ResponseEntity<T> responseObj = ResponseEntity
				.status(responseRaw.getStatusCode())
				.headers(responseRaw.getHeaders())
				.body(readObjFromRawResponse(responseRaw, responseType)); // TODO: Kinda ugly, better handle with converter / interceptor
		processResponse(responseObj);
		return responseObj;
	}

	public <T> ResponseEntity<T> request(String url, Map<String, String> getParams, HttpMethod method, Object requestEntityObject, ParameterizedTypeReference<T> responseType, boolean withAuthorizeParams){
		return request(url, getParams, method, null, requestEntityObject, responseType, withAuthorizeParams);
	}
	public void clearCookies(){
		cookie.clear();
	}
	public void removeCookie(String name){
		for(int i = 0; i < cookie.size(); i++){
			if(cookie.get(i).startsWith(name)){
				cookie.remove(i);
				return;
			}
		}
	}
	public void logout(){
		removeCookie("SESSION");
	}
	public void login(String serverUrl){
		String url = getUrl(serverUrl + "/api/v2/account", null, true);
		request(url, null, HttpMethod.GET, null, String.class, true);
	}
	private void processResponse(ResponseEntity response){
		HttpHeaders headers = response.getHeaders();
		List<String> _cookie = headers.get(HttpHeaders.SET_COOKIE);
		if(_cookie != null && !_cookie.isEmpty()) setCookies(_cookie);
	}
	private void setCookies(List<String> newCookies){
		if(newCookies == null || newCookies.isEmpty()) return;
		for(String newCookieStr : newCookies){
			String newCookieName = getCookieName(newCookieStr);
			String oldCookieValue = getCookie(newCookieName);
			//Удаляем старое значение, чтобы не плодить дубликаты
			if(oldCookieValue != null){
				removeCookie(newCookieName);
			}
			cookie.add(newCookieStr);
		}
	}
	private String getUrl(String url, Map<String, String> getParams, boolean withAuthorizeParams){
		Map<String, String> allParams = new HashMap<>();
		if(getParams != null && !getParams.isEmpty()) allParams.putAll(getParams);
		if(withAuthorizeParams) allParams.putAll(getAuthorizeParams());
		return allParams.isEmpty() ? url : TestUtils.getUriWithParams(url, allParams);
	}

	public String getCookie(String name){
		for(int i = 0; i < cookie.size(); i++){
			String cookieStr = cookie.get(i);
			String cookieName = getCookieName(cookieStr);
			if(name.equals(cookieName)){
				int endIndex = cookieStr.indexOf(';');
				if(endIndex > cookieName.length() + 1)
					return cookieStr.substring(cookieName.length() + 1, endIndex);
			}
		}
		return null;
	}

	public Map<String, String> getCookies(){
		Map<String, String> result = new HashMap<>();
		for(int i = 0; i < cookie.size(); i++){
			String cookieStr = cookie.get(i);
			String cookieName = getCookieName(cookieStr);
			int endIndex = cookieStr.indexOf(';');
			if(endIndex > cookieName.length() + 1)
				result.put(cookieName, cookieStr.substring(cookieName.length() + 1, endIndex));
		}
		return result;
	}

	private String getCookieName(String cookieStr){
		if(Strings.isNullOrEmpty(cookieStr)) return null;
		int nameEndIndex = cookieStr.indexOf('=');
		if(nameEndIndex < 0) return null;
		return cookieStr.substring(0, nameEndIndex);
	}

	public String getOskCookie(){
		return getCookie("osk");
	}

	public void replaceCookie(String name, String newValue){
		String oldValue = getCookie(name);
		for(int i = 0; i < cookie.size(); i++){
			String cookieStr = cookie.get(i);
			if(cookieStr.startsWith(name)){
				cookie.set(i, cookieStr.replace(oldValue, newValue));
			}
		}
	}

	public void replaceOskCookie(String newValue){
		replaceCookie("osk", newValue);
	}

}
