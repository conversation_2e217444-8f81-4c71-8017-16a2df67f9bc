package ru.oskelly.tests.pr.suite3.presentation.api.v2;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AddressTestSupport;
import su.reddot.domain.service.address.DefaultZipCodeValidationService;
import su.reddot.domain.service.dto.*;
import su.reddot.infrastructure.google.GoogleMapsClient;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_03)
public class AccountAddressControllerV2Test extends AbstractSpringTest {
    private static final Long COUNTRY_ID = 11L;
    private static final Long CITY_ID = 2165L;
    public static final String DEFAULT_ZIP = "111111";

    @Value("${test.api.host}")
    private String host;
    @Value("${test.api.port}")
    private int port;
    @Value("${test.api.user-email}")
    private String email;
    @Value("${test.api.user-password}")
    private String password;

    //Клиент для работы с API
    static ApiV2Client apiV2Client;

    @MockBean
    private GoogleMapsClient googleMapsClient;

    @Autowired
    private AddressTestSupport addressTestSupport;

    private String getServerUrl() {
        return "http://" + host + ":" + port;
    }

    private String getServiceUrl() {
        return getServerUrl() + "/api/v2/account/addresses";
    }


    @BeforeEach
    public void initialize() {
        GoogleMapsClient.AddressComponent addressComponent = new GoogleMapsClient.AddressComponent();
        addressComponent.setTypes(Collections.singletonList(DefaultZipCodeValidationService.ZIP_CODE));
        addressComponent.setLongName(DEFAULT_ZIP);

        GoogleMapsClient.GeoCodeResponse.Result result = new GoogleMapsClient.GeoCodeResponse.Result();
        result.setAddressComponents(Collections.singletonList(addressComponent));

        GoogleMapsClient.GeoCodeResponse geoCodeResponse = new GoogleMapsClient.GeoCodeResponse();
        geoCodeResponse.setResults(Collections.singletonList(result));

        when(googleMapsClient.geoCode(any())).thenReturn(geoCodeResponse);

        if (apiV2Client == null) {
            apiV2Client = new ApiV2Client(email, password);
        }

        addressTestSupport.addDeliveryCompanySupport(COUNTRY_ID, CITY_ID);
    }

    private static AddressEndpointAggregationDTO afterSave;

    @Test
    @Order(1)
    public void _01_01_testSaveAddressAggregation() {
        AddressAggregationEndpointRequestDTO addressEndpointDTO = new AddressAggregationEndpointRequestDTO();
        addressEndpointDTO.setFirstName("fihsdfgrgasdsstName");
        addressEndpointDTO.setLastName("lasasdfdhsdfgasdftsName");
        addressEndpointDTO.setPhone("+78005553505");
        AddressAggregationRequestDTO addressDTO = new AddressAggregationRequestDTO();
        addressDTO.setAddress("Бережковская набережная 16а");
        addressDTO.setZipCode(DEFAULT_ZIP);
        addressDTO.setCountry(COUNTRY_ID);
        addressDTO.setCity(CITY_ID);
        addressEndpointDTO.setAddress(addressDTO);
        AddressEndpointAggregationRequestDTO addressEndpointAggregationDTO = new AddressEndpointAggregationRequestDTO();
        addressEndpointAggregationDTO.setPhysicalAddress(addressEndpointDTO);
        addressEndpointAggregationDTO.setUsePhysicalAddressForBilling(true);

        ResponseEntity<Api2Response<AddressEndpointAggregationDTO>> response = apiV2Client.request(getServiceUrl(), null, HttpMethod.POST, addressEndpointAggregationDTO, new ParameterizedTypeReference<Api2Response<AddressEndpointAggregationDTO>>() {
        }, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        afterSave = response.getBody().getData();
    }

    @Test
    @Order(2)
    public void _02_01_testUpdateAddressAggregation() {
        String actualAddressString = "Пречистенская наб., 13 строение 1";

        AddressAggregationEndpointRequestDTO addressEndpointDTO = new AddressAggregationEndpointRequestDTO();
        addressEndpointDTO.setFirstName("firghsdfasdsstName");
        addressEndpointDTO.setLastName("Change");
        addressEndpointDTO.setPhone("+78005553505");
        AddressAggregationRequestDTO addressDTO = new AddressAggregationRequestDTO();
        addressDTO.setAddress(actualAddressString);
        addressDTO.setZipCode(DEFAULT_ZIP);
        addressDTO.setCountry(afterSave.getPhysicalAddress().getAddress().getCountryData().getId());
        addressDTO.setCity(afterSave.getPhysicalAddress().getAddress().getCityData().getId());
        addressEndpointDTO.setAddress(addressDTO);
        AddressEndpointAggregationRequestDTO addressEndpointAggregationDTO = new AddressEndpointAggregationRequestDTO();
        addressEndpointAggregationDTO.setId(afterSave.getId());
        addressEndpointAggregationDTO.setPhysicalAddress(addressEndpointDTO);
        addressEndpointAggregationDTO.setUsePhysicalAddressForBilling(true);


        ResponseEntity<Api2Response<AddressEndpointAggregationDTO>> response = apiV2Client.request(getServiceUrl(), null, HttpMethod.PUT, addressEndpointAggregationDTO, new ParameterizedTypeReference<Api2Response<AddressEndpointAggregationDTO>>() {
        }, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        AddressEndpointAggregationDTO changeData = response.getBody().getData();
        assertEquals("Change", changeData.getPhysicalAddress().getLastName());
        assertEquals(actualAddressString, changeData.getPhysicalAddress().getAddress().getAddress());
    }

    @Test
    @Order(3)
    public void _03_01_testGetAddressAggregationById() {
        Map<String, String> params = new HashMap<>();
        params.put("context", "BUYER_ADDRESS");

        ResponseEntity<Api2Response<List<AddressEndpointAggregationDTO>>> response = apiV2Client.request(getServiceUrl(), params, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<AddressEndpointAggregationDTO>>>() {
        }, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        List<AddressEndpointAggregationDTO> list = response.getBody().getData();
        assertNotEquals(0, list.size());
    }

}
