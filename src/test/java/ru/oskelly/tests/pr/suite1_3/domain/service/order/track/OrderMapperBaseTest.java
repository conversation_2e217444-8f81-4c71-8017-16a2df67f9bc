package ru.oskelly.tests.pr.suite1_3.domain.service.order.track;

import org.assertj.core.api.Assertions;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import java.nio.file.Paths;
import java.util.List;

public class OrderMapperBaseTest extends OrderProcessorExpertiseMockedBaseTest {

    protected ExpectConsumerFactory<IntegrationMobileOrderExpertiseDTO> expectExpertiseStageForAllSnippets(
        String orderProcessingContext
    ) {
        return expectExpertiseStageForAllSnippets(orderProcessingContext, "");
    }

    protected ExpectConsumerFactory<IntegrationMobileOrderExpertiseDTO> expectExpertiseStageForAllSnippets(
        String orderProcessingContext,
        String snippetStartWith
    ) {
        return consumer -> {
            List<String> allSnippets = findAllSnippets(orderProcessingContext, snippetStartWith);

            Assertions.assertThat(allSnippets).isNotEmpty();
            allSnippets.forEach(snippet -> {
                String orderProcessingSnippet = snippet;
                System.out.println("\n\n");
                System.out.println(orderProcessingSnippet);
                System.out.println("\n");

                IntegrationMobileOrderExpertiseDTO extendedOrderProcessingExpertise = loadSnippetByName(
                    Paths.get(orderProcessingContext, orderProcessingSnippet).toString()
                );
                consumer.accept(extendedOrderProcessingExpertise);
            });
        };
    }
}
