package ru.oskelly.tests.pr.suite1_3.domain.service.promocode;

import com.google.common.collect.Sets;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.MessageSourceAccessor;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.applyRule.FilterRelation;
import su.reddot.domain.service.dto.promocode.PromocodeDTOFull;
import su.reddot.domain.service.dto.promocode.PromocodeFilterDTO;
import su.reddot.domain.service.dto.promocode.PromocodeReferenceLinkDTO;
import su.reddot.domain.service.promocode.PromoCodeValidator;
import su.reddot.domain.service.promocode.exception.PromoCodeEditException;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
@ExtendWith(MockitoExtension.class)
class PromoCodeValidatorTest {
    @Mock
    private MessageSourceAccessor messageSourceAccessor;

    @BeforeEach
    void init() {
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.name"))
            .thenReturn("name");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.many-types"))
            .thenReturn("many-types");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.no-types"))
            .thenReturn("no-types");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.percent"))
            .thenReturn("percent");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.amount"))
            .thenReturn("amount");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.order-amount"))
            .thenReturn("order-amount");
        lenient().when(messageSourceAccessor.getMessage(eq("exception.forbidden.promocode.save.date")))
            .thenReturn("date");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.relation"))
            .thenReturn("relation");
        lenient().when(messageSourceAccessor.getMessage("exception.forbidden.promocode.save.user-conflict"))
            .thenReturn("user-conflict");
    }

    private static PromocodeDTOFull createDefaultDTO() {
        final PromocodeDTOFull promocodeDTOFull = new PromocodeDTOFull();
        promocodeDTOFull.setCode("code");
        return promocodeDTOFull;
    }

    private static PromocodeDTOFull createDtoWithAmount() {
        final PromocodeDTOFull promocodeDTOFull = createDefaultDTO();
        promocodeDTOFull.setAmount(BigDecimal.ONE);
        return promocodeDTOFull;
    }

    private static Stream<Arguments> argumentsProvider() {
        final ZonedDateTime startsAt = ZonedDateTime.parse("2007-12-03T10:15:30+01:00[Europe/Paris]");
        final ZonedDateTime expiresAt = ZonedDateTime.parse("2007-11-03T10:15:30+01:00[Europe/Paris]");
        final PromocodeFilterDTO filterDTO = new PromocodeFilterDTO(1L, FilterRelation.GREATER);
        final PromocodeFilterDTO filterDTO2 = new PromocodeFilterDTO(2L, FilterRelation.GREATER);

        return Stream.of(
            Arguments.of( // 1
                new PromocodeDTOFull(),
                "name"),
            Arguments.of( // 2
                createDefaultDTO()
                    .setAmount(BigDecimal.ONE)
                    .setPercent(BigDecimal.ONE),
                "many-types"),
            Arguments.of( // 3
                createDefaultDTO(),
                "no-types"),
            Arguments.of( // 4
                createDefaultDTO()
                    .setPercent(new BigDecimal("101")),
                "percent"),
            Arguments.of( // 5
                createDefaultDTO()
                    .setAmount(new BigDecimal("-20")),
                "amount"),
            Arguments.of( // 6
                createDtoWithAmount()
                    .setBeginPrice(new BigDecimal("-20")),
                "order-amount"),
            Arguments.of( // 7
                createDtoWithAmount()
                    .setStartsAt(startsAt)
                    .setExpiresAt(expiresAt),
                "date"),
            Arguments.of( // 8
                createDtoWithAmount()
                    .setOrdersCountFilters(Sets.newHashSet(filterDTO, filterDTO2)),
                "relation"),
            Arguments.of( // 9
                createDtoWithAmount()
                    .setOrderAmountFilters(Sets.newHashSet(filterDTO, filterDTO2)),
                "relation"),
            Arguments.of( // 10
                createDtoWithAmount()
                    .setOrdersAmountSummaryFilters(Sets.newHashSet(filterDTO, filterDTO2)),
                "relation"),
            Arguments.of( // 11
                createDtoWithAmount()
                    .setOrdersRelativeNumbers(Sets.newHashSet(filterDTO, filterDTO2)),
                "relation"),
            Arguments.of( // 12
                createDtoWithAmount()
                    .setBuyersList(Sets.newHashSet(new PromocodeReferenceLinkDTO()))
                    .setUserLoyaltyTagList(Sets.newHashSet(new PromocodeReferenceLinkDTO())),
                "user-conflict")
        );
    }

    @ParameterizedTest
    @MethodSource("argumentsProvider")
    void validateWithException(PromocodeDTOFull validateObject, String expectedMessage) {
        Assertions.setMaxStackTraceElementsDisplayed(30);
        assertThatThrownBy(() -> PromoCodeValidator.validatePromoCodeDTO(validateObject, messageSourceAccessor))
            .isInstanceOf(PromoCodeEditException.class)
            .hasMessage(expectedMessage);
    }
}
