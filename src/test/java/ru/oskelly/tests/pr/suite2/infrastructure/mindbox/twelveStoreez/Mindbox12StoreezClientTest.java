package ru.oskelly.tests.pr.suite2.infrastructure.mindbox.twelveStoreez;

import com.google.common.io.Files;
import org.hamcrest.Matchers;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.client.ExpectedCount;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.util.ResourceUtils;
import org.springframework.web.client.RestTemplate;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.Mindbox12StoreezClient;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.request.Add12StoreezBonusesRequest;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.request.BalanceChange;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.request.Create12StoreezUserWithBonusesRequest;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.request.CustomerBase;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.request.Get12StoreezUserRequest;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.response.Get12StoreezUserResponse;
import su.reddot.infrastructure.mindbox.twelveStoreez.client.dto.response.Mindbox12StoreezResponse;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.content;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.header;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.queryParam;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = OskellyApplication.class, properties = {
        "app.integration.mindbox.twelve-storeez.secretKey=testSecretKey"
})
@ActiveProfiles(AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class Mindbox12StoreezClientTest {

    public static final String TEST_PHONE = "78005553535";
    public static final String EXTERNAL_ID = "0bbc2dcd-1d1a-4369-b8e3-9c207b006180";
    @Autowired
    ResourceLoader resourceLoader;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    Mindbox12StoreezClient mindbox12StoreezClient;

    private MockRestServiceServer mockServer;

    @BeforeEach
    public void init() {
        mockServer = MockRestServiceServer.createServer(restTemplate);
    }

    @Test
    public void getUserReturns12StoreezUserIfFound() throws IOException {
        File requestFile = ResourceUtils.getFile("classpath:json/mindbox/12Storeez/request/GetUserRequest.json");
        String expectedRequest = Files.readLines(requestFile, StandardCharsets.UTF_8).stream().collect(Collectors.joining());
        mockServer.expect(ExpectedCount.times(1), requestTo("https://api.mindbox.ru/v3/operations/sync?endpointId=Oskelly12Storeez&operation=OskellyGetCustomer"))
                .andExpect(header(HttpHeaders.AUTHORIZATION, "Mindbox secretKey=\"testSecretKey\""))
                .andExpect(content().json(expectedRequest, true))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(resourceLoader.getResource("classpath:json/mindbox/12Storeez/response/GetUserFoundResponse.json")));

        Get12StoreezUserRequest get12StoreezUserRequest = new Get12StoreezUserRequest(TEST_PHONE);

        Get12StoreezUserResponse response = mindbox12StoreezClient.getUser(get12StoreezUserRequest);
        assertThat(response.getStatus()).isEqualTo("Success");
        assertThat(response.getCustomer().getProcessingStatus()).isEqualTo("Found");
        mockServer.verify();
    }

    @Test
    public void createUserWithBonuses() throws IOException {
        File requestFile = ResourceUtils.getFile("classpath:json/mindbox/12Storeez/request/CreateUserWithBonusesRequest.json");
        String expectedRequest = String.join("", Files.readLines(requestFile, StandardCharsets.UTF_8));
        mockServer.expect(ExpectedCount.times(1), requestTo(Matchers.startsWith("https://api.mindbox.ru/v3/operations/sync")))
                .andExpect(header(HttpHeaders.AUTHORIZATION, "Mindbox secretKey=\"testSecretKey\""))
                .andExpect(content().json(expectedRequest, true))
                .andExpect(queryParam("endpointId", "Oskelly12Storeez"))
                .andExpect(queryParam("operation", "Oskelly.RegisterCustomerWithBonus"))
                .andExpect(queryParam("transactionidoskelly", EXTERNAL_ID))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(resourceLoader.getResource("classpath:json/mindbox/12Storeez/response/SuccessfulResponse.json")));

        Create12StoreezUserWithBonusesRequest get12StoreezUserRequest = new Create12StoreezUserWithBonusesRequest()
                .setBalanceChanges(Collections.singletonList(new BalanceChange(1000.0)))
                .setCustomer(new Create12StoreezUserWithBonusesRequest.Customer().setMobilePhone(TEST_PHONE));

        Mindbox12StoreezResponse response = mindbox12StoreezClient.createUserWithBonuses(get12StoreezUserRequest, EXTERNAL_ID);
        assertThat(response.getStatus()).isEqualTo("Success");
        mockServer.verify();
    }

    @Test
    public void addBonusesToUser() throws IOException {
        File requestFile = ResourceUtils.getFile("classpath:json/mindbox/12Storeez/request/AddBonusesToUserRequest.json");
        String expectedRequest = String.join("", Files.readLines(requestFile, StandardCharsets.UTF_8));
        mockServer.expect(ExpectedCount.times(1), requestTo(Matchers.startsWith("https://api.mindbox.ru/v3/operations/sync")))
                .andExpect(header(HttpHeaders.AUTHORIZATION, "Mindbox secretKey=\"testSecretKey\""))
                .andExpect(content().json(expectedRequest, true))
                .andExpect(queryParam("endpointId", "Oskelly12Storeez"))
                .andExpect(queryParam("operation", "Oskelly.BonusPoints"))
                .andExpect(queryParam("transactionidoskelly", EXTERNAL_ID))
                .andRespond(withStatus(HttpStatus.OK).contentType(MediaType.APPLICATION_JSON).body(resourceLoader.getResource("classpath:json/mindbox/12Storeez/response/AddBonusesToUserResponse.json")));

        Add12StoreezBonusesRequest add12StoreezBonusesRequest = new Add12StoreezBonusesRequest()
                .setBalanceChanges(Collections.singletonList(new BalanceChange(1000.0)))
                .setCustomer(new CustomerBase().setMobilePhone(TEST_PHONE));

        Mindbox12StoreezResponse response = mindbox12StoreezClient.addBonusesToUser(add12StoreezBonusesRequest, EXTERNAL_ID);
        assertThat(response.getStatus()).isEqualTo("Success");
        mockServer.verify();
    }
}
