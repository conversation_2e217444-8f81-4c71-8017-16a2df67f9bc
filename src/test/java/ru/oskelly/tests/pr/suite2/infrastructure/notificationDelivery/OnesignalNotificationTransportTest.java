package ru.oskelly.tests.pr.suite2.infrastructure.notificationDelivery;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestClientException;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.MockPublisherConfiguration;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.device.DeviceRepository;
import su.reddot.domain.model.device.Device;
import su.reddot.domain.model.device.DeviceDtype;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.commission.CommissionGridService;
import su.reddot.domain.service.dto.NotificationDeliveryResult;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.dto.notification.NotificationDTO;
import su.reddot.domain.service.master.MasterService;
import su.reddot.domain.service.master.MasterServiceRequest;
import su.reddot.domain.service.notification.NotificationService;
import su.reddot.domain.service.notification.NotificationWithBubbles;
import su.reddot.domain.service.task.ScheduledNotificationDeliveryTaskRunner;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.configuration.OskellyApplication;
import su.reddot.infrastructure.notificationDelivery.onesignal.OnesignalNotificationTransport;
import su.reddot.infrastructure.onesignal.OnesignalClient;
import su.reddot.infrastructure.onesignal.request.impl.UserDevicesNotificationRequest;
import su.reddot.infrastructure.onesignal.response.SendNotificationResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;

@ExtendWith(SpringExtension.class)
@SpringBootTest(
        classes = {
                OskellyApplication.class,
                MockPublisherConfiguration.class
        },
        properties = {
                "app.notificationDelivery.onesignal.enabled=true",
                "app.push.with-testmode=false",
                "app.host=https://test.host/"
        }
)
@MockBeans({
        @MockBean(ScheduledNotificationDeliveryTaskRunner.class)
})
@ActiveProfiles(profiles = AbstractSpringTest.testProfiles)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class OnesignalNotificationTransportTest {

    public static final long NOTIFICATION_ID = -1L;
    public static final String ONESIGNAL_EXCEPTION_MESSAGE = "Failed to send";

    public static final long USER_ID = NOTIFICATION_ID;
    public static final String TEST_APPLE_TOKEN = "some-test-apple-token";
    public static final String TEST_ANDROID_TOKEN = "some-test-android-token";
    public static final String NICKNAME = "IamTestUser";
    @MockBean
    NotificationService notificationService;
    @MockBean
    OnesignalClient onesignalClient;
    @MockBean
    UserService userService;
    @Autowired
    ApplicationEventPublisher applicationEventPublisher;
    @MockBean
    MasterService masterService;

    @Autowired
    DeviceRepository deviceRepository;
    @Autowired
    UserRepository userRepository;
    @Autowired
    OnesignalNotificationTransport onesignalNotificationTransport;
    @Autowired
    CommissionGridService commissionGridService;

    private Long currentUserId;

    @BeforeEach
    public void init() {
        User user = new User()
                .setId(USER_ID)
                .setNickname(NICKNAME)
                .setUserType(User.UserType.SIMPLE_USER)
                .setChangeTime(LocalDateTime.now())
                .setCommissionGrid(commissionGridService.getDefaultCommissionGrid());
        user = userRepository.save(user);
        currentUserId = user.getId();

        given(notificationService.getNotificationWithBubbles(anyLong()))
                .willReturn(notificationWithBubbles());
        Mockito.reset(applicationEventPublisher);
    }

    @AfterEach
    public void cleanup() {
        List<Device> devicesByUserId = deviceRepository.findDevicesByUserId(currentUserId);
        deviceRepository.deleteAll(devicesByUserId);
        userRepository.deleteById(currentUserId);
    }

    @Test
    public void sendNotificationSendsNotifications() throws IOException {
        Device appleDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_APPLE_TOKEN);

        Device androidDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AndroidDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_ANDROID_TOKEN);

        deviceRepository.save(appleDevice);
        deviceRepository.save(androidDevice);

        given(onesignalClient.sendNotification(any()))
                .willReturn(successfulNotificationResponse());

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(-1L);
        assertThat(notificationDeliveryResult.isSuccessfullySent()).isTrue();
    }

    @Test
    public void sendNotificationCallsMasterToDeleteExpiredTokensOnOnesignalError() throws IOException {
        Device appleDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_APPLE_TOKEN);

        Device androidDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AndroidDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_ANDROID_TOKEN);

        deviceRepository.save(appleDevice);
        deviceRepository.save(androidDevice);

        given(onesignalClient.sendNotification(any()))
                .willReturn(failedTokenNotificationResponse());

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SUCCESSFUL);

        ArgumentCaptor<Object> eventCaptor = ArgumentCaptor.forClass(Object.class);
        then(applicationEventPublisher)
                .should(times(1))
                .publishEvent(eventCaptor.capture());

        Object captured = eventCaptor.getValue();
        assertThat(captured).isInstanceOf(MasterServiceRequest.class);
        MasterServiceRequest masterServiceRequest = (MasterServiceRequest) captured;
        assertThat(masterServiceRequest.getUrl()).isEqualTo("/api/v2/master/device/deleteExpiredTokens");
        assertThat(masterServiceRequest.getMediaType()).isEqualTo(MediaType.APPLICATION_JSON_UTF8);
        assertThat(masterServiceRequest.getRequestEntityObject()).isInstanceOf(List.class);
        List<String> expiredTokens = (List<String>) masterServiceRequest.getRequestEntityObject();
        assertThat(expiredTokens).containsExactlyInAnyOrder(TEST_ANDROID_TOKEN, TEST_APPLE_TOKEN);
    }

    @Test
    public void shouldNotTrySendNotificationForDevicesWithEmptyToken() throws IOException {
        Device appleDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now());

        Device androidDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AndroidDevice)
                .setCreateTime(ZonedDateTime.now());

        deviceRepository.save(appleDevice);
        deviceRepository.save(androidDevice);

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        then(applicationEventPublisher)
                .should(never())
                .publishEvent(any());
        then(onesignalClient)
                .should(never())
                .sendNotification(any());

        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Отсутствуют устройства");
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
    }

    @Test
    public void deliverNotificationReturnsSkippedResultWhenNotificationWasDeleted() throws IOException {
        given(notificationService.getNotificationWithBubbles(anyLong()))
                .willReturn(null);

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было удалено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("onesignal");

        given(notificationService.getNotificationWithBubbles(anyLong()))
                .willReturn(new NotificationWithBubbles(null, 1));
        notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было удалено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("onesignal");

        NotificationWithBubbles notificationWithBubbles = notificationWithBubbles();
        notificationWithBubbles.getNotificationDTO().setDeleted(true);
        given(notificationService.getNotificationWithBubbles(anyLong()))
                .willReturn(notificationWithBubbles);
        notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было удалено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("onesignal");

        then(applicationEventPublisher)
                .should(never())
                .publishEvent(any());
        then(onesignalClient)
                .should(never())
                .sendNotification(any());
    }


    @Test
    public void deliverNotificationReturnsSkippedResultWhenNotificationIsRead() throws IOException {
        NotificationWithBubbles notificationWithBubbles = notificationWithBubbles();
        notificationWithBubbles.getNotificationDTO().setRead(true);
        given(notificationService.getNotificationWithBubbles(anyLong()))
                .willReturn(notificationWithBubbles);

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было прочитано");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("onesignal");


        then(applicationEventPublisher)
                .should(never())
                .publishEvent(any());
        then(onesignalClient)
                .should(never())
                .sendNotification(any());
    }

    @Test
    public void deliverNotificationReturnsSkippedResultWhenActionCompleted() throws IOException {
        NotificationWithBubbles notificationWithBubbles = notificationWithBubbles();
        notificationWithBubbles.getNotificationDTO().setActionCompleted(true);
        given(notificationService.getNotificationWithBubbles(anyLong()))
                .willReturn(notificationWithBubbles);

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.SKIPPED);
        assertThat(notificationDeliveryResult.getMessage()).isEqualTo("Уведомление было выполнено");
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("onesignal");


        then(applicationEventPublisher)
                .should(never())
                .publishEvent(any());
        then(onesignalClient)
                .should(never())
                .sendNotification(any());
    }

    @Test
    public void deliverNotificationReturnsFailedResultWhenExceptionInOnesignalClient() throws IOException {
        Device appleDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_APPLE_TOKEN);

        appleDevice = deviceRepository.save(appleDevice);
        List<Long> devicesIds = Collections.singletonList(appleDevice.getId());
        given(onesignalClient.sendNotification(any()))
                .willThrow(new RestClientException(ONESIGNAL_EXCEPTION_MESSAGE));

        NotificationDeliveryResult notificationDeliveryResult = onesignalNotificationTransport.deliverNotification(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getType()).isEqualByComparingTo(NotificationDeliveryResult.ResultType.FAILED);
        assertThat(notificationDeliveryResult.getMessage()).isIn("Ошибка: " + ONESIGNAL_EXCEPTION_MESSAGE);
        assertThat(notificationDeliveryResult.getMetadata()).isIn(devicesIds.toString());
        assertThat(notificationDeliveryResult.getNotificationId()).isEqualTo(NOTIFICATION_ID);
        assertThat(notificationDeliveryResult.getChannel()).isEqualTo("onesignal");


        then(applicationEventPublisher)
                .should(never())
                .publishEvent(any());
    }

    @Test
    public void deliverNotificationSendsValidUrlToOnesignal() throws IOException {
        Device appleDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_APPLE_TOKEN);

        Device androidDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AndroidDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_ANDROID_TOKEN);

        deviceRepository.save(appleDevice);
        deviceRepository.save(androidDevice);

        ArgumentCaptor<UserDevicesNotificationRequest> requestArgumentCaptor = ArgumentCaptor.forClass(UserDevicesNotificationRequest.class);

        given(onesignalClient.sendNotification(requestArgumentCaptor.capture()))
                .willReturn(successfulNotificationResponse());

        onesignalNotificationTransport.deliverNotification(-1L);

        UserDevicesNotificationRequest request = requestArgumentCaptor.getValue();
        assertThat(request.getAppUrl()).isEqualTo("https://test.host/products/1");
    }

    @Test
    public void deliverNotificationSetsContentAvailableTrue() throws IOException {
        Device appleDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AppleDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_APPLE_TOKEN);

        Device androidDevice = new Device()
                .setUserId(currentUserId)
                .setDtype(DeviceDtype.AndroidDevice)
                .setCreateTime(ZonedDateTime.now())
                .setToken(TEST_ANDROID_TOKEN);

        deviceRepository.save(appleDevice);
        deviceRepository.save(androidDevice);

        ArgumentCaptor<UserDevicesNotificationRequest> requestArgumentCaptor = ArgumentCaptor.forClass(UserDevicesNotificationRequest.class);

        given(onesignalClient.sendNotification(requestArgumentCaptor.capture()))
                .willReturn(successfulNotificationResponse());

        onesignalNotificationTransport.deliverNotification(-1L);

        UserDevicesNotificationRequest request = requestArgumentCaptor.getValue();
        assertThat(request.getContentAvailable()).isEqualTo(true);
    }


    private NotificationWithBubbles notificationWithBubbles() {
        NotificationDTO notificationDTO = new NotificationDTO();
        notificationDTO.setId(NOTIFICATION_ID);
        notificationDTO.setTargetObjectUrl("/products/1");
        UserDTO userDTO = new UserDTO();
        userDTO.setId(currentUserId);
        notificationDTO.setTargetUser(userDTO);
        return new NotificationWithBubbles(notificationDTO, 1);
    }

    private SendNotificationResponse failedTokenNotificationResponse() {
        SendNotificationResponse sendNotificationResponse = new SendNotificationResponse();
        SendNotificationResponse.SendError sendError = new SendNotificationResponse.SendError();
        sendError.setInvalidIosTokens(Collections.singletonList(TEST_APPLE_TOKEN));
        sendError.setInvalidAndroidRegIds(Collections.singletonList(TEST_ANDROID_TOKEN));
        sendNotificationResponse.setErrors(sendError);
        return sendNotificationResponse;
    }


    private SendNotificationResponse successfulNotificationResponse() {
        SendNotificationResponse sendNotificationResponse = new SendNotificationResponse();
        sendNotificationResponse.setId("test_id");
        sendNotificationResponse.setErrors(null);
        return sendNotificationResponse;
    }

}
