package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.pr.common.bonuses.BonusesServiceTestConfiguration;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.userbalance.UserBalanceChange;
import su.reddot.domain.model.userbalance.UserBalanceChangeType;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.bank.payout.UserBalancePayoutService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.oskelly.orderprocessing.internal.web.client.OrderMobileApi;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@TestMethodOrder(MethodOrderer.MethodName.class)
@ContextConfiguration(classes = {OrderFlowTestUtils.class, BonusesServiceTestConfiguration.class})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowOrderBalancePayoutsTest extends AbstractSpringTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AgentPaymentJobs agentPaymentJobs;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private CallInTransaction callInTransaction;

    @MockBean
    private OrderMobileApi orderMobileApi;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    public long _00_XX_OrderFlow_OrderBalancePayout_CreateOrder(Boolean enableDebtOnPayout, long payoutAmount) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(usualSellerId).subList(0, 5);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        //
        Order order = orderService.getOrder(orderInfo.getId());
        Assertions.assertThat(order.getId()).isEqualTo(orderInfo.getId());
        //
        if (Objects.nonNull(enableDebtOnPayout)) {
            orderFlowTestUtils.adminPanel_switchDebtOnPayout(orderInfo.getId(), enableDebtOnPayout);
        }
        //
        orderInfo.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, deliveryId);
        //
        orderFlowTestUtils.takeOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        for (OrderPositionDTO orderPosition : orderInfo.getItems()) {
            orderFlowTestUtils.adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        }
        //
        orderFlowTestUtils.adminPanel_Charge(orderInfo.getId());
        orderFlowTestUtils.processHoldComplete(orderInfo);
        //
        orderFlowTestUtils.sendOurselves(orderInfo.getId(), null);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(orderInfo.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        //
        orderFlowTestUtils.sendAgentReport(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        //
        orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.loadAgentReport(orderInfo.getId());
        //
        orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), payoutAmount, null, TcbBankService.SCHEMA, TransactionState.PREPARED);
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateSellerPayout(orderInfo.getId(), payoutAmount, null, TcbBankService.SCHEMA, TransactionState.DONE);
        //
        agentPaymentJobs.transferMoneyToSeller();
        //
        return orderInfo.getId();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_01_orderFlow_balancePayout_usualOrder_fullTcbPayout() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        long orderId = _00_XX_OrderFlow_OrderBalancePayout_CreateOrder(null, 112_500_00);
        //
        orderFlowTestUtils.validateAgentReportPayout(orderId, 1, 112_500_00L, null, TcbBankService.SCHEMA);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_02_orderFlow_balancePayout_usualOrder_fullDebtPayout() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.valueOf(-500_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _00_XX_OrderFlow_OrderBalancePayout_CreateOrder(null, 0);
        //
        orderFlowTestUtils.validateAgentReportPayout(orderId, 1, 112_500_00L, null, UserBalancePayoutService.USER_BALANCE_PAYOUT_SERVICE_NAME);
        //
        UserBalanceChange userBalanceChange = orderFlowTestUtils.getUserBalanceChangeByObjectId(usualSellerId, String.valueOf(orderId));
        Assertions.assertThat(userBalanceChange.getOperationType()).isEqualTo(UserBalanceChangeType.CREDIT);
        Assertions.assertThat(userBalanceChange.getAccountId()).isEqualTo(usualSellerCounterpartyId.toString());
        Assertions.assertThat(userBalanceChange.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(112_500_00, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_03_orderFlow_balancePayout_usualOrder_partialBankDebtPayout() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.valueOf(-100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _00_XX_OrderFlow_OrderBalancePayout_CreateOrder(null, 12_500_00);
        //
        orderFlowTestUtils.validateAgentReportPayout(orderId, 2, 100_000_00L, null, UserBalancePayoutService.USER_BALANCE_PAYOUT_SERVICE_NAME);
        orderFlowTestUtils.validateAgentReportPayout(orderId, 2, 12_500_00L, null, TcbBankService.SCHEMA);
        //
        UserBalanceChange userBalanceChange = orderFlowTestUtils.getUserBalanceChangeByObjectId(usualSellerId, String.valueOf(orderId));
        Assertions.assertThat(userBalanceChange.getOperationType()).isEqualTo(UserBalanceChangeType.CREDIT);
        Assertions.assertThat(userBalanceChange.getAccountId()).isEqualTo(usualSellerCounterpartyId.toString());
        Assertions.assertThat(userBalanceChange.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(100_000_00, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_04_orderFlow_balancePayout_usualOrder_disableDebtOnPayout() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.valueOf(-100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _00_XX_OrderFlow_OrderBalancePayout_CreateOrder(false, 112_500_00);
        //
        orderFlowTestUtils.validateAgentReportPayout(orderId, 1, 112_500_00L, null, TcbBankService.SCHEMA);
    }

    private void validateUnableToSend(long orderId) {
        ResponseEntity<String> sendFailResponse = orderFlowTestUtils.sendOurselves(orderId, null);
        Assertions.assertThat(sendFailResponse.getStatusCode().is4xxClientError()).isTrue();
        Exception sendFailException = orderFlowTestUtils.readExceptionFromText(sendFailResponse.getBody());
        Assertions.assertThat(sendFailException.getMessage()).matches("Заказ .*, подтверждение списания ДС невозможно: некорректное состояние заказа");
    }

    private void validateUnableToConfirm(long orderId) {
        ResponseEntity<String> confirmFailResponse = orderFlowTestUtils.confirmAgentReport(orderId, false);
        Assertions.assertThat(confirmFailResponse.getStatusCode().is4xxClientError()).isTrue();
        Exception sendFailException = orderFlowTestUtils.readExceptionFromText(confirmFailResponse.getBody());
        Assertions.assertThat(sendFailException.getMessage()).matches("Отчет о продаже .* некорректное состояние .*");
    }

    private long _02_XX_orderFlow_balancePayout_concierge_makeOrder(Boolean enableDebtOnPayout, boolean balanceChanging) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(usualSellerId).subList(0, 5);
        for (Product product : products) {
            product.setSelectedConciergeTime(LocalDateTime.now());
        }
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        //
        OrderService.InitOrderResult testOrder = orderFlowTestUtils.holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        orderFlowTestUtils.callOrderHoldCallback(testOrder.getOrderId(), testOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getOrderId(), true);
        Assertions.assertThat(orderInfo.getId()).isNotEqualTo(0);
        orderFlowTestUtils.validateOrderDeliveryState(orderInfo.getId(), null);
        //
        orderInfo.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        orderFlowTestUtils.changeAddressEndpoint(orderInfo.getId(), pickupId, deliveryId);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderDeliveryState(orderInfo.getId(), null);
        //
        orderFlowTestUtils.adminPanel_sendWithDeliveryCompany(orderInfo.getId(), HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderDeliveryState(orderInfo.getId(), DeliveryState.FROM_SELLER_TO_OFFICE);
        //
        if (Objects.nonNull(enableDebtOnPayout)) {
            orderFlowTestUtils.adminPanel_switchDebtOnPayout(orderInfo.getId(), enableDebtOnPayout);
        }
        //
        ResponseEntity<String> paymentFail = orderFlowTestUtils.adminPanel_paymentToConcierge(orderInfo.getId(), false);
        Assertions.assertThat(paymentFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownPayment = orderFlowTestUtils.readExceptionFromText(paymentFail.getBody());
        Assertions.assertThat(thrownPayment.getMessage())
                .matches("Продавец не выбрал реквизиты для оплаты. Необходимо выбрать реквизиты");
        //
        orderFlowTestUtils.changeSellerCounterparty(orderInfo.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        rollbackAndStartNewTransaction();
        validateUnableToSend(orderInfo.getId());
        //
        BigDecimal startBalance = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, null);
        //
        orderFlowTestUtils.adminPanel_paymentToConcierge(orderInfo.getId(), true);
        //
        BigDecimal afterPayment = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, null);
        Assertions.assertThat(afterPayment.compareTo(startBalance) != 0).isEqualTo(balanceChanging);
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        //
        orderFlowTestUtils.processHoldComplete(orderInfo);
        rollbackAndStartNewTransaction();
        validateUnableToSend(orderInfo.getId());
        validateUnableToConfirm(orderInfo.getId());
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        //
        orderFlowTestUtils.prepareSellerPayout(orderInfo.getId(), true);
        commitAndStartNewTransaction();
        validateUnableToSend(orderInfo.getId());
        validateUnableToConfirm(orderInfo.getId());
        //
        orderFlowTestUtils.transferMoneyToSellers(orderInfo.getId());
        commitAndStartNewTransaction();
        validateUnableToSend(orderInfo.getId());
        validateUnableToConfirm(orderInfo.getId());
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        //
        orderFlowTestUtils.validateMoneyToSellers(orderInfo.getId());
        commitAndStartNewTransaction();
        validateUnableToSend(orderInfo.getId());
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        //
        //orderFlowTestUtils.confirmAgentReport(orderInfo.getId(), true);
        //
        agentPaymentJobs.transferMoneyToSeller();
        commitAndStartNewTransaction();
        validateUnableToSend(orderInfo.getId());
        //
        //orderFlowTestUtils.loadAgentReport(orderInfo.getId());
        //
        BigDecimal afterTransfer = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, null);
        Assertions.assertThat(afterTransfer).isEqualByComparingTo(afterPayment);
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderDeliveryState(orderInfo.getId(), DeliveryState.FROM_SELLER_TO_OFFICE);
        //
        return orderInfo.getId();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_01_orderFlow_balancePayout_concierge_fullTcbPayout() {
        BigDecimal startBalance = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        long orderId = _02_XX_orderFlow_balancePayout_concierge_makeOrder(null, false);
        //
        orderFlowTestUtils.validateSellerPayout(orderId, 112_500_00L, null, TcbBankService.SCHEMA, TransactionState.DONE);
        orderFlowTestUtils.validateAgentReportPayout(orderId, 1, 112_500_00L, null, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.validateBalanceChange(usualSellerId, startBalance, BigDecimal.ZERO);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_02_orderFlow_balancePayout_concierge_fullDebtPayout() {
        BigDecimal startBalance = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.valueOf(-500_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _02_XX_orderFlow_balancePayout_concierge_makeOrder(null, true);
        //
        orderFlowTestUtils.validateSellerPayout(orderId, 0L, null, TcbBankService.SCHEMA, TransactionState.DONE);
        orderFlowTestUtils.validateAgentReportPayout(orderId, 1, 112_500_00L, null, UserBalancePayoutService.USER_BALANCE_PAYOUT_SERVICE_NAME);
        //
        orderFlowTestUtils.validateBalanceChange(usualSellerId, startBalance, BigDecimal.valueOf(-112_500_00L, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_03_orderFlow_balancePayout_concierge_partialBankDebtPayout() {
        BigDecimal startBalance = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.valueOf(-100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _02_XX_orderFlow_balancePayout_concierge_makeOrder(null, true);
        //
        orderFlowTestUtils.validateSellerPayout(orderId, 12_500_00L, null, TcbBankService.SCHEMA, TransactionState.DONE);
        orderFlowTestUtils.validateAgentReportPayout(orderId, 2, 100_000_00L, null, UserBalancePayoutService.USER_BALANCE_PAYOUT_SERVICE_NAME);
        orderFlowTestUtils.validateAgentReportPayout(orderId, 2, 12_500_00L, null, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.validateBalanceChange(usualSellerId, startBalance, BigDecimal.valueOf(-100_000_00L, 2));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_04_orderFlow_balancePayout_concierge_disableDebtOnPayout() {
        BigDecimal startBalance = orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.valueOf(-100_000_00, 2));
        commitAndStartNewTransaction();
        //
        long orderId = _02_XX_orderFlow_balancePayout_concierge_makeOrder(false, false);
        //
        orderFlowTestUtils.validateSellerPayout(orderId, 112_500_00L, null, TcbBankService.SCHEMA, TransactionState.DONE);
        orderFlowTestUtils.validateAgentReportPayout(orderId, 1, 112_500_00L, null, TcbBankService.SCHEMA);
        //
        orderFlowTestUtils.validateBalanceChange(usualSellerId, startBalance, BigDecimal.ZERO);
    }

}
