package ru.oskelly.tests.pr.suite1_2.domain.service.export;

import java.io.IOException;

import javax.xml.stream.XMLStreamWriter;

import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.infrastructure.export.feed.oneass.CustomersV2Exporter;
import su.reddot.infrastructure.export.feed.oneass.OneAss12StoreezExporter;
import su.reddot.infrastructure.export.feed.oneass.OneAssV2Exporter;
import su.reddot.infrastructure.export.feed.oneass.PaymentsToSellersExporter;
import su.reddot.infrastructure.export.feed.oneass.ProductsV2Exporter;
import su.reddot.infrastructure.export.feed.oneass.SalesV2Exporter;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_01)
public class OneAssExportTest extends AbstractSpringTest {

    private static final XMLStreamWriter dummyWriter = new DummyXMLStreamWriter();

    @Autowired
    private SalesV2Exporter oneAssSalesV2Exporter;

    @Autowired
    private ProductsV2Exporter oneAssProductsV2Exporter;

    @Autowired
    private CustomersV2Exporter oneAssCustomersV2Exporter;

    @Autowired
    private PaymentsToSellersExporter oneAssPaymentsToSellersExporter;

    @Autowired
    private OneAss12StoreezExporter oneAss12StoreezExporter;

    @Test
    public void testSalesV2Exporter() throws IOException {
        runExporter(oneAssSalesV2Exporter);
    }

    @Test
    public void testProductsV2Exporter() throws IOException {
        runExporter(oneAssProductsV2Exporter);
    }

    @Test
    public void testCustomersV2Exporter() throws IOException {
        runExporter(oneAssCustomersV2Exporter);
    }

    @Test
    public void testPaymentsToSellersExporter() throws IOException {
        runExporter(oneAssPaymentsToSellersExporter);
    }

    @Test
    public void testOneAss12StoreezExporter() throws IOException {
        runExporter(oneAss12StoreezExporter);
    }

    private static void runExporter(OneAssV2Exporter exporter) throws IOException {
        exporter.setCustomWriter(dummyWriter);
        exporter.defaultDelivery();
    }
}
