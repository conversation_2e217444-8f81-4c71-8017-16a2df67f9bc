package ru.oskelly.tests.pr.suite1_1.domain.service.address;

import su.reddot.domain.service.address.AddressUtils;
import su.reddot.domain.service.dto.AddressBreakdownDTO;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link AddressUtils}.
 */
public class AddressUtilsTest {

    @Test
    public void testCreateBreakdownNull() {
        Assertions.assertNull(AddressUtils.createBreakdown(null));
    }

    @Test
    public void testCreateBreakdownHappyPath() {
        AddressBreakdownDTO breakdown = AddressUtils.createBreakdown("ул. Ленина, д. 1, кв. 2");
        Assertions.assertNotNull(breakdown);
        Assertions.assertEquals("ул. Ленина", breakdown.getStreet());
        Assertions.assertEquals("1", breakdown.getHouse());
        Assertions.assertEquals("2", breakdown.getFlat());
    }

    @Test
    public void testCreateBreakdownFailed() {
        AddressBreakdownDTO breakdown = AddressUtils.createBreakdown("ул. Ленина, д. 1 кв. 2");
        Assertions.assertNull(breakdown);
    }
}
