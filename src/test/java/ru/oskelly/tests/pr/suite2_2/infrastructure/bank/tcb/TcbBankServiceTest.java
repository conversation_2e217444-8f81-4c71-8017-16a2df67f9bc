package ru.oskelly.tests.pr.suite2_2.infrastructure.bank.tcb;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockserver.client.MockServerClient;
import org.mockserver.integration.ClientAndServer;
import org.mockserver.model.Header;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.AbstractSpringTest;
import ru.oskelly.tests.TestJsonDataUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.dao.bankoperation.BankOperationRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.exception.CounterpartyException;
import su.reddot.domain.exception.OrderException;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.banktransaction.order.OrderBankOperation;
import su.reddot.domain.model.banktransaction.user.UserBankOperation;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.banktransaction.BankOperationService;
import su.reddot.domain.service.dto.BankOperationDTO;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.BankAccountService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.impl.tcb.response.GetOrderStateResponse;
import su.reddot.infrastructure.util.CallInTransaction;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.model.HttpRequest.request;
import static org.mockserver.model.HttpResponse.response;
import static org.mockserver.model.HttpStatusCode.OK_200;

@TestMethodOrder(MethodOrderer.MethodName.class)
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_02)
public class TcbBankServiceTest extends AbstractSpringTest {
    public static final String MOCK_SERVER_HOST = "localhost";
    public static final Integer MOCK_SERVER_PORT = 8181;
    public static final String cardBindAlreadyBoundCardNumber = "4784 76** **** 7254";
    public static final String cardBindFreshBindingCardNumber = "4784 76** **** 6604";
    public static final List<BankOperation> bankOperationClearList = new ArrayList<>();

    public static List<Order> orderClearList = new ArrayList<>();

    private ClientAndServer mockServer;
    private final ObjectMapper mapper = new ObjectMapper();

    @Value("${test.api.user-id}")
    private Long user01Id; // Aka buyer
    @Value("${test.api.user2-id}")
    private Long user02Id; // Aka seller

    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderPaymentService orderPaymentService;
    @Autowired
    private UserService userService;
    @Autowired
    private BankOperationService<BankOperation> bankOperationService;

    @Autowired
    private BankOperationRepository<BankOperation> bankOperationRepository; // 4 Cleanup
    @Autowired
    private OrderRepository orderRepository; // 4 Cleanup

    @Autowired
    private CallInTransaction callInTransaction;

    private BankAccountService bankAccountService; // not wired, instantiating in test setUp

    private User getUser01(){
        return userService.getUserById(user01Id).orElse(null);
    }

    private User getUser02(){
        return userService.getUserById(user02Id).orElse(null);
    }

    @BeforeEach
    public void setUpOneTest() {
        bankAccountService = orderService.getBankAccountService(TcbBankService.SCHEMA);
        mockServer = startClientAndServer(MOCK_SERVER_PORT);
    }

    @AfterEach
    @Transactional
    public void tearDownOneTest() {
        mockServer.stop();
        bankOperationRepository.deleteAll(bankOperationClearList);
        orderRepository.deleteAll(orderClearList);
        removeCardCounterpartiesForUser(getUser01());
        removeCardCounterpartiesForUser(getUser02());
    }

    @Test
    @Transactional
    public void _01_bankServiceCorrectInstance() {
        assertTrue(bankAccountService instanceof TcbBankService);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_bindCard1stTimeSuccess() throws IOException {
        removeCardCounterpartiesForUser(getUser01()); // Removing card if it`s already bound
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation = getUserBankOperation("_02_bindCard1stTimeSuccess", getUser01(), OperationType.CARD_BIND);
        String responseData = mapper.writeValueAsString(getTestOrderStateResponseCardBindOkay(userBankOperation.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData);
        bankAccountService.handleBindCard(userBankOperation.getUuid());
        commitAndStartNewTransaction();
        //
        String boundCardsList = getUser01().getCounterparties().stream().filter(CardCounterparty.class::isInstance).map(CardCounterparty.class::cast)
                .map(this::counterpartyValidatePropsToString).sorted().collect(Collectors.joining(";"));
        assertEquals("VISA,4784 76** **** 6604,Binder Fresh,********", boundCardsList);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_bindCard2ndTimeAlreadyBoundSuccess() throws IOException {
        removeCardCounterpartiesForUser(getUser01()); // Removing card if it`s already bound
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation = getUserBankOperation("_03_bindCard2ndTimeAlreadyBoundSuccess", getUser01(), OperationType.CARD_BIND);
        String responseData = mapper.writeValueAsString(getTestOrderStateResponseCardBindAlreadyBound(userBankOperation.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData);
        bankAccountService.handleBindCard(userBankOperation.getUuid());
        commitAndStartNewTransaction();
        //
        String boundCardsList = getUser01().getCounterparties().stream().filter(CardCounterparty.class::isInstance).map(CardCounterparty.class::cast)
                .map(this::counterpartyValidatePropsToString).sorted().collect(Collectors.joining(";"));
        assertEquals("VISA,4784 76** **** 7254,Binder Again,********", boundCardsList);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_bindSameCardToOneUserFail() throws IOException {
        removeCardCounterpartiesForUser(getUser01()); // Removing all cards if they already bound
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation01 = getUserBankOperation("_04_bindSameCardFail-1", getUser01(), OperationType.CARD_BIND);
        String responseData01 = mapper.writeValueAsString(getTestOrderStateResponseCardBindAlreadyBound(userBankOperation01.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData01);
        bankAccountService.handleBindCard(userBankOperation01.getUuid());
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation02 = getUserBankOperation("_04_bindSameCardFail-2", getUser01(), OperationType.CARD_BIND);
        String responseData02 = mapper.writeValueAsString(getTestOrderStateResponseCardBindAlreadyBound(userBankOperation02.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData02);
        try {
            bankAccountService.handleBindCard(userBankOperation02.getUuid());
        } catch (CounterpartyException needException) { // Better use error codes later
            assertTrue(Pattern.compile("Пользователь (.*) \\(\\d*\\): банковская карта (.*) уже привязана").matcher(needException.getMessage()).matches());
            rollbackAndStartNewTransaction();
        }
        //
        String boundCardsList = getUser01().getCounterparties().stream().filter(CardCounterparty.class::isInstance).map(CardCounterparty.class::cast)
                .map(this::counterpartyValidatePropsToString).sorted().collect(Collectors.joining(";"));
        assertEquals("VISA,4784 76** **** 7254,Binder Again,********", boundCardsList);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_bindDifferentCardsToOneUserSuccess() throws IOException {
        removeCardCounterpartiesForUser(getUser01()); // Removing all cards if they already bound
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation01 = getUserBankOperation("_05_bindBothCardsSuccess-1", getUser01(), OperationType.CARD_BIND);
        String responseData01 = mapper.writeValueAsString(getTestOrderStateResponseCardBindOkay(userBankOperation01.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData01);
        bankAccountService.handleBindCard(userBankOperation01.getUuid());
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation02 = getUserBankOperation("_05_bindBothCardsSuccess-2", getUser01(), OperationType.CARD_BIND);
        String responseData02 = mapper.writeValueAsString(getTestOrderStateResponseCardBindAlreadyBound(userBankOperation02.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData02);
        bankAccountService.handleBindCard(userBankOperation02.getUuid());
        commitAndStartNewTransaction();
        //
        String boundCardsList = getUser01().getCounterparties().stream().filter(CardCounterparty.class::isInstance).map(CardCounterparty.class::cast)
                .map(this::counterpartyValidatePropsToString).sorted().collect(Collectors.joining(";"));
        assertEquals("VISA,4784 76** **** 6604,Binder Fresh,********;VISA,4784 76** **** 7254,Binder Again,********", boundCardsList);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_bindSameCardToDifferentUsersSuccess() throws IOException {
        // --> User01
        removeCardCounterpartiesForUser(getUser01()); // Removing all cards if they already bound, User01
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation01 = getUserBankOperation("_06_bindSameCardToDifferentUsersSuccess-1", getUser01(), OperationType.CARD_BIND);
        String responseData01 = mapper.writeValueAsString(getTestOrderStateResponseCardBindOkay(userBankOperation01.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData01);
        bankAccountService.handleBindCard(userBankOperation01.getUuid());
        commitAndStartNewTransaction();
        //
        String boundCardsList01 = getUser01().getCounterparties().stream().filter(CardCounterparty.class::isInstance).map(CardCounterparty.class::cast)
                .map(this::counterpartyValidatePropsToString).sorted().collect(Collectors.joining(";"));
        assertEquals("VISA,4784 76** **** 6604,Binder Fresh,********", boundCardsList01);
        // --> User02
        removeCardCounterpartiesForUser(getUser02()); // Removing all cards if they already bound, User02
        commitAndStartNewTransaction();
        //
        UserBankOperation userBankOperation02 = getUserBankOperation("_06_bindSameCardToDifferentUsersSuccess-2", getUser02(), OperationType.CARD_BIND);
        String responseData02 = mapper.writeValueAsString(getTestOrderStateResponseCardBindOkay(userBankOperation02.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData02);
        bankAccountService.handleBindCard(userBankOperation02.getUuid());
        commitAndStartNewTransaction();
        //
        String boundCardsList02 = getUser02().getCounterparties().stream().filter(CardCounterparty.class::isInstance).map(CardCounterparty.class::cast)
                .map(this::counterpartyValidatePropsToString).sorted().collect(Collectors.joining(";"));
        assertEquals("VISA,4784 76** **** 6604,Binder Fresh,********", boundCardsList02);
    }

    private void mockTcbServer_api_v1_order_state_withResponse(String responseData02) {
        mockServer.clear(null);
        new MockServerClient(MOCK_SERVER_HOST, MOCK_SERVER_PORT)
                .when(request().withMethod("POST").withPath("/api/v1/order/state"))
                .respond(response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(responseData02)
                );
    }

    private void mockTcbServer_api_v1_reestrsendjson(String responseData) {
        mockServer.clear(null);
        new MockServerClient(MOCK_SERVER_HOST, MOCK_SERVER_PORT)
                .when(request().withMethod("POST").withPath("/api/v1/reestrsendjson"))
                .respond(response().withStatusCode(OK_200.code())
                        .withHeaders(new Header("Content-Type", "application/json;charset=UTF-8"))
                        .withBody(responseData)
                );
    }

    private void removeCardCounterpartiesForUser(User user) {
        user.getCounterparties().removeIf(CardCounterparty.class::isInstance);
        userService.save(user);
    }

    private UserBankOperation getUserBankOperation(String transactionId, User user, OperationType operationType) {
        UserBankOperation userBankOperation = new UserBankOperation();
        userBankOperation.setUser(user).setBankTransactionId(transactionId).setOperationType(operationType)
                .setUuid(UUID.randomUUID().toString())
                .setBank(TcbBankService.TCB_BANK_NAME).setState(TransactionState.INPROGRESS);
        bankOperationService.createOperation(userBankOperation);
        bankOperationClearList.add(userBankOperation);
        return userBankOperation;
    }

    private GetOrderStateResponse getTestOrderStateResponseCardBindOkay(String uuid) throws IOException {
        String responseJson = "{\"OrderInfo\":{\"ExtId\":\"fcf939e4-463c-415b-8ac9-bc5ab7fda32c\",\"OrderId\":931421,\"OrderNumber\":931421,\"State\":5,\"StateDescription\":\"Успешно\",\"Type\":\"HOLDUNREGISTEREDCARD\",\"Amount\":100,\"Fee\":0,\"DateTime\":\"2021-11-17T15:40:07\",\"StateUpdateDateTime\":\"2021-11-17T15:40:20\"},\"OrderAdditionalInfo\":{\"CardNumber\":\"4784 76** **** 6604\",\"CardRefID\":\"********\",\"CardBrand\":\"VISA\",\"CardExpYear\":\"2022\",\"CardExpMonth\":\"11\",\"CardIssuingBank\":\"TRANSCAPITAL BANK\",\"CardNumberHash\":\"b87cb2d5734c72b410cae472dc48f3e64696afce00cc4331bf278ebab5990820\",\"RC\":\"0\",\"MerchURL\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=fcf939e4-463c-415b-8ac9-bc5ab7fda32c\",\"Expire\":\"2021-11-17T15:50:07.0000000\",\"RedirectUrl\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=fcf939e4-463c-415b-8ac9-bc5ab7fda32c\",\"AutoRedirect\":\"True\",\"AdditionalParameters\":\"{}\",\"RRN\":\"********\",\"Data\":{\"CardNumber\":\"4784 76** **** 6604\",\"CardRefID\":\"********\",\"CardBrand\":\"VISA\",\"CardExpYear\":\"2022\",\"CardExpMonth\":\"11\",\"CardIssuingBank\":\"TRANSCAPITAL BANK\",\"CardNumberHash\":\"b87cb2d5734c72b410cae472dc48f3e64696afce00cc4331bf278ebab5990820\",\"RC\":\"0\",\"MerchURL\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=fcf939e4-463c-415b-8ac9-bc5ab7fda32c\",\"Expire\":\"2021-11-17T15:50:07.0000000\",\"RedirectUrl\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=fcf939e4-463c-415b-8ac9-bc5ab7fda32c\",\"AutoRedirect\":\"True\",\"AdditionalParameters\":\"{}\",\"RRN\":\"********\"}},\"ErrorInfo\":{\"ErrorCode\":0,\"ErrorMessage\":\"Успех\"}}";
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        GetOrderStateResponse getOrderStateResponse = mapper.readValue(responseJson, GetOrderStateResponse.class);
        getOrderStateResponse.getOrderInfo().setExtId(uuid);
        getOrderStateResponse.getOrderAdditionalInfo().setCardHolder("Binder Fresh");
        getOrderStateResponse.getOrderAdditionalInfo().setCardNumber(cardBindFreshBindingCardNumber);
        return getOrderStateResponse;
    }

    private GetOrderStateResponse getTestOrderStateResponseCardBindAlreadyBound(String uuid) throws IOException {
        String responseJson = "{\"OrderInfo\":{\"ExtId\":\"234409d8-475d-4ca5-9e23-3218d4b5db73\",\"OrderId\":931424,\"OrderNumber\":931424,\"State\":6,\"StateDescription\":\"Карта уже привязана! CardRefId=********\",\"Type\":\"HOLDUNREGISTEREDCARD\",\"Amount\":100,\"Fee\":0,\"DateTime\":\"2021-11-17T15:41:40\",\"StateUpdateDateTime\":\"2021-11-17T15:41:46\"},\"OrderAdditionalInfo\":{\"CardNumber\":\"4784 76** **** 7254\",\"CardBrand\":\"VISA\",\"CardExpYear\":\"2022\",\"CardExpMonth\":\"11\",\"CardIssuingBank\":\"TRANSCAPITAL BANK\",\"MerchURL\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=234409d8-475d-4ca5-9e23-3218d4b5db73\",\"Expire\":\"2021-11-17T15:51:40.0000000\",\"RedirectUrl\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=234409d8-475d-4ca5-9e23-3218d4b5db73\",\"AutoRedirect\":\"True\",\"AdditionalParameters\":\"{}\",\"Data\":{\"CardNumber\":\"4784 76** **** 7254\",\"CardBrand\":\"VISA\",\"CardExpYear\":\"2022\",\"CardExpMonth\":\"11\",\"CardIssuingBank\":\"TRANSCAPITAL BANK\",\"MerchURL\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=234409d8-475d-4ca5-9e23-3218d4b5db73\",\"Expire\":\"2021-11-17T15:51:40.0000000\",\"RedirectUrl\":\"http://localhost:8080/api/v2/callback/bindCard?redirect=%2Faccount&bankOperation=234409d8-475d-4ca5-9e23-3218d4b5db73\",\"AutoRedirect\":\"True\",\"AdditionalParameters\":\"{}\"}},\"ErrorInfo\":{\"ErrorCode\":0,\"ErrorMessage\":\"Успех\"}}";
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        GetOrderStateResponse getOrderStateResponse = mapper.readValue(responseJson, GetOrderStateResponse.class);
        getOrderStateResponse.getOrderInfo().setExtId(uuid);
        getOrderStateResponse.getOrderAdditionalInfo().setCardHolder("Binder Again");
        getOrderStateResponse.getOrderAdditionalInfo().setCardNumber(cardBindAlreadyBoundCardNumber);
        return getOrderStateResponse;
    }

    private String counterpartyValidatePropsToString(CardCounterparty cp) {
        return String.join(",", Arrays.asList(cp.getCardBrand(), cp.getCardNumber(), cp.getCardHolder(), cp.getCardRefId()));
    }

    @Test
    @Disabled("Think what to do: maybe remove it?")
    @Transactional
    @Rollback(value = false)
    public void _07_handleBankOperationsWithoutOrderSuccess() throws IOException {
        Order testOrder = getOrderForTesting();
        orderService.saveOrder(testOrder);
        //
        orderService.deleteOrder(testOrder.getId());
        //
        commitAndStartNewTransaction();
        //
        OrderBankOperation orderBankOperation = getOrderBankOperation(TcbBankService.TCB_BANK_NAME,"_07_handleBankOperationsWithoutOrderSuccess", testOrder, OperationType.HOLD);
        orderBankOperation.setState(TransactionState.INPROGRESS);
        bankOperationService.changeOperation(orderBankOperation);
        commitAndStartNewTransaction();
        //
        String responseData02 = mapper.writeValueAsString(getTestOrderStateResponseHoldFail(orderBankOperation.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData02);
        //
        List<BankOperationDTO> bankOperationsDTOs = bankAccountService.checkBankOperationsInProgress(Long.MAX_VALUE);
        BankOperationDTO bankOperationDTO = bankOperationsDTOs.stream().filter(bo -> bo.getId().equals(orderBankOperation.getId())).findAny().orElse(null);
        assertNotNull(bankOperationDTO);
        assertEquals("Время оплаты заявки истекло (Order)", bankOperationDTO.getStateUserText());
        //
        List<BankOperationDTO> processedDTOList = bankAccountService.handleBankOperations(bankOperationsDTOs);
        commitAndStartNewTransaction();
        //
        assertFalse(processedDTOList.stream().anyMatch(bo -> bo.getId().equals(orderBankOperation.getId())),
                "Order deleted: operation must not be processed");
        //
        BankOperation updatedOperation = bankOperationService.getBankOperation(orderBankOperation.getId());
        assertSame(TransactionState.INPROGRESS, updatedOperation.getState());
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _08_unableToDeleteOrderWithUnfinishedBankOperations() {
        long ordersId = callInTransaction.runInNewTransaction(() -> {
                    Order testOrder = getOrderForTesting();
                    orderService.saveOrder(testOrder);
                    //
                    OrderBankOperation orderBankOperation = getOrderBankOperation(TcbBankService.TCB_BANK_NAME, "_08_unableToDeleteOrderWithUnfinishedBankOperations", testOrder, OperationType.HOLD);
                    orderBankOperation.setState(TransactionState.INPROGRESS);
                    bankOperationService.changeOperation(orderBankOperation);
                    return testOrder.getId();
                });
        //
        Exception deleteFailException = Assertions.assertThrows(OrderException.class, () ->
                callInTransaction.runInNewTransaction(() -> orderService.deleteOrder(ordersId))
        );
        Assertions.assertTrue(deleteFailException.getMessage().matches("Невозможно удалить заказ: .*, повторите попытку позже"));
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _09_processHoldCancelledOperationSuccess() throws IOException {
        Order testOrder = getOrderForTesting();
        testOrder.setState(OrderState.HOLD_PROCESSING);
        testOrder.setAmount(BigDecimal.valueOf(100_00, 2));
        orderPaymentService.initOrderPayment(testOrder, null, null);
        orderService.saveOrder(testOrder);
        //
        commitAndStartNewTransaction();
        //
        OrderBankOperation orderBankOperation = getOrderBankOperation(TcbBankService.TCB_BANK_NAME,"_09_processHoldOperationSuccess", testOrder, OperationType.HOLD);
        orderBankOperation.setState(TransactionState.INPROGRESS);
        bankOperationService.changeOperation(orderBankOperation);
        commitAndStartNewTransaction();
        //
        String responseData02 = mapper.writeValueAsString(getTestOrderStateResponseHoldFail(orderBankOperation.getUuid()));
        mockTcbServer_api_v1_order_state_withResponse(responseData02);
        //
        List<BankOperationDTO> bankOperationsDTOs = bankAccountService.checkBankOperationsInProgress(Long.MAX_VALUE);
        BankOperationDTO bankOperationDTO = bankOperationsDTOs.stream().filter(bo -> bo.getId().equals(orderBankOperation.getId())).findAny().orElse(null);
        assertNotNull(bankOperationDTO);
        assertEquals("Время оплаты заявки истекло (Order)", bankOperationDTO.getStateUserText());
        //
        List<BankOperationDTO> processedDTOList = bankAccountService.handleBankOperations(bankOperationsDTOs);
        assertEquals(1, processedDTOList.stream().filter(bo -> bo.getId().equals(orderBankOperation.getId())).count());
        commitAndStartNewTransaction();
        //
        BankOperation updatedBankOperation = bankOperationService.getBankOperation(processedDTOList.get(0).getId());
        assertEquals("Время оплаты заявки истекло (Order)", updatedBankOperation.getStateUserText());
        assertSame(TransactionState.CANCELED, updatedBankOperation.getState());
        assertEquals(responseData02, updatedBankOperation.getRawResponse());
        LocalDateTime expectedPaymentSystemTime = LocalDateTime.parse("2021-10-29T07:02:42",
                DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        assertEquals(expectedPaymentSystemTime, updatedBankOperation.getPaymentSystemTime());
        //
        Order holdOrder = orderService.getOrder(testOrder.getId());
        assertEquals(OrderState.HOLD_ERROR, holdOrder.getState());
    }

    @Test
    @Transactional
    public void _10_processingOnlyOwnOperations() throws IOException {
        Order testOrder = getOrderForTesting();
        orderService.saveOrder(testOrder);
        //
        String coolBankNameText = TcbBankService.TCB_BANK_NAME;
        String failBankNameText = TcbBankService.TCB_BANK_NAME + TcbBankService.TCB_BANK_NAME; // Double Dragon!
        // Adding two cool, own operations
        getOrderBankOperation(coolBankNameText,"_10_processingOnlyOwnOperations_1", testOrder, OperationType.HOLD);
        getOrderBankOperation(coolBankNameText,"_10_processingOnlyOwnOperations_2", testOrder, OperationType.HOLD);
        // Adding two fail, foreign operations
        getOrderBankOperation(failBankNameText,"_10_processingOnlyOwnOperations_3", testOrder, OperationType.HOLD);
        getOrderBankOperation(failBankNameText,"_10_processingOnlyOwnOperations_4", testOrder, OperationType.HOLD);
        String responseData02 = mapper.writeValueAsString(getTestOrderStateResponseHoldFail(""));
        mockTcbServer_api_v1_order_state_withResponse(responseData02);
        //
        List<BankOperationDTO> processedDTOs = bankAccountService.checkBankOperationsInProgress(Long.MAX_VALUE);
        processedDTOs.forEach(boDTO -> assertEquals(coolBankNameText, boDTO.getPaymentSystem()));
        assertEquals(2, processedDTOs.size());
    }

    @Test
    @SneakyThrows
    @Transactional
    public void _11_validateOperations() {
        String jsondData = TestJsonDataUtils.readJsonTestData(this, null);
        mockTcbServer_api_v1_reestrsendjson(jsondData);
        bankAccountService.validateOperations(LocalDate.of(2022, 8, 23));
    }

    private OrderBankOperation getOrderBankOperation(String bankName, String transactionId, Order order, OperationType operationType) {
        OrderBankOperation orderBankOperation = new OrderBankOperation();
        orderBankOperation.setOrder(order).setBankTransactionId(transactionId).setOperationType(operationType)
                .setUuid(UUID.randomUUID().toString())
                .setBank(bankName).setState(TransactionState.INPROGRESS);
        bankOperationService.createOperation(orderBankOperation);
        bankOperationClearList.add(orderBankOperation);
        return orderBankOperation;
    }

    private Order getOrderForTesting() {
        Order order = new Order();
        order.setBuyer(getUser01());
        order.setUuid(UUID.randomUUID());
        order.setState(OrderState.CREATED);
        order.setPaymentVersion(TcbBankService.SCHEMA);
        orderService.saveOrder(order);
        orderClearList.add(order);
        return order;
    }

    private GetOrderStateResponse getTestOrderStateResponseHoldFail(String uuid) throws IOException {
        String responseJson = "{\"OrderInfo\":{\"ExtId\":\"ede2567e-2b35-458a-95ee-ac62d323cbbc\",\"OrderId\":**********,\"OrderNumber\":**********,\"State\":6,\"StateDescription\":\"Время оплаты заявки истекло (Order)\",\"Type\":\"FROMUNREGISTEREDCARD\",\"Amount\":535000,\"Fee\":0,\"DateTime\":\"2021-10-29T10:02:42\",\"StateUpdateDateTime\":\"2021-10-29T10:32:50\"},\"OrderAdditionalInfo\":{\"MercId\":\"102805\",\"GateId\":\"ECOM\",\"MerchURL\":\"https://oskelly.ru/api/v2/acquirers/tcb-1.0/verify-payment?redirectTo=https%3A%2F%2Foskelly.ru%2Forders%2F1209142&operationId=ede2567e-2b35-458a-95ee-ac62d323cbbc\",\"AdditionalParameters\":\"{}\",\"Data\":{\"MercId\":\"102805\",\"GateId\":\"ECOM\",\"MerchURL\":\"https://oskelly.ru/api/v2/acquirers/tcb-1.0/verify-payment?redirectTo=https%3A%2F%2Foskelly.ru%2Forders%2F1209142&operationId=ede2567e-2b35-458a-95ee-ac62d323cbbc\",\"AdditionalParameters\":\"{}\"}},\"ErrorInfo\":{\"ErrorCode\":0,\"ErrorMessage\":\"Успех\"}}";
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        GetOrderStateResponse getOrderStateResponse = mapper.readValue(responseJson, GetOrderStateResponse.class);
        getOrderStateResponse.getOrderInfo().setExtId(uuid);
        return getOrderStateResponse;
    }

}

// Inprogress
//2021-10-29 10:25:00.250  INFO 26093 --- [ool-2-thread-17] s.r.i.bank.impl.tcb.TcbBankClient        : Тело ответа от TKB: {"OrderInfo":{"ExtId":"ede2567e-2b35-458a-95ee-ac62d323cbbc","OrderId":**********,"OrderNumber":**********,"State":1,"StateDescription":"В обработке","Type":"FROMUNREGISTEREDCARD","Amount":535000,"DateTime":"2021-10-29T10:02:42","StateUpdateDateTime":"0001-01-01T00:00:00"},"OrderAdditionalInfo":{"MercId":"102805","MerchURL":"https://oskelly.ru/api/v2/acquirers/tcb-1.0/verify-payment?redirectTo=https%3A%2F%2Foskelly.ru%2Forders%2F1209142&operationId=ede2567e-2b35-458a-95ee-ac62d323cbbc","AdditionalParameters":"{}","Data":{"MercId":"102805","MerchURL":"https://oskelly.ru/api/v2/acquirers/tcb-1.0/verify-payment?redirectTo=https%3A%2F%2Foskelly.ru%2Forders%2F1209142&operationId=ede2567e-2b35-458a-95ee-ac62d323cbbc","AdditionalParameters":"{}"}},"ErrorInfo":{"ErrorCode":0,"ErrorMessage":"Успех"}}