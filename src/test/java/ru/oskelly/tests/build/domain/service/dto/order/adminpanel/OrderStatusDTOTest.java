package ru.oskelly.tests.build.domain.service.dto.order.adminpanel;

import java.util.stream.Stream;

import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.model.order.OrderStatus;
import su.reddot.domain.service.dto.order.adminpanel.OrderStatusDTO;

import org.junit.jupiter.api.Test;

/**
 * Unit tests for {@link OrderStatusDTO}.
 */
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_00)
public class OrderStatusDTOTest {

    /**
     * The test makes sure that every {@link OrderStatus} has a corresponding value in
     * {@link OrderStatusDTO}.
     */
    @Test
    public void testEveryOrderStatusIsInOrderStatusDTO() {
        Stream.of(OrderStatus.values()).forEach(
                orderStatus -> OrderStatusDTO.valueOf(orderStatus.name()));
    }
}
