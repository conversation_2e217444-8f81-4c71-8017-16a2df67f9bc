package su.reddot.component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ClientException;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.CartControllerV2Test;
import su.reddot.domain.dao.address.CityRepository;
import su.reddot.domain.dao.address.CountryRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.delivery.DeliveryInfoRepository;
import su.reddot.domain.dao.logistic.OrderWaybillsRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.address.City;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.delivery.DeliveryInfo;
import su.reddot.domain.model.logistic.OrderWaybills;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.adminpanel.orders.dto.SaveAddressEndpointParams;
import su.reddot.domain.service.dto.AddressDTO;
import su.reddot.domain.service.dto.AddressEndpointDTO;
import su.reddot.domain.service.dto.CityDTO;
import su.reddot.domain.service.dto.CountryDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.logistic.FiasIds;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.cart.CartRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Некоторые тесты обращаются к корзине для проверки функционала, н.п. торги, промокоды, публикация, бан и т.п.
 * Чтобы не плодить одинаковый код по добавлению товаров в корзину и получению данных из корзины создается этот класс.
 */
@Component
@Primary
@RequiredArgsConstructor
public class CartTestSupport {
    @Autowired
    private TestApiConfiguration testApiConfiguration;

    @Setter
    private ApiV2Client apiV2Client;
    @Setter
    private Long userId;

    @Autowired
    private AddressEndpointService addressEndpointService;
    @Autowired
    private UserService userService;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private CountryRepository countryRepository;
    @Autowired
    private CityRepository cityRepository;
    @Autowired
    private DeliveryInfoRepository deliveryInfoRepository;
    @Autowired
    private AddressTestSupport addressTestSupport;
    @Autowired
    private OrderWaybillsRepository orderWaybillsRepository;
    @Autowired
    private AddressEndpointRepository addressEndpointRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    public static String HOLD_V1_ENDPOINT = "/hold";
    public static String HOLD_V2_ENDPOINT = "/holdv2";

    private final String deliveryAddressEndpointCountry = "Россия";
	private final String deliveryAddressEndpointZip = "436842";
	private final String deliveryAddressEndpointRegion = "Московская область";
    private final String deliveryAddressEndpointRegionFiasId = FiasIds.MOSCOW_REGION;
	private final String deliveryAddressEndpointCity = "Москва";
    private final String deliveryAddressEndpointCityFiasId = FiasIds.MOSCOW;
	private final String deliveryAddressEndpointAddress = "Цветной бульвар, д.1";
	private final String deliveryAddressEndpointPhone = "+79202341740";
	private final String deliveryAddressEndpointFirstName = "Иван";
	private final String deliveryAddressEndpointaPatronymicName = "Сергеевич";
	private final String deliveryAddressEndpointaLastName = "Белых";

    private String getCartServiceUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/cart";
    }

    private String getCartDeliveryAddressEndpointUrl() {
        return getCartServiceUrl() + "/deliveryAddressEndpoint";
    }

    private String getCartItemsUrl() {
        return getCartServiceUrl() + "/items";
    }

    private String getCartCheckPromocodeUrl() {
        return getCartServiceUrl() + "/checkPromoCode";
    }

    private String getHoldUrl(String holdProcName) {
        return getCartServiceUrl() + holdProcName;
    }

    @SneakyThrows
    public GroupedCart getCart(boolean withAuthorizeParams, String currencyCode, Long countryId) {
        Map<String, String> getParams = new HashMap<>();
        Optional.ofNullable(currencyCode).ifPresent(value -> getParams.put("currencyCode", value));
        Optional.ofNullable(countryId).ifPresent(value -> getParams.put("countryId", value.toString()));
        ResponseEntity<String> response = apiV2Client.request(getCartServiceUrl(), getParams, HttpMethod.GET, null, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<GroupedCart> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<GroupedCart>>() {});
        return body.getData();
    }

    @SneakyThrows
    public GroupedCart getCart(boolean withAuthorizeParams, String currencyCode) {
        return getCart(withAuthorizeParams, currencyCode, null);
    }

    @SneakyThrows
    public GroupedCart getCart(CartRequest cartRequest, boolean withAuthorizeParams) {
        Map<String, String> getParams = new HashMap<>();
        Optional.ofNullable(cartRequest.getCurrencyCode()).ifPresent(value -> getParams.put("currencyCode", value));
        Optional.ofNullable(cartRequest.getCountryId()).ifPresent(value -> getParams.put("countryId", value.toString()));
        Optional.ofNullable(cartRequest.getPromoCode()).ifPresent(value -> getParams.put("promoCode", value));
        Optional.ofNullable(cartRequest.getSellerId()).ifPresent(value -> getParams.put("sellerId", value.toString()));
        if (cartRequest.isCheckoutMode()) { getParams.put("checkoutMode", String.valueOf(cartRequest.isCheckoutMode())); }
        ResponseEntity<String> response = apiV2Client.request(getCartServiceUrl(), getParams, HttpMethod.GET, null, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<GroupedCart> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<GroupedCart>>() {});
        return body.getData();
    }

    public void resetAuthInfo() {
        apiV2Client.clearCookies();
    }

    @SneakyThrows
    public GroupedCart cleanCart(boolean withAuthorizeParams) {
        ResponseEntity<String> response = apiV2Client.request(getCartServiceUrl(), null, HttpMethod.DELETE, null, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<GroupedCart> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<GroupedCart>>() {});
        assertNull(body.getData());
        return body.getData();
    }

    @SneakyThrows
    public GroupedCart addToCartWithParamsSuccessful(CartControllerV2Test.CartAddRequest cartAddRequest, boolean withAuthorizeParams) {
        MultiValueMap<String, Object> params = TestUtils.getMultivalueMapWithObjectFields(cartAddRequest);
        ResponseEntity<String> response = apiV2Client.request(getCartItemsUrl(), null, HttpMethod.PUT, MediaType.APPLICATION_FORM_URLENCODED, params, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        Api2Response<GroupedCart> body = objectMapper.readValue(response.getBody(), new TypeReference<Api2Response<GroupedCart>>() {});
        assertNotNull(body.getData());

        GroupedCart cart = body.getData();

        //В каждой группе finalAmount и finalAmountWithoutDeliveryCost отличаются на сумму доставки
        for(OrderDTO group : cart.getGroups()){
            assertNotNull(group.getClearAmount());
            assertNotNull(group.getFinalAmount());
            assertNotNull(group.getFinalAmountWithoutDeliveryCost());
            assertNotNull(group.getDeliveryCost());
            if (Objects.isNull(cartAddRequest.getCurrencyCode())) {
                assertEquals(group.getFinalAmount(), group.getFinalAmountWithoutDeliveryCost().add(group.getDeliveryCost()).add(group.getDutiesAmount()));
            }
        }

        return cart;
    }

    @SneakyThrows
    public void addToCartWithParamsUnsuccessful4xx(CartControllerV2Test.CartAddRequest cartAddRequest, boolean withAuthorizeParams) {
        MultiValueMap<String, Object> params = TestUtils.getMultivalueMapWithObjectFields(cartAddRequest);
        ResponseEntity<String> response = apiV2Client.request(getCartItemsUrl(), null, HttpMethod.PUT, MediaType.APPLICATION_FORM_URLENCODED, params, String.class, withAuthorizeParams);
        assertTrue(response.getStatusCode().is4xxClientError());
        assertNotNull(response.getBody());

        throw new ApiV2ClientException("4xx on addToCart", response.getBody(), null);
    }

    public GroupedCart addToCartSuccessful(@NonNull Long productId, @NonNull Long sizeId, Integer count, boolean withAuthorizeParams){
        CartControllerV2Test.CartAddRequest request = new CartControllerV2Test.CartAddRequest().setProductId(productId).setSizeId(sizeId).setCount(count);
        return addToCartWithParamsSuccessful(request, withAuthorizeParams);
    }

    public GroupedCart addToCartSuccessful(@NonNull Long productId, @NonNull Long sizeId, Integer count){
        return addToCartSuccessful(productId, sizeId, count, false);
    }


    public OrderDTO checkPromoCode(Long sellerId, String promoCode) {
        Map<String, String> params = TestUtils.getOneParamAsMap("sellerId", sellerId);
        params.put("promoCode", promoCode);
        ResponseEntity<Api2Response<OrderDTO>> response = apiV2Client.request(getCartCheckPromocodeUrl(), params, HttpMethod.GET, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public OrderService.InitOrderResult holdWithSetAddressEndpointPCnCC(String holdProcName, Long sellerId, String promoCode, String paymentSystem) {
        //Сначала нужно поставить точку доставки
        setCartAddressEndpoint();

        Map<String, String> params = TestUtils.getOneParamAsMap("sellerId", sellerId);
        if(promoCode != null) params.put("promoCode", promoCode);
        if(paymentSystem != null) params.put("paymentSystem", paymentSystem);
        ResponseEntity<Api2Response<OrderService.InitOrderResult>> response = apiV2Client.request(getHoldUrl(holdProcName), params, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public OrderService.InitOrderResult holdWithSetAddressEndpoint(String holdProcName, Long sellerId, String promoCode) {
        return holdWithSetAddressEndpointPCnCC(holdProcName, sellerId, promoCode, null);
    }

    public OrderService.InitOrderResult holdSuccessful(String holdProcName, Long sellerId, boolean withAuthorizeParams){
        ResponseEntity<Api2Response<OrderService.InitOrderResult>> response = apiV2Client.request(getHoldUrl(holdProcName), TestUtils.getOneParamAsMap("sellerId", sellerId), HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public OrderService.InitOrderResult holdWithPromoCodeSuccessful(String holdProcName, Long sellerId, String promoCode, boolean withAuthorizeParams){
        Map<String, String> params = TestUtils.getOneParamAsMap("sellerId", sellerId);
        params.put("promoCode", promoCode);
        ResponseEntity<Api2Response<OrderService.InitOrderResult>> response = apiV2Client.request(getHoldUrl(holdProcName), params, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, withAuthorizeParams);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public OrderService.InitOrderResult holdCartWithParams(Long sellerId, HoldRequest holdRequest) {
        String holdEndPoint = HOLD_V2_ENDPOINT;
        Map<String, String> params = TestUtils.getOneParamAsMap("sellerId", sellerId);
        if (Objects.nonNull(holdRequest)) {
            holdEndPoint = ObjectUtils.firstNonNull(holdRequest.getHoldEndPoint(), holdEndPoint);
            Optional.ofNullable(holdRequest.getPromoCode()).ifPresent(value -> params.put("promoCode", value));
            Optional.ofNullable(holdRequest.getCurrencyCode()).ifPresent(value -> params.put("currencyCode", value));
            Optional.ofNullable(holdRequest.getPaymentSystem()).ifPresent(value -> params.put("paymentSystem", value));
            Optional.ofNullable(holdRequest.getType()).ifPresent(value -> params.put("type", value));
            Optional.ofNullable(holdRequest.getPaymentBuyerCounterpartyId()).ifPresent(value -> params.put("paymentBuyerCounterpartyId", value.toString()));
        }

        ResponseEntity<Api2Response<OrderService.InitOrderResult>> response = apiV2Client.request(getHoldUrl(holdEndPoint), params, HttpMethod.POST, MediaType.APPLICATION_FORM_URLENCODED, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getData());
        return response.getBody().getData();
    }

    public void setCartAddressEndpoint() {
        ResponseEntity<String> response = apiV2Client.request(getCartDeliveryAddressEndpointUrl(), null, HttpMethod.PUT, getDeliveryAddressEndpointParam(), String.class, false);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        TestUtils.assertStringContainsAllWords(response.getBody(), deliveryAddressEndpointaLastName, deliveryAddressEndpointAddress, deliveryAddressEndpointPhone);
    }

    public AddressEndpoint getDeliveryAddressEndpoint() {
        AddressEndpointDTO addressEndpointDto = new AddressEndpointDTO();
        addressEndpointDto.setFirstName(deliveryAddressEndpointFirstName);
        addressEndpointDto.setPatronymicName(deliveryAddressEndpointaPatronymicName);
        addressEndpointDto.setLastName(deliveryAddressEndpointaLastName);
        addressEndpointDto.setPhone(deliveryAddressEndpointPhone);
        AddressDTO addressDto = new AddressDTO();
        addressDto.setZipCode(deliveryAddressEndpointZip);
        addressDto.setCountry(deliveryAddressEndpointCountry);
        addressDto.setRegion(deliveryAddressEndpointRegion);
        addressDto.setRegionFiasId(deliveryAddressEndpointRegionFiasId);
        addressDto.setCity(deliveryAddressEndpointCity);
        addressDto.setCityFiasId(deliveryAddressEndpointCityFiasId);
        addressDto.setAddress(deliveryAddressEndpointAddress);
        addressEndpointDto.setAddress(addressDto);
        return addressEndpointService.getAddressEndpoint(getBuyer(), addressEndpointDto);
    }

    public MultiValueMap<String, String> getDeliveryAddressEndpointParam() {
        return getDeliveryAddressEndpointParam(getDeliveryAddressEndpoint());
    }

    public AddressEndpoint getPricedDeliveryAddress() {
        Set<Long> pricedCountries = deliveryInfoRepository.findAll().stream()
                .map(DeliveryInfo::getCountry).filter(Objects::nonNull)
                .map(Country::getId).collect(Collectors.toSet());
        Country country = countryRepository.findAll().stream()
                .filter(c -> pricedCountries.contains(c.getId()))
                .filter(c -> c.getCurrency() != null && "EUR".equals(c.getCurrency().getIsoCode()))
                .findAny().orElseThrow(IllegalStateException::new);
        City city = cityRepository.findAllByCountryIdOrderByPopulationDesc(country.getId()).stream()
                .findAny().orElseThrow(IllegalStateException::new);
        addressTestSupport.addDeliveryCompanySupport(country.getId(), city.getId());
        CountryDTO countryDto = new CountryDTO();
        countryDto.setId(country.getId());
        countryDto.setName(country.getName());
        countryDto.setIsoCodeAlpha2(country.getIsoCodeAlpha2());
        CityDTO cityDto = new CityDTO();
        cityDto.setId(city.getId());
        cityDto.setName(city.getName());
        AddressEndpointDTO addressEndpointDto = new AddressEndpointDTO()
                .setFirstName(deliveryAddressEndpointFirstName)
                .setPatronymicName(deliveryAddressEndpointaPatronymicName)
                .setLastName(deliveryAddressEndpointaLastName)
                .setPhone(deliveryAddressEndpointPhone)
                .setAddress(new AddressDTO()
                        .setZipCode("123")
                        .setCountryData(countryDto)
                        .setCountry(country.getName())
                        .setRegion(city.getName())
                        .setCityData(cityDto)
                        .setCity(city.getName())
                        .setAddress("Улица Строителей 123"));
        SaveAddressEndpointParams params = SaveAddressEndpointParams.builder()
                .addressEndpoint(addressEndpointDto).user(getBuyer()).includedDeliveryCost(false)
                .build();
        return addressEndpointRepository.getOne(addressEndpointService.save(params).getId());
    }

    public MultiValueMap<String, String> getDeliveryAddressEndpointParam(AddressEndpoint addressEndpoint) {
        return TestUtils.getOneParamAsMultiValueMap("deliveryAddressEndpointId", addressEndpoint.getId());
    }

    public User getBuyer() {
        return userService.getUserById(userId).orElse(null);
    }

    public void cleanup(){
        List<Order> buyerOrders = orderRepository.findAllByBuyerOrderByIdDesc(getBuyer());
        for (Order order : buyerOrders) {
            List<OrderWaybills> orderWaybills = orderWaybillsRepository.findAllByOrder(order);
            orderWaybillsRepository.deleteAll(orderWaybills);
        }
        orderRepository.deleteAll(buyerOrders);
        AddressEndpoint addressEndpoint = getDeliveryAddressEndpoint();
        if (addressEndpoint != null){
            jdbcTemplate.execute("DELETE FROM address_endpoint WHERE id=" + addressEndpoint.getId());
            //addressEndpointService.delete(addressEndpoint);
        }
    }


}
