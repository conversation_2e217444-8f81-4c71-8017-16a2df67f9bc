package su.reddot.infrastructure.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;

public class CalculateQueryHashUtilTest {

    @Test
    public void calculateQueryHashThreadSafe() throws ExecutionException, InterruptedException {
        ArrayList<String> queries = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            queries.add("I am some query");
        }

        List<CompletableFuture<String>> futures = queries
            .stream().parallel()
            .map(q -> CompletableFuture.supplyAsync(() -> CalculateQueryHashUtil.calculateMurmurHash(q)))
            .collect(Collectors.toList());

        Set<String> murmurHashes = new HashSet<>();
        for (CompletableFuture<String> f : futures) {
            murmurHashes.add(f.get());
        }

        assertEquals(1, murmurHashes.size(), "all murmur hashes must be same");
    }
}
