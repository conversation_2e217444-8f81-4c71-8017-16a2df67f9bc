package su.reddot.domain.dao.activity;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import su.reddot.domain.model.activity.Activity;

public interface ActivityTestRepository <T extends Activity> extends JpaRepository<T, Long> {

    @Query(value = "SELECT * FROM activity a WHERE " +
            "(user_id IS NOT NULL AND user_id = :userId) OR (guest_token IS NOT NULL AND guest_token <> '' AND guest_token = :guestToken) ORDER BY update_time DESC",
            nativeQuery = true)
    List<T> findAllActivities(@Param("userId") Long userId, @Param("guestToken") String guestToken);

    @Query(value = "SELECT * FROM activity a WHERE dtype = :dtype " +
            "AND (a.product_id = :objectId OR a.brand_id = :objectId OR a.profile_id = :objectId OR a.order_id = :objectId) " +
            "ORDER BY update_time DESC LIMIT 1",
            nativeQuery = true)
    T findLastActivityByObjectIdAndDtype(@Param("objectId") Long objectId, @Param("dtype") String dtype);

    @Query(value = "SELECT * FROM activity a WHERE dtype = :dtype " +
            "AND ((user_id IS NOT NULL AND user_id = :userId) OR (guest_token IS NOT NULL AND guest_token <> '' AND guest_token = :guestToken)) ORDER BY update_time DESC, id DESC LIMIT :limit",
            nativeQuery = true)
    List<T> findLastActivities(@Param("dtype") String dtype, @Param("userId") Long userId, @Param("guestToken") String guestToken, @Param("limit") int limit);

    @Modifying
    @Transactional
    void deleteByUserIdOrGuestToken(Long userId, String guestToken);
}
